/**
 * 表格优化工具
 * 提供虚拟滚动、分页缓存、搜索防抖等功能
 */

class TableOptimizer {
    constructor() {
        this.cache = new Map();
        this.searchDebounceTimeout = null;
        this.init();
    }

    init() {
        this.setupTableOptimizations();
        this.setupSearchOptimizations();
        this.setupPaginationOptimizations();
    }

    /**
     * 设置表格优化
     */
    setupTableOptimizations() {
        // 为所有数据表格添加优化
        document.querySelectorAll('[data-table-optimize]').forEach(table => {
            this.optimizeTable(table);
        });

        // 监听动态添加的表格
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1) {
                        const tables = node.querySelectorAll('[data-table-optimize]');
                        tables.forEach(table => this.optimizeTable(table));
                    }
                });
            });
        });

        observer.observe(document.body, { childList: true, subtree: true });
    }

    /**
     * 优化单个表格
     */
    optimizeTable(table) {
        // 添加加载状态容器
        this.addLoadingContainer(table);
        
        // 设置行悬停效果
        this.setupRowHover(table);
        
        // 设置选择功能
        this.setupRowSelection(table);
        
        // 设置排序功能
        this.setupSorting(table);
        
        // 设置虚拟滚动（对于大数据量）
        if (table.getAttribute('data-virtual-scroll') === 'true') {
            this.setupVirtualScroll(table);
        }
    }

    /**
     * 添加加载状态容器
     */
    addLoadingContainer(table) {
        if (table.querySelector('.table-loading-container')) return;

        const loadingContainer = document.createElement('div');
        loadingContainer.className = 'table-loading-container hidden absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center z-10';
        loadingContainer.innerHTML = `
            <div class="text-center">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <div class="mt-2 text-sm text-gray-600">加载中...</div>
            </div>
        `;

        const wrapper = table.closest('.table-wrapper') || table.parentNode;
        if (wrapper && !wrapper.style.position) {
            wrapper.style.position = 'relative';
        }
        wrapper.appendChild(loadingContainer);
    }

    /**
     * 显示表格加载状态
     */
    showTableLoading(table) {
        const container = table.closest('.table-wrapper') || table.parentNode;
        const loadingContainer = container.querySelector('.table-loading-container');
        if (loadingContainer) {
            loadingContainer.classList.remove('hidden');
        }
    }

    /**
     * 隐藏表格加载状态
     */
    hideTableLoading(table) {
        const container = table.closest('.table-wrapper') || table.parentNode;
        const loadingContainer = container.querySelector('.table-loading-container');
        if (loadingContainer) {
            loadingContainer.classList.add('hidden');
        }
    }

    /**
     * 设置行悬停效果
     */
    setupRowHover(table) {
        const tbody = table.querySelector('tbody');
        if (!tbody) return;

        tbody.addEventListener('mouseenter', (e) => {
            if (e.target.closest('tr')) {
                e.target.closest('tr').classList.add('bg-gray-50');
            }
        }, true);

        tbody.addEventListener('mouseleave', (e) => {
            if (e.target.closest('tr')) {
                e.target.closest('tr').classList.remove('bg-gray-50');
            }
        }, true);
    }

    /**
     * 设置行选择功能
     */
    setupRowSelection(table) {
        const selectAllCheckbox = table.querySelector('input[type="checkbox"][data-select-all]');
        const rowCheckboxes = table.querySelectorAll('tbody input[type="checkbox"][data-select-row]');

        if (!selectAllCheckbox || rowCheckboxes.length === 0) return;

        // 全选/取消全选
        selectAllCheckbox.addEventListener('change', (e) => {
            const isChecked = e.target.checked;
            rowCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
                this.updateRowSelection(checkbox.closest('tr'), isChecked);
            });
            this.updateSelectionCounter(table);
        });

        // 单行选择
        rowCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                this.updateRowSelection(e.target.closest('tr'), e.target.checked);
                this.updateSelectAllState(table);
                this.updateSelectionCounter(table);
            });
        });
    }

    /**
     * 更新行选择状态
     */
    updateRowSelection(row, isSelected) {
        if (isSelected) {
            row.classList.add('bg-blue-50', 'border-blue-200');
        } else {
            row.classList.remove('bg-blue-50', 'border-blue-200');
        }
    }

    /**
     * 更新全选状态
     */
    updateSelectAllState(table) {
        const selectAllCheckbox = table.querySelector('input[type="checkbox"][data-select-all]');
        const rowCheckboxes = table.querySelectorAll('tbody input[type="checkbox"][data-select-row]');
        
        const checkedCount = Array.from(rowCheckboxes).filter(cb => cb.checked).length;
        
        if (checkedCount === 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        } else if (checkedCount === rowCheckboxes.length) {
            selectAllCheckbox.checked = true;
            selectAllCheckbox.indeterminate = false;
        } else {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = true;
        }
    }

    /**
     * 更新选择计数器
     */
    updateSelectionCounter(table) {
        const rowCheckboxes = table.querySelectorAll('tbody input[type="checkbox"][data-select-row]');
        const checkedCount = Array.from(rowCheckboxes).filter(cb => cb.checked).length;
        
        const counter = document.querySelector('[data-selection-counter]');
        if (counter) {
            counter.textContent = checkedCount;
        }

        // 显示/隐藏批量操作工具栏
        const batchActions = document.querySelector('[data-batch-actions]');
        if (batchActions) {
            if (checkedCount > 0) {
                batchActions.classList.remove('hidden');
            } else {
                batchActions.classList.add('hidden');
            }
        }
    }

    /**
     * 设置排序功能
     */
    setupSorting(table) {
        const sortableHeaders = table.querySelectorAll('th[data-sortable]');
        
        sortableHeaders.forEach(header => {
            header.style.cursor = 'pointer';
            header.classList.add('select-none');
            
            // 添加排序图标
            if (!header.querySelector('.sort-icon')) {
                const icon = document.createElement('span');
                icon.className = 'sort-icon ml-1 text-gray-400';
                icon.innerHTML = '↕';
                header.appendChild(icon);
            }

            header.addEventListener('click', () => {
                this.handleSort(table, header);
            });
        });
    }

    /**
     * 处理排序
     */
    handleSort(table, header) {
        const sortField = header.getAttribute('data-sortable');
        const currentOrder = header.getAttribute('data-sort-order') || 'none';
        
        // 清除其他列的排序状态
        table.querySelectorAll('th[data-sortable]').forEach(th => {
            if (th !== header) {
                th.setAttribute('data-sort-order', 'none');
                const icon = th.querySelector('.sort-icon');
                if (icon) icon.innerHTML = '↕';
            }
        });

        // 设置当前列的排序状态
        let newOrder;
        switch (currentOrder) {
            case 'none':
            case 'desc':
                newOrder = 'asc';
                break;
            case 'asc':
                newOrder = 'desc';
                break;
        }

        header.setAttribute('data-sort-order', newOrder);
        const icon = header.querySelector('.sort-icon');
        if (icon) {
            icon.innerHTML = newOrder === 'asc' ? '↑' : '↓';
        }

        // 触发排序请求
        this.requestSort(table, sortField, newOrder);
    }

    /**
     * 请求排序
     */
    async requestSort(table, field, order) {
        this.showTableLoading(table);
        
        try {
            const url = new URL(window.location.href);
            url.searchParams.set('sort', field);
            url.searchParams.set('order', order);
            url.searchParams.set('ajax', '1');

            const response = await window.optimizedFetch(url.toString(), {
                useCache: false
            });

            if (response.success && response.html) {
                // 更新表格内容
                const tbody = table.querySelector('tbody');
                if (tbody) {
                    tbody.innerHTML = response.html;
                    this.setupRowSelection(table);
                }
            }
        } catch (error) {
            console.error('Sort request failed:', error);
            window.showNotification('排序失败，请稍后重试', 'error');
        } finally {
            this.hideTableLoading(table);
        }
    }

    /**
     * 设置搜索优化
     */
    setupSearchOptimizations() {
        const searchInputs = document.querySelectorAll('[data-table-search]');
        
        searchInputs.forEach(input => {
            input.addEventListener('input', (e) => {
                const tableId = input.getAttribute('data-table-search');
                const table = document.getElementById(tableId) || document.querySelector(`[data-table-id="${tableId}"]`);
                
                if (table) {
                    this.debouncedSearch(table, e.target.value);
                }
            });
        });
    }

    /**
     * 防抖搜索
     */
    debouncedSearch(table, query) {
        if (this.searchDebounceTimeout) {
            clearTimeout(this.searchDebounceTimeout);
        }

        this.searchDebounceTimeout = setTimeout(() => {
            this.performSearch(table, query);
        }, 300);
    }

    /**
     * 执行搜索
     */
    async performSearch(table, query) {
        this.showTableLoading(table);
        
        try {
            const url = new URL(window.location.href);
            url.searchParams.set('search', query);
            url.searchParams.set('ajax', '1');

            const response = await window.optimizedFetch(url.toString(), {
                useCache: false
            });

            if (response.success && response.html) {
                const tbody = table.querySelector('tbody');
                if (tbody) {
                    tbody.innerHTML = response.html;
                    this.setupRowSelection(table);
                }
                
                // 更新分页信息
                if (response.pagination) {
                    this.updatePagination(response.pagination);
                }
            }
        } catch (error) {
            console.error('Search request failed:', error);
            window.showNotification('搜索失败，请稍后重试', 'error');
        } finally {
            this.hideTableLoading(table);
        }
    }

    /**
     * 设置分页优化
     */
    setupPaginationOptimizations() {
        document.addEventListener('click', (e) => {
            const paginationLink = e.target.closest('[data-pagination-link]');
            if (paginationLink) {
                e.preventDefault();
                this.handlePaginationClick(paginationLink);
            }
        });
    }

    /**
     * 处理分页点击
     */
    async handlePaginationClick(link) {
        const url = link.href;
        const tableSelector = link.getAttribute('data-table-target') || '[data-table-optimize]';
        const table = document.querySelector(tableSelector);
        
        if (!table) return;

        // 检查缓存
        const cacheKey = `pagination_${url}`;
        let response = window.performanceOptimizer.getCache(cacheKey);
        
        if (!response) {
            this.showTableLoading(table);
            
            try {
                const ajaxUrl = new URL(url);
                ajaxUrl.searchParams.set('ajax', '1');
                
                response = await window.optimizedFetch(ajaxUrl.toString(), {
                    useCache: true
                });
                
                // 缓存分页结果
                window.performanceOptimizer.setCache(cacheKey, response, 2 * 60 * 1000); // 2分钟缓存
            } catch (error) {
                console.error('Pagination request failed:', error);
                window.showNotification('加载失败，请稍后重试', 'error');
                return;
            } finally {
                this.hideTableLoading(table);
            }
        }

        if (response.success && response.html) {
            // 更新表格内容
            const tbody = table.querySelector('tbody');
            if (tbody) {
                tbody.innerHTML = response.html;
                this.setupRowSelection(table);
            }
            
            // 更新分页信息
            if (response.pagination) {
                this.updatePagination(response.pagination);
            }
            
            // 更新URL
            history.pushState(null, '', url);
        }
    }

    /**
     * 更新分页信息
     */
    updatePagination(paginationData) {
        const paginationContainer = document.querySelector('[data-pagination-container]');
        if (paginationContainer && paginationData.html) {
            paginationContainer.innerHTML = paginationData.html;
        }
    }

    /**
     * 虚拟滚动设置（用于大数据量表格）
     */
    setupVirtualScroll(table) {
        // 这是一个简化的虚拟滚动实现
        const tbody = table.querySelector('tbody');
        if (!tbody) return;

        const itemHeight = 50; // 每行高度
        const containerHeight = 400; // 可视区域高度
        const visibleItems = Math.ceil(containerHeight / itemHeight);
        
        let totalItems = parseInt(table.getAttribute('data-total-items')) || 0;
        let scrollTop = 0;
        let startIndex = 0;
        
        // 创建虚拟滚动容器
        const scrollContainer = document.createElement('div');
        scrollContainer.style.height = `${containerHeight}px`;
        scrollContainer.style.overflowY = 'auto';
        
        const virtualTable = table.cloneNode(true);
        virtualTable.querySelector('tbody').style.height = `${totalItems * itemHeight}px`;
        
        scrollContainer.appendChild(virtualTable);
        table.parentNode.replaceChild(scrollContainer, table);
        
        // 监听滚动事件
        scrollContainer.addEventListener('scroll', this.throttle(() => {
            scrollTop = scrollContainer.scrollTop;
            startIndex = Math.floor(scrollTop / itemHeight);
            
            this.updateVisibleRows(virtualTable, startIndex, visibleItems);
        }, 16)); // 60fps
    }

    /**
     * 更新可见行
     */
    async updateVisibleRows(table, startIndex, visibleItems) {
        const endIndex = Math.min(startIndex + visibleItems, parseInt(table.getAttribute('data-total-items')));
        
        try {
            const response = await window.optimizedFetch(`/api/table-data/?start=${startIndex}&end=${endIndex}`, {
                useCache: true
            });
            
            if (response.success && response.rows) {
                const tbody = table.querySelector('tbody');
                tbody.innerHTML = response.rows.map(row => this.renderRow(row)).join('');
                
                // 设置偏移
                tbody.style.transform = `translateY(${startIndex * 50}px)`;
            }
        } catch (error) {
            console.error('Virtual scroll update failed:', error);
        }
    }

    /**
     * 渲染行
     */
    renderRow(rowData) {
        // 这里需要根据具体的数据结构来实现
        return `<tr>${Object.values(rowData).map(value => `<td>${value}</td>`).join('')}</tr>`;
    }

    /**
     * 节流函数
     */
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
}

// 全局实例
window.tableOptimizer = new TableOptimizer();

// 导出便捷函数
window.showTableLoading = (table) => window.tableOptimizer.showTableLoading(table);
window.hideTableLoading = (table) => window.tableOptimizer.hideTableLoading(table);