/**
 * JWT认证管理工具
 * 提供统一的JWT token管理和认证功能
 */

class JWTAuthManager {
    constructor() {
        this.tokenKey = 'access_token';
        this.refreshTokenKey = 'refresh_token';
        this.userInfoKey = 'user_info';
        this.baseURL = window.location.origin;
    }

    /**
     * 获取存储的token
     */
    getToken() {
        return localStorage.getItem(this.tokenKey);
    }

    /**
     * 获取refresh token
     */
    getRefreshToken() {
        return localStorage.getItem(this.refreshTokenKey);
    }

    /**
     * 获取用户信息
     */
    getUserInfo() {
        const userInfo = localStorage.getItem(this.userInfoKey);
        return userInfo ? JSON.parse(userInfo) : null;
    }

    /**
     * 保存token和用户信息
     */
    saveTokens(accessToken, refreshToken, userInfo = null) {
        localStorage.setItem(this.tokenKey, accessToken);
        if (refreshToken) {
            localStorage.setItem(this.refreshTokenKey, refreshToken);
        }
        if (userInfo) {
            localStorage.setItem(this.userInfoKey, JSON.stringify(userInfo));
        }
    }

    /**
     * 清除所有认证信息
     */
    clearTokens() {
        localStorage.removeItem(this.tokenKey);
        localStorage.removeItem(this.refreshTokenKey);
        localStorage.removeItem(this.userInfoKey);
    }

    /**
     * 检查是否已登录
     */
    isAuthenticated() {
        return !!this.getToken();
    }

    /**
     * 获取认证请求头
     */
    getAuthHeaders() {
        const token = this.getToken();
        return token ? {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        } : {};
    }

    /**
     * 验证token有效性
     */
    async validateToken() {
        const token = this.getToken();
        if (!token) return false;

        try {
            const response = await fetch('/api/token/status/', {
                method: 'GET',
                headers: this.getAuthHeaders()
            });
            
            if (response.ok) {
                const data = await response.json();
                return data.success && data.data.valid;
            }
            return false;
        } catch (error) {
            console.error('Token验证失败:', error);
            return false;
        }
    }

    /**
     * 刷新access token
     */
    async refreshAccessToken() {
        const refreshToken = this.getRefreshToken();
        if (!refreshToken) {
            this.clearTokens();
            return false;
        }

        try {
            const response = await fetch('/api/token/refresh/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ refresh_token: refreshToken })
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.saveTokens(data.tokens.access_token, null);
                    return true;
                }
            }
            
            this.clearTokens();
            return false;
        } catch (error) {
            console.error('Token刷新失败:', error);
            this.clearTokens();
            return false;
        }
    }

    /**
     * 执行认证请求（自动处理token刷新）
     */
    async authenticatedRequest(url, options = {}) {
        const headers = {
            ...this.getAuthHeaders(),
            ...options.headers
        };

        let response = await fetch(url, {
            ...options,
            headers
        });

        // 如果token过期，尝试刷新
        if (response.status === 401) {
            const refreshed = await this.refreshAccessToken();
            if (refreshed) {
                // 使用新token重试请求
                headers.Authorization = `Bearer ${this.getToken()}`;
                response = await fetch(url, {
                    ...options,
                    headers
                });
            } else {
                // 刷新失败，重定向到登录页
                this.redirectToLogin();
                return null;
            }
        }

        return response;
    }

    /**
     * 登录
     */
    async login(username, password) {
        try {
            const response = await fetch('/api/login/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ username, password })
            });

            const data = await response.json();
            
            if (data.success) {
                this.saveTokens(
                    data.tokens.access_token,
                    data.tokens.refresh_token,
                    data.user
                );
                return { success: true, user: data.user };
            } else {
                return { success: false, message: data.message };
            }
        } catch (error) {
            console.error('登录失败:', error);
            return { success: false, message: '网络错误，请稍后重试' };
        }
    }

    /**
     * 退出登录
     */
    async logout() {
        const token = this.getToken();
        if (token) {
            try {
                await fetch('/api/logout/', {
                    method: 'POST',
                    headers: this.getAuthHeaders()
                });
            } catch (error) {
                console.error('退出登录请求失败:', error);
            }
        }
        
        this.clearTokens();
        this.redirectToLogin();
    }

    /**
     * 从所有设备退出
     */
    async logoutAllDevices() {
        const token = this.getToken();
        if (token) {
            try {
                await fetch('/api/logout/all/', {
                    method: 'POST',
                    headers: this.getAuthHeaders()
                });
            } catch (error) {
                console.error('全设备退出请求失败:', error);
            }
        }
        
        this.clearTokens();
        this.redirectToLogin();
    }

    /**
     * 重定向到登录页
     */
    redirectToLogin() {
        window.location.href = '/admin/login/';
    }

    /**
     * 检查认证状态并跳转
     */
    async checkAuthAndRedirect() {
        const isValid = await this.validateToken();
        if (!isValid) {
            this.redirectToLogin();
        }
    }

    /**
     * 初始化认证系统
     */
    async init() {
        if (this.isAuthenticated()) {
            const isValid = await this.validateToken();
            if (!isValid) {
                // 尝试刷新token
                const refreshed = await this.refreshAccessToken();
                if (!refreshed) {
                    this.redirectToLogin();
                }
            }
        } else {
            // 未登录，检查是否为登录页面
            if (!window.location.pathname.includes('login')) {
                this.redirectToLogin();
            }
        }
    }
}

// 创建全局实例
window.authManager = new JWTAuthManager();

// 页面加载时初始化
if (typeof document !== 'undefined') {
    document.addEventListener('DOMContentLoaded', function() {
        // 重新启用JWT认证检查（已修复JWT认证问题）
        if (window.location.pathname.includes('/admin/') && !window.location.pathname.includes('/admin/login/')) {
            authManager.init();
        }
    });
}

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = JWTAuthManager;
}