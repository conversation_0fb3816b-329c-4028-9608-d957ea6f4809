/**
 * ECharts图表工具库
 * 提供统一的图表创建、配置和数据处理功能
 */

class ChartManager {
    constructor() {
        this.charts = new Map();
        this.defaultTheme = {
            color: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4', '#84cc16', '#f97316'],
            backgroundColor: 'transparent',
            textStyle: {
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
                color: '#374151'
            },
            animation: true,
            animationDuration: 1000
        };
    }

    /**
     * 创建或更新图表
     * @param {string} containerId - 容器ID
     * @param {Object} option - ECharts配置选项
     * @param {boolean} notMerge - 是否不合并配置
     */
    createChart(containerId, option, notMerge = false) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error(`图表容器 ${containerId} 不存在`);
            return null;
        }

        let chart = this.charts.get(containerId);
        
        if (!chart) {
            chart = echarts.init(container);
            this.charts.set(containerId, chart);
            
            // 添加窗口大小变化监听
            window.addEventListener('resize', () => {
                chart.resize();
            });
        }

        // 合并默认主题配置
        const finalOption = this.mergeTheme(option);
        chart.setOption(finalOption, notMerge);
        
        return chart;
    }

    /**
     * 销毁图表
     * @param {string} containerId - 容器ID
     */
    destroyChart(containerId) {
        const chart = this.charts.get(containerId);
        if (chart) {
            chart.dispose();
            this.charts.delete(containerId);
        }
    }

    /**
     * 合并主题配置
     * @param {Object} option - 原始配置
     * @returns {Object} 合并后的配置
     */
    mergeTheme(option) {
        return {
            ...this.defaultTheme,
            ...option,
            textStyle: {
                ...this.defaultTheme.textStyle,
                ...(option.textStyle || {})
            }
        };
    }

    /**
     * 显示加载状态
     * @param {string} containerId - 容器ID
     * @param {string} text - 加载文本
     */
    showLoading(containerId, text = '数据加载中...') {
        const chart = this.charts.get(containerId);
        if (chart) {
            chart.showLoading('default', {
                text: text,
                color: '#3b82f6',
                textColor: '#374151',
                maskColor: 'rgba(255, 255, 255, 0.8)',
                zlevel: 0
            });
        }
    }

    /**
     * 隐藏加载状态
     * @param {string} containerId - 容器ID
     */
    hideLoading(containerId) {
        const chart = this.charts.get(containerId);
        if (chart) {
            chart.hideLoading();
        }
    }

    /**
     * 创建饼图
     * @param {string} containerId - 容器ID
     * @param {Array} data - 数据数组 [{name: '', value: number}]
     * @param {Object} options - 额外配置
     */
    createPieChart(containerId, data, options = {}) {
        const defaultOption = {
            title: {
                text: options.title || '',
                left: 'center',
                textStyle: {
                    fontSize: 16,
                    fontWeight: 'bold'
                }
            },
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                orient: options.legendOrient || 'horizontal',
                left: options.legendLeft || 'center',
                bottom: options.legendBottom || '10%'
            },
            series: [{
                name: options.seriesName || '数据',
                type: 'pie',
                radius: options.radius || '60%',
                center: options.center || ['50%', '45%'],
                data: data,
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                label: {
                    formatter: options.labelFormatter || '{b}: {c}'
                }
            }]
        };

        return this.createChart(containerId, defaultOption);
    }

    /**
     * 创建柱状图
     * @param {string} containerId - 容器ID
     * @param {Array} categories - X轴分类
     * @param {Array} series - 系列数据
     * @param {Object} options - 额外配置
     */
    createBarChart(containerId, categories, series, options = {}) {
        const defaultOption = {
            title: {
                text: options.title || '',
                left: 'center',
                textStyle: {
                    fontSize: 16,
                    fontWeight: 'bold'
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            legend: {
                data: series.map(s => s.name),
                top: options.legendTop || '10%'
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: categories,
                axisLine: {
                    lineStyle: {
                        color: '#e5e7eb'
                    }
                },
                axisLabel: {
                    color: '#6b7280',
                    interval: 0,
                    rotate: options.xAxisRotate || 0
                }
            },
            yAxis: {
                type: 'value',
                axisLine: {
                    lineStyle: {
                        color: '#e5e7eb'
                    }
                },
                axisLabel: {
                    color: '#6b7280'
                },
                splitLine: {
                    lineStyle: {
                        color: '#f3f4f6'
                    }
                }
            },
            series: series.map(s => ({
                ...s,
                type: 'bar',
                itemStyle: {
                    borderRadius: [4, 4, 0, 0]
                }
            }))
        };

        return this.createChart(containerId, defaultOption);
    }

    /**
     * 创建折线图
     * @param {string} containerId - 容器ID
     * @param {Array} categories - X轴分类
     * @param {Array} series - 系列数据
     * @param {Object} options - 额外配置
     */
    createLineChart(containerId, categories, series, options = {}) {
        const defaultOption = {
            title: {
                text: options.title || '',
                left: 'center',
                textStyle: {
                    fontSize: 16,
                    fontWeight: 'bold'
                }
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: series.map(s => s.name),
                top: options.legendTop || '10%'
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: categories,
                axisLine: {
                    lineStyle: {
                        color: '#e5e7eb'
                    }
                },
                axisLabel: {
                    color: '#6b7280'
                }
            },
            yAxis: {
                type: 'value',
                axisLine: {
                    lineStyle: {
                        color: '#e5e7eb'
                    }
                },
                axisLabel: {
                    color: '#6b7280'
                },
                splitLine: {
                    lineStyle: {
                        color: '#f3f4f6'
                    }
                }
            },
            series: series.map(s => ({
                ...s,
                type: 'line',
                smooth: true,
                symbol: 'circle',
                symbolSize: 6,
                lineStyle: {
                    width: 3
                },
                areaStyle: options.showArea ? {
                    opacity: 0.1
                } : null
            }))
        };

        return this.createChart(containerId, defaultOption);
    }

    /**
     * 创建散点图（用于人才九宫格）
     * @param {string} containerId - 容器ID
     * @param {Array} data - 散点数据
     * @param {Object} options - 额外配置
     */
    createScatterChart(containerId, data, options = {}) {
        const defaultOption = {
            title: {
                text: options.title || '人才九宫格',
                left: 'center',
                textStyle: {
                    fontSize: 16,
                    fontWeight: 'bold'
                }
            },
            tooltip: {
                trigger: 'item',
                formatter: function(params) {
                    return `${params.data.name}<br/>绩效分数: ${params.data.value[0]}<br/>潜力分数: ${params.data.value[1]}`;
                }
            },
            grid: {
                left: '10%',
                right: '10%',
                bottom: '15%',
                top: '20%'
            },
            xAxis: {
                type: 'value',
                name: '绩效分数',
                nameLocation: 'middle',
                nameGap: 30,
                min: 0,
                max: 100,
                interval: 20,
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: '#e5e7eb',
                        type: 'dashed'
                    }
                },
                axisLine: {
                    lineStyle: {
                        color: '#6b7280'
                    }
                }
            },
            yAxis: {
                type: 'value',
                name: '潜力分数',
                nameLocation: 'middle',
                nameGap: 40,
                min: 0,
                max: 100,
                interval: 20,
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: '#e5e7eb',
                        type: 'dashed'
                    }
                },
                axisLine: {
                    lineStyle: {
                        color: '#6b7280'
                    }
                }
            },
            series: [{
                type: 'scatter',
                data: data,
                symbolSize: function(data) {
                    return options.symbolSize || 10;
                },
                itemStyle: {
                    opacity: 0.8
                },
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowColor: 'rgba(120, 36, 50, 0.5)'
                    }
                }
            }]
        };

        // 添加九宫格分割线
        if (options.showGrid) {
            defaultOption.graphic = [
                // 垂直分割线
                {
                    type: 'line',
                    shape: {
                        x1: 0.333,
                        y1: 0,
                        x2: 0.333,
                        y2: 1
                    },
                    style: {
                        stroke: '#94a3b8',
                        lineWidth: 2
                    }
                },
                {
                    type: 'line',
                    shape: {
                        x1: 0.666,
                        y1: 0,
                        x2: 0.666,
                        y2: 1
                    },
                    style: {
                        stroke: '#94a3b8',
                        lineWidth: 2
                    }
                },
                // 水平分割线
                {
                    type: 'line',
                    shape: {
                        x1: 0,
                        y1: 0.333,
                        x2: 1,
                        y2: 0.333
                    },
                    style: {
                        stroke: '#94a3b8',
                        lineWidth: 2
                    }
                },
                {
                    type: 'line',
                    shape: {
                        x1: 0,
                        y1: 0.666,
                        x2: 1,
                        y2: 0.666
                    },
                    style: {
                        stroke: '#94a3b8',
                        lineWidth: 2
                    }
                }
            ];
        }

        return this.createChart(containerId, defaultOption);
    }

    /**
     * 从API获取数据并创建图表
     * @param {string} url - API地址
     * @param {string} containerId - 容器ID
     * @param {Function} chartCreator - 图表创建函数
     * @param {Object} params - 请求参数
     */
    async loadDataAndCreateChart(url, containerId, chartCreator, params = {}) {
        this.showLoading(containerId);
        
        try {
            const queryString = Object.keys(params).length > 0 
                ? '?' + new URLSearchParams(params).toString() 
                : '';
            
            const response = await fetch(url + queryString, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': Utils.getCsrfToken()
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            
            if (!result.success) {
                throw new Error(result.message || '数据获取失败');
            }

            this.hideLoading(containerId);
            return chartCreator(containerId, result.data);

        } catch (error) {
            this.hideLoading(containerId);
            console.error('图表数据加载失败:', error);
            
            // 显示错误信息
            const container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = `
                    <div class="flex items-center justify-center h-64 text-gray-500">
                        <div class="text-center">
                            <i data-lucide="alert-circle" class="w-12 h-12 mx-auto mb-2 text-red-400"></i>
                            <p class="text-sm">数据加载失败</p>
                            <p class="text-xs text-gray-400 mt-1">${error.message}</p>
                        </div>
                    </div>
                `;
                lucide.createIcons();
            }
            
            return null;
        }
    }

    /**
     * 刷新所有图表
     */
    refreshAllCharts() {
        this.charts.forEach((chart, containerId) => {
            chart.resize();
        });
    }

    /**
     * 销毁所有图表
     */
    destroyAllCharts() {
        this.charts.forEach((chart, containerId) => {
            chart.dispose();
        });
        this.charts.clear();
    }
}

// 全局图表管理器实例
const chartManager = new ChartManager();

// 页面卸载时清理图表
window.addEventListener('beforeunload', () => {
    chartManager.destroyAllCharts();
});

// 导出给全局使用
window.ChartManager = ChartManager;
window.chartManager = chartManager;