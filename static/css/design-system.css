/**
 * 企业员工评价系统 - 设计系统
 * 统一的视觉语言和设计规范
 * 版本: 1.0.0
 */

/* ========================================
   设计令牌 (Design Tokens)
   ======================================== */

:root {
  /* 主色调系统 */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;  /* 主色调 */
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  /* 语义化色彩 */
  --success-50: #ecfdf5;
  --success-100: #d1fae5;
  --success-500: #10b981;
  --success-600: #059669;
  --success-700: #047857;

  --warning-50: #fffbeb;
  --warning-100: #fef3c7;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  --warning-700: #b45309;

  --error-50: #fef2f2;
  --error-100: #fee2e2;
  --error-500: #ef4444;
  --error-600: #dc2626;
  --error-700: #b91c1c;

  --info-50: #f0f9ff;
  --info-100: #e0f2fe;
  --info-500: #06b6d4;
  --info-600: #0891b2;
  --info-700: #0e7490;

  /* 中性色系统 */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* 字体系统 */
  --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', Consolas, 'Monaco', monospace;

  /* 字号系统 */
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */
  --text-4xl: 2.25rem;   /* 36px */

  /* 行高系统 */
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;

  /* 字重系统 */
  --font-thin: 100;
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;

  /* 间距系统 */
  --space-px: 1px;
  --space-0: 0;
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
  --space-24: 6rem;     /* 96px */

  /* 阴影系统 */
  --shadow-xs: 0 0 0 1px rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

  /* 圆角系统 */
  --radius-none: 0;
  --radius-sm: 0.125rem;  /* 2px */
  --radius: 0.25rem;      /* 4px */
  --radius-md: 0.375rem;  /* 6px */
  --radius-lg: 0.5rem;    /* 8px */
  --radius-xl: 0.75rem;   /* 12px */
  --radius-2xl: 1rem;     /* 16px */
  --radius-3xl: 1.5rem;   /* 24px */
  --radius-full: 9999px;

  /* 过渡动画 */
  --transition-none: none;
  --transition-all: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-default: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: 75ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);

  /* Z-index 层级 */
  --z-auto: auto;
  --z-0: 0;
  --z-10: 10;
  --z-20: 20;
  --z-30: 30;
  --z-40: 40;
  --z-50: 50;
  --z-tooltip: 1000;
  --z-modal: 1050;
  --z-popover: 1100;
  --z-notification: 1200;

  /* 断点系统 */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}

/* ========================================
   基础重置和标准化
   ======================================== */

/* 现代化的 CSS 重置 */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  font-family: var(--font-family-sans);
}

body {
  margin: 0;
  font-family: var(--font-family-sans);
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--gray-900);
  background-color: var(--gray-50);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 标题系统 */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: var(--font-semibold);
  line-height: var(--leading-tight);
  color: var(--gray-900);
}

h1 { font-size: var(--text-3xl); margin-bottom: var(--space-6); }
h2 { font-size: var(--text-2xl); margin-bottom: var(--space-5); }
h3 { font-size: var(--text-xl); margin-bottom: var(--space-4); }
h4 { font-size: var(--text-lg); margin-bottom: var(--space-3); }
h5 { font-size: var(--text-base); margin-bottom: var(--space-2); }
h6 { font-size: var(--text-sm); margin-bottom: var(--space-2); }

/* 段落和文本 */
p {
  margin: 0 0 var(--space-4) 0;
  line-height: var(--leading-relaxed);
  color: var(--gray-700);
}

/* 链接 */
a {
  color: var(--primary-600);
  text-decoration: none;
  transition: var(--transition-default);
}

a:hover {
  color: var(--primary-700);
  text-decoration: underline;
}

a:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* 列表 */
ul, ol {
  margin: 0 0 var(--space-4) var(--space-6);
  padding: 0;
}

li {
  margin-bottom: var(--space-1);
}

/* 图片 */
img {
  max-width: 100%;
  height: auto;
  border-style: none;
}

/* 表格 */
table {
  border-collapse: collapse;
  width: 100%;
}

/* ========================================
   工具类 (Utility Classes)
   ======================================== */

/* 文本颜色 */
.text-primary { color: var(--primary-600) !important; }
.text-success { color: var(--success-600) !important; }
.text-warning { color: var(--warning-600) !important; }
.text-error { color: var(--error-600) !important; }
.text-info { color: var(--info-600) !important; }
.text-muted { color: var(--gray-500) !important; }
.text-white { color: white !important; }

/* 背景颜色 */
.bg-primary { background-color: var(--primary-500) !important; }
.bg-success { background-color: var(--success-500) !important; }
.bg-warning { background-color: var(--warning-500) !important; }
.bg-error { background-color: var(--error-500) !important; }
.bg-info { background-color: var(--info-500) !important; }
.bg-white { background-color: white !important; }
.bg-gray-50 { background-color: var(--gray-50) !important; }
.bg-gray-100 { background-color: var(--gray-100) !important; }

/* 阴影 */
.shadow-xs { box-shadow: var(--shadow-xs) !important; }
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow { box-shadow: var(--shadow) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }

/* 圆角 */
.rounded-none { border-radius: var(--radius-none) !important; }
.rounded-sm { border-radius: var(--radius-sm) !important; }
.rounded { border-radius: var(--radius) !important; }
.rounded-md { border-radius: var(--radius-md) !important; }
.rounded-lg { border-radius: var(--radius-lg) !important; }
.rounded-xl { border-radius: var(--radius-xl) !important; }
.rounded-full { border-radius: var(--radius-full) !important; }

/* 间距 */
.m-0 { margin: 0 !important; }
.m-1 { margin: var(--space-1) !important; }
.m-2 { margin: var(--space-2) !important; }
.m-3 { margin: var(--space-3) !important; }
.m-4 { margin: var(--space-4) !important; }
.m-6 { margin: var(--space-6) !important; }
.m-8 { margin: var(--space-8) !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: var(--space-1) !important; }
.p-2 { padding: var(--space-2) !important; }
.p-3 { padding: var(--space-3) !important; }
.p-4 { padding: var(--space-4) !important; }
.p-6 { padding: var(--space-6) !important; }
.p-8 { padding: var(--space-8) !important; }

/* 字体大小 */
.text-xs { font-size: var(--text-xs) !important; }
.text-sm { font-size: var(--text-sm) !important; }
.text-base { font-size: var(--text-base) !important; }
.text-lg { font-size: var(--text-lg) !important; }
.text-xl { font-size: var(--text-xl) !important; }
.text-2xl { font-size: var(--text-2xl) !important; }
.text-3xl { font-size: var(--text-3xl) !important; }

/* 字重 */
.font-light { font-weight: var(--font-light) !important; }
.font-normal { font-weight: var(--font-normal) !important; }
.font-medium { font-weight: var(--font-medium) !important; }
.font-semibold { font-weight: var(--font-semibold) !important; }
.font-bold { font-weight: var(--font-bold) !important; }

/* 显示控制 */
.hidden { display: none !important; }
.block { display: block !important; }
.inline { display: inline !important; }
.inline-block { display: inline-block !important; }
.flex { display: flex !important; }
.inline-flex { display: inline-flex !important; }
.grid { display: grid !important; }

/* Flex 布局 */
.flex-col { flex-direction: column !important; }
.flex-row { flex-direction: row !important; }
.items-center { align-items: center !important; }
.items-start { align-items: flex-start !important; }
.items-end { align-items: flex-end !important; }
.justify-center { justify-content: center !important; }
.justify-between { justify-content: space-between !important; }
.justify-around { justify-content: space-around !important; }
.justify-start { justify-content: flex-start !important; }
.justify-end { justify-content: flex-end !important; }

/* 定位 */
.relative { position: relative !important; }
.absolute { position: absolute !important; }
.fixed { position: fixed !important; }
.sticky { position: sticky !important; }

/* 过渡动画 */
.transition-all { transition: var(--transition-all) !important; }
.transition-none { transition: var(--transition-none) !important; }

/* 鼠标样式 */
.cursor-pointer { cursor: pointer !important; }
.cursor-not-allowed { cursor: not-allowed !important; }

/* 用户选择 */
.select-none { user-select: none !important; }
.select-text { user-select: text !important; }

/* 溢出控制 */
.overflow-hidden { overflow: hidden !important; }
.overflow-auto { overflow: auto !important; }
.overflow-x-auto { overflow-x: auto !important; }
.overflow-y-auto { overflow-y: auto !important; }

/* 宽度和高度 */
.w-full { width: 100% !important; }
.h-full { height: 100% !important; }
.min-h-screen { min-height: 100vh !important; }

/* ========================================
   组件基础样式
   ======================================== */

/* 容器 */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--space-4);
  padding-right: var(--space-4);
}

@media (min-width: 640px) {
  .container { max-width: 640px; }
}

@media (min-width: 768px) {
  .container { max-width: 768px; }
}

@media (min-width: 1024px) {
  .container { max-width: 1024px; }
}

@media (min-width: 1280px) {
  .container { max-width: 1280px; }
}

@media (min-width: 1536px) {
  .container { max-width: 1536px; }
}

/* 卡片 */
.card {
  background-color: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  overflow: hidden;
  transition: var(--transition-default);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--gray-200);
}

.card-body {
  padding: var(--space-6);
}

.card-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--gray-200);
  background-color: var(--gray-50);
}

/* 按钮基础样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
  font-size: var(--text-sm);
  line-height: 1;
  transition: var(--transition-default);
  cursor: pointer;
  border: 1px solid transparent;
  text-decoration: none;
  user-select: none;
  white-space: nowrap;
}

.btn:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 按钮尺寸 */
.btn-xs {
  padding: var(--space-1) var(--space-2);
  font-size: var(--text-xs);
}

.btn-sm {
  padding: var(--space-2) var(--space-3);
  font-size: var(--text-sm);
}

.btn-md {
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-sm);
}

.btn-lg {
  padding: var(--space-4) var(--space-6);
  font-size: var(--text-base);
}

/* 按钮变体 */
.btn-primary {
  background-color: var(--primary-500);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-600);
  color: white;
}

.btn-secondary {
  background-color: var(--gray-100);
  color: var(--gray-900);
  border-color: var(--gray-300);
}

.btn-secondary:hover {
  background-color: var(--gray-200);
  color: var(--gray-900);
}

.btn-success {
  background-color: var(--success-500);
  color: white;
}

.btn-success:hover {
  background-color: var(--success-600);
  color: white;
}

.btn-warning {
  background-color: var(--warning-500);
  color: white;
}

.btn-warning:hover {
  background-color: var(--warning-600);
  color: white;
}

.btn-error {
  background-color: var(--error-500);
  color: white;
}

.btn-error:hover {
  background-color: var(--error-600);
  color: white;
}

.btn-outline-primary {
  background-color: transparent;
  color: var(--primary-600);
  border-color: var(--primary-300);
}

.btn-outline-primary:hover {
  background-color: var(--primary-50);
  color: var(--primary-700);
}

.btn-ghost {
  background-color: transparent;
  color: var(--gray-600);
  border-color: transparent;
}

.btn-ghost:hover {
  background-color: var(--gray-100);
  color: var(--gray-900);
}

/* 表单控件 */
.form-input,
.form-select,
.form-textarea {
  display: block;
  width: 100%;
  padding: var(--space-3);
  font-size: var(--text-sm);
  line-height: var(--leading-normal);
  color: var(--gray-900);
  background-color: white;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  transition: var(--transition-default);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input.is-invalid,
.form-select.is-invalid,
.form-textarea.is-invalid {
  border-color: var(--error-500);
}

.form-input.is-invalid:focus,
.form-select.is-invalid:focus,
.form-textarea.is-invalid:focus {
  border-color: var(--error-500);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-input.is-valid,
.form-select.is-valid,
.form-textarea.is-valid {
  border-color: var(--success-500);
}

.form-input:disabled,
.form-select:disabled,
.form-textarea:disabled {
  background-color: var(--gray-100);
  color: var(--gray-500);
  cursor: not-allowed;
}

/* 表单标签 */
.form-label {
  display: block;
  margin-bottom: var(--space-2);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--gray-700);
}

.form-label.required::after {
  content: '*';
  color: var(--error-500);
  margin-left: var(--space-1);
}

/* 表单帮助文本 */
.form-help {
  margin-top: var(--space-1);
  font-size: var(--text-xs);
  color: var(--gray-500);
}

.form-error {
  margin-top: var(--space-1);
  font-size: var(--text-xs);
  color: var(--error-600);
}

.form-success {
  margin-top: var(--space-1);
  font-size: var(--text-xs);
  color: var(--success-600);
}

/* 徽章 */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-2);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  border-radius: var(--radius-full);
  white-space: nowrap;
}

.badge-primary {
  background-color: var(--primary-100);
  color: var(--primary-800);
}

.badge-success {
  background-color: var(--success-100);
  color: var(--success-800);
}

.badge-warning {
  background-color: var(--warning-100);
  color: var(--warning-800);
}

.badge-error {
  background-color: var(--error-100);
  color: var(--error-800);
}

.badge-gray {
  background-color: var(--gray-100);
  color: var(--gray-800);
}

/* 表格 */
.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: var(--space-3) var(--space-4);
  text-align: left;
  border-bottom: 1px solid var(--gray-200);
}

.table th {
  background-color: var(--gray-50);
  font-weight: var(--font-medium);
  font-size: var(--text-xs);
  color: var(--gray-700);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.table tbody tr:hover {
  background-color: var(--gray-50);
}

.table tbody tr.selected {
  background-color: var(--primary-50);
}

/* 加载状态 */
.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--gray-200);
  border-top: 2px solid var(--primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式工具类 */
@media (max-width: 639px) {
  .sm\:hidden { display: none !important; }
  .sm\:block { display: block !important; }
}

@media (min-width: 640px) and (max-width: 767px) {
  .md\:hidden { display: none !important; }
  .md\:block { display: block !important; }
}

@media (min-width: 768px) {
  .lg\:hidden { display: none !important; }
  .lg\:block { display: block !important; }
}