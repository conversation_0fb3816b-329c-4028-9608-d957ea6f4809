# -*- coding: utf-8 -*-
"""
站内通信服务类
提供自动化通知和消息发送功能
"""

from django.utils import timezone
from django.db import transaction
from django.db.models import QuerySet
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Union
import logging

from .models import Message, MessageRecipient, NotificationTemplate
from organizations.models import Staff, Department

logger = logging.getLogger(__name__)


class NotificationService:
    """
    通知服务类
    负责处理各种业务事件的自动化通知
    """
    
    def __init__(self):
        self.logger = logger
    
    @transaction.atomic
    def send_evaluation_batch_start_notification(self, batch, participants: List[Staff]) -> bool:
        """
        发送考评批次开始通知
        
        Args:
            batch: EvaluationBatch实例
            participants: 参与者列表
            
        Returns:
            bool: 发送是否成功
        """
        try:
            template = self._get_template('EVALUATION_START')
            if not template:
                self.logger.warning("考评开始通知模板不存在")
                return False
            
            # 准备模板变量
            context = {
                'batch_name': batch.name,
                'start_date': batch.start_date.strftime('%Y年%m月%d日'),
                'end_date': batch.end_date.strftime('%Y年%m月%d日'),
                'description': batch.description or '请及时完成考评任务'
            }
            
            # 渲染模板
            rendered = template.render(context)
            
            # 创建消息
            message = Message.objects.create(
                message_type='EVALUATION',
                subject=rendered['subject'],
                content=rendered['content'],
                priority='HIGH',
                related_model='EvaluationBatch',
                related_id=batch.id,
                expires_at=batch.end_date + timedelta(days=7),  # 批次结束后7天过期
                created_by='system'
            )
            
            # 批量创建接收者记录
            recipients = []
            for participant in participants:
                recipients.append(MessageRecipient(
                    message=message,
                    recipient=participant,
                    recipient_type='STAFF',
                    created_by='system'
                ))
            
            MessageRecipient.objects.bulk_create(recipients)
            
            self.logger.info(f"考评批次开始通知发送成功: {batch.name}, 接收者数量: {len(participants)}")
            return True
            
        except Exception as e:
            self.logger.error(f"发送考评批次开始通知失败: {str(e)}")
            return False
    
    @transaction.atomic
    def send_evaluation_deadline_reminder(self, batch, pending_relations) -> bool:
        """
        发送考评截止提醒通知
        
        Args:
            batch: EvaluationBatch实例
            pending_relations: 未完成的考评关系列表
            
        Returns:
            bool: 发送是否成功
        """
        try:
            template = self._get_template('EVALUATION_REMIND')
            if not template:
                self.logger.warning("考评提醒通知模板不存在")
                return False
            
            # 按评价者分组未完成任务
            evaluator_tasks = {}
            for relation in pending_relations:
                evaluator = relation.evaluator
                if evaluator not in evaluator_tasks:
                    evaluator_tasks[evaluator] = []
                evaluator_tasks[evaluator].append(relation)
            
            # 为每个评价者发送个性化提醒
            for evaluator, tasks in evaluator_tasks.items():
                days_left = (batch.end_date.date() - timezone.now().date()).days
                
                context = {
                    'batch_name': batch.name,
                    'staff_name': evaluator.name,
                    'remaining_tasks': len(tasks),
                    'days_left': days_left,
                    'end_date': batch.end_date.strftime('%Y年%m月%d日 %H:%M')
                }
                
                rendered = template.render(context)
                
                # 创建个人提醒消息
                message = Message.objects.create(
                    message_type='EVALUATION',
                    subject=rendered['subject'],
                    content=rendered['content'],
                    priority='HIGH' if days_left <= 1 else 'MEDIUM',
                    related_model='EvaluationBatch',
                    related_id=batch.id,
                    expires_at=batch.end_date,
                    created_by='system'
                )
                
                MessageRecipient.objects.create(
                    message=message,
                    recipient=evaluator,
                    recipient_type='STAFF',
                    created_by='system'
                )
            
            self.logger.info(f"考评截止提醒发送成功: {batch.name}, 提醒人数: {len(evaluator_tasks)}")
            return True
            
        except Exception as e:
            self.logger.error(f"发送考评截止提醒失败: {str(e)}")
            return False
    
    @transaction.atomic
    def send_evaluation_completion_notification(self, record) -> bool:
        """
        发送考评完成通知
        
        Args:
            record: EvaluationRecord实例
            
        Returns:
            bool: 发送是否成功
        """
        try:
            template = self._get_template('EVALUATION_COMPLETE')
            if not template:
                self.logger.warning("考评完成通知模板不存在")
                return False
            
            relation = record.relation
            batch = relation.batch
            
            context = {
                'batch_name': batch.name,
                'evaluator_name': relation.evaluator.name,
                'evaluatee_name': relation.evaluatee.name,
                'completion_time': record.completion_time.strftime('%Y年%m月%d日 %H:%M'),
                'total_score': record.total_score
            }
            
            rendered = template.render(context)
            
            # 通知被评价者
            message = Message.objects.create(
                message_type='EVALUATION',
                subject=rendered['subject'],
                content=rendered['content'],
                priority='MEDIUM',
                related_model='EvaluationRecord',
                related_id=record.id,
                created_by='system'
            )
            
            MessageRecipient.objects.create(
                message=message,
                recipient=relation.evaluatee,
                recipient_type='STAFF',
                created_by='system'
            )
            
            # 如果不是匿名结果，也通知评价者
            if not batch.anonymous_results:
                MessageRecipient.objects.create(
                    message=message,
                    recipient=relation.evaluator,
                    recipient_type='STAFF',
                    created_by='system'
                )
            
            self.logger.info(f"考评完成通知发送成功: {relation}")
            return True
            
        except Exception as e:
            self.logger.error(f"发送考评完成通知失败: {str(e)}")
            return False
    
    @transaction.atomic
    def send_batch_completion_summary(self, batch) -> bool:
        """
        发送批次完成总结通知
        
        Args:
            batch: EvaluationBatch实例
            
        Returns:
            bool: 发送是否成功
        """
        try:
            # 获取统计数据
            total_relations = batch.evaluationrelation_set.count()
            completed_relations = batch.evaluationrelation_set.filter(
                evaluationrecord__isnull=False
            ).count()
            completion_rate = (completed_relations / total_relations * 100) if total_relations > 0 else 0
            
            participants = batch.get_participants_count()
            
            # 创建总结消息
            subject = f"考评批次【{batch.name}】已完成"
            content = f"""
尊敬的管理员，

考评批次【{batch.name}】已完成，以下是统计信息：

📊 完成情况：
• 总考评关系数：{total_relations}
• 已完成关系数：{completed_relations}
• 完成率：{completion_rate:.1f}%

👥 参与统计：
• 评价者人数：{participants['evaluators']}
• 被评价者人数：{participants['evaluatees']}
• 总参与人数：{participants['total']}

⏰ 批次信息：
• 开始时间：{batch.start_date.strftime('%Y年%m月%d日 %H:%M')}
• 结束时间：{batch.end_date.strftime('%Y年%m月%d日 %H:%M')}
• 完成时间：{timezone.now().strftime('%Y年%m月%d日 %H:%M')}

请及时查看考评结果和生成相关报告。

系统自动发送
            """.strip()
            
            message = Message.objects.create(
                message_type='EVALUATION',
                subject=subject,
                content=content,
                priority='MEDIUM',
                related_model='EvaluationBatch',
                related_id=batch.id,
                created_by='system'
            )
            
            # 发送给管理员
            from organizations.models import Role
            admins = Staff.objects.filter(
                role__in=[Role.SUPER_ADMIN, Role.SYSTEM_ADMIN, Role.HR_ADMIN],
                deleted_at__isnull=True
            )
            
            recipients = []
            for admin in admins:
                recipients.append(MessageRecipient(
                    message=message,
                    recipient=admin,
                    recipient_type='STAFF',
                    created_by='system'
                ))
            
            MessageRecipient.objects.bulk_create(recipients)
            
            self.logger.info(f"批次完成总结通知发送成功: {batch.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"发送批次完成总结通知失败: {str(e)}")
            return False
    
    @transaction.atomic
    def send_system_maintenance_notification(self, title: str, content: str, 
                                           target_departments: List[Department] = None,
                                           priority: str = 'HIGH') -> bool:
        """
        发送系统维护通知
        
        Args:
            title: 通知标题
            content: 通知内容
            target_departments: 目标部门列表，为空则发送给所有人
            priority: 优先级
            
        Returns:
            bool: 发送是否成功
        """
        try:
            message = Message.objects.create(
                message_type='SYSTEM',
                subject=title,
                content=content,
                priority=priority,
                is_broadcast=target_departments is None,
                created_by='system'
            )
            
            # 确定接收者
            if target_departments:
                # 发送给指定部门
                recipients = []
                for dept in target_departments:
                    staff_list = Staff.objects.filter(
                        department=dept,
                        deleted_at__isnull=True
                    )
                    for staff in staff_list:
                        recipients.append(MessageRecipient(
                            message=message,
                            recipient=staff,
                            recipient_type='DEPARTMENT',
                            created_by='system'
                        ))
            else:
                # 发送给所有人
                all_staff = Staff.objects.filter(deleted_at__isnull=True)
                recipients = []
                for staff in all_staff:
                    recipients.append(MessageRecipient(
                        message=message,
                        recipient=staff,
                        recipient_type='ALL',
                        created_by='system'
                    ))
            
            MessageRecipient.objects.bulk_create(recipients)
            
            self.logger.info(f"系统维护通知发送成功: {title}, 接收者数量: {len(recipients)}")
            return True
            
        except Exception as e:
            self.logger.error(f"发送系统维护通知失败: {str(e)}")
            return False
    
    def _get_template(self, template_type: str) -> Optional[NotificationTemplate]:
        """
        获取通知模板
        
        Args:
            template_type: 模板类型
            
        Returns:
            NotificationTemplate实例或None
        """
        try:
            return NotificationTemplate.objects.get(
                template_type=template_type,
                is_active=True
            )
        except NotificationTemplate.DoesNotExist:
            return None
    
    def check_and_send_deadline_reminders(self) -> int:
        """
        检查并发送截止提醒（用于定时任务）
        
        Returns:
            int: 发送的通知数量
        """
        from evaluations.models import EvaluationBatch, EvaluationRelation
        
        sent_count = 0
        now = timezone.now()
        
        # 查找需要提醒的批次（距离截止还有1天和3天）
        reminder_dates = [
            now + timedelta(days=1),  # 明天截止
            now + timedelta(days=3),  # 3天后截止
        ]
        
        for reminder_date in reminder_dates:
            # 查找在提醒日期截止的活跃批次
            batches = EvaluationBatch.objects.filter(
                status='active',
                end_date__date=reminder_date.date()
            )
            
            for batch in batches:
                # 获取未完成的关系
                pending_relations = EvaluationRelation.objects.filter(
                    batch=batch,
                    evaluationrecord__isnull=True
                )
                
                if pending_relations.exists():
                    if self.send_evaluation_deadline_reminder(batch, pending_relations):
                        sent_count += 1
        
        return sent_count
    
    def send_evaluation_timeout_alert(self, batch: 'EvaluationBatch', timeout_relations: QuerySet) -> bool:
        """
        发送考评超时异常通知
        向管理员发送超时警报，向超时用户发送催促通知
        
        Args:
            batch: 考评批次
            timeout_relations: 超时的考评关系QuerySet
            
        Returns:
            是否发送成功
        """
        try:
            with transaction.atomic():
                # 获取超时通知模板
                template = self._get_template('EVALUATION_TIMEOUT_ALERT')
                if not template:
                    logger.error("未找到考评超时通知模板")
                    return False
                
                # 统计超时情况
                timeout_count = timeout_relations.count()
                timeout_users = []
                timeout_departments = set()
                
                for relation in timeout_relations:
                    timeout_users.append({
                        'name': relation.evaluator.name,
                        'department': relation.evaluator.department.name if relation.evaluator.department else '未知部门',
                        'evaluatee': relation.evaluatee.name,
                        'days_overdue': (timezone.now().date() - batch.end_date.date()).days
                    })
                    if relation.evaluator.department:
                        timeout_departments.add(relation.evaluator.department.name)
                
                # 向管理员发送超时警报
                admin_success = self._send_timeout_alert_to_admins(
                    batch, timeout_count, timeout_users, timeout_departments, template
                )
                
                # 向超时用户发送催促通知
                user_success = self._send_timeout_reminder_to_users(
                    batch, timeout_relations, template
                )
                
                # 记录审计日志
                AuditLog.objects.create(
                    user='system',
                    action='timeout_alert',
                    target_model='evaluationbatch',
                    target_id=batch.id,
                    description=f'发送考评超时警报: 批次【{batch.name}】有{timeout_count}个超时任务',
                    details={
                        'batch_name': batch.name,
                        'timeout_count': timeout_count,
                        'affected_departments': list(timeout_departments),
                        'admin_notification_sent': admin_success,
                        'user_reminders_sent': user_success
                    }
                )
                
                return admin_success or user_success
                
        except Exception as e:
            logger.error(f"发送考评超时通知失败: {str(e)}")
            return False
    
    def _send_timeout_alert_to_admins(self, batch, timeout_count, timeout_users, timeout_departments, template):
        """向管理员发送超时警报"""
        try:
            # 获取HR管理员和系统管理员
            from organizations.models import Staff
            admins = Staff.objects.filter(
                role__in=['hr_admin', 'system_admin', 'super_admin'],
                is_active=True,
                deleted_at__isnull=True
            )
            
            if not admins.exists():
                logger.warning("未找到管理员用户")
                return False
            
            # 生成超时用户列表HTML
            timeout_users_html = ""
            for user in timeout_users[:10]:  # 只显示前10个
                timeout_users_html += f"""
                <tr>
                    <td style="padding: 8px; border-bottom: 1px solid #eee;">{user['name']}</td>
                    <td style="padding: 8px; border-bottom: 1px solid #eee;">{user['department']}</td>
                    <td style="padding: 8px; border-bottom: 1px solid #eee;">{user['evaluatee']}</td>
                    <td style="padding: 8px; border-bottom: 1px solid #eee; color: #dc2626;">{user['days_overdue']}天</td>
                </tr>
                """
            
            if len(timeout_users) > 10:
                timeout_users_html += f"""
                <tr>
                    <td colspan="4" style="padding: 8px; text-align: center; color: #666;">
                        还有{len(timeout_users) - 10}个超时任务未显示...
                    </td>
                </tr>
                """
            
            # 渲染模板
            context = {
                'batch_name': batch.name,
                'timeout_count': timeout_count,
                'departments': ', '.join(timeout_departments),
                'overdue_days': (timezone.now().date() - batch.end_date.date()).days,
                'timeout_users_table': timeout_users_html,
                'batch_end_date': batch.end_date.strftime('%Y年%m月%d日'),
                'current_time': timezone.now().strftime('%Y年%m月%d日 %H:%M')
            }
            
            rendered = template.render(context)
            
            # 创建消息
            message = Message.objects.create(
                message_type='SYSTEM',
                sender_type='SYSTEM',
                subject=rendered['subject'],
                content=rendered['content'],
                priority='HIGH',
                related_model='EvaluationBatch',
                related_id=batch.id,
                created_by='system'
            )
            
            # 批量创建接收者
            recipients = []
            for admin in admins:
                recipients.append(
                    MessageRecipient(
                        message=message,
                        recipient=admin,
                        recipient_type='INDIVIDUAL'
                    )
                )
            
            MessageRecipient.objects.bulk_create(recipients)
            
            logger.info(f"向{len(recipients)}位管理员发送超时警报成功")
            return True
            
        except Exception as e:
            logger.error(f"向管理员发送超时警报失败: {str(e)}")
            return False
    
    def _send_timeout_reminder_to_users(self, batch, timeout_relations, template):
        """向超时用户发送催促通知"""
        try:
            success_count = 0
            
            for relation in timeout_relations:
                try:
                    # 检查是否已经发送过超时提醒（避免重复发送）
                    existing_message = Message.objects.filter(
                        related_model='EvaluationRelation',
                        related_id=relation.id,
                        message_type='EVALUATION',
                        subject__contains='超时催促'
                    ).first()
                    
                    if existing_message:
                        continue  # 已发送过，跳过
                    
                    overdue_days = (timezone.now().date() - batch.end_date.date()).days
                    
                    # 渲染个人催促通知模板
                    context = {
                        'staff_name': relation.evaluator.name,
                        'batch_name': batch.name,
                        'evaluatee_name': relation.evaluatee.name,
                        'overdue_days': overdue_days,
                        'end_date': batch.end_date.strftime('%Y年%m月%d日')
                    }
                    
                    # 使用EVALUATION_DEADLINE_REMINDER模板的催促版本
                    reminder_template = self._get_template('EVALUATION_DEADLINE_REMINDER')
                    if reminder_template:
                        rendered = reminder_template.render(context)
                        
                        # 修改主题为超时催促
                        subject = f"【超时催促】{rendered['subject']}"
                        content = f"""
                        <div style="color: #dc2626; font-weight: bold; margin-bottom: 16px;">
                            ⚠️ 此考评任务已超时{overdue_days}天，请尽快完成！
                        </div>
                        {rendered['content']}
                        """
                        
                        # 创建催促消息
                        message = Message.objects.create(
                            message_type='EVALUATION',
                            sender_type='SYSTEM',
                            subject=subject,
                            content=content,
                            priority='HIGH',
                            related_model='EvaluationRelation',
                            related_id=relation.id,
                            created_by='system'
                        )
                        
                        # 发送给评价者
                        MessageRecipient.objects.create(
                            message=message,
                            recipient=relation.evaluator,
                            recipient_type='INDIVIDUAL'
                        )
                        
                        success_count += 1
                        
                except Exception as e:
                    logger.error(f"向用户{relation.evaluator.name}发送超时催促失败: {str(e)}")
                    continue
            
            logger.info(f"向{success_count}位用户发送超时催促成功")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"发送超时催促失败: {str(e)}")
            return False
    
    def check_and_send_timeout_alerts(self) -> int:
        """
        检查并发送所有超时考评的异常通知
        用于定时任务调用
        
        Returns:
            发送的警报数量
        """
        try:
            sent_count = 0
            now = timezone.now()
            
            # 查找已经截止但仍有未完成任务的批次
            expired_batches = EvaluationBatch.objects.filter(
                status='active',
                end_date__lt=now
            )
            
            for batch in expired_batches:
                # 查找该批次中的超时关系（未完成且已过期）
                timeout_relations = EvaluationRelation.objects.filter(
                    batch=batch,
                    status__in=['pending', 'draft'],  # 未完成状态
                    deleted_at__isnull=True
                ).select_related('evaluator', 'evaluatee', 'evaluator__department')
                
                if timeout_relations.exists():
                    # 检查是否需要发送警报（避免频繁发送）
                    overdue_days = (now.date() - batch.end_date.date()).days
                    
                    # 只在特定时间点发送警报：1天、3天、7天、然后每周一次
                    should_send = (
                        overdue_days == 1 or  # 超时1天
                        overdue_days == 3 or  # 超时3天
                        overdue_days == 7 or  # 超时1周
                        (overdue_days > 7 and overdue_days % 7 == 0)  # 超时1周后每周发送
                    )
                    
                    if should_send:
                        if self.send_evaluation_timeout_alert(batch, timeout_relations):
                            sent_count += 1
                            logger.info(f"批次【{batch.name}】超时警报发送成功，涉及{timeout_relations.count()}个任务")
            
            return sent_count
            
        except Exception as e:
            logger.error(f"检查和发送超时警报失败: {str(e)}")
            return 0


# 全局通知服务实例
notification_service = NotificationService()