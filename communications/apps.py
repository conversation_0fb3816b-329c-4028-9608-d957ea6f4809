# -*- coding: utf-8 -*-
"""
站内通信应用配置
"""

from django.apps import AppConfig


class CommunicationsConfig(AppConfig):
    """站内通信应用配置类"""
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'communications'
    verbose_name = '站内通信'
    
    def ready(self):
        """应用启动时的初始化操作"""
        # 导入信号处理器
        try:
            from . import signals
        except ImportError:
            pass