# -*- coding: utf-8 -*-
"""
站内通信管理后台配置 - 简化版
"""

from django.contrib import admin
from .models import Message, MessageRecipient, Announcement, NotificationTemplate, MessageReadLog


@admin.register(Message)
class MessageAdmin(admin.ModelAdmin):
    """消息管理"""
    list_display = ['subject', 'message_type', 'sender', 'priority', 'created_at']
    list_filter = ['message_type', 'priority', 'created_at']
    search_fields = ['subject', 'content']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(MessageRecipient)
class MessageRecipientAdmin(admin.ModelAdmin):
    """消息接收者管理"""
    list_display = ['message', 'recipient', 'is_read', 'created_at']
    list_filter = ['is_read', 'created_at']
    search_fields = ['message__subject', 'recipient__name']


@admin.register(Announcement)
class AnnouncementAdmin(admin.ModelAdmin):
    """公告管理"""
    list_display = ['title', 'announcement_type', 'is_published', 'created_at']
    list_filter = ['announcement_type', 'is_published', 'created_at']
    search_fields = ['title', 'content']


@admin.register(NotificationTemplate)
class NotificationTemplateAdmin(admin.ModelAdmin):
    """通知模板管理"""
    list_display = ['name', 'template_type', 'is_active', 'created_at']
    list_filter = ['template_type', 'is_active']
    search_fields = ['name', 'subject_template']


@admin.register(MessageReadLog)
class MessageReadLogAdmin(admin.ModelAdmin):
    """消息阅读日志管理"""
    list_display = ['message', 'reader', 'read_at']
    list_filter = ['read_at']
    search_fields = ['message__subject', 'reader__name']
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False