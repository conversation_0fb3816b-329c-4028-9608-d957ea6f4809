# -*- coding: utf-8 -*-
"""
站内通信信号处理器
监听业务事件并触发自动通知
"""

from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver, Signal
from django.utils import timezone
from django.db import transaction
import logging

from .services import notification_service

logger = logging.getLogger(__name__)

# 自定义信号
batch_status_changed = Signal()
evaluation_deadline_approaching = Signal()


@receiver(post_save, sender='evaluations.EvaluationBatch')
def handle_batch_status_change(sender, instance, created, **kwargs):
    """
    处理考评批次状态变更
    """
    try:
        # 如果是新创建的批次，不做处理
        if created:
            return
            
        # 检查状态是否变为active
        if instance.status == 'active':
            # 延迟处理，避免在事务中发送通知
            transaction.on_commit(
                lambda: send_batch_start_notification(instance)
            )
        
        # 检查状态是否变为completed
        elif instance.status == 'completed':
            transaction.on_commit(
                lambda: send_batch_completion_notification(instance)
            )
            
    except Exception as e:
        logger.error(f"处理批次状态变更失败: {str(e)}")


@receiver(post_save, sender='evaluations.EvaluationRecord')
def handle_evaluation_completion(sender, instance, created, **kwargs):
    """
    处理考评完成事件
    """
    try:
        if created:
            # 延迟发送完成通知
            transaction.on_commit(
                lambda: notification_service.send_evaluation_completion_notification(instance)
            )
            
            # 检查批次是否全部完成
            batch = instance.relation.batch
            transaction.on_commit(
                lambda: check_batch_completion(batch)
            )
            
    except Exception as e:
        logger.error(f"处理考评完成事件失败: {str(e)}")


def send_batch_start_notification(batch):
    """
    发送批次开始通知
    """
    try:
        # 获取所有参与者
        from evaluations.models import EvaluationRelation
        
        # 获取所有评价者
        evaluator_ids = EvaluationRelation.objects.filter(
            batch=batch
        ).values_list('evaluator_id', flat=True).distinct()
        
        # 获取所有被评价者
        evaluatee_ids = EvaluationRelation.objects.filter(
            batch=batch
        ).values_list('evaluatee_id', flat=True).distinct()
        
        # 合并参与者ID
        participant_ids = set(evaluator_ids) | set(evaluatee_ids)
        
        # 获取参与者对象
        from organizations.models import Staff
        participants = Staff.objects.filter(
            id__in=participant_ids,
            deleted_at__isnull=True
        )
        
        # 发送通知
        success = notification_service.send_evaluation_batch_start_notification(
            batch, list(participants)
        )
        
        if success:
            logger.info(f"批次开始通知发送成功: {batch.name}")
        else:
            logger.warning(f"批次开始通知发送失败: {batch.name}")
            
    except Exception as e:
        logger.error(f"发送批次开始通知异常: {str(e)}")


def send_batch_completion_notification(batch):
    """
    发送批次完成通知
    """
    try:
        success = notification_service.send_batch_completion_summary(batch)
        
        if success:
            logger.info(f"批次完成通知发送成功: {batch.name}")
        else:
            logger.warning(f"批次完成通知发送失败: {batch.name}")
            
    except Exception as e:
        logger.error(f"发送批次完成通知异常: {str(e)}")


def check_batch_completion(batch):
    """
    检查批次是否完成
    """
    try:
        from evaluations.models import EvaluationRelation
        
        total_relations = EvaluationRelation.objects.filter(batch=batch).count()
        completed_relations = EvaluationRelation.objects.filter(
            batch=batch,
            evaluationrecord__isnull=False
        ).count()
        
        # 如果全部完成且批次状态不是completed，更新状态
        if total_relations > 0 and completed_relations == total_relations:
            if batch.status == 'active':
                batch.status = 'completed'
                batch.save(update_fields=['status'])
                logger.info(f"批次 {batch.name} 已自动标记为完成")
                
    except Exception as e:
        logger.error(f"检查批次完成状态异常: {str(e)}")


# 手动触发函数，用于管理命令或定时任务
def send_deadline_reminders():
    """
    发送截止提醒（用于定时任务）
    """
    try:
        sent_count = notification_service.check_and_send_deadline_reminders()
        logger.info(f"定时提醒任务完成，发送通知数量: {sent_count}")
        return sent_count
    except Exception as e:
        logger.error(f"发送定时提醒失败: {str(e)}")
        return 0


def send_system_notification(title, content, departments=None, priority='MEDIUM'):
    """
    发送系统通知（用于管理接口）
    """
    try:
        success = notification_service.send_system_maintenance_notification(
            title=title,
            content=content,
            target_departments=departments,
            priority=priority
        )
        
        if success:
            logger.info(f"系统通知发送成功: {title}")
        else:
            logger.warning(f"系统通知发送失败: {title}")
            
        return success
        
    except Exception as e:
        logger.error(f"发送系统通知异常: {str(e)}")
        return False


def check_and_send_timeout_alerts():
    """
    检查并发送考评超时异常通知
    用于定时任务调用
    """
    try:
        sent_count = notification_service.check_and_send_timeout_alerts()
        logger.info(f"超时检查任务完成，发送警报数量: {sent_count}")
        return sent_count
    except Exception as e:
        logger.error(f"检查和发送超时警报失败: {str(e)}")
        return 0


def send_timeout_alert_for_batch(batch):
    """
    为指定批次发送超时警报
    用于手动触发或特定事件触发
    """
    try:
        from evaluations.models import EvaluationRelation
        from django.utils import timezone
        
        # 检查批次是否已过期且有未完成任务
        if batch.end_date >= timezone.now():
            logger.info(f"批次【{batch.name}】尚未过期，无需发送超时警报")
            return False
        
        # 查找超时的考评关系
        timeout_relations = EvaluationRelation.objects.filter(
            batch=batch,
            status__in=['pending', 'draft'],
            deleted_at__isnull=True
        ).select_related('evaluator', 'evaluatee', 'evaluator__department')
        
        if not timeout_relations.exists():
            logger.info(f"批次【{batch.name}】没有超时任务")
            return False
        
        # 发送超时警报
        success = notification_service.send_evaluation_timeout_alert(batch, timeout_relations)
        
        if success:
            logger.info(f"批次【{batch.name}】超时警报发送成功，涉及{timeout_relations.count()}个任务")
        else:
            logger.warning(f"批次【{batch.name}】超时警报发送失败")
        
        return success
        
    except Exception as e:
        logger.error(f"为批次【{batch.name}】发送超时警报异常: {str(e)}")
        return False


# 定期超时检查任务（可以通过Celery或cron调用）
def periodic_timeout_check():
    """
    定期超时检查任务
    建议每天执行一次
    """
    try:
        from evaluations.models import EvaluationBatch
        from django.utils import timezone
        
        logger.info("开始执行定期超时检查任务")
        
        # 查找已过期但仍活跃的批次
        expired_batches = EvaluationBatch.objects.filter(
            status='active',
            end_date__lt=timezone.now()
        )
        
        total_alerts_sent = 0
        for batch in expired_batches:
            if send_timeout_alert_for_batch(batch):
                total_alerts_sent += 1
        
        logger.info(f"定期超时检查任务完成，共发送{total_alerts_sent}个警报")
        return total_alerts_sent
        
    except Exception as e:
        logger.error(f"定期超时检查任务异常: {str(e)}")
        return 0