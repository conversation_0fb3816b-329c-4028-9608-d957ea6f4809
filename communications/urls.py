# -*- coding: utf-8 -*-
"""
站内通信URL配置
"""

from django.urls import path, include
from . import views

app_name = 'communications'

# API路由
api_patterns = [
    # 消息相关API
    path('messages/', views.MessageListAPIView.as_view(), name='message_list_api'),
    path('messages/<int:pk>/', views.MessageDetailAPIView.as_view(), name='message_detail_api'),
    path('messages/send/', views.MessageSendAPIView.as_view(), name='message_send_api'),
    path('messages/<int:pk>/read/', views.MessageMarkReadAPIView.as_view(), name='message_read_api'),
    path('messages/unread-count/', views.UnreadMessageCountAPIView.as_view(), name='unread_count_api'),
    path('messages/batch-read/', views.BatchMarkReadAPIView.as_view(), name='batch_read_api'),
    
    # 公告相关API
    path('announcements/', views.AnnouncementListAPIView.as_view(), name='announcement_list_api'),
    path('announcements/<int:pk>/', views.AnnouncementDetailAPIView.as_view(), name='announcement_detail_api'),
    path('announcements/create/', views.AnnouncementCreateAPIView.as_view(), name='announcement_create_api'),
    path('announcements/<int:pk>/delete/', views.AnnouncementDeleteAPIView.as_view(), name='announcement_delete_api'),
    
    # 基础数据API
    path('staff-data/', views.StaffDataAPIView.as_view(), name='staff_data_api'),
    
    # 实时通信API
    path('poll/', views.MessagePollAPIView.as_view(), name='message_poll_api'),
    path('messages/recent/', views.RecentMessagesAPIView.as_view(), name='recent_messages_api'),
    path('notifications/recent/', views.RecentNotificationsAPIView.as_view(), name='recent_notifications_api'),
]

# 管理后台路由
admin_patterns = [
    # 消息管理页面
    path('messages/', views.MessageCenterView.as_view(), name='message_center'),
    path('messages/<int:pk>/', views.MessageDetailView.as_view(), name='message_detail'),
    path('messages/compose/', views.MessageComposeView.as_view(), name='message_compose'),
    
    # 公告管理页面
    path('announcements/', views.AnnouncementListView.as_view(), name='announcement_list'),
    path('announcements/create/', views.AnnouncementCreateView.as_view(), name='announcement_create'),
    path('announcements/<int:pk>/', views.AnnouncementDetailView.as_view(), name='announcement_detail'),
    path('announcements/<int:pk>/edit/', views.AnnouncementUpdateView.as_view(), name='announcement_update'),
    
    # 通知模板管理
    path('templates/', views.NotificationTemplateListView.as_view(), name='template_list'),
    path('templates/create/', views.NotificationTemplateCreateView.as_view(), name='template_create'),
    path('templates/<int:pk>/edit/', views.NotificationTemplateUpdateView.as_view(), name='template_update'),
]

# 匿名端路由
anonymous_patterns = [
    # 消息查看（只读）
    path('messages/', views.AnonymousMessageListView.as_view(), name='anonymous_message_list'),
    path('notifications/', views.AnonymousNotificationListView.as_view(), name='anonymous_notification_list'),
]

urlpatterns = [
    # API接口
    path('api/', include(api_patterns)),
    
    # 管理后台
    path('admin/', include(admin_patterns)),
    
    # 匿名端
    path('anonymous/', include(anonymous_patterns)),
]