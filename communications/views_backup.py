# -*- coding: utf-8 -*-
"""
站内通信视图
提供消息通知、公告发布等功能的视图层
"""

from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse
from django.views.generic import ListView, DetailView, CreateView, UpdateView
from django.views import View
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Q, Count, Prefetch
from django.utils import timezone
from django.core.paginator import Paginator
from django.conf import settings
from django.utils.decorators import method_decorator
import json

from common.security.permissions import require_permission, require_role, Permission, Role
from organizations.models import Staff, Department
from .models import (
    Message, MessageRecipient, Announcement,
    NotificationTemplate, MessageReadLog
)


class CommunicationBaseMixin:
    """通信模块基础混入类"""
    
    def get_current_staff(self):
        """获取当前员工对象"""
        if hasattr(self.request, 'staff'):
            return self.request.staff
        return None
    
    def get_staff_messages(self, staff):
        """获取员工的消息"""
        return MessageRecipient.objects.filter(
            recipient=staff,
            is_deleted=False
        ).select_related(
            'message', 'message__sender'
        ).order_by('-created_at')


# ================================
# API视图 - 用于AJAX调用
# ================================

class MessageListAPIView(LoginRequiredMixin, CommunicationBaseMixin, View):
    """消息列表API"""
    
    def get(self, request):
        """获取消息列表"""
        staff = self.get_current_staff()
        if not staff:
            return JsonResponse({'error': '用户未认证'}, status=401)
        
        # 获取查询参数
        message_type = request.GET.get('type', '')
        is_read = request.GET.get('read', '')
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))
        
        # 构建查询
        queryset = self.get_staff_messages(staff)
        
        if message_type:
            queryset = queryset.filter(message__message_type=message_type)
        
        if is_read == 'true':
            queryset = queryset.filter(is_read=True)
        elif is_read == 'false':
            queryset = queryset.filter(is_read=False)
        
        # 分页
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)
        
        # 构建响应数据
        messages = []
        for recipient in page_obj:
            message_data = {
                'id': recipient.message.id,
                'subject': recipient.message.subject,
                'content': recipient.message.content[:200] + '...' if len(recipient.message.content) > 200 else recipient.message.content,
                'message_type': recipient.message.message_type,
                'priority': recipient.message.priority,
                'sender': {
                    'id': recipient.message.sender.id if recipient.message.sender else None,
                    'name': recipient.message.sender.name if recipient.message.sender else '系统'
                },
                'is_read': recipient.is_read,
                'is_starred': recipient.is_starred,
                'read_at': recipient.read_at.isoformat() if recipient.read_at else None,
                'created_at': recipient.message.created_at.isoformat(),
            }
            messages.append(message_data)
        
        return JsonResponse({
            'messages': messages,
            'pagination': {
                'current_page': page_obj.number,
                'total_pages': paginator.num_pages,
                'total_count': paginator.count,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous(),
            }
        })


class MessageDetailAPIView(LoginRequiredMixin, CommunicationBaseMixin, View):
    """消息详情API"""
    
    def get(self, request, pk):
        """获取消息详情"""
        staff = self.get_current_staff()
        if not staff:
            return JsonResponse({'error': '用户未认证'}, status=401)
        
        try:
            recipient = MessageRecipient.objects.select_related(
                'message', 'message__sender'
            ).get(
                message_id=pk,
                recipient=staff,
                is_deleted=False
            )
        except MessageRecipient.DoesNotExist:
            return JsonResponse({'error': '消息不存在'}, status=404)
        
        # 自动标记为已读
        if not recipient.is_read:
            recipient.mark_as_read()
            # 记录阅读日志
            MessageReadLog.objects.create(
                message=recipient.message,
                reader=staff,
                ip_address=self.get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', '')[:500]
            )
        
        message_data = {
            'id': recipient.message.id,
            'subject': recipient.message.subject,
            'content': recipient.message.content,
            'message_type': recipient.message.message_type,
            'priority': recipient.message.priority,
            'sender': {
                'id': recipient.message.sender.id if recipient.message.sender else None,
                'name': recipient.message.sender.name if recipient.message.sender else '系统'
            },
            'is_read': recipient.is_read,
            'is_starred': recipient.is_starred,
            'read_at': recipient.read_at.isoformat() if recipient.read_at else None,
            'created_at': recipient.message.created_at.isoformat(),
            'expires_at': recipient.message.expires_at.isoformat() if recipient.message.expires_at else None,
        }
        
        return JsonResponse({'message': message_data})
    
    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class MessageSendAPIView(LoginRequiredMixin, CommunicationBaseMixin, View):
    """发送消息API"""
    
    def post(self, request):
        """发送消息"""
        staff = self.get_current_staff()
        if not staff:
            return JsonResponse({'error': '用户未认证'}, status=401)
        
        try:
            data = json.loads(request.body)
        except json.JSONDecodeError:
            return JsonResponse({'error': '无效的JSON数据'}, status=400)
        
        # 验证必需字段
        required_fields = ['subject', 'content', 'recipients']
        for field in required_fields:
            if field not in data:
                return JsonResponse({'error': f'缺少必需字段: {field}'}, status=400)
        
        # 创建消息
        message = Message.objects.create(
            message_type='PERSONAL',
            sender=staff,
            subject=data['subject'],
            content=data['content'],
            priority=data.get('priority', 'MEDIUM'),
            created_by=staff.name
        )
        
        # 添加接收者
        recipients_created = 0
        for recipient_id in data['recipients']:
            try:
                recipient_staff = Staff.objects.get(id=recipient_id)
                MessageRecipient.objects.create(
                    message=message,
                    recipient=recipient_staff,
                    recipient_type='STAFF',
                    created_by=staff.name
                )
                recipients_created += 1
            except Staff.DoesNotExist:
                continue
        
        return JsonResponse({
            'message': '消息发送成功',
            'message_id': message.id,
            'recipients_count': recipients_created
        })


class MessageMarkReadAPIView(LoginRequiredMixin, CommunicationBaseMixin, View):
    """标记消息已读API"""
    
    def post(self, request, pk):
        """标记消息为已读"""
        staff = self.get_current_staff()
        if not staff:
            return JsonResponse({'error': '用户未认证'}, status=401)
        
        try:
            recipient = MessageRecipient.objects.get(
                message_id=pk,
                recipient=staff,
                is_deleted=False
            )
            
            if not recipient.is_read:
                recipient.mark_as_read()
                # 记录阅读日志
                MessageReadLog.objects.create(
                    message=recipient.message,
                    reader=staff,
                    ip_address=self.get_client_ip(request),
                    user_agent=request.META.get('HTTP_USER_AGENT', '')[:500]
                )
                
            return JsonResponse({'message': '标记成功'})
            
        except MessageRecipient.DoesNotExist:
            return JsonResponse({'error': '消息不存在'}, status=404)
    
    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class UnreadMessageCountAPIView(LoginRequiredMixin, CommunicationBaseMixin, View):
    """未读消息数量API"""
    
    def get(self, request):
        """获取未读消息数量"""
        staff = self.get_current_staff()
        if not staff:
            return JsonResponse({'error': '用户未认证'}, status=401)
        
        # 总未读数量
        total_unread = MessageRecipient.objects.filter(
            recipient=staff,
            is_read=False,
            is_deleted=False
        ).count()
        
        # 按类型分组统计
        type_counts = MessageRecipient.objects.filter(
            recipient=staff,
            is_read=False,
            is_deleted=False
        ).values('message__message_type').annotate(
            count=Count('id')
        )
        
        type_breakdown = {}
        for item in type_counts:
            type_breakdown[item['message__message_type']] = item['count']
        
        return JsonResponse({
            'total_unread': total_unread,
            'type_breakdown': type_breakdown
        })


class BatchMarkReadAPIView(LoginRequiredMixin, CommunicationBaseMixin, View):
    """批量标记已读API"""
    
    def post(self, request):
        """批量标记消息为已读"""
        staff = self.get_current_staff()
        if not staff:
            return JsonResponse({'error': '用户未认证'}, status=401)
        
        try:
            data = json.loads(request.body)
            message_ids = data.get('message_ids', [])
        except json.JSONDecodeError:
            return JsonResponse({'error': '无效的JSON数据'}, status=400)
        
        if not message_ids:
            return JsonResponse({'error': '未指定消息ID'}, status=400)
        
        # 批量更新
        updated_count = MessageRecipient.objects.filter(
            message_id__in=message_ids,
            recipient=staff,
            is_read=False,
            is_deleted=False
        ).update(
            is_read=True,
            read_at=timezone.now()
        )
        
        return JsonResponse({
            'message': '批量标记成功',
            'updated_count': updated_count
        })


class MessagePollAPIView(LoginRequiredMixin, CommunicationBaseMixin, View):
    """消息轮询API"""
    
    def get(self, request):
        """轮询新消息"""
        staff = self.get_current_staff()
        if not staff:
            return JsonResponse({'error': '用户未认证'}, status=401)
        
        # 获取最后检查时间
        last_check = request.GET.get('last_check')
        if last_check:
            try:
                from datetime import datetime
                last_check_time = datetime.fromisoformat(last_check.replace('Z', '+00:00'))
            except (ValueError, TypeError):
                last_check_time = timezone.now() - timezone.timedelta(minutes=5)
        else:
            last_check_time = timezone.now() - timezone.timedelta(minutes=5)
        
        # 查询新消息
        new_messages = MessageRecipient.objects.filter(
            recipient=staff,
            is_deleted=False,
            created_at__gt=last_check_time
        ).select_related('message', 'message__sender').order_by('-created_at')[:10]
        
        messages = []
        for recipient in new_messages:
            message_data = {
                'id': recipient.message.id,
                'subject': recipient.message.subject,
                'message_type': recipient.message.message_type,
                'priority': recipient.message.priority,
                'sender': {
                    'name': recipient.message.sender.name if recipient.message.sender else '系统'
                },
                'is_read': recipient.is_read,
                'created_at': recipient.message.created_at.isoformat(),
            }
            messages.append(message_data)
        
        # 未读数量
        unread_count = MessageRecipient.objects.filter(
            recipient=staff,
            is_read=False,
            is_deleted=False
        ).count()
        
        return JsonResponse({
            'new_messages': messages,
            'unread_count': unread_count,
            'current_time': timezone.now().isoformat()
        })


# ================================
# 公告相关API
# ================================

class AnnouncementListAPIView(LoginRequiredMixin, CommunicationBaseMixin, View):
    """公告列表API"""
    
    def get(self, request):
        """获取公告列表"""
        staff = self.get_current_staff()
        if not staff:
            return JsonResponse({'error': '用户未认证'}, status=401)
        
        # 构建查询
        queryset = Announcement.objects.filter(
            is_published=True,
            publish_at__lte=timezone.now()
        ).filter(
            Q(expire_at__isnull=True) | Q(expire_at__gt=timezone.now())
        ).filter(
            Q(target_department__isnull=True) | Q(target_department=staff.department)
        ).select_related('author', 'target_department').order_by('-is_pinned', '-created_at')
        
        # 分页
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 10))
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)
        
        announcements = []
        for announcement in page_obj:
            announcement_data = {
                'id': announcement.id,
                'title': announcement.title,
                'content': announcement.content[:200] + '...' if len(announcement.content) > 200 else announcement.content,
                'announcement_type': announcement.announcement_type,
                'author': {
                    'name': announcement.author.name if announcement.author else '系统'
                },
                'is_pinned': announcement.is_pinned,
                'view_count': announcement.view_count,
                'created_at': announcement.created_at.isoformat(),
            }
            announcements.append(announcement_data)
        
        return JsonResponse({
            'announcements': announcements,
            'pagination': {
                'current_page': page_obj.number,
                'total_pages': paginator.num_pages,
                'total_count': paginator.count,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous(),
            }
        })


class AnnouncementDetailAPIView(LoginRequiredMixin, CommunicationBaseMixin, View):
    """公告详情API"""
    
    def get(self, request, pk):
        """获取公告详情"""
        staff = self.get_current_staff()
        if not staff:
            return JsonResponse({'error': '用户未认证'}, status=401)
        
        try:
            announcement = Announcement.objects.select_related(
                'author', 'target_department'
            ).get(
                id=pk,
                is_published=True,
                publish_at__lte=timezone.now()
            )
            
            # 检查权限
            if announcement.target_department and announcement.target_department != staff.department:
                return JsonResponse({'error': '无权查看此公告'}, status=403)
            
            # 增加查看次数
            announcement.increment_view_count()
            
            announcement_data = {
                'id': announcement.id,
                'title': announcement.title,
                'content': announcement.content,
                'announcement_type': announcement.announcement_type,
                'author': {
                    'name': announcement.author.name if announcement.author else '系统'
                },
                'target_department': {
                    'name': announcement.target_department.name if announcement.target_department else '全公司'
                },
                'is_pinned': announcement.is_pinned,
                'view_count': announcement.view_count,
                'is_html': announcement.is_html,
                'created_at': announcement.created_at.isoformat(),
                'expire_at': announcement.expire_at.isoformat() if announcement.expire_at else None,
            }
            
            return JsonResponse({'announcement': announcement_data})
            
        except Announcement.DoesNotExist:
            return JsonResponse({'error': '公告不存在'}, status=404)


class AnnouncementCreateAPIView(CommunicationBaseMixin, View):
    """创建公告API"""
    
    @require_permission(Permission.SYS_MANAGE_SETTINGS)
    def post(self, request):
        """创建公告"""
        staff = self.get_current_staff()
        if not staff:
            return JsonResponse({'error': '用户未认证'}, status=401)
        
        try:
            data = json.loads(request.body)
        except json.JSONDecodeError:
            return JsonResponse({'error': '无效的JSON数据'}, status=400)
        
        # 验证必需字段
        required_fields = ['title', 'content']
        for field in required_fields:
            if field not in data:
                return JsonResponse({'error': f'缺少必需字段: {field}'}, status=400)
        
        # 创建公告
        announcement_data = {
            'title': data['title'],
            'content': data['content'],
            'announcement_type': data.get('announcement_type', 'SYSTEM'),
            'author': staff,
            'is_pinned': data.get('is_pinned', False),
            'is_html': data.get('is_html', False),
            'created_by': staff.name
        }
        
        # 处理目标部门
        if data.get('target_department_id'):
            try:
                department = Department.objects.get(id=data['target_department_id'])
                announcement_data['target_department'] = department
            except Department.DoesNotExist:
                return JsonResponse({'error': '指定的部门不存在'}, status=400)
        
        # 处理过期时间
        if data.get('expire_at'):
            try:
                from datetime import datetime
                expire_at = datetime.fromisoformat(data['expire_at'].replace('Z', '+00:00'))
                announcement_data['expire_at'] = expire_at
            except (ValueError, TypeError):
                return JsonResponse({'error': '无效的过期时间格式'}, status=400)
        
        announcement = Announcement.objects.create(**announcement_data)
        
        return JsonResponse({
            'message': '公告创建成功',
            'announcement_id': announcement.id
        })


class RecentNotificationsAPIView(LoginRequiredMixin, CommunicationBaseMixin, View):
    """最近通知API"""
    
    def get(self, request):
        """获取最近通知"""
        staff = self.get_current_staff()
        if not staff:
            return JsonResponse({'error': '用户未认证'}, status=401)
        
        # 最近的未读消息（限制5条）
        recent_messages = MessageRecipient.objects.filter(
            recipient=staff,
            is_read=False,
            is_deleted=False
        ).select_related(
            'message', 'message__sender'
        ).order_by('-created_at')[:5]
        
        # 最近的公告（限制3条）
        recent_announcements = Announcement.objects.filter(
            is_published=True,
            publish_at__lte=timezone.now()
        ).filter(
            Q(expire_at__isnull=True) | Q(expire_at__gt=timezone.now())
        ).filter(
            Q(target_department__isnull=True) | Q(target_department=staff.department)
        ).order_by('-created_at')[:3]
        
        # 构建响应数据
        notifications = []
        
        # 添加消息通知
        for recipient in recent_messages:
            notifications.append({
                'type': 'message',
                'id': recipient.message.id,
                'title': recipient.message.subject,
                'content': recipient.message.content[:100] + '...' if len(recipient.message.content) > 100 else recipient.message.content,
                'priority': recipient.message.priority,
                'created_at': recipient.message.created_at.isoformat(),
                'url': f'/communications/admin/messages/{recipient.message.id}/'
            })
        
        # 添加公告通知
        for announcement in recent_announcements:
            notifications.append({
                'type': 'announcement',
                'id': announcement.id,
                'title': announcement.title,
                'content': announcement.content[:100] + '...' if len(announcement.content) > 100 else announcement.content,
                'is_pinned': announcement.is_pinned,
                'created_at': announcement.created_at.isoformat(),
                'url': f'/communications/admin/announcements/{announcement.id}/'
            })
        
        # 按时间排序
        notifications.sort(key=lambda x: x['created_at'], reverse=True)
        
        return JsonResponse({
            'notifications': notifications[:8],  # 最多返回8条
            'total_unread_messages': recent_messages.count()
        })


# ================================
# 管理后台页面视图
# ================================

class MessageCenterView(LoginRequiredMixin, CommunicationBaseMixin, ListView):
    """消息中心页面"""
    model = MessageRecipient
    template_name = 'admin/communications/message_center.html'
    context_object_name = 'message_recipients'
    paginate_by = 20
    
    def get_queryset(self):
        """获取当前用户的消息"""
        staff = self.get_current_staff()
        if not staff:
            return MessageRecipient.objects.none()
        
        return self.get_staff_messages(staff)
    
    def get_context_data(self, **kwargs):
        """添加额外的上下文数据"""
        context = super().get_context_data(**kwargs)
        staff = self.get_current_staff()
        
        if staff:
            # 统计数据
            context['unread_count'] = MessageRecipient.objects.filter(
                recipient=staff, is_read=False, is_deleted=False
            ).count()
            
            context['total_count'] = MessageRecipient.objects.filter(
                recipient=staff, is_deleted=False
            ).count()
            
            # 消息类型统计
            type_stats = MessageRecipient.objects.filter(
                recipient=staff, is_deleted=False
            ).values('message__message_type').annotate(count=Count('id'))
            
            context['type_stats'] = {item['message__message_type']: item['count'] for item in type_stats}
        
        return context


class MessageDetailView(LoginRequiredMixin, CommunicationBaseMixin, DetailView):
    """消息详情页面"""
    model = Message
    template_name = 'admin/communications/message_detail.html'
    context_object_name = 'message'
    
    def get_object(self, queryset=None):
        """获取消息对象并检查权限"""
        staff = self.get_current_staff()
        message = super().get_object(queryset)
        
        # 检查用户是否有权查看此消息
        try:
            recipient = MessageRecipient.objects.get(
                message=message,
                recipient=staff,
                is_deleted=False
            )
            
            # 自动标记为已读
            if not recipient.is_read:
                recipient.mark_as_read()
                # 记录阅读日志
                MessageReadLog.objects.create(
                    message=message,
                    reader=staff,
                    ip_address=self.get_client_ip(),
                    user_agent=self.request.META.get('HTTP_USER_AGENT', '')[:500]
                )
            
            return message
            
        except MessageRecipient.DoesNotExist:
            from django.http import Http404
            raise Http404("消息不存在或无权查看")
    
    def get_client_ip(self):
        """获取客户端IP地址"""
        x_forwarded_for = self.request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = self.request.META.get('REMOTE_ADDR')
        return ip


# ================================
# 其他视图类将在后续实现...
# ================================

class MessageComposeView(LoginRequiredMixin, View):
    """消息编写页面"""
    def get(self, request):
        return render(request, 'admin/communications/message_compose.html')


class AnnouncementListView(LoginRequiredMixin, ListView):
    """公告列表页面"""
    model = Announcement
    template_name = 'admin/communications/announcement_list.html'
    
    def get_queryset(self):
        return Announcement.objects.filter(is_published=True).order_by('-is_pinned', '-created_at')


@method_decorator(require_permission(Permission.SYS_MANAGE_SETTINGS), name='dispatch')
class AnnouncementCreateView(CreateView):
    """创建公告页面"""
    model = Announcement
    template_name = 'admin/communications/announcement_create.html'
    fields = ['title', 'content', 'announcement_type', 'is_pinned']


class AnnouncementDetailView(LoginRequiredMixin, DetailView):
    """公告详情页面"""
    model = Announcement
    template_name = 'admin/communications/announcement_detail.html'


@method_decorator(require_permission(Permission.SYS_MANAGE_SETTINGS), name='dispatch')
class AnnouncementUpdateView(UpdateView):
    """编辑公告页面"""
    model = Announcement
    template_name = 'admin/communications/announcement_update.html'
    fields = ['title', 'content', 'announcement_type', 'is_pinned']


@method_decorator(require_permission(Permission.SYS_MANAGE_SETTINGS), name='dispatch')
class NotificationTemplateListView(ListView):
    """通知模板列表页面"""
    model = NotificationTemplate
    template_name = 'admin/communications/template_list.html'


@method_decorator(require_permission(Permission.SYS_MANAGE_SETTINGS), name='dispatch')
class NotificationTemplateCreateView(CreateView):
    """创建通知模板页面"""
    model = NotificationTemplate
    template_name = 'admin/communications/template_create.html'
    fields = ['template_type', 'name', 'subject_template', 'content_template']


@method_decorator(require_permission(Permission.SYS_MANAGE_SETTINGS), name='dispatch')
class NotificationTemplateUpdateView(UpdateView):
    """编辑通知模板页面"""
    model = NotificationTemplate
    template_name = 'admin/communications/template_update.html'
    fields = ['template_type', 'name', 'subject_template', 'content_template']


# ================================
# 匿名端视图
# ================================

class AnonymousMessageListView(ListView):
    """匿名端消息列表（只读）"""
    model = MessageRecipient
    template_name = 'anonymous/communications/message_list.html'
    
    def get_queryset(self):
        # 这里需要根据匿名用户的身份获取相应的消息
        # 具体实现取决于匿名认证系统的设计
        return MessageRecipient.objects.none()


class AnonymousNotificationListView(ListView):
    """匿名端通知列表（只读）"""
    model = Announcement
    template_name = 'anonymous/communications/notification_list.html'
    
    def get_queryset(self):
        return Announcement.objects.filter(
            is_published=True,
            target_department__isnull=True  # 只显示全公司公告
        ).order_by('-is_pinned', '-created_at')