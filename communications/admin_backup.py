# -*- coding: utf-8 -*-
"""
站内通信管理后台配置
"""

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import (
    Message, MessageRecipient, Announcement,
    NotificationTemplate, MessageReadLog
)


@admin.register(Message)
class MessageAdmin(admin.ModelAdmin):
    """消息管理"""
    list_display = [
        'subject', 'message_type', 'sender', 'priority',
        'is_broadcast', 'recipient_count', 'unread_count', 'created_at'
    ]
    list_filter = [
        'message_type', 'priority', 'is_broadcast',
        'created_at', 'sender'
    ]
    search_fields = ['subject', 'content', 'sender__name']
    readonly_fields = ['created_at', 'updated_at']
    fieldsets = (
        ('基本信息', {
            'fields': ('message_type', 'sender', 'subject', 'content')
        }),
        ('消息设置', {
            'fields': ('priority', 'is_broadcast', 'expires_at')
        }),
        ('关联信息', {
            'fields': ('related_model', 'related_id'),
            'classes': ('collapse',)
        }),
        ('系统信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def recipient_count(self, obj):
        """接收者总数"""
        return obj.get_total_recipients()
    recipient_count.short_description = '接收者数量'
    
    def unread_count(self, obj):
        """未读数量"""
        count = obj.get_unread_count()
        if count > 0:
            return format_html(
                '<span style="color: red; font-weight: bold;">{}</span>',
                count
            )
        return count
    unread_count.short_description = '未读数量'


@admin.register(MessageRecipient)
class MessageRecipientAdmin(admin.ModelAdmin):
    """消息接收者管理"""
    list_display = [
        'message_subject', 'recipient', 'recipient_type',
        'is_read', 'read_at', 'is_starred', 'is_deleted'
    ]
    list_filter = [
        'recipient_type', 'is_read', 'is_starred',
        'is_deleted', 'created_at'
    ]
    search_fields = [
        'message__subject', 'recipient__name',
        'message__content'
    ]
    readonly_fields = ['created_at', 'updated_at', 'read_at']
    
    def message_subject(self, obj):
        """消息主题"""
        return obj.message.subject[:50]
    message_subject.short_description = '消息主题'


@admin.register(Announcement)
class AnnouncementAdmin(admin.ModelAdmin):
    """公告管理"""
    list_display = [
        'title', 'announcement_type', 'author',
        'target_department', 'is_pinned', 'is_published',
        'view_count', 'created_at'
    ]
    list_filter = [
        'announcement_type', 'is_pinned', 'is_published',
        'target_department', 'created_at'
    ]
    search_fields = ['title', 'content', 'author__name']
    readonly_fields = ['view_count', 'created_at', 'updated_at']
    fieldsets = (
        ('基本信息', {
            'fields': ('title', 'content', 'announcement_type', 'author')
        }),
        ('发布设置', {
            'fields': ('target_department', 'is_pinned', 'is_published')
        }),
        ('时间设置', {
            'fields': ('publish_at', 'expire_at')
        }),
        ('内容格式', {
            'fields': ('is_html',),
            'classes': ('collapse',)
        }),
        ('统计信息', {
            'fields': ('view_count', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related(
            'author', 'target_department'
        )


@admin.register(NotificationTemplate)
class NotificationTemplateAdmin(admin.ModelAdmin):
    """通知模板管理"""
    list_display = [
        'name', 'template_type', 'is_active', 'created_at'
    ]
    list_filter = ['template_type', 'is_active', 'created_at']
    search_fields = ['name', 'subject_template', 'content_template']
    readonly_fields = ['created_at', 'updated_at']
    fieldsets = (
        ('基本信息', {
            'fields': ('template_type', 'name', 'is_active')
        }),
        ('模板内容', {
            'fields': ('subject_template', 'content_template')
        }),
        ('变量配置', {
            'fields': ('variables',),
            'classes': ('collapse',)
        }),
        ('系统信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(MessageReadLog)
class MessageReadLogAdmin(admin.ModelAdmin):
    """消息阅读日志管理"""
    list_display = [
        'message_subject', 'reader', 'read_at', 'ip_address'
    ]
    list_filter = ['read_at']
    search_fields = [
        'message__subject', 'reader__name', 'ip_address'
    ]
    readonly_fields = ['created_at', 'updated_at']
    
    def message_subject(self, obj):
        """消息主题"""
        return obj.message.subject[:50]
    message_subject.short_description = '消息主题'
    
    def has_add_permission(self, request):
        """禁止手动添加日志"""
        return False
    
    def has_change_permission(self, request, obj=None):
        """禁止修改日志"""
        return False