# Generated by Django 5.2.3 on 2025-07-30 05:35

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('organizations', '0005_update_role_field'),
    ]

    operations = [
        migrations.CreateModel(
            name='NotificationTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='记录创建时间', verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='记录最后更新时间', verbose_name='更新时间')),
                ('created_by', models.CharField(blank=True, help_text='记录创建者', max_length=50, null=True, verbose_name='创建人')),
                ('updated_by', models.CharField(blank=True, help_text='记录最后更新者', max_length=50, null=True, verbose_name='更新人')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='软删除时间，为空表示未删除', null=True, verbose_name='删除时间')),
                ('deleted_by', models.CharField(blank=True, help_text='软删除操作者', max_length=50, null=True, verbose_name='删除人')),
                ('template_type', models.CharField(choices=[('EVALUATION_START', '考评开始通知'), ('EVALUATION_REMIND', '考评提醒通知'), ('EVALUATION_COMPLETE', '考评完成通知'), ('PASSWORD_RESET', '密码重置通知'), ('ROLE_CHANGE', '角色变更通知'), ('ACCOUNT_STATUS', '账户状态通知'), ('SYSTEM_MAINTENANCE', '系统维护通知')], help_text='通知模板的类型', max_length=30, unique=True, verbose_name='模板类型')),
                ('name', models.CharField(help_text='模板的显示名称', max_length=100, verbose_name='模板名称')),
                ('subject_template', models.CharField(help_text='消息主题的模板，支持变量替换', max_length=200, verbose_name='主题模板')),
                ('content_template', models.TextField(help_text='消息内容的模板，支持变量替换', verbose_name='内容模板')),
                ('variables', models.JSONField(default=dict, help_text='模板中使用的变量定义（JSON格式）', verbose_name='模板变量')),
                ('is_active', models.BooleanField(default=True, help_text='模板是否启用', verbose_name='是否启用')),
            ],
            options={
                'verbose_name': '通知模板',
                'verbose_name_plural': '通知模板',
                'ordering': ['template_type'],
            },
        ),
        migrations.CreateModel(
            name='Message',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='记录创建时间', verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='记录最后更新时间', verbose_name='更新时间')),
                ('created_by', models.CharField(blank=True, help_text='记录创建者', max_length=50, null=True, verbose_name='创建人')),
                ('updated_by', models.CharField(blank=True, help_text='记录最后更新者', max_length=50, null=True, verbose_name='更新人')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='软删除时间，为空表示未删除', null=True, verbose_name='删除时间')),
                ('deleted_by', models.CharField(blank=True, help_text='软删除操作者', max_length=50, null=True, verbose_name='删除人')),
                ('message_type', models.CharField(choices=[('SYSTEM', '系统通知'), ('PERSONAL', '个人消息'), ('EVALUATION', '考评相关'), ('ANNOUNCEMENT', '公告通知')], default='SYSTEM', help_text='消息的分类类型', max_length=20, verbose_name='消息类型')),
                ('subject', models.CharField(help_text='消息的标题或主题', max_length=200, verbose_name='消息主题')),
                ('content', models.TextField(help_text='消息的详细内容', verbose_name='消息内容')),
                ('priority', models.CharField(choices=[('HIGH', '高优先级'), ('MEDIUM', '中优先级'), ('LOW', '低优先级')], default='MEDIUM', help_text='消息的重要程度', max_length=10, verbose_name='优先级')),
                ('is_broadcast', models.BooleanField(default=False, help_text='是否向所有用户广播此消息', verbose_name='是否广播')),
                ('related_model', models.CharField(blank=True, help_text='相关联的业务模型名称', max_length=50, verbose_name='关联模型')),
                ('related_id', models.PositiveIntegerField(blank=True, help_text='相关联的业务对象ID', null=True, verbose_name='关联对象ID')),
                ('expires_at', models.DateTimeField(blank=True, help_text='消息的过期时间，过期后不再显示', null=True, verbose_name='过期时间')),
                ('sender', models.ForeignKey(blank=True, help_text='消息发送者，为空表示系统消息', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sent_messages', to='organizations.staff', verbose_name='发送者')),
            ],
            options={
                'verbose_name': '消息',
                'verbose_name_plural': '消息',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='MessageReadLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='记录创建时间', verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='记录最后更新时间', verbose_name='更新时间')),
                ('created_by', models.CharField(blank=True, help_text='记录创建者', max_length=50, null=True, verbose_name='创建人')),
                ('updated_by', models.CharField(blank=True, help_text='记录最后更新者', max_length=50, null=True, verbose_name='更新人')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='软删除时间，为空表示未删除', null=True, verbose_name='删除时间')),
                ('deleted_by', models.CharField(blank=True, help_text='软删除操作者', max_length=50, null=True, verbose_name='删除人')),
                ('read_at', models.DateTimeField(default=django.utils.timezone.now, help_text='消息被阅读的具体时间', verbose_name='阅读时间')),
                ('ip_address', models.GenericIPAddressField(blank=True, help_text='阅读时的IP地址', null=True, verbose_name='IP地址')),
                ('user_agent', models.TextField(blank=True, help_text='阅读时的浏览器信息', verbose_name='用户代理')),
                ('message', models.ForeignKey(help_text='被阅读的消息', on_delete=django.db.models.deletion.CASCADE, related_name='read_logs', to='communications.message', verbose_name='消息')),
                ('reader', models.ForeignKey(help_text='阅读消息的用户', on_delete=django.db.models.deletion.CASCADE, related_name='message_read_logs', to='organizations.staff', verbose_name='阅读者')),
            ],
            options={
                'verbose_name': '消息阅读日志',
                'verbose_name_plural': '消息阅读日志',
                'ordering': ['-read_at'],
            },
        ),
        migrations.CreateModel(
            name='MessageRecipient',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='记录创建时间', verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='记录最后更新时间', verbose_name='更新时间')),
                ('created_by', models.CharField(blank=True, help_text='记录创建者', max_length=50, null=True, verbose_name='创建人')),
                ('updated_by', models.CharField(blank=True, help_text='记录最后更新者', max_length=50, null=True, verbose_name='更新人')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='软删除时间，为空表示未删除', null=True, verbose_name='删除时间')),
                ('deleted_by', models.CharField(blank=True, help_text='软删除操作者', max_length=50, null=True, verbose_name='删除人')),
                ('recipient_type', models.CharField(choices=[('STAFF', '个人'), ('DEPARTMENT', '部门'), ('ALL', '全体')], default='STAFF', help_text='接收者的类型分类', max_length=20, verbose_name='接收者类型')),
                ('is_read', models.BooleanField(default=False, help_text='接收者是否已读此消息', verbose_name='是否已读')),
                ('read_at', models.DateTimeField(blank=True, help_text='消息被阅读的时间', null=True, verbose_name='阅读时间')),
                ('is_starred', models.BooleanField(default=False, help_text='接收者是否收藏此消息', verbose_name='是否收藏')),
                ('is_deleted', models.BooleanField(default=False, help_text='接收者是否删除此消息', verbose_name='是否删除')),
                ('message', models.ForeignKey(help_text='关联的消息', on_delete=django.db.models.deletion.CASCADE, related_name='recipients', to='communications.message', verbose_name='消息')),
                ('recipient', models.ForeignKey(help_text='消息接收者', on_delete=django.db.models.deletion.CASCADE, related_name='received_messages', to='organizations.staff', verbose_name='接收者')),
            ],
            options={
                'verbose_name': '消息接收者',
                'verbose_name_plural': '消息接收者',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Announcement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='记录创建时间', verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='记录最后更新时间', verbose_name='更新时间')),
                ('created_by', models.CharField(blank=True, help_text='记录创建者', max_length=50, null=True, verbose_name='创建人')),
                ('updated_by', models.CharField(blank=True, help_text='记录最后更新者', max_length=50, null=True, verbose_name='更新人')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='软删除时间，为空表示未删除', null=True, verbose_name='删除时间')),
                ('deleted_by', models.CharField(blank=True, help_text='软删除操作者', max_length=50, null=True, verbose_name='删除人')),
                ('title', models.CharField(help_text='公告的标题', max_length=200, verbose_name='公告标题')),
                ('content', models.TextField(help_text='公告的详细内容', verbose_name='公告内容')),
                ('announcement_type', models.CharField(choices=[('SYSTEM', '系统公告'), ('DEPARTMENT', '部门公告'), ('EVALUATION', '考评公告'), ('MAINTENANCE', '维护通知'), ('POLICY', '政策更新')], default='SYSTEM', help_text='公告的分类类型', max_length=20, verbose_name='公告类型')),
                ('is_pinned', models.BooleanField(default=False, help_text='是否在公告列表中置顶显示', verbose_name='是否置顶')),
                ('is_published', models.BooleanField(default=True, help_text='是否对目标用户可见', verbose_name='是否发布')),
                ('publish_at', models.DateTimeField(default=django.utils.timezone.now, help_text='公告的发布时间', verbose_name='发布时间')),
                ('expire_at', models.DateTimeField(blank=True, help_text='公告的过期时间，过期后不再显示', null=True, verbose_name='过期时间')),
                ('view_count', models.PositiveIntegerField(default=0, help_text='公告被查看的次数', verbose_name='查看次数')),
                ('is_html', models.BooleanField(default=False, help_text='内容是否为HTML格式', verbose_name='是否HTML格式')),
                ('author', models.ForeignKey(help_text='公告的发布者', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='authored_announcements', to='organizations.staff', verbose_name='发布者')),
                ('target_department', models.ForeignKey(blank=True, help_text='公告的目标部门，为空表示全公司', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='announcements', to='organizations.department', verbose_name='目标部门')),
            ],
            options={
                'verbose_name': '公告',
                'verbose_name_plural': '公告',
                'ordering': ['-is_pinned', '-created_at'],
                'indexes': [models.Index(fields=['announcement_type', 'is_published'], name='communicati_announc_518f1a_idx'), models.Index(fields=['target_department', 'is_published'], name='communicati_target__d1a961_idx'), models.Index(fields=['is_pinned', 'created_at'], name='communicati_is_pinn_d3bb64_idx'), models.Index(fields=['publish_at', 'expire_at'], name='communicati_publish_606271_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='message',
            index=models.Index(fields=['message_type', 'created_at'], name='communicati_message_7f4650_idx'),
        ),
        migrations.AddIndex(
            model_name='message',
            index=models.Index(fields=['sender', 'created_at'], name='communicati_sender__7da57d_idx'),
        ),
        migrations.AddIndex(
            model_name='message',
            index=models.Index(fields=['priority', 'created_at'], name='communicati_priorit_625eba_idx'),
        ),
        migrations.AddIndex(
            model_name='message',
            index=models.Index(fields=['expires_at'], name='communicati_expires_3fb034_idx'),
        ),
        migrations.AddIndex(
            model_name='messagereadlog',
            index=models.Index(fields=['message', 'read_at'], name='communicati_message_9451bd_idx'),
        ),
        migrations.AddIndex(
            model_name='messagereadlog',
            index=models.Index(fields=['reader', 'read_at'], name='communicati_reader__6fdfe2_idx'),
        ),
        migrations.AddIndex(
            model_name='messagerecipient',
            index=models.Index(fields=['recipient', 'is_read'], name='communicati_recipie_525888_idx'),
        ),
        migrations.AddIndex(
            model_name='messagerecipient',
            index=models.Index(fields=['recipient', 'is_deleted'], name='communicati_recipie_a84213_idx'),
        ),
        migrations.AddIndex(
            model_name='messagerecipient',
            index=models.Index(fields=['message', 'is_read'], name='communicati_message_a802f1_idx'),
        ),
        migrations.AddIndex(
            model_name='messagerecipient',
            index=models.Index(fields=['created_at'], name='communicati_created_ea846f_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='messagerecipient',
            unique_together={('message', 'recipient')},
        ),
    ]
