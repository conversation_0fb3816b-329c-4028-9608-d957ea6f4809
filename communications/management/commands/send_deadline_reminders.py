# -*- coding: utf-8 -*-
"""
发送截止提醒的管理命令
用于定时任务调用
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from communications.signals import send_deadline_reminders


class Command(BaseCommand):
    help = '检查并发送考评截止提醒通知'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='只检查不发送',
        )
    
    def handle(self, *args, **options):
        """执行命令"""
        dry_run = options['dry_run']
        
        self.stdout.write(f"开始检查截止提醒任务... 时间: {timezone.now()}")
        
        if dry_run:
            self.stdout.write("运行模式: 仅检查，不发送通知")
            # 这里可以添加检查逻辑但不发送
            from evaluations.models import EvaluationBatch
            from datetime import timedelta
            
            now = timezone.now()
            upcoming_batches = EvaluationBatch.objects.filter(
                status='active',
                end_date__gte=now,
                end_date__lte=now + timedelta(days=3)
            )
            
            self.stdout.write(f"发现 {upcoming_batches.count()} 个即将截止的批次:")
            for batch in upcoming_batches:
                days_left = (batch.end_date.date() - now.date()).days
                self.stdout.write(f"  - {batch.name}: 还有{days_left}天截止")
                
        else:
            # 实际发送提醒
            sent_count = send_deadline_reminders()
            
            if sent_count > 0:
                self.stdout.write(
                    self.style.SUCCESS(f'成功发送 {sent_count} 个截止提醒通知')
                )
            else:
                self.stdout.write(
                    self.style.WARNING('没有需要发送的提醒通知')
                )
        
        self.stdout.write("截止提醒任务完成")