# -*- coding: utf-8 -*-
"""
检查和发送考评超时异常通知的管理命令
用于定时任务调用
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from communications.services import notification_service


class Command(BaseCommand):
    help = '检查并发送考评超时异常通知'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='只检查不发送通知',
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='显示详细信息',
        )
    
    def handle(self, *args, **options):
        """执行命令"""
        dry_run = options['dry_run']
        verbose = options['verbose']
        
        self.stdout.write(f"开始检查考评超时情况... 时间: {timezone.now()}")
        
        if dry_run:
            self.stdout.write("运行模式: 仅检查，不发送通知")
            self._check_timeout_batches(verbose)
        else:
            # 实际发送超时警报
            sent_count = notification_service.check_and_send_timeout_alerts()
            
            if sent_count > 0:
                self.stdout.write(
                    self.style.SUCCESS(f'成功发送 {sent_count} 个超时警报通知')
                )
            else:
                self.stdout.write(
                    self.style.WARNING('没有需要发送的超时警报')
                )
        
        self.stdout.write("超时检查任务完成")
    
    def _check_timeout_batches(self, verbose=False):
        """检查超时批次（仅检查不发送）"""
        from evaluations.models import EvaluationBatch, EvaluationRelation
        from datetime import timedelta
        
        now = timezone.now()
        
        # 查找已经截止但仍有未完成任务的批次
        expired_batches = EvaluationBatch.objects.filter(
            status='active',
            end_date__lt=now
        )
        
        total_timeout_count = 0
        batch_count = 0
        
        for batch in expired_batches:
            # 查找该批次中的超时关系
            timeout_relations = EvaluationRelation.objects.filter(
                batch=batch,
                status__in=['pending', 'draft'],
                deleted_at__isnull=True
            ).select_related('evaluator', 'evaluatee', 'evaluator__department')
            
            timeout_count = timeout_relations.count()
            if timeout_count > 0:
                batch_count += 1
                total_timeout_count += timeout_count
                overdue_days = (now.date() - batch.end_date.date()).days
                
                self.stdout.write(f"📋 批次: {batch.name}")
                self.stdout.write(f"   截止时间: {batch.end_date.strftime('%Y-%m-%d %H:%M')}")
                self.stdout.write(f"   超时天数: {overdue_days}天")
                self.stdout.write(f"   超时任务: {timeout_count}个")
                
                if verbose:
                    # 显示超时用户详情
                    departments = set()
                    for relation in timeout_relations[:5]:  # 只显示前5个
                        dept_name = relation.evaluator.department.name if relation.evaluator.department else '未知部门'
                        departments.add(dept_name)
                        self.stdout.write(
                            f"     - {relation.evaluator.name}({dept_name}) 评价 {relation.evaluatee.name}"
                        )
                    
                    if timeout_count > 5:
                        self.stdout.write(f"     ... 还有{timeout_count - 5}个超时任务")
                    
                    self.stdout.write(f"   涉及部门: {', '.join(departments)}")
                
                # 检查是否需要发送警报
                should_send = (
                    overdue_days == 1 or
                    overdue_days == 3 or
                    overdue_days == 7 or
                    (overdue_days > 7 and overdue_days % 7 == 0)
                )
                
                if should_send:
                    self.stdout.write(f"   ⚠️  需要发送警报（超时{overdue_days}天）")
                else:
                    self.stdout.write(f"   ℹ️  暂不发送警报")
                
                self.stdout.write("")
        
        if batch_count == 0:
            self.stdout.write("✅ 没有发现超时的考评批次")
        else:
            self.stdout.write(f"📊 总计: {batch_count}个批次有超时任务，共{total_timeout_count}个超时任务")