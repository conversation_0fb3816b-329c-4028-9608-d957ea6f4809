# -*- coding: utf-8 -*-
"""
创建默认通知模板的管理命令
"""

from django.core.management.base import BaseCommand
from communications.models import NotificationTemplate


class Command(BaseCommand):
    help = '创建默认的通知模板'
    
    def handle(self, *args, **options):
        """执行命令"""
        templates = [
            {
                'template_type': 'EVALUATION_START',
                'name': '考评开始通知模板',
                'subject_template': '考评批次【{batch_name}】已开始',
                'content_template': '''
尊敬的{staff_name}，

考评批次【{batch_name}】已正式开始，请您及时参与评价。

📅 考评时间：
• 开始时间：{start_date}
• 结束时间：{end_date}

📋 考评说明：
{description}

⚠️ 重要提醒：
• 请在截止时间前完成所有分配给您的考评任务
• 确保评价客观、公正，基于实际工作表现
• 如有疑问，请及时联系管理员

感谢您的配合！

系统自动发送
                '''.strip(),
                'variables': {
                    'batch_name': '考评批次名称',
                    'staff_name': '员工姓名',
                    'start_date': '开始日期',
                    'end_date': '结束日期',
                    'description': '批次描述'
                }
            },
            {
                'template_type': 'EVALUATION_REMIND',
                'name': '考评提醒通知模板',
                'subject_template': '考评提醒：批次【{batch_name}】还有{days_left}天截止',
                'content_template': '''
尊敬的{staff_name}，

考评批次【{batch_name}】即将截止，请抓紧时间完成评价！

⏰ 截止信息：
• 截止时间：{end_date}
• 剩余时间：还有{days_left}天

📝 待完成任务：
• 您还有 {remaining_tasks} 个考评任务未完成
• 请登录系统查看详细任务列表

🚨 紧急提醒：
为确保考评工作顺利进行，请务必在截止时间前完成所有评价任务。
逾期未完成的任务将影响整体考评进度。

立即前往考评系统 →

系统自动发送
                '''.strip(),
                'variables': {
                    'batch_name': '考评批次名称',
                    'staff_name': '员工姓名',
                    'days_left': '剩余天数',
                    'end_date': '截止时间',
                    'remaining_tasks': '剩余任务数'
                }
            },
            {
                'template_type': 'EVALUATION_COMPLETE',
                'name': '考评完成通知模板',
                'subject_template': '考评完成通知：您的考评记录已提交',
                'content_template': '''
尊敬的{evaluatee_name}，

您在考评批次【{batch_name}】中的一项评价已完成。

📊 评价信息：
• 评价者：{evaluator_name}
• 完成时间：{completion_time}
• 评价得分：{total_score}分

✅ 评价状态：已完成并记录

📋 温馨提示：
• 此评价已纳入您的综合考评结果
• 完整的考评报告将在批次结束后统一发布
• 如对评价结果有疑问，请联系人事部门

感谢您的参与和配合！

系统自动发送
                '''.strip(),
                'variables': {
                    'batch_name': '考评批次名称',
                    'evaluator_name': '评价者姓名',
                    'evaluatee_name': '被评价者姓名',
                    'completion_time': '完成时间',
                    'total_score': '总得分'
                }
            },
            {
                'template_type': 'SYSTEM_MAINTENANCE',
                'name': '系统维护通知模板',
                'subject_template': '系统维护通知：{maintenance_title}',
                'content_template': '''
各位用户，

系统将进行维护，具体信息如下：

🔧 维护内容：
{maintenance_content}

⏰ 维护时间：
• 开始时间：{start_time}
• 预计结束：{end_time}

⚠️ 影响范围：
在维护期间，系统可能无法正常访问，请您提前做好相关准备。

📞 联系方式：
如有紧急情况，请联系系统管理员。

感谢您的理解与支持！

系统管理员
                '''.strip(),
                'variables': {
                    'maintenance_title': '维护标题',
                    'maintenance_content': '维护内容',
                    'start_time': '开始时间',
                    'end_time': '结束时间'
                }
            },
            {
                'template_type': 'PASSWORD_RESET',
                'name': '密码重置通知模板',
                'subject_template': '密码重置通知',
                'content_template': '''
尊敬的{staff_name}，

您的账户密码已成功重置。

🔐 账户信息：
• 用户名：{username}
• 重置时间：{reset_time}
• 操作来源：{source}

🛡️ 安全提醒：
• 请及时登录系统修改为您的专属密码
• 建议使用包含数字、字母和特殊字符的复杂密码
• 不要与他人分享您的登录信息

如非本人操作，请立即联系系统管理员。

系统自动发送
                '''.strip(),
                'variables': {
                    'staff_name': '员工姓名',
                    'username': '用户名',
                    'reset_time': '重置时间',
                    'source': '操作来源'
                }
            },
            {
                'template_type': 'ROLE_CHANGE',
                'name': '角色变更通知模板',
                'subject_template': '角色权限变更通知',
                'content_template': '''
尊敬的{staff_name}，

您的系统角色权限已更新。

👤 权限变更：
• 原角色：{old_role}
• 新角色：{new_role}
• 变更时间：{change_time}
• 操作人员：{operator}

📋 变更说明：
{change_reason}

🔄 生效时间：
新权限将在您下次登录时生效，请重新登录系统以获取最新权限。

如有疑问，请联系系统管理员。

系统自动发送
                '''.strip(),
                'variables': {
                    'staff_name': '员工姓名',
                    'old_role': '原角色',
                    'new_role': '新角色',
                    'change_time': '变更时间',
                    'operator': '操作人员',
                    'change_reason': '变更原因'
                }
            },
            {
                'template_type': 'ACCOUNT_STATUS',
                'name': '账户状态通知模板',
                'subject_template': '账户状态变更通知',
                'content_template': '''
尊敬的{staff_name}，

您的账户状态已更新。

👤 状态变更：
• 当前状态：{new_status}
• 变更时间：{change_time}
• 操作人员：{operator}

📋 变更说明：
{change_reason}

ℹ️ 影响说明：
{impact_description}

如有疑问，请联系系统管理员。

系统自动发送
                '''.strip(),
                'variables': {
                    'staff_name': '员工姓名',
                    'new_status': '新状态',
                    'change_time': '变更时间',
                    'operator': '操作人员',
                    'change_reason': '变更原因',
                    'impact_description': '影响说明'
                }
            },
            {
                'template_type': 'EVALUATION_TIMEOUT_ALERT',
                'name': '考评超时异常通知模板',
                'subject_template': '🚨 考评超时警报：批次【{batch_name}】有{timeout_count}个任务超时',
                'content_template': '''
<div style="max-width: 800px; margin: 0 auto; font-family: 'Segoe UI', Arial, sans-serif;">
    <div style="background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0;">
        <h2 style="margin: 0; font-size: 24px;">⚠️ 考评超时异常警报</h2>
        <p style="margin: 8px 0 0 0; opacity: 0.9;">系统检测到考评任务出现超时情况，请及时处理</p>
    </div>
    
    <div style="background: #ffffff; padding: 24px; border: 1px solid #e5e7eb; border-top: none; border-radius: 0 0 8px 8px;">
        <div style="background: #fef2f2; border: 1px solid #fecaca; border-radius: 6px; padding: 16px; margin-bottom: 20px;">
            <h3 style="margin: 0 0 12px 0; color: #991b1b; font-size: 18px;">📊 超时统计</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 12px;">
                <div style="background: white; padding: 12px; border-radius: 4px; border-left: 4px solid #dc2626;">
                    <div style="font-size: 14px; color: #6b7280;">批次名称</div>
                    <div style="font-size: 16px; font-weight: 600; color: #1f2937;">{batch_name}</div>
                </div>
                <div style="background: white; padding: 12px; border-radius: 4px; border-left: 4px solid #dc2626;">
                    <div style="font-size: 14px; color: #6b7280;">超时任务数</div>
                    <div style="font-size: 16px; font-weight: 600; color: #dc2626;">{timeout_count} 个</div>
                </div>
                <div style="background: white; padding: 12px; border-radius: 4px; border-left: 4px solid #dc2626;">
                    <div style="font-size: 14px; color: #6b7280;">超时天数</div>
                    <div style="font-size: 16px; font-weight: 600; color: #dc2626;">{overdue_days} 天</div>
                </div>
                <div style="background: white; padding: 12px; border-radius: 4px; border-left: 4px solid #dc2626;">
                    <div style="font-size: 14px; color: #6b7280;">涉及部门</div>
                    <div style="font-size: 16px; font-weight: 600; color: #1f2937;">{departments}</div>
                </div>
            </div>
        </div>
        
        <div style="margin-bottom: 20px;">
            <h3 style="margin: 0 0 12px 0; color: #1f2937; font-size: 18px;">📋 超时任务详情</h3>
            <p style="margin: 0 0 12px 0; color: #6b7280; font-size: 14px;">截止时间：{batch_end_date}</p>
            <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
                <thead>
                    <tr style="background: #f9fafb;">
                        <th style="padding: 12px 8px; text-align: left; border-bottom: 2px solid #e5e7eb; font-weight: 600; color: #374151;">评价者</th>
                        <th style="padding: 12px 8px; text-align: left; border-bottom: 2px solid #e5e7eb; font-weight: 600; color: #374151;">部门</th>
                        <th style="padding: 12px 8px; text-align: left; border-bottom: 2px solid #e5e7eb; font-weight: 600; color: #374151;">被评价者</th>
                        <th style="padding: 12px 8px; text-align: left; border-bottom: 2px solid #e5e7eb; font-weight: 600; color: #374151;">超时天数</th>
                    </tr>
                </thead>
                <tbody>
                    {timeout_users_table}
                </tbody>
            </table>
        </div>
        
        <div style="background: #fffbeb; border: 1px solid #fed7aa; border-radius: 6px; padding: 16px; margin-bottom: 20px;">
            <h3 style="margin: 0 0 8px 0; color: #92400e; font-size: 16px;">🎯 建议处理措施</h3>
            <ul style="margin: 0; padding-left: 20px; color: #92400e;">
                <li>联系相关部门负责人，了解超时原因</li>
                <li>向超时员工发送催促通知</li>
                <li>必要时可延长考评截止时间</li>
                <li>检查系统是否存在技术问题</li>
            </ul>
        </div>
        
        <div style="text-align: center; padding: 16px 0; border-top: 1px solid #e5e7eb;">
            <p style="margin: 0; color: #6b7280; font-size: 12px;">
                此警报由系统自动发送 | 发送时间：{current_time}
            </p>
        </div>
    </div>
</div>
                '''.strip(),
                'variables': {
                    'batch_name': '考评批次名称',
                    'timeout_count': '超时任务数量',
                    'overdue_days': '超时天数',
                    'departments': '涉及部门列表',
                    'batch_end_date': '批次截止日期',
                    'timeout_users_table': '超时用户表格HTML',
                    'current_time': '当前发送时间'
                }
            }
        ]
        
        created_count = 0
        updated_count = 0
        
        for template_data in templates:
            template, created = NotificationTemplate.objects.get_or_create(
                template_type=template_data['template_type'],
                defaults=template_data
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'创建模板: {template.name}')
                )
            else:
                # 更新现有模板
                for field, value in template_data.items():
                    if field != 'template_type':
                        setattr(template, field, value)
                template.save()
                updated_count += 1
                self.stdout.write(
                    self.style.WARNING(f'更新模板: {template.name}')
                )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'模板处理完成: 新建{created_count}个，更新{updated_count}个'
            )
        )