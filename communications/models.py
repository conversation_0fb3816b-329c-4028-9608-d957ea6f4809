# -*- coding: utf-8 -*-
"""
站内通信模型
包含消息通知、公告发布等内部沟通功能的数据模型
"""

from django.db import models
from django.utils import timezone
from common.models import BaseModel


class Message(BaseModel):
    """
    消息模型
    用于存储系统内部的各种消息通知
    """
    MESSAGE_TYPES = [
        ('SYSTEM', '系统通知'),
        ('PERSONAL', '个人消息'),
        ('EVALUATION', '考评相关'),
        ('ANNOUNCEMENT', '公告通知'),
    ]
    
    PRIORITY_CHOICES = [
        ('HIGH', '高优先级'),
        ('MEDIUM', '中优先级'),
        ('LOW', '低优先级'),
    ]
    
    message_type = models.CharField(
        max_length=20,
        choices=MESSAGE_TYPES,
        default='SYSTEM',
        verbose_name='消息类型',
        help_text='消息的分类类型'
    )
    
    sender = models.ForeignKey(
        'organizations.Staff',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='sent_messages',
        verbose_name='发送者',
        help_text='消息发送者，为空表示系统消息'
    )
    
    subject = models.CharField(
        max_length=200,
        verbose_name='消息主题',
        help_text='消息的标题或主题'
    )
    
    content = models.TextField(
        verbose_name='消息内容',
        help_text='消息的详细内容'
    )
    
    priority = models.CharField(
        max_length=10,
        choices=PRIORITY_CHOICES,
        default='MEDIUM',
        verbose_name='优先级',
        help_text='消息的重要程度'
    )
    
    is_broadcast = models.BooleanField(
        default=False,
        verbose_name='是否广播',
        help_text='是否向所有用户广播此消息'
    )
    
    # 相关业务对象引用（可选）
    related_model = models.CharField(
        max_length=50,
        blank=True,
        verbose_name='关联模型',
        help_text='相关联的业务模型名称'
    )
    related_id = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name='关联对象ID',
        help_text='相关联的业务对象ID'
    )
    
    # 消息有效期
    expires_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='过期时间',
        help_text='消息的过期时间，过期后不再显示'
    )
    
    class Meta:
        verbose_name = '消息'
        verbose_name_plural = '消息'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['message_type', 'created_at']),
            models.Index(fields=['sender', 'created_at']),
            models.Index(fields=['priority', 'created_at']),
            models.Index(fields=['expires_at']),
        ]
    
    def __str__(self):
        sender_name = self.sender.name if self.sender else '系统'
        return f'{sender_name}: {self.subject}'
    
    @property
    def is_expired(self):
        """检查消息是否过期"""
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False
    
    def get_unread_count(self):
        """获取未读人数"""
        return self.recipients.filter(is_read=False, is_deleted=False).count()
    
    def get_total_recipients(self):
        """获取总接收人数"""
        return self.recipients.filter(is_deleted=False).count()


class MessageRecipient(BaseModel):
    """
    消息接收者模型
    记录消息的接收者和读取状态
    """
    RECIPIENT_TYPES = [
        ('STAFF', '个人'),
        ('DEPARTMENT', '部门'),
        ('ALL', '全体'),
    ]
    
    message = models.ForeignKey(
        Message,
        on_delete=models.CASCADE,
        related_name='recipients',
        verbose_name='消息',
        help_text='关联的消息'
    )
    
    recipient = models.ForeignKey(
        'organizations.Staff',
        on_delete=models.CASCADE,
        related_name='received_messages',
        verbose_name='接收者',
        help_text='消息接收者'
    )
    
    recipient_type = models.CharField(
        max_length=20,
        choices=RECIPIENT_TYPES,
        default='STAFF',
        verbose_name='接收者类型',
        help_text='接收者的类型分类'
    )
    
    is_read = models.BooleanField(
        default=False,
        verbose_name='是否已读',
        help_text='接收者是否已读此消息'
    )
    
    read_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='阅读时间',
        help_text='消息被阅读的时间'
    )
    
    is_starred = models.BooleanField(
        default=False,
        verbose_name='是否收藏',
        help_text='接收者是否收藏此消息'
    )
    
    is_deleted = models.BooleanField(
        default=False,
        verbose_name='是否删除',
        help_text='接收者是否删除此消息'
    )
    
    class Meta:
        verbose_name = '消息接收者'
        verbose_name_plural = '消息接收者'
        unique_together = ['message', 'recipient']
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['recipient', 'is_read']),
            models.Index(fields=['recipient', 'is_deleted']),
            models.Index(fields=['message', 'is_read']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f'{self.recipient.name} - {self.message.subject}'
    
    def mark_as_read(self):
        """标记消息为已读"""
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.save(update_fields=['is_read', 'read_at'])
    
    def mark_as_unread(self):
        """标记消息为未读"""
        if self.is_read:
            self.is_read = False
            self.read_at = None
            self.save(update_fields=['is_read', 'read_at'])


class Announcement(BaseModel):
    """
    公告模型
    用于发布系统公告、部门通知等信息
    """
    ANNOUNCEMENT_TYPES = [
        ('SYSTEM', '系统公告'),
        ('DEPARTMENT', '部门公告'),
        ('EVALUATION', '考评公告'),
        ('MAINTENANCE', '维护通知'),
        ('POLICY', '政策更新'),
    ]
    
    title = models.CharField(
        max_length=200,
        verbose_name='公告标题',
        help_text='公告的标题'
    )
    
    content = models.TextField(
        verbose_name='公告内容',
        help_text='公告的详细内容'
    )
    
    announcement_type = models.CharField(
        max_length=20,
        choices=ANNOUNCEMENT_TYPES,
        default='SYSTEM',
        verbose_name='公告类型',
        help_text='公告的分类类型'
    )
    
    author = models.ForeignKey(
        'organizations.Staff',
        on_delete=models.SET_NULL,
        null=True,
        related_name='authored_announcements',
        verbose_name='发布者',
        help_text='公告的发布者'
    )
    
    target_department = models.ForeignKey(
        'organizations.Department',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='announcements',
        verbose_name='目标部门',
        help_text='公告的目标部门，为空表示全公司'
    )
    
    is_pinned = models.BooleanField(
        default=False,
        verbose_name='是否置顶',
        help_text='是否在公告列表中置顶显示'
    )
    
    is_published = models.BooleanField(
        default=True,
        verbose_name='是否发布',
        help_text='是否对目标用户可见'
    )
    
    publish_at = models.DateTimeField(
        default=timezone.now,
        verbose_name='发布时间',
        help_text='公告的发布时间'
    )
    
    expire_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='过期时间',
        help_text='公告的过期时间，过期后不再显示'
    )
    
    view_count = models.PositiveIntegerField(
        default=0,
        verbose_name='查看次数',
        help_text='公告被查看的次数'
    )
    
    # 富文本内容支持
    is_html = models.BooleanField(
        default=False,
        verbose_name='是否HTML格式',
        help_text='内容是否为HTML格式'
    )
    
    class Meta:
        verbose_name = '公告'
        verbose_name_plural = '公告'
        ordering = ['-is_pinned', '-created_at']
        indexes = [
            models.Index(fields=['announcement_type', 'is_published']),
            models.Index(fields=['target_department', 'is_published']),
            models.Index(fields=['is_pinned', 'created_at']),
            models.Index(fields=['publish_at', 'expire_at']),
        ]
    
    def __str__(self):
        return self.title
    
    @property
    def is_expired(self):
        """检查公告是否过期"""
        if self.expire_at:
            return timezone.now() > self.expire_at
        return False
    
    @property
    def is_active(self):
        """检查公告是否有效（已发布且未过期）"""
        return self.is_published and not self.is_expired
    
    def increment_view_count(self):
        """增加查看次数"""
        self.view_count += 1
        self.save(update_fields=['view_count'])


class NotificationTemplate(BaseModel):
    """
    通知模板模型
    用于标准化各种系统通知的内容格式
    """
    TEMPLATE_TYPES = [
        ('EVALUATION_START', '考评开始通知'),
        ('EVALUATION_REMIND', '考评提醒通知'),
        ('EVALUATION_COMPLETE', '考评完成通知'),
        ('PASSWORD_RESET', '密码重置通知'),
        ('ROLE_CHANGE', '角色变更通知'),
        ('ACCOUNT_STATUS', '账户状态通知'),
        ('SYSTEM_MAINTENANCE', '系统维护通知'),
    ]
    
    template_type = models.CharField(
        max_length=30,
        choices=TEMPLATE_TYPES,
        unique=True,
        verbose_name='模板类型',
        help_text='通知模板的类型'
    )
    
    name = models.CharField(
        max_length=100,
        verbose_name='模板名称',
        help_text='模板的显示名称'
    )
    
    subject_template = models.CharField(
        max_length=200,
        verbose_name='主题模板',
        help_text='消息主题的模板，支持变量替换'
    )
    
    content_template = models.TextField(
        verbose_name='内容模板',
        help_text='消息内容的模板，支持变量替换'
    )
    
    variables = models.JSONField(
        default=dict,
        verbose_name='模板变量',
        help_text='模板中使用的变量定义（JSON格式）'
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name='是否启用',
        help_text='模板是否启用'
    )
    
    class Meta:
        verbose_name = '通知模板'
        verbose_name_plural = '通知模板'
        ordering = ['template_type']
    
    def __str__(self):
        return self.name
    
    def render(self, context=None):
        """
        渲染模板
        将模板变量替换为实际值
        """
        if context is None:
            context = {}
        
        subject = self.subject_template
        content = self.content_template
        
        # 简单的变量替换（可以后续升级为更复杂的模板引擎）
        for key, value in context.items():
            placeholder = f'{{{{{key}}}}}'
            subject = subject.replace(placeholder, str(value))
            content = content.replace(placeholder, str(value))
        
        return {
            'subject': subject,
            'content': content
        }


class MessageReadLog(BaseModel):
    """
    消息阅读日志模型
    记录消息的详细阅读历史，用于统计分析
    """
    message = models.ForeignKey(
        Message,
        on_delete=models.CASCADE,
        related_name='read_logs',
        verbose_name='消息',
        help_text='被阅读的消息'
    )
    
    reader = models.ForeignKey(
        'organizations.Staff',
        on_delete=models.CASCADE,
        related_name='message_read_logs',
        verbose_name='阅读者',
        help_text='阅读消息的用户'
    )
    
    read_at = models.DateTimeField(
        default=timezone.now,
        verbose_name='阅读时间',
        help_text='消息被阅读的具体时间'
    )
    
    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        verbose_name='IP地址',
        help_text='阅读时的IP地址'
    )
    
    user_agent = models.TextField(
        blank=True,
        verbose_name='用户代理',
        help_text='阅读时的浏览器信息'
    )
    
    class Meta:
        verbose_name = '消息阅读日志'
        verbose_name_plural = '消息阅读日志'
        ordering = ['-read_at']
        indexes = [
            models.Index(fields=['message', 'read_at']),
            models.Index(fields=['reader', 'read_at']),
        ]
    
    def __str__(self):
        return f'{self.reader.name} 阅读了 {self.message.subject} ({self.read_at})'