#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
创建管理员用户脚本
用于创建可以登录管理后台的超级管理员账户
"""

import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
django.setup()

from organizations.models import Staff, Department
from common.security.permissions import Role

def create_admin_user():
    """创建管理员用户"""
    print("🚀 开始创建管理员用户...")
    
    try:
        # 检查是否已有管理员
        existing_admin = Staff.objects.filter(
            role__in=['super_admin', 'system_admin'], 
            deleted_at__isnull=True,
            is_active=True
        ).first()
        
        if existing_admin:
            print(f"⚠️ 已存在管理员用户: {existing_admin.username} ({existing_admin.name})")
            print(f"   角色: {existing_admin.get_role_display()}")
            
            choice = input("是否要重置密码？(y/N): ").lower().strip()
            if choice == 'y':
                password = input("请输入新密码（默认admin123）: ").strip()
                if not password:
                    password = 'admin123'
                
                existing_admin.set_password(password)
                existing_admin.save()
                print(f"✅ 已重置 {existing_admin.username} 的密码")
                return existing_admin.username, password
            else:
                return None, None
        
        # 确保有管理部门
        admin_dept, created = Department.objects.get_or_create(
            dept_code='ADMIN',
            defaults={
                'name': '管理部门',
                'description': '系统管理部门',
                'is_active': True,
                'sort_order': 1
            }
        )
        
        if created:
            print(f"✅ 已创建管理部门: {admin_dept.name}")
        else:
            print(f"📁 使用现有管理部门: {admin_dept.name}")
        
        # 获取用户输入
        username = input("请输入管理员用户名（默认admin）: ").strip()
        if not username:
            username = 'admin'
            
        name = input("请输入管理员姓名（默认系统管理员）: ").strip()
        if not name:
            name = '系统管理员'
            
        password = input("请输入密码（默认admin123）: ").strip()
        if not password:
            password = 'admin123'
        
        # 检查用户名是否已存在
        if Staff.objects.filter(username=username, deleted_at__isnull=True).exists():
            print(f"❌ 用户名 {username} 已存在")
            return None, None
        
        # 创建管理员用户
        admin_user = Staff.objects.create(
            username=username,
            name=name,
            employee_no=f'ADMIN{Staff.objects.filter(deleted_at__isnull=True).count() + 1:03d}',
            department=admin_dept,
            role=Role.SUPER_ADMIN,
            is_active=True,
            email=f'{username}@company.com',
            phone='',
            position=None,
        )
        
        # 设置密码
        admin_user.set_password(password)
        admin_user.save()
        
        print(f"✅ 成功创建超级管理员:")
        print(f"   用户名: {admin_user.username}")
        print(f"   姓名: {admin_user.name}")
        print(f"   员工编号: {admin_user.employee_no}")
        print(f"   部门: {admin_user.department.name}")
        print(f"   角色: {admin_user.get_role_display()}")
        print(f"   密码: {password}")
        
        return username, password
        
    except Exception as e:
        print(f"❌ 创建管理员用户失败: {str(e)}")
        return None, None

def test_login(username, password):
    """测试登录功能"""
    if not username or not password:
        return
        
    print(f"\n🔐 测试登录功能...")
    
    try:
        # 查找用户
        staff = Staff.objects.get(username=username, deleted_at__isnull=True, is_active=True)
        
        # 验证密码
        if staff.check_password(password):
            print(f"✅ 密码验证成功")
            
            # 检查管理权限
            if staff.is_manager:
                print(f"✅ 管理权限验证成功")
                print(f"   可访问管理后台")
            else:
                print(f"❌ 无管理权限")
        else:
            print(f"❌ 密码验证失败")
            
    except Staff.DoesNotExist:
        print(f"❌ 用户不存在")
    except Exception as e:
        print(f"❌ 登录测试失败: {str(e)}")

def main():
    print("=" * 50)
    print("📋 管理员用户创建工具")
    print("=" * 50)
    
    username, password = create_admin_user()
    
    if username and password:
        test_login(username, password)
        
        print(f"\n🌐 登录信息:")
        print(f"   URL: http://localhost:8000/admin/login/")
        print(f"   用户名: {username}")
        print(f"   密码: {password}")
        print(f"")
        print(f"💡 提示: 登录成功后请及时修改密码")
    
    print("\n" + "=" * 50)

if __name__ == '__main__':
    main()