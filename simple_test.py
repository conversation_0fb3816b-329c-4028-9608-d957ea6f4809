#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化版JWT认证修复验证脚本
"""

import os
import sys
import django

# 配置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
django.setup()

def main():
    print("🔧 JWT认证修复验证")
    print("=" * 40)
    
    try:
        # 1. 测试JWT基本功能
        from common.security.jwt_auth import JWTAuthentication
        from organizations.models import Staff
        
        admin_user = Staff.objects.filter(role='super_admin', is_active=True, deleted_at__isnull=True).first()
        if not admin_user:
            print("❌ 未找到管理员用户")
            return False
        
        # 生成token
        tokens = JWTAuthentication.generate_tokens(admin_user)
        print("✅ JWT token生成成功")
        
        # 验证token
        payload = JWTAuthentication.verify_token(tokens['access_token'], 'access')
        if payload:
            print("✅ JWT token验证成功")
        else:
            print("❌ JWT token验证失败")
            return False
        
        # 2. 测试权限装饰器兼容性
        from common.security.permissions import require_permission, Permission, has_permission
        
        # 直接测试权限检查函数
        if has_permission(admin_user, Permission.SYS_MANAGE_PERMISSIONS):
            print("✅ 权限检查功能正常")
        else:
            print("❌ 权限检查功能异常")
            return False
            
        # 3. 测试模拟请求处理
        from django.http import HttpRequest
        
        request = HttpRequest()
        request.current_staff = admin_user
        request.is_authenticated = True
        
        # 测试权限装饰器能否识别current_staff
        from common.security.permissions import permission_manager
        if permission_manager.has_permission(admin_user, Permission.ORG_VIEW_DEPARTMENT):
            print("✅ 部门访问权限正常")
        else:
            print("❌ 部门访问权限异常")
            return False
        
        print("\n🎉 核心功能验证通过！")
        print("💡 建议：")
        print("   1. 重启开发服务器")
        print("   2. 重新登录系统")
        print("   3. 尝试访问部门管理和权限管理页面")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)