#!/usr/bin/env python
"""
JWT认证修复验证脚本 - 简化版
测试修复后的JWT认证流程是否正常工作
"""

import os
import sys
import django
import requests
import json
from datetime import datetime

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
django.setup()

def test_api_login():
    """测试API登录"""
    print("测试API登录端点...")
    
    try:
        login_data = {
            'username': 'admin',
            'password': '123456'
        }
        
        response = requests.post(
            'http://127.0.0.1:8000/api/login/',
            json=login_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("SUCCESS: API登录成功")
                print(f"用户: {data['user']['name']} ({data['user']['username']})")
                return data['tokens']['access_token'], data['tokens']['refresh_token']
            else:
                print(f"ERROR: {data.get('message', '登录失败')}")
                return None, None
        else:
            print(f"ERROR: HTTP {response.status_code}")
            print(f"响应: {response.text}")
            return None, None
            
    except requests.exceptions.ConnectionError:
        print("ERROR: 无法连接到服务器")
        return None, None
    except Exception as e:
        print(f"ERROR: {e}")
        return None, None

def test_token_validation(access_token):
    """测试token验证"""
    print("\n测试token验证...")
    
    try:
        headers = {'Authorization': f'Bearer {access_token}'}
        response = requests.get(
            'http://127.0.0.1:8000/api/token/status/',
            headers=headers,
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("SUCCESS: Token验证通过")
            return True
        else:
            print("ERROR: Token验证失败")
            return False
            
    except Exception as e:
        print(f"ERROR: {e}")
        return False

def test_login_page():
    """测试登录页面"""
    print("\n测试登录页面...")
    
    try:
        response = requests.get('http://127.0.0.1:8000/admin/login/', timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text
            has_ajax = 'fetch(\'/api/login/\'' in content
            has_storage = 'localStorage.setItem' in content
            
            print(f"包含AJAX登录: {has_ajax}")
            print(f"包含token存储: {has_storage}")
            
            if has_ajax and has_storage:
                print("SUCCESS: 登录页面已正确修改")
                return True
            else:
                print("WARNING: 登录页面可能需要进一步检查")
                return False
        else:
            print("ERROR: 无法加载登录页面")
            return False
            
    except Exception as e:
        print(f"ERROR: {e}")
        return False

def check_database():
    """检查数据库"""
    print("检查数据库连接...")
    
    try:
        from organizations.models import Staff
        
        admin_user = Staff.objects.filter(username='admin').first()
        if admin_user:
            print(f"SUCCESS: 找到管理员用户 {admin_user.name}")
            print(f"角色: {admin_user.get_role_display()}")
            print(f"激活状态: {admin_user.is_active}")
            return True
        else:
            print("ERROR: 未找到管理员用户")
            return False
            
    except Exception as e:
        print(f"ERROR: 数据库检查失败 - {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("JWT认证修复验证测试")
    print("=" * 50)
    
    # 1. 检查数据库
    if not check_database():
        print("数据库检查失败，停止测试")
        return
    
    # 2. 测试API登录
    access_token, refresh_token = test_api_login()
    if not access_token:
        print("API登录失败，停止测试")
        return
    
    # 3. 测试token验证
    if not test_token_validation(access_token):
        print("Token验证失败")
    
    # 4. 测试登录页面
    test_login_page()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)

if __name__ == '__main__':
    main()