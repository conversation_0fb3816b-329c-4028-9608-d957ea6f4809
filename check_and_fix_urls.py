#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
系统性检查和修复URL命名空间错误的脚本
"""

import os
import re
import sys

# 处理Windows编码问题
if sys.platform.startswith('win'):
    import io
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

def check_template_urls():
    """检查模板文件中的URL错误"""
    print("=== 检查模板文件URL ===")
    
    template_files = [
        'templates/admin/permissions/manage.html',
        'templates/admin/anonymous/manage.html',
        'templates/admin/base_admin.html',
        'templates/admin/dashboard.html',
        'templates/admin/staff/create.html',
        'templates/admin/staff/update.html',
        'templates/admin/staff/detail.html',
        'templates/admin/staff/import.html',
        'templates/admin/department/create.html',
        'templates/admin/department/detail.html',
        'templates/admin/department/list.html',
        'templates/admin/department/update.html',
    ]
    
    # 定义错误和正确的URL模式映射
    url_fixes = {
        # 组织管理URL
        "'organizations:staff_list'": "'organizations:admin:staff_list'",
        "'organizations:department_list'": "'organizations:admin:department_list'",
        "'organizations:position_list'": "'organizations:admin:position_list'",
        "'organizations:permissions_manage'": "'organizations:admin:permissions_manage'",
        "'organizations:anonymous_codes_manage'": "'organizations:admin:anonymous_codes_manage'",
        "'organizations:dashboard'": "'organizations:admin:dashboard'",
        "'organizations:login'": "'organizations:admin:login'",
        "'organizations:logout'": "'organizations:admin:logout'",
        "'organizations:profile_management'": "'organizations:admin:profile_management'",
        "'organizations:change_password'": "'organizations:admin:change_password'",
        
        # 其他模块类似模式
        "'evaluations:template_list'": "'evaluations:admin:template_list'",
        "'evaluations:batch_list'": "'evaluations:admin:batch_list'",
        "'evaluations:rules_list'": "'evaluations:admin:rules_list'",
        "'evaluations:progress_list'": "'evaluations:admin:progress_list'",
        
        "'reports:report_list'": "'reports:admin:report_list'",
        "'reports:analytics'": "'reports:admin:analytics'",
        "'reports:talent_assessment_list'": "'reports:admin:talent_assessment_list'",
    }
    
    issues_found = []
    fixes_applied = []
    
    for template_file in template_files:
        full_path = os.path.join(os.path.dirname(__file__), template_file)
        
        if os.path.exists(full_path):
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    original_content = content
                
                # 检查每个URL模式
                file_fixes = []
                for wrong_url, correct_url in url_fixes.items():
                    if wrong_url in content:
                        content = content.replace(wrong_url, correct_url)
                        file_fixes.append(f"{wrong_url} -> {correct_url}")
                        issues_found.append(f"{template_file}: {wrong_url}")
                
                # 如果有修复，写回文件
                if file_fixes:
                    with open(full_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    print(f"✅ 修复文件: {template_file}")
                    for fix in file_fixes:
                        print(f"   - {fix}")
                    fixes_applied.extend(file_fixes)
                else:
                    print(f"✅ 检查通过: {template_file}")
                    
            except Exception as e:
                print(f"❌ 检查文件失败 {template_file}: {str(e)}")
                issues_found.append(f"检查失败: {template_file}")
        else:
            print(f"⚠️  文件不存在: {template_file}")
    
    return issues_found, fixes_applied

def check_view_urls():
    """检查视图文件中的URL错误"""
    print("\n=== 检查视图文件URL ===")
    
    view_files = [
        'organizations/views.py',
        'organizations/views_permissions.py', 
        'organizations/views_anonymous.py',
        'evaluations/views.py',
        'reports/views.py',
    ]
    
    # 定义错误的重定向模式
    redirect_fixes = {
        "redirect('organizations:staff_list')": "redirect('organizations:admin:staff_list')",
        "redirect('organizations:department_list')": "redirect('organizations:admin:department_list')",
        "redirect('organizations:permissions_manage')": "redirect('organizations:admin:permissions_manage')",
        "redirect('organizations:anonymous_codes_manage')": "redirect('organizations:admin:anonymous_codes_manage')",
        "redirect('organizations:dashboard')": "redirect('organizations:admin:dashboard')",
        "redirect('organizations:login')": "redirect('organizations:admin:login')",
        "redirect('organizations:logout')": "redirect('organizations:admin:logout')",
        
        "redirect('evaluations:template_list')": "redirect('evaluations:admin:template_list')",
        "redirect('evaluations:batch_list')": "redirect('evaluations:admin:batch_list')",
        
        "redirect('reports:report_list')": "redirect('reports:admin:report_list')",
    }
    
    issues_found = []
    fixes_applied = []
    
    for view_file in view_files:
        full_path = os.path.join(os.path.dirname(__file__), view_file)
        
        if os.path.exists(full_path):
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    original_content = content
                
                # 检查每个重定向模式
                file_fixes = []
                for wrong_redirect, correct_redirect in redirect_fixes.items():
                    if wrong_redirect in content:
                        content = content.replace(wrong_redirect, correct_redirect)
                        file_fixes.append(f"{wrong_redirect} -> {correct_redirect}")
                        issues_found.append(f"{view_file}: {wrong_redirect}")
                
                # 如果有修复，写回文件
                if file_fixes:
                    with open(full_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    print(f"✅ 修复文件: {view_file}")
                    for fix in file_fixes:
                        print(f"   - {fix}")
                    fixes_applied.extend(file_fixes)
                else:
                    print(f"✅ 检查通过: {view_file}")
                    
            except Exception as e:
                print(f"❌ 检查文件失败 {view_file}: {str(e)}")
                issues_found.append(f"检查失败: {view_file}")
        else:
            print(f"⚠️  文件不存在: {view_file}")
    
    return issues_found, fixes_applied

def validate_url_patterns():
    """验证修复后的URL是否能够正确解析"""
    print("\n=== 验证URL解析 ===")
    
    try:
        import django
        import os
        
        # 配置Django环境
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
        django.setup()
        
        from django.urls import reverse
        
        # 测试关键URL
        test_urls = [
            ('organizations:admin:dashboard', '仪表板'),
            ('organizations:admin:staff_list', '员工列表'),
            ('organizations:admin:department_list', '部门列表'),
            ('organizations:admin:permissions_manage', '权限管理'),
            ('organizations:admin:anonymous_codes_manage', '匿名编号管理'),
            ('organizations:admin:login', '登录页'),
            ('organizations:admin:logout', '退出登录'),
        ]
        
        success_count = 0
        for url_name, description in test_urls:
            try:
                resolved_url = reverse(url_name)
                print(f"✅ {description}: {url_name} -> {resolved_url}")
                success_count += 1
            except Exception as e:
                print(f"❌ {description}: {url_name} -> 错误: {str(e)}")
        
        return success_count, len(test_urls)
        
    except Exception as e:
        print(f"❌ Django URL验证失败: {str(e)}")
        return 0, 0

def main():
    """主函数"""
    print("系统性URL命名空间错误检查和修复")
    print("=" * 60)
    
    # 检查模板文件
    template_issues, template_fixes = check_template_urls()
    
    # 检查视图文件
    view_issues, view_fixes = check_view_urls()
    
    # 验证URL解析
    success_count, total_count = validate_url_patterns()
    
    # 汇总报告
    print("\n" + "=" * 60)
    print("修复结果汇总")
    print("=" * 60)
    
    total_issues = len(template_issues) + len(view_issues)
    total_fixes = len(template_fixes) + len(view_fixes)
    
    print(f"发现问题: {total_issues} 个")
    print(f"应用修复: {total_fixes} 个")
    print(f"URL验证: {success_count}/{total_count} 通过")
    
    if total_issues == 0:
        print("🎉 没有发现URL命名空间问题！")
    elif total_fixes >= total_issues:
        print("🎉 所有发现的问题已修复！")
        print("\n💡 建议接下来的步骤：")
        print("   1. 重启Django开发服务器")
        print("   2. 重新测试权限管理和匿名编号管理页面")
        print("   3. 检查所有导航链接是否正常工作")
    else:
        print("⚠️  部分问题未能自动修复，需要手动检查")
        
        print("\n未修复的问题：")
        remaining_issues = template_issues + view_issues
        for issue in remaining_issues:
            if not any(fix in issue for fix in template_fixes + view_fixes):
                print(f"   - {issue}")
    
    return total_fixes >= total_issues and success_count == total_count

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)