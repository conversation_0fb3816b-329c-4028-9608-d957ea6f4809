# 部门Excel导入功能说明

## 功能概述

部门Excel导入功能已经完成升级，现在支持按**部门名称**关联上级部门，而不是之前的部门编号。这使得导入更加直观和用户友好。

## 主要特性

### ✅ 新增功能
1. **按名称关联上级部门** - 使用部门名称而不是编号来建立层级关系
2. **智能层级排序** - 系统自动按层级顺序处理，确保上级部门先被创建
3. **详细错误反馈** - 提供具体的行号和错误信息
4. **灵活的导入选项** - 支持更新现有部门、跳过错误记录等

### 📋 Excel模板格式

| 列名 | 是否必填 | 说明 |
|------|----------|------|
| 部门编号* | ✅ 必填 | 部门的唯一编号，不能重复 |
| 部门名称* | ✅ 必填 | 部门的名称 |
| 上级部门名称 | ⭕ 可选 | 上级部门的名称，留空表示顶级部门 |
| 备注 | ⭕ 可选 | 部门职责描述或其他说明信息 |

### 📝 示例数据

```
部门编号*  | 部门名称*    | 上级部门名称 | 备注
DEPT001   | 总经理室     |             | 公司最高管理层
DEPT002   | 技术部       | 总经理室     | 负责技术研发工作
DEPT003   | 人事部       | 总经理室     | 负责人力资源管理
DEPT004   | 前端开发组   | 技术部       | 负责前端开发
DEPT005   | 后端开发组   | 技术部       | 负责后端开发
DEPT006   | 测试部       | 技术部       |
```

## 使用步骤

### 1. 下载模板
- 访问部门导入页面：`/organizations/admin/import/departments/`
- 点击"下载模板"按钮获取最新的Excel模板

### 2. 填写数据
- 按照模板格式填写部门信息
- **重要**：上级部门名称必须是已存在的部门名称
- 建议按层级顺序填写（先填写上级部门，再填写下级部门）

### 3. 上传导入
- 选择填写好的Excel文件
- 配置导入选项（可选）
- 点击"开始导入"

### 4. 查看结果
- 系统会显示导入进度和结果
- 如有错误，会显示详细的错误信息

## 导入逻辑

### 🔄 处理顺序
1. **数据验证** - 检查必填字段、数据格式等
2. **智能排序** - 按层级关系排序，确保上级部门先处理
3. **关联查找** - 根据部门名称查找上级部门
4. **数据导入** - 创建或更新部门记录

### ⚠️ 注意事项
1. **部门编号唯一性** - 部门编号必须在系统中唯一
2. **上级部门存在性** - 上级部门名称必须是已存在的部门
3. **备注字段可选** - 备注字段可以为空，用于填写部门职责或说明
4. **层级关系** - 系统会自动处理层级关系，无需手动排序

### 🛡️ 错误处理
- **必填字段缺失** - 提示具体缺失的字段
- **上级部门不存在** - 提示找不到指定名称的上级部门
- **数据格式错误** - 提示具体的格式问题
- **备注字段处理** - 自动处理空值和格式

## 技术实现

### 修改的文件
1. **common/excel_utils.py** - 更新模板生成器
2. **organizations/views.py** - 新增自定义导入逻辑
3. **templates/admin/department/import.html** - 更新页面说明

### 核心改进
1. **字段映射更新** - 从`上级部门编号`改为`上级部门名称`
2. **自定义导入方法** - 实现按名称查找上级部门的逻辑
3. **智能排序算法** - 确保层级关系正确处理
4. **增强错误处理** - 提供更详细的错误信息

## 测试验证

### ✅ 已验证功能
- [x] Excel模板生成正确
- [x] 字段映射一致性
- [x] 数据验证逻辑
- [x] 层级处理逻辑
- [x] 错误处理机制

### 🧪 测试场景
- [x] 正常层级导入
- [x] 缺少必填字段处理
- [x] 上级部门不存在处理
- [x] 部门经理不存在处理
- [x] 重复部门编号处理

## 使用建议

### 📋 最佳实践
1. **分批导入** - 对于大量数据，建议分批导入
2. **层级顺序** - 虽然系统会自动排序，但建议按层级顺序填写
3. **数据验证** - 导入前先验证数据的完整性和准确性
4. **备份数据** - 导入前建议备份现有数据

### 🚨 常见问题
1. **Q: 上级部门名称写错了怎么办？**
   A: 系统会提示找不到上级部门，请检查名称是否正确

2. **Q: 可以同时导入多个层级吗？**
   A: 可以，系统会自动按层级排序处理

3. **Q: 部门经理不存在会影响导入吗？**
   A: 不会，系统会记录警告但继续导入

4. **Q: 如何更新现有部门？**
   A: 使用相同的部门编号，系统会自动更新现有记录

## 总结

部门Excel导入功能现在支持按部门名称关联上级部门，使得组织架构的导入更加直观和用户友好。系统会自动处理层级关系，确保数据的正确性和完整性。
