#!/usr/bin/env python
"""
创建简单测试用户脚本（不依赖安全字段）
"""
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
django.setup()

from organizations.models import Staff, Department

def create_simple_test_user():
    """创建简单测试用户"""
    try:
        # 1. 创建测试部门
        dept, created = Department.objects.get_or_create(
            dept_code='TEST001',
            defaults={
                'name': '测试部门',
                'is_active': True,
                'sort_order': 1
            }
        )
        
        if created:
            print(f"✅ 创建部门成功: {dept.name}")
        else:
            print(f"ℹ️  部门已存在: {dept.name}")
        
        # 2. 删除现有测试用户
        existing_users = Staff.objects.filter(username='testadmin')
        if existing_users.exists():
            existing_users.delete()
            print("🗑️  删除现有测试用户")
        
        # 3. 创建新用户（只使用基本字段）
        staff = Staff.objects.create(
            username='testadmin',
            employee_no='EMP001',
            name='测试管理员',
            department=dept,
            role='super_admin',
            is_active=True,
            email='<EMAIL>'
        )
        
        # 4. 设置密码
        staff.set_password('test123456')
        staff.save()
        
        print("✅ 测试用户创建成功!")
        print(f"   用户名: {staff.username}")
        print(f"   密码: test123456")
        print(f"   员工编号: {staff.employee_no}")
        print(f"   姓名: {staff.name}")
        print(f"   角色: {staff.role}")
        print(f"   部门: {staff.department.name}")
        
        # 5. 验证密码
        from django.contrib.auth.hashers import check_password
        is_valid = check_password('test123456', staff.password)
        print(f"   密码验证: {'✅ 正确' if is_valid else '❌ 错误'}")
        
        return staff
        
    except Exception as e:
        print(f"❌ 创建用户失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return None

if __name__ == '__main__':
    print("🚀 开始创建简单测试用户...")
    print("=" * 50)
    
    user = create_simple_test_user()
    
    if user:
        print("\n" + "=" * 50)
        print("🎉 测试用户创建成功!")
        print("\n📝 登录信息:")
        print("   URL: http://127.0.0.1:8000/admin/login/")
        print("   用户名: testadmin")
        print("   密码: test123456")
        print("\n现在可以进行基础JWT功能测试了！")
        print("注意：安全字段功能暂时禁用，需要后续修复模型定义。")
    else:
        print("\n❌ 用户创建失败")