# CLAUDE.md

## 必须用中文回复我。

## 开发界面要美观，简洁

## 通用编码规范

### 代码质量标准

- 必须有完整的注释，包括函数说明、参数说明、返回值说明
- 变量命名必须有意义，禁止使用a、b、c这种无意义命名
- 复杂逻辑必须有注释解释

### 错误处理原则

- 所有可能的错误都必须处理
- 错误信息要明确，便于调试
- 不允许空catch块
- 关键路径必须有日志记录

### 性能要求

- 数据库查询必须有索引支持
- 循环内不允许执行数据库操作
- 大数据量处理必须分批进行
- 缓存策略要明确

## 产品功能简洁、易用，界面交互、风格符合用户角色使用,具有专业的设计感

## 将对话中的知识（项目结构，数据库设计，具体功能实现，存在的问题，未来需要解决的东西等）存储为本地 Markdown 文件 totalknowledge.md，重要的东西每步都要记录。

## 涉及到需求变更、技术变更，也要记录到totalknowledge.md

## 每次执行任务前，必须要回顾上下文，读取totalknowledge.md里面的知识内容，了解项目规则约束、task.md。

## 严格按照需求文档开发，不要无中生有，不要有缺失。

## 你需要站在用户角度思考产品功能和交互，特别是需求里面没有提及的，如果和上面这条有冲突，请咨询我的意见是采纳还是拒绝。

## 开发记忆

- 写代码时记得用context7这个mcp查找最新文档
- 思考过程中使用sequential-thinking这个mcp进行深度思考
- 在WSL的Ubuntu系统中开发，系统未安装Python等程序，需要在Windows系统中执行命令
- 查询API参考或最佳实践时调用context7这个mcp工具查询最新的文档

## Sequential-Thinking (SequentialThinking) MCP应用场景

- 复杂项目需求分析：用于将复杂的项目需求分解为多个连贯的思考步骤，系统地分析每个环节的可能性和挑战
- 产品功能设计：通过多轮深入思考，逐步展开和细化产品功能，确保设计的全面性和深度
- 技术方案评估：对不同技术方案进行顺序化分析，比较各方案的优缺点，最终选择最佳解决方案
- 风险管理：逐步推演可能出现的风险点，制定详细的预防和应对策略
- 算法和系统架构设计：通过多轮思考梳理系统架构的各个层面，确保设计的完整性和可扩展性
- 创新brainstorming：激发创新思维，通过顺序思考引导思路的发散和深入
- 软件开发流程优化：分析现有开发流程，逐步找出瓶颈和改进点
- 客户需求深度挖掘：通过多轮思考，全面理解和分析客户潜在需求
- 产品迭代策略制定：系统地思考产品下一步迭代的方向和具体措施
- 复杂问题解决：将复杂问题拆解为可管理的步骤，逐步推进问题解决

## 页面开发指导

- 在当前的"页面模板"文件夹中有6个参考页面的html文档，系统的页面风格将按这些模板进行开发，细节可以根据实际需要灵活调整

## 问题解决与沟通

- 诚实是首要原则。遇到困难或不确定时，禁止猜测或伪装完成！必须主动澄清用户需求或请求提供更多信息
- 面对技术难题，应优先寻求克服之道，而非简单降级或更换方案
- 对于解决不了的问题请在文档中如实记录，而非蒙混过关
- 保有批判性思维，若发现技术限制或存在更优的实现方案，你应主动提出质疑并驱动设计改进

## 代码质量与维护

- 在代码清理和重构时，优先处理技术债务和基础架构问题
- 高度重视代码质量、系统稳定性改进，并确保与设计文档保持一致