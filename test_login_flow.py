#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试完整的登录流程
"""

import requests
import json

def test_login_flow():
    """测试完整的登录流程"""
    base_url = "http://127.0.0.1:8000"
    
    print("=== 测试登录流程 ===")
    
    # 1. 测试登录页面访问
    print("\n1. 测试登录页面访问...")
    try:
        response = requests.get(f"{base_url}/admin/login/")
        print(f"   登录页面状态码: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ 登录页面可以正常访问")
        else:
            print(f"   ❌ 登录页面访问失败: {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ 登录页面访问异常: {e}")
        return
    
    # 2. 测试登录API
    print("\n2. 测试登录API...")
    login_data = {
        "username": "admin",
        "password": "123456"
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/login/",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"   登录API状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   登录成功: {data.get('success', False)}")
            
            if data.get('success'):
                print(f"   用户信息: {data.get('user', {}).get('name')}")
                print(f"   用户角色: {data.get('user', {}).get('role')}")
                
                # 保存token用于后续测试
                access_token = data.get('tokens', {}).get('access_token')
                if access_token:
                    print("   ✅ 获取到访问令牌")
                    
                    # 3. 测试仪表板访问
                    print("\n3. 测试仪表板访问...")
                    
                    # 使用Cookie方式（模拟浏览器）
                    cookies = {'access_token': access_token}
                    dashboard_response = requests.get(
                        f"{base_url}/admin/",
                        cookies=cookies
                    )
                    
                    print(f"   仪表板访问状态码: {dashboard_response.status_code}")
                    
                    if dashboard_response.status_code == 200:
                        print("   ✅ 仪表板可以正常访问")
                        if "仪表板" in dashboard_response.text or "dashboard" in dashboard_response.text.lower():
                            print("   ✅ 仪表板内容正常")
                        else:
                            print("   ⚠️ 仪表板内容可能有问题")
                    elif dashboard_response.status_code == 302:
                        print(f"   ⚠️ 仪表板重定向到: {dashboard_response.headers.get('Location', '未知')}")
                    else:
                        print(f"   ❌ 仪表板访问失败: {dashboard_response.status_code}")
                        
                    # 4. 测试使用Authorization头访问
                    print("\n4. 测试使用Authorization头访问...")
                    headers = {'Authorization': f'Bearer {access_token}'}
                    auth_response = requests.get(
                        f"{base_url}/admin/",
                        headers=headers
                    )
                    
                    print(f"   Authorization头访问状态码: {auth_response.status_code}")
                    
                else:
                    print("   ❌ 未获取到访问令牌")
            else:
                print(f"   ❌ 登录失败: {data.get('message', '未知错误')}")
        else:
            print(f"   ❌ 登录API失败: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   错误信息: {error_data.get('message', '未知错误')}")
            except:
                print(f"   响应内容: {response.text[:200]}")
                
    except Exception as e:
        print(f"   ❌ 登录API异常: {e}")
    
    # 5. 测试未认证访问仪表板
    print("\n5. 测试未认证访问仪表板...")
    try:
        unauth_response = requests.get(f"{base_url}/admin/", allow_redirects=False)
        print(f"   未认证访问状态码: {unauth_response.status_code}")

        if unauth_response.status_code == 302:
            redirect_url = unauth_response.headers.get('Location', '')
            print(f"   重定向到: {redirect_url}")
            if '/login/' in redirect_url:
                print("   ✅ 正确重定向到登录页面")
            else:
                print("   ⚠️ 重定向目标可能不正确")
        elif unauth_response.status_code == 200:
            print("   ⚠️ 未认证用户可以直接访问仪表板（可能有安全问题）")
        else:
            print(f"   ❌ 未认证访问异常: {unauth_response.status_code}")

    except Exception as e:
        print(f"   ❌ 未认证访问异常: {e}")

if __name__ == '__main__':
    test_login_flow()
    print("\n=== 测试完成 ===")
