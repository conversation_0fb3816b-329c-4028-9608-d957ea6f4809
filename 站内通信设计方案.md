# 📊 企业考评系统站内通信设计方案

## 🎯 项目概述

### 业务背景
本方案为企业考评评分系统设计专用的站内通信功能，旨在为考评业务流程提供完整的内部沟通和通知支持。系统需要支持150+员工规模，7种角色层级，涵盖系统通知、个人消息、考评相关通知和公告发布等多种通信场景。

### 核心目标
- **业务集成**：与考评流程深度集成，自动化通知提醒
- **权限精细化**：基于企业组织架构的权限控制
- **用户体验**：直观的消息管理界面，实时的状态更新
- **扩展性**：为未来功能扩展预留充分空间

## 🏗️ 整体架构设计理念

### 1. **分离与解耦设计**
```
消息主体(Message) ←→ 接收关系(MessageRecipient) ←→ 用户(Staff)
```
这种设计避免了单一大表的臃肿，实现了：
- **消息内容与接收者分离**：一条消息可以有多个接收者
- **读取状态个性化**：每个接收者都有独立的读取状态
- **扩展性保证**：未来可以轻松添加转发、收藏等个性化功能

### 2. **业务场景驱动设计**

#### 企业考评系统的特殊需求：
- **层级化通信**：管理员→员工、系统→用户、部门内部
- **业务关联性**：消息与考评批次、评分记录等业务对象关联
- **时效性管理**：考评截止提醒、临时通知等有明确的生命周期
- **权限控制**：部门定向发送、角色权限管控

## 📋 核心数据表设计分析

### 1. **Message（消息主表）** - 核心通信载体

```python
class Message(BaseModel):
    message_type = models.CharField(max_length=20, choices=MESSAGE_TYPES)
    sender = models.ForeignKey('organizations.Staff', null=True, blank=True)
    subject = models.CharField(max_length=200)
    content = models.TextField()
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES)
    is_broadcast = models.BooleanField(default=False)
    related_model = models.CharField(max_length=50, blank=True)
    related_id = models.PositiveIntegerField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)
```

#### 🔍 设计理由详解：

**消息类型分类 (message_type)**：
```python
MESSAGE_TYPES = [
    ('SYSTEM', '系统通知'),    # 系统级消息，如维护、更新通知
    ('PERSONAL', '个人消息'),   # 一对一私信
    ('EVALUATION', '考评相关'), # 与考评业务强关联
    ('ANNOUNCEMENT', '公告通知'), # 重要公告广播
]
```
- **业务导向**：每种类型对应不同的业务场景和处理逻辑
- **权限控制**：不同类型的消息有不同的发送权限要求
- **界面展示**：支持按类型筛选和不同的展示样式

**优先级机制 (priority)**：
```python
PRIORITY_CHOICES = [
    ('HIGH', '高优先级'),     # 紧急通知，红色标识
    ('MEDIUM', '中优先级'),   # 普通消息，默认样式  
    ('LOW', '低优先级'),      # 一般信息，灰色标识
]
```
- **视觉引导**：高优先级消息在界面上更突出
- **处理顺序**：系统可以优先处理高优先级消息
- **通知策略**：高优先级消息可以触发即时通知

**业务关联设计 (related_model + related_id)**：
```python
# 示例关联：
related_model='EvaluationBatch', related_id=123  # 关联考评批次
related_model='Staff', related_id=456            # 关联特定员工
```
- **松耦合设计**：避免硬编码外键，保持灵活性
- **业务追溯**：消息可以快速定位到相关的业务对象
- **扩展性**：未来可以关联任意类型的业务模型

**消息生命周期管理 (expires_at)**：
- **自动过期**：过期消息自动不再显示，保持信息的时效性
- **存储优化**：定期清理过期消息，优化数据库性能
- **业务需求**：考评截止提醒等有明确时效性的消息

### 2. **MessageRecipient（接收者关系表）** - 个性化消息管理

```python
class MessageRecipient(BaseModel):
    message = models.ForeignKey(Message, on_delete=models.CASCADE)
    recipient = models.ForeignKey('organizations.Staff', on_delete=models.CASCADE)
    recipient_type = models.CharField(max_length=20, choices=RECIPIENT_TYPES)
    is_read = models.BooleanField(default=False)
    read_at = models.DateTimeField(null=True, blank=True)
    is_starred = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
```

#### 🔍 设计理由详解：

**一对多关系处理**：
- **数据规范化**：避免在Message表中存储接收者列表
- **个性化状态**：每个接收者都有独立的读取状态、收藏状态
- **删除控制**：用户删除消息不影响其他接收者

**接收者类型分类 (recipient_type)**：
```python
RECIPIENT_TYPES = [
    ('STAFF', '个人'),        # 发送给特定个人
    ('DEPARTMENT', '部门'),   # 部门群发的标识
    ('ALL', '全体'),          # 全员广播的标识
]
```
- **发送方式标识**：记录消息是如何发送的
- **统计分析**：便于分析消息传达的覆盖面
- **权限审计**：追踪消息的发送范围

**软删除机制 (is_deleted)**：
- **用户体验**：用户可以"删除"消息，但不影响系统记录
- **数据完整性**：保持消息传达的完整记录
- **恢复机制**：必要时可以恢复用户"删除"的消息

**个性化功能 (is_starred)**：
- **用户体验**：支持消息收藏功能
- **重要信息**：用户可以标记重要消息便于后续查找
- **扩展基础**：为未来更多个性化功能打基础

### 3. **Announcement（公告系统）** - 企业信息发布平台

```python
class Announcement(BaseModel):
    title = models.CharField(max_length=200)
    content = models.TextField()
    announcement_type = models.CharField(max_length=20, choices=ANNOUNCEMENT_TYPES)
    author = models.ForeignKey('organizations.Staff', null=True)
    target_department = models.ForeignKey('organizations.Department', null=True, blank=True)
    is_pinned = models.BooleanField(default=False)
    is_published = models.BooleanField(default=True)
    publish_at = models.DateTimeField(default=timezone.now)
    expire_at = models.DateTimeField(null=True, blank=True)
    view_count = models.PositiveIntegerField(default=0)
    is_html = models.BooleanField(default=False)
```

#### 🔍 设计理由详解：

**独立公告系统设计**：
- **功能区分**：公告与消息有不同的生命周期和展示方式
- **权限管理**：公告的发布权限更严格
- **展示优化**：公告支持置顶、富文本等特殊展示需求

**公告类型分类 (announcement_type)**：
```python
ANNOUNCEMENT_TYPES = [
    ('SYSTEM', '系统公告'),      # 系统维护、更新等
    ('DEPARTMENT', '部门公告'),  # 部门内部通知
    ('EVALUATION', '考评公告'),  # 考评相关重要说明
    ('MAINTENANCE', '维护通知'), # 系统维护通知
    ('POLICY', '政策更新'),      # 制度政策变更
]
```
- **分类管理**：不同类型的公告有不同的重要性和处理方式
- **权限控制**：系统公告只有管理员可以发布
- **展示区分**：不同类型在界面上有不同的标识和样式

**部门定向发布 (target_department)**：
```python
target_department = None          # 全公司公告
target_department = department_id # 特定部门公告
```
- **精准投放**：避免无关人员收到不必要的信息
- **权限控制**：部门经理只能发布本部门公告
- **信息安全**：敏感信息可以限制在特定部门内

**时间控制机制**：
```python
publish_at = models.DateTimeField(default=timezone.now)  # 发布时间
expire_at = models.DateTimeField(null=True, blank=True)  # 过期时间
```
- **定时发布**：支持预约发布功能
- **自动下线**：过期公告自动不再显示
- **生命周期管理**：避免过期信息的干扰

**互动统计 (view_count)**：
- **传达效果**：统计公告的查看次数
- **重要性评估**：高查看量的公告说明关注度高
- **改进依据**：为公告发布策略提供数据支持

### 4. **NotificationTemplate（通知模板）** - 标准化通知管理

```python
class NotificationTemplate(BaseModel):
    template_type = models.CharField(max_length=30, choices=TEMPLATE_TYPES, unique=True)
    name = models.CharField(max_length=100)
    subject_template = models.CharField(max_length=200)
    content_template = models.TextField()
    variables = models.JSONField(default=dict)
    is_active = models.BooleanField(default=True)
```

#### 🔍 设计理由详解：

**模板化设计思想**：
- **标准化**：统一的通知格式，提升专业度
- **效率提升**：避免重复编写相似的通知内容
- **维护便利**：集中管理通知模板，便于批量修改

**业务模板类型 (template_type)**：
```python
TEMPLATE_TYPES = [
    ('EVALUATION_START', '考评开始通知'),
    ('EVALUATION_REMIND', '考评提醒通知'),
    ('EVALUATION_COMPLETE', '考评完成通知'),
    ('PASSWORD_RESET', '密码重置通知'),
    ('ROLE_CHANGE', '角色变更通知'),
    ('ACCOUNT_STATUS', '账户状态通知'),
    ('SYSTEM_MAINTENANCE', '系统维护通知'),
]
```
- **业务覆盖**：涵盖系统中主要的通知场景
- **唯一性保证**：每种类型只有一个模板，避免混乱
- **扩展性**：可以根据业务需要添加新的模板类型

**变量替换机制**：
```python
# 模板示例
subject_template = "考评批次【{batch_name}】即将截止"
content_template = "尊敬的{staff_name}，您在考评批次【{batch_name}】中还有{remaining_tasks}个任务未完成..."

# 变量定义
variables = {
    "batch_name": "考评批次名称",
    "staff_name": "员工姓名", 
    "remaining_tasks": "剩余任务数"
}
```
- **动态内容**：根据具体业务数据生成个性化消息
- **可维护性**：模板和数据分离，便于管理
- **扩展性**：可以轻松添加新的变量类型

**模板状态管理 (is_active)**：
- **版本控制**：可以停用旧模板，启用新模板
- **A/B测试**：支持模板效果的对比测试
- **灵活切换**：根据业务需要灵活启用/停用模板

### 5. **MessageReadLog（阅读日志）** - 详细行为追踪

```python
class MessageReadLog(BaseModel):
    message = models.ForeignKey(Message, on_delete=models.CASCADE)
    reader = models.ForeignKey('organizations.Staff', on_delete=models.CASCADE)
    read_at = models.DateTimeField(default=timezone.now)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
```

#### 🔍 设计理由详解：

**详细行为追踪**：
- **审计需求**：企业系统需要完整的操作日志
- **统计分析**：分析消息的传达效果和阅读习惯
- **安全监控**：异常访问行为的检测

**安全信息记录**：
- **IP地址**：追踪访问来源，支持安全分析
- **用户代理**：记录设备和浏览器信息
- **时间戳**：精确的操作时间记录

**业务价值**：
- **传达效果分析**：统计消息的实际阅读情况
- **用户行为分析**：了解用户的消息阅读习惯
- **系统优化依据**：为消息推送策略提供数据支持

## 🚀 性能优化设计

### 1. **索引策略**

```python
# Message表索引
indexes = [
    models.Index(fields=['message_type', 'created_at']),  # 按类型和时间查询
    models.Index(fields=['sender', 'created_at']),        # 发送者历史消息
    models.Index(fields=['priority', 'created_at']),      # 优先级排序
    models.Index(fields=['expires_at']),                  # 过期消息清理
]

# MessageRecipient表索引
indexes = [
    models.Index(fields=['recipient', 'is_read']),        # 未读消息查询
    models.Index(fields=['recipient', 'is_deleted']),     # 用户消息列表
    models.Index(fields=['message', 'is_read']),          # 消息读取统计
]

# Announcement表索引
indexes = [
    models.Index(fields=['announcement_type', 'is_published']),
    models.Index(fields=['target_department', 'is_published']),
    models.Index(fields=['is_pinned', 'created_at']),
    models.Index(fields=['publish_at', 'expire_at']),
]
```

#### 索引设计原则：
- **查询频率优先**：为最常用的查询条件创建索引
- **复合索引**：多字段组合查询使用复合索引
- **覆盖索引**：尽可能让索引覆盖查询需要的所有字段

### 2. **查询优化设计**

**分页查询支持**：
- 按时间倒序的默认排序：`ordering = ['-created_at']`
- 支持高效的分页查询，避免大数据量的性能问题
- 使用游标分页处理大数据集

**关联查询优化**：
```python
# 优化前
messages = MessageRecipient.objects.filter(recipient=user)
for msg_recipient in messages:
    print(msg_recipient.message.subject)  # N+1查询问题

# 优化后
messages = MessageRecipient.objects.filter(recipient=user).select_related('message', 'message__sender')
for msg_recipient in messages:
    print(msg_recipient.message.subject)  # 单次查询
```

**缓存策略**：
```python
# 未读消息数量缓存
cache_key = f"unread_count_{user_id}"
unread_count = cache.get(cache_key)
if unread_count is None:
    unread_count = MessageRecipient.objects.filter(
        recipient_id=user_id, is_read=False, is_deleted=False
    ).count()
    cache.set(cache_key, unread_count, 300)  # 缓存5分钟
```

### 3. **数据归档策略**

**消息归档**：
```python
# 定期归档策略
- 30天前的已读消息 → 归档到历史表
- 90天前的未读消息 → 标记为过期
- 1年前的消息日志 → 压缩存储
```

**性能监控**：
- 数据库查询时间监控
- API响应时间监控
- 内存使用监控

## 🔒 安全性设计

### 1. **数据安全**

**数据完整性**：
- **软删除机制**：继承`BaseModel`，支持软删除和审计字段
- **外键约束**：保证数据引用的完整性
- **数据备份**：定期备份关键数据

**敏感信息保护**：
```python
# 敏感消息内容加密
from django.conf import settings
from cryptography.fernet import Fernet

def encrypt_content(content):
    cipher = Fernet(settings.MESSAGE_ENCRYPTION_KEY)
    return cipher.encrypt(content.encode()).decode()

def decrypt_content(encrypted_content):
    cipher = Fernet(settings.MESSAGE_ENCRYPTION_KEY)
    return cipher.decrypt(encrypted_content.encode()).decode()
```

### 2. **访问控制**

**权限矩阵**：
```python
# 消息发送权限
SEND_PERMISSIONS = {
    'SYSTEM': [Role.SUPER_ADMIN, Role.SYSTEM_ADMIN],
    'PERSONAL': [Role.SUPER_ADMIN, Role.HR_ADMIN, Role.DEPT_MANAGER, Role.ADMIN],
    'EVALUATION': [Role.SUPER_ADMIN, Role.EVAL_ADMIN],
    'ANNOUNCEMENT': [Role.SUPER_ADMIN, Role.HR_ADMIN],
}

# 公告发布权限
ANNOUNCEMENT_PERMISSIONS = {
    'SYSTEM': [Role.SUPER_ADMIN],
    'DEPARTMENT': [Role.SUPER_ADMIN, Role.HR_ADMIN, Role.DEPT_MANAGER],
    'EVALUATION': [Role.SUPER_ADMIN, Role.EVAL_ADMIN],
}
```

**数据访问控制**：
```python
def get_accessible_messages(user):
    """获取用户可访问的消息"""
    base_query = MessageRecipient.objects.filter(
        recipient=user,
        is_deleted=False
    )
    
    # 根据用户角色和部门过滤
    if user.role in [Role.SUPER_ADMIN, Role.SYSTEM_ADMIN]:
        # 超级管理员可以看到所有消息
        return base_query
    elif user.role == Role.DEPT_MANAGER:
        # 部门经理可以看到本部门相关消息
        return base_query.filter(
            Q(message__target_department__isnull=True) |
            Q(message__target_department=user.department)
        )
    else:
        # 普通用户只能看到发给自己的消息
        return base_query
```

### 3. **操作审计**

**完整的审计日志**：
```python
class MessageAuditLog(BaseModel):
    action_type = models.CharField(max_length=20)  # CREATE, READ, UPDATE, DELETE
    message = models.ForeignKey(Message, on_delete=models.CASCADE)
    operator = models.ForeignKey(Staff, on_delete=models.CASCADE)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    action_details = models.JSONField()
```

**敏感操作监控**：
- 批量消息发送监控
- 异常访问行为检测
- 权限变更操作记录

## 🎯 扩展性设计

### 1. **水平扩展能力**

**微服务架构支持**：
```python
# 消息服务独立部署
class MessageService:
    def send_message(self, sender_id, recipients, content, message_type):
        """发送消息的核心服务"""
        pass
    
    def get_user_messages(self, user_id, filters):
        """获取用户消息的核心服务"""
        pass
```

**数据库分片支持**：
```python
# 按用户ID分片
def get_database_for_user(user_id):
    shard_id = user_id % settings.DATABASE_SHARDS
    return f'messages_shard_{shard_id}'
```

### 2. **功能扩展预留**

**多媒体消息支持**：
```python
class MessageAttachment(BaseModel):
    message = models.ForeignKey(Message, on_delete=models.CASCADE)
    file_name = models.CharField(max_length=255)
    file_path = models.CharField(max_length=500)
    file_type = models.CharField(max_length=50)
    file_size = models.PositiveIntegerField()
```

**消息状态扩展**：
```python
MESSAGE_STATUS_CHOICES = [
    ('DRAFT', '草稿'),
    ('SENT', '已发送'),
    ('DELIVERED', '已送达'),
    ('READ', '已读'),
    ('RECALLED', '已撤回'),
    ('EXPIRED', '已过期'),
]
```

**通知渠道扩展**：
```python
NOTIFICATION_CHANNELS = [
    ('WEB', '站内消息'),
    ('EMAIL', '邮件通知'),
    ('SMS', '短信通知'),
    ('PUSH', 'APP推送'),
    ('WECHAT', '微信通知'),
]
```

### 3. **集成能力**

**第三方系统集成**：
```python
class ExternalNotificationService:
    def send_email_notification(self, message):
        """发送邮件通知"""
        pass
    
    def send_sms_notification(self, message):
        """发送短信通知"""
        pass
    
    def send_wechat_notification(self, message):
        """发送微信通知"""
        pass
```

**Webhook支持**：
```python
class WebhookEndpoint(BaseModel):
    name = models.CharField(max_length=100)
    url = models.URLField()
    secret_key = models.CharField(max_length=255)
    event_types = models.JSONField()  # 订阅的事件类型
    is_active = models.BooleanField(default=True)
```

## 🎨 用户界面设计规范

### 1. **消息中心界面**

**统计卡片设计**：
```html
<!-- 统计卡片布局 -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
                <i data-lucide="mail" class="w-6 h-6 text-blue-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">总消息数</p>
                <p class="text-2xl font-bold text-gray-900">{{ total_count }}</p>
            </div>
        </div>
    </div>
</div>
```

**消息列表设计**：
- 未读消息用蓝色背景标识
- 高优先级消息显示红色标签
- 消息类型用不同图标区分
- 支持批量操作（全选、批量已读、批量删除）

### 2. **响应式设计**

**移动端适配**：
```css
/* 移动端消息列表 */
@media (max-width: 768px) {
    .message-list {
        padding: 0.5rem;
    }
    
    .message-item {
        padding: 1rem;
        margin-bottom: 0.5rem;
    }
    
    .message-actions {
        flex-direction: column;
        gap: 0.5rem;
    }
}
```

**交互设计**：
- 下拉刷新支持
- 无限滚动加载
- 滑动操作（标记已读、删除）
- 长按多选功能

### 3. **可访问性设计**

**无障碍访问**：
```html
<!-- 语义化标签 -->
<main role="main" aria-label="消息中心">
    <section aria-label="未读消息">
        <h2 id="unread-messages">未读消息</h2>
        <ul aria-labelledby="unread-messages">
            <li role="article" aria-describedby="msg-1-desc">
                <h3>消息标题</h3>
                <p id="msg-1-desc">消息内容描述</p>
            </li>
        </ul>
    </section>
</main>
```

**键盘导航**：
- Tab键在可交互元素间导航
- Enter键选择/展开消息
- 空格键标记已读/未读
- Delete键删除消息

## 📊 监控与分析

### 1. **业务指标监控**

**消息传达效率**：
```python
def calculate_message_metrics():
    """计算消息传达指标"""
    return {
        'total_sent': Message.objects.count(),
        'delivery_rate': MessageRecipient.objects.filter(is_deleted=False).count() / MessageRecipient.objects.count(),
        'read_rate': MessageRecipient.objects.filter(is_read=True).count() / MessageRecipient.objects.count(),
        'avg_read_time': calculate_average_read_time(),
    }
```

**用户活跃度分析**：
```python
def analyze_user_activity():
    """分析用户消息活跃度"""
    return {
        'daily_active_users': get_daily_message_users(),
        'message_frequency': get_message_frequency_by_user(),
        'peak_hours': get_message_peak_hours(),
    }
```

### 2. **系统性能监控**

**数据库性能**：
```python
# 慢查询监控
SLOW_QUERY_THRESHOLD = 1.0  # 1秒

def monitor_database_performance():
    with connection.cursor() as cursor:
        cursor.execute("SHOW PROCESSLIST")
        long_running_queries = [
            query for query in cursor.fetchall()
            if query['Time'] > SLOW_QUERY_THRESHOLD
        ]
    return long_running_queries
```

**API性能监控**：
```python
import time
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page

class MessageListAPIView(View):
    @method_decorator(cache_page(60 * 5))  # 缓存5分钟
    def get(self, request):
        start_time = time.time()
        # 处理逻辑
        end_time = time.time()
        
        # 记录响应时间
        response_time = end_time - start_time
        if response_time > 2.0:  # 超过2秒记录警告
            logger.warning(f"Slow API response: {response_time}s")
```

### 3. **错误监控与报警**

**异常监控**：
```python
def monitor_message_errors():
    """监控消息相关错误"""
    error_patterns = [
        'Failed to send message',
        'Database connection timeout',
        'Template rendering error',
    ]
    
    for pattern in error_patterns:
        error_count = search_logs_for_pattern(pattern, last_hour=True)
        if error_count > ALERT_THRESHOLD:
            send_alert(f"High error rate detected: {pattern}")
```

## 🔄 部署与运维

### 1. **部署架构**

**容器化部署**：
```dockerfile
# Dockerfile for message service
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["gunicorn", "--bind", "0.0.0.0:8000", "wsgi:application"]
```

**负载均衡配置**：
```nginx
upstream message_service {
    server message_app_1:8000;
    server message_app_2:8000;
    server message_app_3:8000;
}

server {
    listen 80;
    location /communications/ {
        proxy_pass http://message_service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 2. **数据备份策略**

**定期备份**：
```bash
#!/bin/bash
# 消息数据备份脚本
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/messages"

# 备份数据库
pg_dump -h $DB_HOST -U $DB_USER $DB_NAME > $BACKUP_DIR/messages_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/messages_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "messages_*.sql.gz" -mtime +7 -delete
```

**灾难恢复**：
```bash
#!/bin/bash
# 灾难恢复脚本
BACKUP_FILE=$1

if [ -z "$BACKUP_FILE" ]; then
    echo "Usage: $0 <backup_file>"
    exit 1
fi

# 停止服务
systemctl stop message_service

# 恢复数据库
gunzip -c $BACKUP_FILE | psql -h $DB_HOST -U $DB_USER $DB_NAME

# 重启服务
systemctl start message_service
```

### 3. **监控告警**

**Prometheus监控配置**：
```yaml
# prometheus.yml
scrape_configs:
  - job_name: 'message-service'
    static_configs:
      - targets: ['message-service:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s
```

**Grafana仪表板**：
```json
{
  "dashboard": {
    "title": "Message Service Dashboard",
    "panels": [
      {
        "title": "Message Send Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(messages_sent_total[5m])"
          }
        ]
      },
      {
        "title": "API Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))"
          }
        ]
      }
    ]
  }
}
```

## 📋 总结

### 设计优势

1. **业务融合度高**：
   - 与企业考评流程深度集成
   - 支持复杂的企业组织架构
   - 满足多角色、多部门的通信需求

2. **技术架构合理**：
   - 分层设计，职责清晰
   - 数据库设计规范，性能优化充分
   - 扩展性强，支持未来功能演进

3. **用户体验优秀**：
   - 直观的界面设计
   - 完善的交互功能
   - 移动端友好的响应式设计

4. **安全性保障**：
   - 完整的权限控制体系
   - 详细的操作审计日志
   - 数据安全保护机制

### 实施建议

1. **分阶段实施**：
   - 第一阶段：基础消息功能
   - 第二阶段：公告系统和模板功能
   - 第三阶段：高级特性和性能优化

2. **持续优化**：
   - 根据用户反馈持续改进
   - 性能监控和调优
   - 功能扩展和升级

3. **团队协作**：
   - 前后端协作开发
   - 定期代码审查
   - 文档维护和知识分享

这个设计方案为企业考评系统提供了完整、可靠、易扩展的站内通信解决方案，能够显著提升企业内部沟通效率和用户体验。