#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试职位导入相关URL
"""

import requests

def test_urls():
    """测试URL访问"""
    base_url = "http://127.0.0.1:8000"
    
    urls_to_test = [
        "/admin/excel/template/position/",
        "/admin/import/positions/",
        "/admin/positions/",
    ]
    
    print("=== URL访问测试 ===")
    
    for url in urls_to_test:
        full_url = base_url + url
        try:
            response = requests.get(full_url, allow_redirects=False)
            print(f"URL: {url}")
            print(f"状态码: {response.status_code}")
            print(f"Content-Type: {response.headers.get('Content-Type', 'N/A')}")
            
            if response.status_code == 302:
                print(f"重定向到: {response.headers.get('Location', 'N/A')}")
            elif response.status_code == 200:
                print("✓ 访问成功")
            else:
                print(f"✗ 访问失败")
            
            print("-" * 50)
            
        except Exception as e:
            print(f"URL: {url}")
            print(f"错误: {e}")
            print("-" * 50)

if __name__ == '__main__':
    test_urls()
