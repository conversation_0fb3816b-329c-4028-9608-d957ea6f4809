#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
URL命名空间修复验证脚本
检查Django URL解析是否正常工作
"""

import os
import sys
import django

# 处理Windows编码问题
if sys.platform.startswith('win'):
    import io
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

# 配置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
django.setup()

def test_url_resolutions():
    """测试URL解析"""
    print("=== 测试URL命名空间解析 ===")
    
    try:
        from django.urls import reverse
        
        # 测试关键URL的解析
        test_urls = [
            ('organizations:admin:dashboard', '仪表板'),
            ('organizations:admin:permissions_manage', '权限管理'),
            ('organizations:admin:anonymous_codes_manage', '匿名编号管理'),
            ('organizations:admin:staff_role_edit', '员工角色编辑', {'staff_id': 1}),
            ('organizations:admin:department_list', '部门列表'),
            ('organizations:admin:staff_list', '员工列表'),
            ('organizations:admin:login', '登录页'),
        ]
        
        success_count = 0
        
        for url_data in test_urls:
            if len(url_data) == 3:
                url_name, description, kwargs = url_data
            else:
                url_name, description = url_data
                kwargs = {}
            
            try:
                resolved_url = reverse(url_name, kwargs=kwargs)
                print(f"✅ {description}: {url_name} -> {resolved_url}")
                success_count += 1
            except Exception as e:
                print(f"❌ {description}: {url_name} -> 错误: {str(e)}")
        
        print(f"\nURL解析结果: {success_count}/{len(test_urls)} 个URL解析成功")
        
        return success_count == len(test_urls)
        
    except Exception as e:
        print(f"❌ URL解析测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_specific_fix():
    """测试具体的修复"""
    print("\n=== 测试具体修复内容 ===")
    
    try:
        # 检查修复的文件内容
        permissions_file = os.path.join(os.path.dirname(__file__), 'organizations', 'views_permissions.py')
        
        if os.path.exists(permissions_file):
            with open(permissions_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 检查是否还有错误的URL引用
                if "redirect('admin:permissions" in content:
                    print("❌ 仍存在错误的URL引用: admin:permissions")
                    return False
                
                if "redirect('organizations:admin:permissions_manage')" in content:
                    print("✅ 权限管理重定向URL已修复")
                else:
                    print("❌ 权限管理重定向URL修复不完整")
                    return False
                
                if "redirect('organizations:admin:staff_role_edit'" in content:
                    print("✅ 员工角色编辑重定向URL已修复")
                else:
                    print("❌ 员工角色编辑重定向URL修复不完整")
                    return False
        
        # 检查模板文件
        template_files = [
            'templates/admin/permissions/manage.html',
            'templates/admin/anonymous/manage.html'
        ]
        
        for template_file in template_files:
            full_path = os.path.join(os.path.dirname(__file__), template_file)
            if os.path.exists(full_path):
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if "{% url 'admin:dashboard' %}" in content:
                        print(f"❌ {template_file} 仍有错误的URL引用")
                        return False
                    elif "{% url 'organizations:admin:dashboard' %}" in content:
                        print(f"✅ {template_file} URL引用已修复")
                    else:
                        print(f"⚠️  {template_file} 可能没有相关URL引用")
        
        return True
        
    except Exception as e:
        print(f"❌ 具体修复测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("URL命名空间修复验证测试")
    print("=" * 50)
    
    test_results = []
    
    # 运行测试
    test_results.append(("URL解析测试", test_url_resolutions()))
    test_results.append(("具体修复验证", test_specific_fix()))
    
    # 输出结果
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    
    success_count = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            success_count += 1
    
    print(f"\n总体结果: {success_count}/{len(test_results)} 项测试通过")
    
    if success_count == len(test_results):
        print("🎉 所有URL命名空间问题已修复！")
        print("\n💡 建议接下来的步骤：")
        print("   1. 重启Django开发服务器")
        print("   2. 重新访问权限管理和匿名编号管理页面")
        print("   3. 测试页面间的跳转功能")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步检查")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)