# 企业考评评分系统需求文档

## 1. 项目概述

### 1.1 项目背景
开发一个完整的企业考评评分系统，用于企业内部员工考评管理，支持匿名评分和后台管理两个端口。

### 1.2 技术栈
- 后端：Python + Django
- 前端：Django Templates + Tailwind CSS
- 数据库：MySQL
- 特点：自定义认证系统，美观简洁的界面设计

## 2. 组织架构

### 2.1 部门结构
- **总经理室**：1名正总经理 + 2名副总经理（分管不同部门）
- **业务部门**：
  - 财务科
  - 直营门店A
  - 直营门店B  
  - 教材部
  - 综合管理办
  - 大客户部

### 2.2 职位层级体系（1-9级）
- **1-4级**：基层员工（1级员工、2级员工、3级员工、4级员工）
- **5级**：副主管
- **6级**：正主管（柜组长）
- **7级**：副经理
- **8级**：正经理
- **9级**：领导班子（总经理室人员）

### 2.3 管理层次划分
- **高层**：总经理室（不被考核，但考核他人）
- **中层**：各部门正副经理
- **中层以下**：柜组长、4级、3级、2级、1级员工

## 3. 功能模块设计

### 3.1 匿名评分端
**目标用户**：所有参与考评的员工

#### 3.1.1 核心功能
- **匿名登录**：使用系统生成的匿名编号登录
- **考评任务**：查看分配给自己的考评任务列表
- **匿名评分**：对指定人员进行匿名评分
- **个人信息查看**：
  - 查看自己的基本信息（姓名、部门、岗位级别）
  - 查看自己的考评结果（匿名化处理，不显示评分人）

#### 3.1.2 用户体验要求
- 界面简洁直观，专注考评功能
- 完全匿名化设计，保护评分人隐私
- 操作流程简单，降低使用门槛

### 3.2 管理员后台端
**目标用户**：管理人员（分权限等级）

#### 3.2.1 基础数据管理
- **部门管理**
  - 部门信息的增删改查
  - Excel批量导入（格式：部门名称，员工数量）
  
- **职位管理**
  - 职位层级管理（1-9级对应关系）
  - 职位权限配置
  
- **人员管理**
  - 员工信息的增删改查
  - Excel批量导入（格式：姓名，部门名称，职位名称，是否管理人员）
  - 匿名编号生成和管理
  - 登录权限分配

#### 3.2.2 考评管理
- **评分卷管理**
  - 创建和编辑考评表单模板
  - 评分项管理（支持不同题型）
  - 评分标准设置
  
- **评分批次管理**
  - 创建考评活动批次
  - 设置考评时间周期
  - 智能分配考评关系
  - 手动调整考评关系
  
- **智能分配系统**
  - 基于职位层级的自动分配算法
  - 权重设置（下级→上级：0.8，上级→下级：1.2，同级：1.0）
  - 支持自定义调整和跨部门协同考核

#### 3.2.3 统计分析
- **评分统计**
  - 个人考评结果汇总
  - 部门考评结果对比
  - 历史趋势分析
  - 可视化图表展示

#### 3.2.4 系统管理
- **审计日志**
  - 所有操作记录追踪
  - 数据变更历史
  - 安全事件监控
  
- **权限管理**
  - 基于角色的权限控制
  - 部门数据访问隔离
  - 操作权限细分

#### 3.2.5 扩展功能
- **考试题库管理**（后期扩展）
  - 题库管理（单选、多选、判断、简答）
  - 试卷组合
  - 考试批次管理

## 4. 考评关系智能分配机制

### 4.1 分配原则
基于职位层级、部门归属和业务关系，自动生成考评人与被考评人的对应关系。

### 4.2 中层人员考评规则
**适用对象**：各部门正副经理

**考评人构成**：
- 同部门所有下级员工（权重：0.8）
- 直接上级领导/总经理室人员（权重：1.2）
- 同级其他部门中层管理者（权重：1.0）

**特殊规则**：
- 其他部门的中层以下人员不参与中层考评
- 总经理室人员参与所有中层考评但不被考评

### 4.3 中层以下人员考评规则

#### 4.3.1 柜组长考评规则
**考评人构成**：
- 所属部门的1-4级下级员工
- 部门正副经理（直接上级）
- 同部门其他柜组长（同级）

#### 4.3.2 基层员工（1-4级）考评规则
**考评人构成**：
- 部门正副经理
- 直接管理的柜组长

### 4.4 权重机制
- **下级评上级**：权重 0.8（体现谦逊客观）
- **上级评下级**：权重 1.2（体现管理权威）
- **同级互评**：权重 1.0（体现平等关系）
- **跨部门中层互评**：权重 1.0

### 4.5 自定义调整功能
- 支持手动添加或删除考评关系
- 支持跨部门协同考核设置
- 支持权重个性化调整
- 提供批量操作和单独调整两种模式

## 5. 系统安全与数据保护

### 5.1 匿名机制
- **匿名编号生成规则**：部门代码 + 职位代码 + 随机数字
- **隐私保护**：考评过程中只显示匿名编号，不显示真实姓名
- **权限隔离**：管理员可查看映射关系，普通用户无法获取

### 5.2 数据安全
- **软删除机制**：核心数据采用软删除，防止误删
- **审计字段**：created_at, updated_at, created_by, updated_by
- **操作日志**：记录所有关键操作，确保可追溯性
- **数据加密**：敏感信息加密存储

### 5.3 权限控制
- **超级管理员**：全部权限
- **部门经理**：只能管理本部门人员和数据
- **普通管理员**：基础查看权限
- **员工用户**：个人信息查看和考评参与权限

## 6. 非功能性需求

### 6.1 可用性
- 界面简洁直观，符合用户角色使用习惯
- 响应式设计，支持不同设备访问
- 操作流程优化，减少学习成本

### 6.2 性能要求
- 数据库查询必须有索引支持
- 大数据量处理采用分批机制
- 页面加载时间控制在3秒内

### 6.3 可扩展性
- 模块化设计，支持功能扩展
- 支持集成其他企业系统
- 数据库设计支持业务增长

### 6.4 可维护性
- 代码注释完整，便于维护
- 错误日志详细，便于问题排查
- 配置文件化，便于环境部署

## 7. 技术实现要点

### 7.1 认证系统
- 独立于Django User系统的自定义认证
- 支持管理端和匿名端双重登录模式
- 会话管理和安全控制

### 7.2 数据库设计
- 遵循第三范式，确保数据一致性
- 软删除和审计字段标准化
- 外键约束和索引优化

### 7.3 前端设计
- Django Templates + Tailwind CSS
- 组件化设计，提高复用性
- 交互友好，用户体验优先

## 8. 实施计划

### 8.1 开发阶段
1. **需求确认和系统设计**
2. **数据库设计和环境搭建**
3. **基础框架和认证系统开发**
4. **核心功能模块开发**
5. **前端界面开发和优化**
6. **测试和部署上线**

### 8.2 验收标准
- 功能完整性：所有需求功能正常运行
- 性能指标：满足并发和响应时间要求
- 安全性：通过安全测试和数据保护验证
- 易用性：用户操作简单直观，满意度高

---

**文档版本**：v1.0  
**创建时间**：2025-07-25  
**最后更新**：2025-07-25