#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
创建测试数据脚本
为部门管理功能创建基础测试数据
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
django.setup()

from organizations.models import Department, Position, Staff
from django.contrib.auth.hashers import make_password

def create_test_data():
    """创建测试数据"""
    
    print("开始创建测试数据...")
    
    # 1. 创建顶级部门 - 总经理室
    gm_office, created = Department.objects.get_or_create(
        dept_code='GM',
        defaults={
            'name': '总经理室',
            'description': '企业最高管理机构',
            'is_active': True,
            'sort_order': 1,
            'created_by': 'system'
        }
    )
    if created:
        print(f"创建部门: {gm_office}")
    
    # 2. 创建其他6个部门
    departments_data = [
        {'code': 'HR', 'name': '人力资源部', 'desc': '负责人力资源管理和员工发展'},
        {'code': 'IT', 'name': '信息技术部', 'desc': '负责信息系统建设和技术支持'},
        {'code': 'FIN', 'name': '财务部', 'desc': '负责财务管理和会计核算'},
        {'code': 'MKT', 'name': '市场部', 'desc': '负责市场营销和品牌推广'},
        {'code': 'OPS', 'name': '运营部', 'desc': '负责业务运营和流程管理'},
        {'code': 'PROD', 'name': '生产部', 'desc': '负责产品生产和质量管理'},
    ]
    
    created_departments = [gm_office]
    
    for dept_data in departments_data:
        dept, created = Department.objects.get_or_create(
            dept_code=dept_data['code'],
            defaults={
                'name': dept_data['name'],
                'parent_department': gm_office,
                'description': dept_data['desc'],
                'is_active': True,
                'sort_order': len(created_departments) + 1,
                'created_by': 'system'
            }
        )
        if created:
            print(f"创建部门: {dept}")
        created_departments.append(dept)
    
    # 3. 为每个部门创建职位
    positions_data = [
        {'level': 9, 'name': '总经理', 'is_mgmt': True},
        {'level': 8, 'name': '部门经理', 'is_mgmt': True},
        {'level': 7, 'name': '副经理', 'is_mgmt': True},
        {'level': 6, 'name': '主管', 'is_mgmt': True},
        {'level': 5, 'name': '副主管', 'is_mgmt': True},
        {'level': 4, 'name': '高级专员', 'is_mgmt': False},
        {'level': 3, 'name': '专员', 'is_mgmt': False},
        {'level': 2, 'name': '助理专员', 'is_mgmt': False},
        {'level': 1, 'name': '实习生', 'is_mgmt': False},
    ]
    
    created_positions = []
    
    for dept in created_departments:
        for i, pos_data in enumerate(positions_data):
            # 总经理室只有总经理职位
            if dept.dept_code == 'GM' and pos_data['level'] != 9:
                continue
            # 其他部门不设总经理职位
            if dept.dept_code != 'GM' and pos_data['level'] == 9:
                continue
                
            pos, created = Position.objects.get_or_create(
                department=dept,
                position_code=f"{dept.dept_code}_{pos_data['level']}",
                defaults={
                    'name': pos_data['name'],
                    'level': pos_data['level'],
                    'is_management': pos_data['is_mgmt'],
                    'description': f"{dept.name}{pos_data['name']}",
                    'is_active': True,
                    'sort_order': i + 1,
                    'created_by': 'system'
                }
            )
            if created:
                print(f"创建职位: {pos}")
            created_positions.append(pos)
    
    # 4. 创建测试员工
    staff_data = [
        {
            'username': 'admin', 'name': '系统管理员', 'dept': 'GM', 
            'employee_no': 'GM001', 'role': 'super_admin', 'level': 9
        },
        {
            'username': 'hr_manager', 'name': '张人事', 'dept': 'HR', 
            'employee_no': 'HR001', 'role': 'dept_manager', 'level': 8
        },
        {
            'username': 'it_manager', 'name': '李技术', 'dept': 'IT', 
            'employee_no': 'IT001', 'role': 'dept_manager', 'level': 8
        },
        {
            'username': 'fin_manager', 'name': '王会计', 'dept': 'FIN', 
            'employee_no': 'FIN001', 'role': 'dept_manager', 'level': 8
        },
        {
            'username': 'test_user', 'name': '测试用户', 'dept': 'IT', 
            'employee_no': 'IT002', 'role': 'employee', 'level': 4
        },
    ]
    
    for staff_info in staff_data:
        # 找到对应部门和职位
        department = Department.objects.get(dept_code=staff_info['dept'])
        position = Position.objects.filter(
            department=department, 
            level=staff_info['level']
        ).first()
        
        staff, created = Staff.objects.get_or_create(
            username=staff_info['username'],
            defaults={
                'employee_no': staff_info['employee_no'],
                'name': staff_info['name'],
                'department': department,
                'position': position,
                'role': staff_info['role'],
                'password': make_password('123456'),  # 默认密码
                'is_active': True,
                'email': f"{staff_info['username']}@company.com",
                'created_by': 'system'
            }
        )
        
        if created:
            print(f"创建员工: {staff}")
            # 为员工生成匿名编号
            if not staff.anonymous_code:
                staff.generate_anonymous_code()
                staff.save()
                print(f"  匿名编号: {staff.anonymous_code}")
    
    # 5. 设置部门经理
    departments_with_managers = [
        ('HR', 'hr_manager'),
        ('IT', 'it_manager'), 
        ('FIN', 'fin_manager'),
    ]
    
    for dept_code, manager_username in departments_with_managers:
        try:
            dept = Department.objects.get(dept_code=dept_code)
            manager = Staff.objects.get(username=manager_username)
            dept.manager = manager
            dept.save()
            print(f"设置 {dept.name} 经理: {manager.name}")
        except Exception as e:
            print(f"设置部门经理失败: {e}")
    
    print("\n测试数据创建完成！")
    print("\n创建的账号信息:")
    print("用户名: admin, 密码: 123456 (超级管理员)")
    print("用户名: hr_manager, 密码: 123456 (HR经理)")
    print("用户名: it_manager, 密码: 123456 (IT经理)")
    print("用户名: fin_manager, 密码: 123456 (财务经理)")
    print("用户名: test_user, 密码: 123456 (普通员工)")

if __name__ == '__main__':
    try:
        create_test_data()
    except Exception as e:
        print(f"创建测试数据时出错: {e}")
        import traceback
        traceback.print_exc()