#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
全面排查和修复所有剩余的URL错误
"""

import os
import re
import sys

# 处理Windows编码问题
if sys.platform.startswith('win'):
    import io
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

def scan_all_url_patterns():
    """扫描所有文件中的URL模式"""
    print("=== 全面扫描URL模式 ===")
    
    # 定义可能有问题的URL模式
    problematic_patterns = [
        # organizations 模块错误模式
        r"'organizations:(?!admin:|anonymous:)[a-zA-Z_]+'",
        r'"organizations:(?!admin:|anonymous:)[a-zA-Z_]+"',
        
        # evaluations 模块错误模式
        r"'evaluations:(?!admin:|anonymous:)[a-zA-Z_]+'",
        r'"evaluations:(?!admin:|anonymous:)[a-zA-Z_]+"',
        
        # reports 模块错误模式
        r"'reports:(?!admin:|anonymous:)[a-zA-Z_]+'",
        r'"reports:(?!admin:|anonymous:)[a-zA-Z_]+"',
        
        # 直接使用admin命名空间的错误
        r"'admin:[a-zA-Z_]+'",
        r'"admin:[a-zA-Z_]+"',
        
        # redirect中的错误模式
        r"redirect\(['\"](?:organizations|evaluations|reports):(?!admin:|anonymous:)[a-zA-Z_]+['\"]",
    ]
    
    errors_found = []
    files_scanned = 0
    
    # 扫描目录
    scan_dirs = ['templates', 'organizations', 'evaluations', 'reports', 'common']
    
    for scan_dir in scan_dirs:
        dir_path = os.path.join(os.path.dirname(__file__), scan_dir)
        if os.path.exists(dir_path):
            for root, dirs, files in os.walk(dir_path):
                for file in files:
                    if file.endswith(('.py', '.html')):
                        file_path = os.path.join(root, file)
                        files_scanned += 1
                        
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                                
                                # 检查每个问题模式
                                for i, pattern in enumerate(problematic_patterns):
                                    matches = re.findall(pattern, content)
                                    for match in matches:
                                        rel_path = os.path.relpath(file_path, os.path.dirname(__file__))
                                        errors_found.append({
                                            'file': rel_path,
                                            'pattern': match,
                                            'type': f'pattern_{i+1}'
                                        })
                                        
                        except Exception as e:
                            print(f"⚠️  无法读取文件 {file_path}: {str(e)}")
    
    print(f"扫描了 {files_scanned} 个文件")
    
    if errors_found:
        print(f"❌ 发现 {len(errors_found)} 个可能的URL错误:")
        
        # 按文件分组显示
        files_with_errors = {}
        for error in errors_found:
            if error['file'] not in files_with_errors:
                files_with_errors[error['file']] = []
            files_with_errors[error['file']].append(error['pattern'])
        
        for file_path, patterns in files_with_errors.items():
            print(f"   📁 {file_path}:")
            for pattern in set(patterns):  # 去重
                print(f"      - {pattern}")
        
        return errors_found
    else:
        print("✅ 没有发现明显的URL模式错误")
        return []

def check_specific_error_cases():
    """检查特定的已知错误情况"""
    print("\n=== 检查特定错误情况 ===")
    
    specific_checks = [
        {
            'description': '匿名登录URL错误',
            'files': ['templates/admin/permissions/manage.html', 'templates/admin/anonymous/manage.html'],
            'should_not_contain': "'organizations:anonymous_login'",
            'should_contain': "'organizations:anonymous:anonymous_login'"
        },
        {
            'description': '员工列表URL错误', 
            'files': ['templates/admin/permissions/manage.html', 'templates/admin/anonymous/manage.html'],
            'should_not_contain': "'organizations:staff_list'",
            'should_contain': "'organizations:admin:staff_list'"
        },
        {
            'description': '权限管理重定向错误',
            'files': ['organizations/views_permissions.py'],
            'should_not_contain': "redirect('admin:permissions:",
            'should_contain': "redirect('organizations:admin:permissions_manage')"
        },
        {
            'description': '仪表板URL错误',
            'files': ['templates/admin/permissions/manage.html', 'templates/admin/anonymous/manage.html'],
            'should_not_contain': "'admin:dashboard'",
            'should_contain': "'organizations:admin:dashboard'"
        }
    ]
    
    all_passed = True
    
    for check in specific_checks:
        print(f"\n🔍 {check['description']}:")
        
        for file_path in check['files']:
            full_path = os.path.join(os.path.dirname(__file__), file_path)
            
            if os.path.exists(full_path):
                try:
                    with open(full_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                        has_error = check['should_not_contain'] in content
                        has_correct = check['should_contain'] in content
                        
                        if has_error:
                            print(f"   ❌ {file_path}: 仍包含错误模式 {check['should_not_contain']}")
                            all_passed = False
                        elif has_correct:
                            print(f"   ✅ {file_path}: 使用正确模式")
                        else:
                            print(f"   ⚠️  {file_path}: 未找到相关URL引用")
                            
                except Exception as e:
                    print(f"   ❌ {file_path}: 读取失败 - {str(e)}")
                    all_passed = False
            else:
                print(f"   ❌ {file_path}: 文件不存在")
                all_passed = False
    
    return all_passed

def suggest_fixes():
    """建议修复方案"""
    print("\n=== 修复建议 ===")
    
    suggestions = [
        "🔧 URL命名空间规范:",
        "   - 管理端: 'app:admin:view_name'",
        "   - 匿名端: 'app:anonymous:view_name'",
        "",
        "🔧 常见修复模式:",
        "   - 'organizations:staff_list' → 'organizations:admin:staff_list'",
        "   - 'organizations:anonymous_login' → 'organizations:anonymous:anonymous_login'", 
        "   - 'evaluations:batch_list' → 'evaluations:admin:batch_list'",
        "   - 'reports:report_list' → 'reports:admin:report_list'",
        "",
        "🔧 检查重点文件:",
        "   - templates/admin/permissions/manage.html",
        "   - templates/admin/anonymous/manage.html",
        "   - organizations/views_permissions.py",
        "   - organizations/views_anonymous.py",
        "",
        "🔧 测试方法:",
        "   1. 重启Django开发服务器",
        "   2. 访问 /admin/permissions/",
        "   3. 访问 /admin/anonymous-codes/",
        "   4. 测试所有导航链接",
    ]
    
    for suggestion in suggestions:
        print(suggestion)

def main():
    """主函数"""
    print("全面URL错误排查工具")
    print("=" * 60)
    
    # 扫描所有URL模式
    pattern_errors = scan_all_url_patterns()
    
    # 检查特定错误情况
    specific_passed = check_specific_error_cases()
    
    # 建议修复方案
    suggest_fixes()
    
    # 总结
    print("\n" + "=" * 60)
    print("排查结果总结")
    print("=" * 60)
    
    if len(pattern_errors) == 0 and specific_passed:
        print("🎉 全面排查完成，未发现明显的URL错误！")
        print("\n✅ 建议测试步骤:")
        print("   1. 重启Django开发服务器")
        print("   2. 测试权限管理页面: /admin/permissions/")
        print("   3. 测试匿名编号管理页面: /admin/anonymous-codes/")
        print("   4. 测试页面间的导航链接")
        return True
    else:
        print("⚠️  仍发现以下问题:")
        if len(pattern_errors) > 0:
            print(f"   - 模式错误: {len(pattern_errors)} 个")
        if not specific_passed:
            print("   - 特定检查未通过")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)