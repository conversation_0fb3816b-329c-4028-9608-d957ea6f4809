# -*- coding: utf-8 -*-
"""
考评应用视图包
包含考评模板、批次、关系管理和匿名评分功能
"""

# 从原始 views.py 导入所有视图类
from evaluations.views_backup import *

# 导入向导视图模块
from . import wizard_views

# 确保所有视图都可以被导入
__all__ = [
    # 模板管理视图
    'TemplateListView',
    'TemplateCreateView', 
    'TemplateDetailView',
    'TemplateUpdateView',
    'TemplateDeleteView',
    'TemplateCopyView',
    
    # 权重规则管理视图
    'WeightingRuleListView',
    'WeightingRuleCreateView',
    'WeightingRuleUpdateView', 
    'WeightingRuleDeleteView',
    'WeightingRuleToggleView',
    'WeightingRuleCopyView',
    
    # 考评批次管理视图
    'BatchListView',
    'BatchCreateView',
    'BatchDetailView',
    'BatchUpdateView', 
    'BatchDeleteView',
    'BatchActivateView',
    'BatchCompleteView',
    'BatchAssignView',
    'BatchRelationListView',
    
    # 考评关系管理视图
    'RelationListView',
    'RelationCreateView',
    'RelationUpdateView',
    'RelationDeleteView',
    
    # 考评进度视图
    'ProgressListView',
    'ProgressDetailView',
    'ParticipantTasksView',
    'SendIndividualReminderView',
    'ExportBatchDetailView',
    'ProgressExportView',
    
    # 匿名端视图
    'AnonymousHomeView',
    'AnonymousTaskListView',
    'AnonymousEvaluateView',
    'AnonymousSubmitView',
    'AnonymousDraftView',
    'AnonymousResultsView',
    
    # 向导视图模块
    'wizard_views',
]
