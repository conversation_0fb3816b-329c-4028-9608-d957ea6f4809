# -*- coding: utf-8 -*-
"""
向导视图控制器
提供简化的向导式操作界面
"""

import json
import logging
from django.shortcuts import render, get_object_or_404, redirect
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.urls import reverse

from ..models import EvaluationBatch, EvaluationTemplate
from ..services.simplified_weighting import SimplifiedWeightingService
from ..services.template_wizard import TemplateWizardService, TemplateRecommendationService
from ..services.assignment_strategy import AssignmentStrategyService
from organizations.models import Staff

logger = logging.getLogger(__name__)


@login_required
def weight_wizard_view(request, batch_id):
    """权重配置向导页面"""
    batch = get_object_or_404(EvaluationBatch, id=batch_id)
    
    # 检查权限
    if not request.user.has_perm('evaluations.change_weightingrule'):
        messages.error(request, '您没有权限配置权重规则')
        return redirect('evaluations:admin:batch_detail', batch_id=batch_id)
    
    context = {
        'batch': batch,
        'schemes': SimplifiedWeightingService.list_all_schemes(),
        'current_scheme': SimplifiedWeightingService(batch, request.user.staff).get_current_scheme()
    }
    
    return render(request, 'admin/wizard/weight_wizard.html', context)


@login_required
@require_http_methods(["POST"])
def weight_preview_api(request):
    """权重方案预览API"""
    try:
        data = json.loads(request.body)
        batch_id = data.get('batch_id')
        scheme_name = data.get('scheme_name')
        
        if not batch_id or not scheme_name:
            return JsonResponse({'success': False, 'error': '缺少必要参数'})
        
        batch = get_object_or_404(EvaluationBatch, id=batch_id)
        service = SimplifiedWeightingService(batch, request.user.staff)
        
        preview = service.preview_scheme_impact(scheme_name)
        
        if 'error' in preview:
            return JsonResponse({'success': False, 'error': preview['error']})
        
        return JsonResponse({
            'success': True,
            'preview': {
                'total_relations': preview.get('total_relations', 0),
                'weight_changes': len(preview.get('weight_impact', {})),
                'expected_effect': preview.get('scheme_info', {}).get('description', ''),
                'relation_stats': preview.get('relation_stats', {}),
                'weight_impact': preview.get('weight_impact', {})
            }
        })
        
    except Exception as e:
        logger.error(f"权重预览失败: {str(e)}")
        return JsonResponse({'success': False, 'error': f'预览失败: {str(e)}'})


@login_required
@require_http_methods(["POST"])
def apply_weight_api(request):
    """应用权重配置API"""
    try:
        data = json.loads(request.body)
        batch_id = data.get('batch_id')
        scheme_name = data.get('scheme_name')
        custom_weights = data.get('custom_weights', {})
        
        if not batch_id or not scheme_name:
            return JsonResponse({'success': False, 'error': '缺少必要参数'})
        
        batch = get_object_or_404(EvaluationBatch, id=batch_id)
        service = SimplifiedWeightingService(batch, request.user.staff)
        
        # 转换自定义权重格式
        custom_adjustments = {}
        if custom_weights:
            weight_mapping = {
                'subordinate': 'subordinate_to_superior',
                'superior': 'superior_to_subordinate',
                'peer': 'peer_to_peer',
                'crossDept': 'cross_department'
            }
            
            for key, value in custom_weights.items():
                if key in weight_mapping:
                    custom_adjustments[weight_mapping[key]] = float(value)
        
        result = service.apply_preset_scheme(scheme_name, custom_adjustments)
        
        if result['success']:
            messages.success(request, f'权重方案 "{scheme_name}" 应用成功')
            
        return JsonResponse(result)
        
    except Exception as e:
        logger.error(f"应用权重配置失败: {str(e)}")
        return JsonResponse({'success': False, 'error': f'应用失败: {str(e)}'})


@login_required
def template_wizard_view(request):
    """模板创建向导页面"""
    # 检查权限
    if not request.user.has_perm('evaluations.add_evaluationtemplate'):
        messages.error(request, '您没有权限创建模板')
        return redirect('evaluations:admin:template_list')
    
    context = {
        'presets': TemplateWizardService.list_all_presets()
    }
    
    return render(request, 'admin/wizard/template_wizard.html', context)


@login_required
@require_http_methods(["POST"])
def template_preview_api(request):
    """模板预览API"""
    try:
        data = json.loads(request.body)
        preset_name = data.get('preset_name')
        
        if not preset_name:
            return JsonResponse({'success': False, 'error': '缺少预设名称'})
        
        service = TemplateWizardService(request.user.staff)
        preview = service.preview_preset(preset_name)
        
        if 'error' in preview:
            return JsonResponse({'success': False, 'error': preview['error']})
        
        return JsonResponse({
            'success': True,
            'preview': preview
        })
        
    except Exception as e:
        logger.error(f"模板预览失败: {str(e)}")
        return JsonResponse({'success': False, 'error': f'预览失败: {str(e)}'})


@login_required
@require_http_methods(["POST"])
def template_recommendation_api(request):
    """模板推荐API"""
    try:
        data = json.loads(request.body)
        organization_type = data.get('organization_type')
        evaluation_purpose = data.get('evaluation_purpose')
        
        if not organization_type or not evaluation_purpose:
            return JsonResponse({'success': False, 'error': '缺少必要参数'})
        
        recommendations = TemplateRecommendationService.recommend_templates(
            organization_type, evaluation_purpose
        )
        
        return JsonResponse({
            'success': True,
            'recommendations': recommendations
        })
        
    except Exception as e:
        logger.error(f"模板推荐失败: {str(e)}")
        return JsonResponse({'success': False, 'error': f'推荐失败: {str(e)}'})


@login_required
@require_http_methods(["POST"])
def create_template_api(request):
    """创建模板API"""
    try:
        data = json.loads(request.body)
        preset_name = data.get('preset_name')
        template_name = data.get('template_name')
        template_description = data.get('template_description')
        
        if not preset_name or not template_name:
            return JsonResponse({'success': False, 'error': '缺少必要参数'})
        
        service = TemplateWizardService(request.user.staff)
        result = service.create_from_preset(
            preset_name, 
            template_name, 
            template_description
        )
        
        if result['success']:
            messages.success(request, f'模板 "{template_name}" 创建成功')
            
        return JsonResponse(result)
        
    except Exception as e:
        logger.error(f"创建模板失败: {str(e)}")
        return JsonResponse({'success': False, 'error': f'创建失败: {str(e)}'})


@login_required
def assignment_wizard_view(request, batch_id):
    """分配策略向导页面"""
    batch = get_object_or_404(EvaluationBatch, id=batch_id)
    
    # 检查权限
    if not request.user.has_perm('evaluations.change_evaluationrelation'):
        messages.error(request, '您没有权限配置考评关系')
        return redirect('evaluations:admin:batch_detail', batch_id=batch_id)
    
    context = {
        'batch': batch,
        'strategies': AssignmentStrategyService.list_all_strategies()
    }
    
    return render(request, 'admin/wizard/assignment_wizard.html', context)


@login_required
@require_http_methods(["POST"])
def assignment_preview_api(request):
    """分配策略预览API"""
    try:
        data = json.loads(request.body)
        batch_id = data.get('batch_id')
        strategy_name = data.get('strategy_name')
        custom_rules = data.get('custom_rules', {})
        
        if not batch_id or not strategy_name:
            return JsonResponse({'success': False, 'error': '缺少必要参数'})
        
        batch = get_object_or_404(EvaluationBatch, id=batch_id)
        service = AssignmentStrategyService(batch, request.user.staff)
        
        preview = service.preview_strategy_impact(strategy_name, custom_rules)
        
        if 'error' in preview:
            return JsonResponse({'success': False, 'error': preview['error']})
        
        return JsonResponse({
            'success': True,
            'preview': preview
        })
        
    except Exception as e:
        logger.error(f"分配预览失败: {str(e)}")
        return JsonResponse({'success': False, 'error': f'预览失败: {str(e)}'})


@login_required
@require_http_methods(["POST"])
def execute_assignment_api(request):
    """执行分配策略API"""
    try:
        data = json.loads(request.body)
        batch_id = data.get('batch_id')
        strategy_name = data.get('strategy_name')
        custom_rules = data.get('custom_rules', {})
        
        if not batch_id or not strategy_name:
            return JsonResponse({'success': False, 'error': '缺少必要参数'})
        
        batch = get_object_or_404(EvaluationBatch, id=batch_id)
        service = AssignmentStrategyService(batch, request.user.staff)
        
        result = service.execute_strategy(strategy_name, custom_rules)
        
        if result['success']:
            messages.success(request, f'分配策略 "{strategy_name}" 执行成功，创建了 {result["relations_created"]} 个考评关系')
            
        return JsonResponse(result)
        
    except Exception as e:
        logger.error(f"执行分配策略失败: {str(e)}")
        return JsonResponse({'success': False, 'error': f'执行失败: {str(e)}'})


@login_required
def wizard_dashboard_view(request):
    """向导仪表板页面"""
    context = {
        'weight_schemes_count': len(SimplifiedWeightingService.list_all_schemes()),
        'template_presets_count': len(TemplateWizardService.list_all_presets()),
        'assignment_strategies_count': len(AssignmentStrategyService.list_all_strategies()),
        'recent_batches': EvaluationBatch.objects.filter(
            deleted_at__isnull=True
        ).order_by('-created_at')[:5],
        'recent_templates': EvaluationTemplate.objects.filter(
            deleted_at__isnull=True
        ).order_by('-created_at')[:5]
    }
    
    return render(request, 'admin/wizard/dashboard.html', context)


@login_required
def help_center_view(request):
    """帮助中心页面"""
    context = {
        'weight_schemes': SimplifiedWeightingService.list_all_schemes(),
        'template_presets': TemplateWizardService.list_all_presets(),
        'assignment_strategies': AssignmentStrategyService.list_all_strategies()
    }
    
    return render(request, 'admin/wizard/help_center.html', context)


# 辅助函数
def get_wizard_navigation_context(current_wizard=None):
    """获取向导导航上下文"""
    wizards = [
        {
            'name': 'weight',
            'title': '权重配置向导',
            'description': '简化权重规则配置',
            'icon': 'balance-scale',
            'url': 'evaluations:admin:weight_wizard'
        },
        {
            'name': 'template',
            'title': '模板创建向导',
            'description': '快速创建评价模板',
            'icon': 'file-text',
            'url': 'evaluations:admin:template_wizard'
        },
        {
            'name': 'assignment',
            'title': '分配策略向导',
            'description': '智能分配考评关系',
            'icon': 'users',
            'url': 'evaluations:admin:assignment_wizard'
        }
    ]
    
    for wizard in wizards:
        wizard['is_current'] = wizard['name'] == current_wizard
    
    return {'wizards': wizards}


def validate_wizard_permissions(user, wizard_type):
    """验证向导权限"""
    permission_map = {
        'weight': 'evaluations.change_weightingrule',
        'template': 'evaluations.add_evaluationtemplate',
        'assignment': 'evaluations.change_evaluationrelation'
    }
    
    required_permission = permission_map.get(wizard_type)
    if not required_permission:
        return False
    
    return user.has_perm(required_permission)


def log_wizard_action(user, action, details=None):
    """记录向导操作日志"""
    from common.models import AuditLog
    
    try:
        AuditLog.objects.create(
            user=user.username,
            action=f'wizard_{action}',
            target_model='wizard',
            description=f'向导操作: {action}',
            details=details or {}
        )
    except Exception as e:
        logger.error(f"记录向导操作日志失败: {str(e)}")


# 错误处理装饰器
def handle_wizard_errors(func):
    """向导错误处理装饰器"""
    def wrapper(request, *args, **kwargs):
        try:
            return func(request, *args, **kwargs)
        except Exception as e:
            logger.error(f"向导操作失败: {str(e)}")
            if request.headers.get('Content-Type') == 'application/json':
                return JsonResponse({
                    'success': False,
                    'error': f'操作失败: {str(e)}'
                })
            else:
                messages.error(request, f'操作失败: {str(e)}')
                return redirect('evaluations:admin:wizard_dashboard')
    
    return wrapper
