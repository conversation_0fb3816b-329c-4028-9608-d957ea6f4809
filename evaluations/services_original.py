# -*- coding: utf-8 -*-
"""
考评业务服务层
实现智能分配算法和核心业务逻辑
"""

from django.db import transaction
from django.db.models import Q, F
from django.utils import timezone
from typing import List, Dict, Tuple, Optional
import logging

from .models import (
    EvaluationBatch, EvaluationRelation, EvaluationTemplate, 
    WeightingRule, EvaluationRecord, EvaluationItemRecord
)
from organizations.models import Staff, Department, Position
from common.models import AuditLog

logger = logging.getLogger(__name__)


class IntelligentAssignmentService:
    """
    智能分配服务
    实现基于规则引擎的自动考评关系分配算法
    """

    def __init__(self, batch: EvaluationBatch, operator: Staff):
        """
        初始化智能分配服务
        
        Args:
            batch: 考评批次
            operator: 操作人员
        """
        self.batch = batch
        self.operator = operator
        self.assignment_rules = self._load_assignment_rules()
        self.weighting_rules = self._load_weighting_rules()

    def execute_assignment(self, force_regenerate: bool = False) -> Dict:
        """
        执行智能分配
        
        Args:
            force_regenerate: 是否强制重新生成（清除现有关系）
            
        Returns:
            分配结果统计
        """
        try:
            with transaction.atomic():
                # 清除现有关系（如果需要）
                if force_regenerate:
                    self._clear_existing_relations()
                
                # 获取参与人员
                participants = self._get_eligible_participants()
                
                # 执行分配逻辑
                relations_created = 0
                errors = []
                
                for evaluatee in participants:
                    try:
                        evaluators = self._find_evaluators_for(evaluatee)
                        for evaluator, weight_factor in evaluators:
                            if self._create_evaluation_relation(evaluator, evaluatee, weight_factor):
                                relations_created += 1
                    except Exception as e:
                        error_msg = f"为 {evaluatee.name} 分配评价者时出错: {str(e)}"
                        errors.append(error_msg)
                        logger.error(error_msg)
                
                # 记录审计日志
                self._log_assignment_result(relations_created, len(errors))
                
                return {
                    'success': True,
                    'relations_created': relations_created,
                    'participants_count': len(participants),
                    'errors': errors,
                    'assignment_summary': self._generate_assignment_summary()
                }
                
        except Exception as e:
            logger.error(f"智能分配执行失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'relations_created': 0
            }

    def _get_eligible_participants(self) -> List[Staff]:
        """
        获取符合条件的参与人员
        排除总经理室人员（他们只评价别人，不被评价）
        
        Returns:
            参与考评的员工列表
        """
        # 获取总经理室
        gm_office = Department.objects.filter(dept_code='GM').first()
        
        # 获取所有活跃员工，排除总经理室
        participants = Staff.objects.filter(
            is_active=True,
            deleted_at__isnull=True
        ).exclude(
            department=gm_office
        ).select_related('department', 'position')
        
        return list(participants)

    def _find_evaluators_for(self, evaluatee: Staff) -> List[Tuple[Staff, float]]:
        """
        为指定员工找到评价者列表
        根据职位层级和部门关系确定评价者和权重
        
        Args:
            evaluatee: 被评价者
            
        Returns:
            评价者和权重的列表 [(evaluator, weight_factor), ...]
        """
        evaluators = []
        
        if not evaluatee.position:
            logger.warning(f"员工 {evaluatee.name} 没有设置职位，跳过分配")
            return evaluators
        
        # 根据职位层级确定分配规则
        position_level = evaluatee.position.level
        
        if position_level in [7, 8]:  # 中层管理者（副经理、正经理）
            evaluators = self._get_middle_management_evaluators(evaluatee)
        elif position_level == 6:  # 柜组长（正主管）
            evaluators = self._get_supervisor_evaluators(evaluatee)
        elif position_level in [1, 2, 3, 4, 5]:  # 基层员工和副主管
            evaluators = self._get_employee_evaluators(evaluatee)
        else:
            logger.warning(f"未识别的职位层级 {position_level}，员工: {evaluatee.name}")
        
        return evaluators

    def _get_middle_management_evaluators(self, evaluatee: Staff) -> List[Tuple[Staff, float]]:
        """
        获取中层管理者的评价者
        规则：同部门下级(0.8) + 直接上级(1.2) + 同级中层(1.0) + 总经理室(1.2)
        """
        evaluators = []
        
        # 1. 同部门下级员工 (权重: 0.8)
        department_subordinates = Staff.objects.filter(
            department=evaluatee.department,
            position__level__lt=evaluatee.position.level,
            is_active=True,
            deleted_at__isnull=True
        ).exclude(id=evaluatee.id)
        
        for subordinate in department_subordinates:
            weight = self._calculate_weight_factor(subordinate, evaluatee, 'subordinate_to_superior')
            evaluators.append((subordinate, weight))
        
        # 2. 总经理室人员 (权重: 1.2)
        gm_office = Department.objects.filter(dept_code='GM').first()
        if gm_office:
            gm_staff = Staff.objects.filter(
                department=gm_office,
                is_active=True,
                deleted_at__isnull=True
            )
            for gm_member in gm_staff:
                weight = self._calculate_weight_factor(gm_member, evaluatee, 'superior_to_subordinate')
                evaluators.append((gm_member, weight))
        
        # 3. 同级其他部门中层管理者 (权重: 1.0)
        peer_managers = Staff.objects.filter(
            position__level__in=[7, 8],  # 副经理、正经理
            is_active=True,
            deleted_at__isnull=True
        ).exclude(
            id=evaluatee.id
        ).exclude(
            department=evaluatee.department  # 排除同部门
        )
        
        for peer in peer_managers:
            weight = self._calculate_weight_factor(peer, evaluatee, 'peer_to_peer')
            evaluators.append((peer, weight))
        
        return evaluators

    def _get_supervisor_evaluators(self, evaluatee: Staff) -> List[Tuple[Staff, float]]:
        """
        获取柜组长的评价者
        规则：部门下级 + 部门经理 + 同部门柜组长 + 总经理室
        """
        evaluators = []
        
        # 1. 所属部门的1-5级下级员工
        department_subordinates = Staff.objects.filter(
            department=evaluatee.department,
            position__level__in=[1, 2, 3, 4, 5],  # 1-4级员工 + 副主管
            is_active=True,
            deleted_at__isnull=True
        ).exclude(id=evaluatee.id)
        
        for subordinate in department_subordinates:
            weight = self._calculate_weight_factor(subordinate, evaluatee, 'subordinate_to_superior')
            evaluators.append((subordinate, weight))
        
        # 2. 部门正副经理
        department_managers = Staff.objects.filter(
            department=evaluatee.department,
            position__level__in=[7, 8],  # 副经理、正经理
            is_active=True,
            deleted_at__isnull=True
        )
        
        for manager in department_managers:
            weight = self._calculate_weight_factor(manager, evaluatee, 'superior_to_subordinate')
            evaluators.append((manager, weight))
        
        # 3. 同部门其他柜组长
        peer_supervisors = Staff.objects.filter(
            department=evaluatee.department,
            position__level=6,  # 正主管
            is_active=True,
            deleted_at__isnull=True
        ).exclude(id=evaluatee.id)
        
        for peer in peer_supervisors:
            weight = self._calculate_weight_factor(peer, evaluatee, 'peer_to_peer')
            evaluators.append((peer, weight))
        
        # 4. 总经理室人员
        gm_office = Department.objects.filter(dept_code='GM').first()
        if gm_office:
            gm_staff = Staff.objects.filter(
                department=gm_office,
                is_active=True,
                deleted_at__isnull=True
            )
            for gm_member in gm_staff:
                weight = self._calculate_weight_factor(gm_member, evaluatee, 'superior_to_subordinate')
                evaluators.append((gm_member, weight))
        
        return evaluators

    def _get_employee_evaluators(self, evaluatee: Staff) -> List[Tuple[Staff, float]]:
        """
        获取基层员工的评价者
        规则：部门经理 + 直接管理的柜组长 + 总经理室
        """
        evaluators = []
        
        # 1. 部门正副经理
        department_managers = Staff.objects.filter(
            department=evaluatee.department,
            position__level__in=[7, 8],  # 副经理、正经理
            is_active=True,
            deleted_at__isnull=True
        )
        
        for manager in department_managers:
            weight = self._calculate_weight_factor(manager, evaluatee, 'superior_to_subordinate')
            evaluators.append((manager, weight))
        
        # 2. 直接管理的柜组长
        supervisors = Staff.objects.filter(
            department=evaluatee.department,
            position__level=6,  # 正主管
            is_active=True,
            deleted_at__isnull=True
        )
        
        for supervisor in supervisors:
            weight = self._calculate_weight_factor(supervisor, evaluatee, 'superior_to_subordinate')
            evaluators.append((supervisor, weight))
        
        # 3. 总经理室人员
        gm_office = Department.objects.filter(dept_code='GM').first()
        if gm_office:
            gm_staff = Staff.objects.filter(
                department=gm_office,
                is_active=True,
                deleted_at__isnull=True
            )
            for gm_member in gm_staff:
                weight = self._calculate_weight_factor(gm_member, evaluatee, 'superior_to_subordinate')
                evaluators.append((gm_member, weight))
        
        return evaluators

    def _calculate_weight_factor(self, evaluator: Staff, evaluatee: Staff, relation_type: str) -> float:
        """
        计算权重系数
        基于预设规则和动态权重规则计算最终权重
        
        Args:
            evaluator: 评价者
            evaluatee: 被评价者
            relation_type: 关系类型
            
        Returns:
            权重系数
        """
        # 默认权重映射
        default_weights = {
            'subordinate_to_superior': 0.8,  # 下级评上级
            'superior_to_subordinate': 1.2,  # 上级评下级
            'peer_to_peer': 1.0,  # 同级互评
            'cross_department': 1.0,  # 跨部门评价
        }
        
        base_weight = default_weights.get(relation_type, 1.0)
        
        # 查找匹配的权重规则
        for rule in self.weighting_rules:
            if rule.matches_condition(evaluator, evaluatee):
                return rule.weight_factor
        
        return base_weight

    def _create_evaluation_relation(self, evaluator: Staff, evaluatee: Staff, weight_factor: float) -> bool:
        """
        创建考评关系
        
        Args:
            evaluator: 评价者
            evaluatee: 被评价者
            weight_factor: 权重系数
            
        Returns:
            是否创建成功
        """
        try:
            # 检查是否已存在关系
            existing_relation = EvaluationRelation.objects.filter(
                batch=self.batch,
                evaluator=evaluator,
                evaluatee=evaluatee,
                deleted_at__isnull=True
            ).first()
            
            if existing_relation:
                return False  # 关系已存在
            
            # 创建新关系
            EvaluationRelation.objects.create(
                batch=self.batch,
                evaluator=evaluator,
                evaluatee=evaluatee,
                template=self.batch.default_template,
                weight_factor=weight_factor,
                status='pending',
                created_by=self.operator.username
            )
            
            return True
            
        except Exception as e:
            logger.error(f"创建考评关系失败: {evaluator.name} -> {evaluatee.name}, 错误: {str(e)}")
            return False

    def _clear_existing_relations(self):
        """清除批次现有的考评关系"""
        EvaluationRelation.objects.filter(
            batch=self.batch,
            deleted_at__isnull=True
        ).update(
            deleted_at=timezone.now(),
            updated_by=self.operator.username
        )

    def _load_assignment_rules(self) -> Dict:
        """加载分配规则配置"""
        # 这里可以从配置文件或数据库加载自定义规则
        return {
            'include_gm_office': True,  # 包含总经理室人员
            'cross_department_managers': True,  # 跨部门中层互评
            'min_evaluators_per_person': 3,  # 每人最少评价者数量
            'max_evaluators_per_person': 15,  # 每人最多评价者数量
        }

    def _load_weighting_rules(self) -> List[WeightingRule]:
        """加载权重规则"""
        return list(WeightingRule.objects.filter(
            is_active=True,
            deleted_at__isnull=True
        ).order_by('-priority'))

    def _generate_assignment_summary(self) -> Dict:
        """生成分配结果摘要"""
        relations = EvaluationRelation.objects.filter(
            batch=self.batch,
            deleted_at__isnull=True
        )
        
        # 统计信息
        total_relations = relations.count()
        evaluators_count = relations.values('evaluator').distinct().count()
        evaluatees_count = relations.values('evaluatee').distinct().count()
        
        # 权重分布
        weight_distribution = {}
        for relation in relations:
            weight = float(relation.weight_factor)
            weight_distribution[weight] = weight_distribution.get(weight, 0) + 1
        
        return {
            'total_relations': total_relations,
            'evaluators_count': evaluators_count,
            'evaluatees_count': evaluatees_count,
            'weight_distribution': weight_distribution,
            'avg_evaluators_per_person': round(total_relations / max(evaluatees_count, 1), 2)
        }

    def _log_assignment_result(self, relations_created: int, error_count: int):
        """记录分配结果到审计日志"""
        AuditLog.objects.create(
            user=self.operator.username,
            action='batch_assign',
            target_model='evaluationbatch',
            target_id=self.batch.id,
            description=f'智能分配完成: 创建 {relations_created} 个考评关系，{error_count} 个错误',
            details={
                'batch_name': self.batch.name,
                'relations_created': relations_created,
                'error_count': error_count,
                'operator': self.operator.name
            }
        )


class EvaluationProgressService:
    """
    考评进度服务
    计算和更新考评进度统计
    """

    @staticmethod
    def update_batch_progress(batch: EvaluationBatch) -> Dict:
        """
        更新批次进度统计
        
        Args:
            batch: 考评批次
            
        Returns:
            进度统计信息
        """
        try:
            # 获取所有考评关系
            relations = EvaluationRelation.objects.filter(
                batch=batch,
                deleted_at__isnull=True
            )
            
            total_relations = relations.count()
            if total_relations == 0:
                return {'completion_rate': 0, 'message': '暂无考评关系'}
            
            # 计算已完成的关系
            completed_relations = relations.filter(status='completed').count()
            completion_rate = round((completed_relations / total_relations) * 100, 2)
            
            # 更新进度记录
            from reports.models import EvaluationProgress
            progress, created = EvaluationProgress.objects.get_or_create(
                batch=batch,
                defaults={
                    'total_relations': total_relations,
                    'completed_relations': completed_relations,
                    'completion_rate': completion_rate,
                    'created_by': 'system'
                }
            )
            
            if not created:
                # 更新现有记录
                progress.total_relations = total_relations
                progress.completed_relations = completed_relations
                progress.completion_rate = completion_rate
                progress.updated_by = 'system'
                progress.save()
            
            return {
                'total_relations': total_relations,
                'completed_relations': completed_relations,
                'completion_rate': completion_rate,
                'remaining_relations': total_relations - completed_relations
            }
            
        except Exception as e:
            logger.error(f"更新批次进度失败: {str(e)}")
            return {'error': str(e)}


class EvaluationSubmissionService:
    """
    考评提交服务
    处理匿名端的评分提交
    """

    def __init__(self, relation: EvaluationRelation, evaluator: Staff):
        """
        初始化提交服务
        
        Args:
            relation: 考评关系
            evaluator: 评价者
        """
        self.relation = relation
        self.evaluator = evaluator
        self.template = relation.template

    def save_draft(self, evaluation_data: Dict) -> Dict:
        """
        保存评分草稿
        
        Args:
            evaluation_data: 评分数据
            
        Returns:
            保存结果
        """
        try:
            # 验证权限
            if self.relation.evaluator != self.evaluator:
                return {'success': False, 'error': '无权限保存此考评'}
            
            if self.relation.status == 'completed':
                return {'success': False, 'error': '该考评已完成，无法保存草稿'}
            
            if self.relation.batch.status != 'active':
                return {'success': False, 'error': '考评批次未激活或已结束'}
            
            # 创建或更新草稿记录（不改变关系状态）
            record, created = EvaluationRecord.objects.get_or_create(
                relation=self.relation,
                defaults={
                    'total_score': 0,
                    'weighted_score': 0,
                    'comment': '',
                    'created_by': self.evaluator.username
                }
            )
            
            # 删除现有的评分项记录
            EvaluationItemRecord.objects.filter(
                evaluation_record=record
            ).delete()
            
            # 处理评分项数据
            total_score = 0
            item_records = []
            
            for item_id, item_data in evaluation_data.get('items', {}).items():
                try:
                    from .models import EvaluationItem
                    item = EvaluationItem.objects.get(id=item_id, template=self.template)
                    
                    # 创建评分项记录
                    item_record = EvaluationItemRecord.objects.create(
                        evaluation_record=record,
                        evaluation_item=item,
                        score=item_data.get('score', 0),
                        tier_selected=item_data.get('tier'),
                        text_content=item_data.get('text', ''),
                        created_by=self.evaluator.username
                    )
                    
                    item_records.append(item_record)
                    total_score += float(item_data.get('score', 0))
                    
                except Exception as e:
                    logger.error(f"处理评分项 {item_id} 失败: {str(e)}")
            
            # 更新记录（但不改变关系状态）
            weighted_score = total_score * float(self.relation.weight_factor)
            record.total_score = total_score
            record.weighted_score = weighted_score
            record.comment = evaluation_data.get('comment', '')
            record.updated_by = self.evaluator.username
            record.save()
            
            # 更新关系状态为草稿（如果之前是pending）
            if self.relation.status == 'pending':
                self.relation.status = 'draft'
                self.relation.updated_by = self.evaluator.username
                self.relation.save()
            
            return {
                'success': True,
                'message': '草稿保存成功',
                'total_score': total_score,
                'weighted_score': weighted_score,
                'items_saved': len(item_records)
            }
            
        except Exception as e:
            logger.error(f"保存评分草稿失败: {str(e)}")
            return {'success': False, 'error': f'保存失败: {str(e)}'}

    @transaction.atomic
    def submit_evaluation(self, evaluation_data: Dict) -> Dict:
        """
        提交考评结果
        
        Args:
            evaluation_data: 评分数据
            
        Returns:
            提交结果
        """
        try:
            # 验证提交权限
            if self.relation.evaluator != self.evaluator:
                return {'success': False, 'error': '无权限提交此考评'}
            
            if self.relation.status == 'completed':
                return {'success': False, 'error': '该考评已完成，无法重复提交'}
            
            if self.relation.batch.status != 'active':
                return {'success': False, 'error': '考评批次未激活或已结束'}
            
            # 创建或更新考评记录
            record, created = EvaluationRecord.objects.get_or_create(
                relation=self.relation,
                defaults={
                    'total_score': 0,
                    'weighted_score': 0,
                    'comment': '',
                    'created_by': self.evaluator.username
                }
            )
            
            # 处理评分项数据
            total_score = 0
            item_records = []
            
            for item_id, item_data in evaluation_data.get('items', {}).items():
                try:
                    from .models import EvaluationItem
                    item = EvaluationItem.objects.get(id=item_id, template=self.template)
                    
                    # 创建评分项记录
                    item_record = EvaluationItemRecord.objects.create(
                        evaluation_record=record,
                        evaluation_item=item,
                        score=item_data.get('score', 0),
                        tier_selected=item_data.get('tier'),
                        text_content=item_data.get('text', ''),
                        created_by=self.evaluator.username
                    )
                    
                    item_records.append(item_record)
                    total_score += float(item_data.get('score', 0))
                    
                except Exception as e:
                    logger.error(f"处理评分项 {item_id} 失败: {str(e)}")
            
            # 更新记录
            weighted_score = total_score * float(self.relation.weight_factor)
            record.total_score = total_score
            record.raw_score = total_score  # 原始得分
            record.completion_time = timezone.now()  # 设置完成时间，这会触发通知信号
            record.comments = evaluation_data.get('comment', '')
            record.updated_by = self.evaluator.username
            record.save()
            
            # 更新关系状态
            self.relation.status = 'completed'
            self.relation.updated_by = self.evaluator.username
            self.relation.save()
            
            # 记录审计日志
            AuditLog.objects.create(
                user=self.evaluator.username,
                action='submit_evaluation',
                target_model='evaluationrelation',
                target_id=self.relation.id,
                description=f'提交考评: {self.evaluator.name} 评价 {self.relation.evaluatee.name}',
                details={
                    'total_score': total_score,
                    'weighted_score': weighted_score,
                    'items_count': len(item_records)
                }
            )
            
            # 更新批次进度
            EvaluationProgressService.update_batch_progress(self.relation.batch)
            
            return {
                'success': True,
                'total_score': total_score,
                'weighted_score': weighted_score,
                'items_submitted': len(item_records)
            }
            
        except Exception as e:
            logger.error(f"提交考评失败: {str(e)}")
            return {'success': False, 'error': f'提交失败: {str(e)}'}