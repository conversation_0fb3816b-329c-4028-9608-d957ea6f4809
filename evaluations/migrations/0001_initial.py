# Generated by Django 5.2.4 on 2025-07-25 04:54

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('organizations', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='EvaluationBatch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='记录创建时间', verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='记录最后更新时间', verbose_name='更新时间')),
                ('created_by', models.CharField(blank=True, help_text='记录创建者', max_length=50, null=True, verbose_name='创建人')),
                ('updated_by', models.CharField(blank=True, help_text='记录最后更新者', max_length=50, null=True, verbose_name='更新人')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='软删除时间，为空表示未删除', null=True, verbose_name='删除时间')),
                ('deleted_by', models.CharField(blank=True, help_text='软删除操作者', max_length=50, null=True, verbose_name='删除人')),
                ('name', models.CharField(help_text='考评批次的名称', max_length=100, verbose_name='批次名称')),
                ('description', models.TextField(blank=True, help_text='考评批次的详细说明', verbose_name='批次描述')),
                ('start_date', models.DateTimeField(help_text='考评开始时间', verbose_name='开始时间')),
                ('end_date', models.DateTimeField(help_text='考评结束时间', verbose_name='结束时间')),
                ('status', models.CharField(choices=[('draft', '草稿'), ('active', '进行中'), ('completed', '已完成'), ('cancelled', '已取消')], default='draft', help_text='当前批次的状态', max_length=20, verbose_name='批次状态')),
                ('auto_assign', models.BooleanField(default=True, help_text='是否使用智能分配算法', verbose_name='自动分配')),
                ('allow_self_evaluation', models.BooleanField(default=False, help_text='是否允许员工进行自我评价', verbose_name='允许自评')),
                ('anonymous_results', models.BooleanField(default=True, help_text='评价结果是否对被评价者匿名', verbose_name='匿名结果')),
            ],
            options={
                'verbose_name': '考评批次',
                'verbose_name_plural': '考评批次',
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='EvaluationTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='记录创建时间', verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='记录最后更新时间', verbose_name='更新时间')),
                ('created_by', models.CharField(blank=True, help_text='记录创建者', max_length=50, null=True, verbose_name='创建人')),
                ('updated_by', models.CharField(blank=True, help_text='记录最后更新者', max_length=50, null=True, verbose_name='更新人')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='软删除时间，为空表示未删除', null=True, verbose_name='删除时间')),
                ('deleted_by', models.CharField(blank=True, help_text='软删除操作者', max_length=50, null=True, verbose_name='删除人')),
                ('name', models.CharField(help_text='考评模板的名称', max_length=100, verbose_name='模板名称')),
                ('description', models.TextField(blank=True, help_text='模板的详细描述和使用说明', verbose_name='模板描述')),
                ('template_type', models.CharField(choices=[('structured', '结构化评分'), ('open', '开放式评分'), ('mixed', '混合式评分')], default='structured', help_text='评分模式类型', max_length=20, verbose_name='模板类型')),
                ('is_default', models.BooleanField(default=False, help_text='系统默认使用的模板', verbose_name='是否默认模板')),
                ('is_active', models.BooleanField(default=True, help_text='模板是否可用', verbose_name='是否启用')),
                ('sort_order', models.PositiveIntegerField(default=0, help_text='模板显示顺序', verbose_name='排序')),
            ],
            options={
                'verbose_name': '考评模板',
                'verbose_name_plural': '考评模板',
                'ordering': ['sort_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='WeightingRule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='记录创建时间', verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='记录最后更新时间', verbose_name='更新时间')),
                ('created_by', models.CharField(blank=True, help_text='记录创建者', max_length=50, null=True, verbose_name='创建人')),
                ('updated_by', models.CharField(blank=True, help_text='记录最后更新者', max_length=50, null=True, verbose_name='更新人')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='软删除时间，为空表示未删除', null=True, verbose_name='删除时间')),
                ('deleted_by', models.CharField(blank=True, help_text='软删除操作者', max_length=50, null=True, verbose_name='删除人')),
                ('name', models.CharField(help_text='权重规则的名称', max_length=100, verbose_name='规则名称')),
                ('description', models.TextField(blank=True, help_text='规则的详细说明', verbose_name='规则描述')),
                ('condition_type', models.CharField(choices=[('department', '部门关系'), ('position_level', '职位层级'), ('relation_type', '评价关系'), ('default', '默认规则')], help_text='规则匹配的条件类型', max_length=20, verbose_name='条件类型')),
                ('condition_value', models.CharField(blank=True, help_text='具体的条件值', max_length=100, verbose_name='条件值')),
                ('relation_type', models.CharField(blank=True, choices=[('subordinate_to_superior', '下级评上级'), ('superior_to_subordinate', '上级评下级'), ('peer_to_peer', '同级互评'), ('cross_department', '跨部门评价')], help_text='评价者与被评价者的关系', max_length=30, verbose_name='关系类型')),
                ('weight_factor', models.DecimalField(decimal_places=2, default=1.0, help_text='该规则的权重系数', max_digits=3, validators=[django.core.validators.MinValueValidator(0.1), django.core.validators.MaxValueValidator(5.0)], verbose_name='权重系数')),
                ('priority', models.PositiveIntegerField(default=1, help_text='规则匹配的优先级（数字越大优先级越高）', verbose_name='优先级')),
                ('is_active', models.BooleanField(default=True, help_text='规则是否生效', verbose_name='是否启用')),
            ],
            options={
                'verbose_name': '权重规则',
                'verbose_name_plural': '权重规则',
                'ordering': ['-priority', 'name'],
            },
        ),
        migrations.CreateModel(
            name='EvaluationRelation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='记录创建时间', verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='记录最后更新时间', verbose_name='更新时间')),
                ('created_by', models.CharField(blank=True, help_text='记录创建者', max_length=50, null=True, verbose_name='创建人')),
                ('updated_by', models.CharField(blank=True, help_text='记录最后更新者', max_length=50, null=True, verbose_name='更新人')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='软删除时间，为空表示未删除', null=True, verbose_name='删除时间')),
                ('deleted_by', models.CharField(blank=True, help_text='软删除操作者', max_length=50, null=True, verbose_name='删除人')),
                ('weight_factor', models.DecimalField(decimal_places=2, default=1.0, help_text='该关系的权重系数', max_digits=3, validators=[django.core.validators.MinValueValidator(0.1), django.core.validators.MaxValueValidator(5.0)], verbose_name='权重系数')),
                ('is_assigned', models.BooleanField(default=False, help_text='关系是否已分配给评价者', verbose_name='是否已分配')),
                ('assignment_method', models.CharField(choices=[('auto', '自动分配'), ('manual', '手动分配')], default='auto', help_text='关系的分配方式', max_length=20, verbose_name='分配方式')),
                ('batch', models.ForeignKey(help_text='考评关系所属的批次', on_delete=django.db.models.deletion.CASCADE, to='evaluations.evaluationbatch', verbose_name='所属批次')),
                ('evaluatee', models.ForeignKey(help_text='接受评价的员工', on_delete=django.db.models.deletion.CASCADE, related_name='evaluatee_relations', to='organizations.staff', verbose_name='被评价者')),
                ('evaluator', models.ForeignKey(help_text='执行评价的员工', on_delete=django.db.models.deletion.CASCADE, related_name='evaluator_relations', to='organizations.staff', verbose_name='评价者')),
                ('template', models.ForeignKey(help_text='该关系使用的考评模板', on_delete=django.db.models.deletion.CASCADE, to='evaluations.evaluationtemplate', verbose_name='使用模板')),
            ],
            options={
                'verbose_name': '考评关系',
                'verbose_name_plural': '考评关系',
                'ordering': ['batch', 'evaluator', 'evaluatee'],
                'unique_together': {('batch', 'evaluator', 'evaluatee')},
            },
        ),
        migrations.CreateModel(
            name='EvaluationRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='记录创建时间', verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='记录最后更新时间', verbose_name='更新时间')),
                ('created_by', models.CharField(blank=True, help_text='记录创建者', max_length=50, null=True, verbose_name='创建人')),
                ('updated_by', models.CharField(blank=True, help_text='记录最后更新者', max_length=50, null=True, verbose_name='更新人')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='软删除时间，为空表示未删除', null=True, verbose_name='删除时间')),
                ('deleted_by', models.CharField(blank=True, help_text='软删除操作者', max_length=50, null=True, verbose_name='删除人')),
                ('total_score', models.DecimalField(blank=True, decimal_places=2, help_text='加权计算后的总分', max_digits=6, null=True, verbose_name='总得分')),
                ('raw_score', models.DecimalField(blank=True, decimal_places=2, help_text='未加权的原始总分', max_digits=6, null=True, verbose_name='原始得分')),
                ('completion_time', models.DateTimeField(help_text='评价完成的时间', verbose_name='完成时间')),
                ('time_spent', models.PositiveIntegerField(blank=True, help_text='完成评价花费的时间', null=True, verbose_name='用时（秒）')),
                ('comments', models.TextField(blank=True, help_text='对被评价者的综合评价意见', verbose_name='综合评价')),
                ('ip_address', models.GenericIPAddressField(blank=True, help_text='提交评价时的IP地址', null=True, verbose_name='IP地址')),
                ('relation', models.OneToOneField(help_text='对应的考评关系', on_delete=django.db.models.deletion.CASCADE, to='evaluations.evaluationrelation', verbose_name='考评关系')),
            ],
            options={
                'verbose_name': '考评记录',
                'verbose_name_plural': '考评记录',
                'ordering': ['-completion_time'],
            },
        ),
        migrations.CreateModel(
            name='EvaluationItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='记录创建时间', verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='记录最后更新时间', verbose_name='更新时间')),
                ('created_by', models.CharField(blank=True, help_text='记录创建者', max_length=50, null=True, verbose_name='创建人')),
                ('updated_by', models.CharField(blank=True, help_text='记录最后更新者', max_length=50, null=True, verbose_name='更新人')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='软删除时间，为空表示未删除', null=True, verbose_name='删除时间')),
                ('deleted_by', models.CharField(blank=True, help_text='软删除操作者', max_length=50, null=True, verbose_name='删除人')),
                ('name', models.CharField(help_text='评分项的名称', max_length=100, verbose_name='评分项名称')),
                ('description', models.TextField(blank=True, help_text='评分项的详细说明和标准', verbose_name='评分说明')),
                ('scoring_mode', models.CharField(choices=[('score', '数值评分'), ('level', '等级评分'), ('text', '文本评价')], default='score', help_text='该项目的评分方式', max_length=20, verbose_name='评分模式')),
                ('max_score', models.PositiveIntegerField(default=10, help_text='该项目的最高得分', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(100)], verbose_name='最高分')),
                ('weight', models.DecimalField(decimal_places=2, default=1.0, help_text='该项目在总分中的权重', max_digits=3, validators=[django.core.validators.MinValueValidator(0.1), django.core.validators.MaxValueValidator(5.0)], verbose_name='权重')),
                ('is_required', models.BooleanField(default=True, help_text='评分时是否必须填写', verbose_name='是否必填')),
                ('is_active', models.BooleanField(default=True, help_text='评分项是否可用', verbose_name='是否启用')),
                ('sort_order', models.PositiveIntegerField(default=0, help_text='评分项显示顺序', verbose_name='排序')),
                ('template', models.ForeignKey(help_text='评分项所属的模板', on_delete=django.db.models.deletion.CASCADE, to='evaluations.evaluationtemplate', verbose_name='所属模板')),
            ],
            options={
                'verbose_name': '评分项',
                'verbose_name_plural': '评分项',
                'ordering': ['template', 'sort_order'],
                'unique_together': {('template', 'name')},
            },
        ),
        migrations.AddField(
            model_name='evaluationbatch',
            name='default_template',
            field=models.ForeignKey(help_text='该批次默认使用的考评模板', on_delete=django.db.models.deletion.CASCADE, to='evaluations.evaluationtemplate', verbose_name='默认模板'),
        ),
        migrations.CreateModel(
            name='ScoringTier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='记录创建时间', verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='记录最后更新时间', verbose_name='更新时间')),
                ('created_by', models.CharField(blank=True, help_text='记录创建者', max_length=50, null=True, verbose_name='创建人')),
                ('updated_by', models.CharField(blank=True, help_text='记录最后更新者', max_length=50, null=True, verbose_name='更新人')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='软删除时间，为空表示未删除', null=True, verbose_name='删除时间')),
                ('deleted_by', models.CharField(blank=True, help_text='软删除操作者', max_length=50, null=True, verbose_name='删除人')),
                ('tier_name', models.CharField(help_text='等级的名称（如：优秀、良好等）', max_length=50, verbose_name='等级名称')),
                ('tier_value', models.PositiveIntegerField(help_text='该等级对应的分值', verbose_name='等级分值')),
                ('description', models.TextField(blank=True, help_text='该等级的详细描述和标准', verbose_name='等级描述')),
                ('sort_order', models.PositiveIntegerField(default=0, help_text='等级显示顺序', verbose_name='排序')),
                ('item', models.ForeignKey(help_text='等级所属的评分项', on_delete=django.db.models.deletion.CASCADE, to='evaluations.evaluationitem', verbose_name='所属评分项')),
            ],
            options={
                'verbose_name': '评分等级',
                'verbose_name_plural': '评分等级',
                'ordering': ['item', 'sort_order'],
                'unique_together': {('item', 'tier_name')},
            },
        ),
        migrations.CreateModel(
            name='EvaluationItemRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='记录创建时间', verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='记录最后更新时间', verbose_name='更新时间')),
                ('created_by', models.CharField(blank=True, help_text='记录创建者', max_length=50, null=True, verbose_name='创建人')),
                ('updated_by', models.CharField(blank=True, help_text='记录最后更新者', max_length=50, null=True, verbose_name='更新人')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='软删除时间，为空表示未删除', null=True, verbose_name='删除时间')),
                ('deleted_by', models.CharField(blank=True, help_text='软删除操作者', max_length=50, null=True, verbose_name='删除人')),
                ('score', models.DecimalField(decimal_places=2, help_text='该项目的得分', max_digits=5, validators=[django.core.validators.MinValueValidator(0)], verbose_name='得分')),
                ('text_value', models.TextField(blank=True, help_text='如果是文本评价，填写的内容', verbose_name='文本评价')),
                ('item', models.ForeignKey(help_text='对应的评分项', on_delete=django.db.models.deletion.CASCADE, to='evaluations.evaluationitem', verbose_name='评分项')),
                ('record', models.ForeignKey(help_text='所属的考评记录', on_delete=django.db.models.deletion.CASCADE, to='evaluations.evaluationrecord', verbose_name='考评记录')),
                ('tier', models.ForeignKey(blank=True, help_text='如果是等级评分，选择的等级', null=True, on_delete=django.db.models.deletion.CASCADE, to='evaluations.scoringtier', verbose_name='选择等级')),
            ],
            options={
                'verbose_name': '评分项记录',
                'verbose_name_plural': '评分项记录',
                'ordering': ['record', 'item__sort_order'],
                'unique_together': {('record', 'item')},
            },
        ),
    ]
