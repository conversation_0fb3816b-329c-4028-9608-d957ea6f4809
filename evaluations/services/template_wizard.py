# -*- coding: utf-8 -*-
"""
模板创建向导服务
提供预设模板方案，简化模板创建过程
"""

from django.db import transaction
from typing import Dict, List, Optional
import logging

from ..models import EvaluationTemplate, EvaluationItem, ScoringTier
from organizations.models import Staff

logger = logging.getLogger(__name__)


class TemplateWizardService:
    """模板创建向导服务"""
    
    # 预设模板方案
    TEMPLATE_PRESETS = {
        'simple_numeric': {
            'name': '简单数值评分',
            'description': '适合快速评分，每项0-10分制',
            'icon': '📊',
            'difficulty': '简单',
            'time_estimate': '2-3分钟',
            'suitable_for': '日常考评、快速反馈、简单评估',
            'template_type': 'structured',
            'items': [
                {
                    'name': '工作态度',
                    'description': '工作积极性、责任心、主动性',
                    'scoring_mode': 'score',
                    'max_score': 10,
                    'weight': 1.0,
                    'is_required': True
                },
                {
                    'name': '工作能力',
                    'description': '专业技能、学习能力、解决问题能力',
                    'scoring_mode': 'score',
                    'max_score': 10,
                    'weight': 1.2,
                    'is_required': True
                },
                {
                    'name': '工作成果',
                    'description': '工作质量、效率、完成度',
                    'scoring_mode': 'score',
                    'max_score': 10,
                    'weight': 1.3,
                    'is_required': True
                },
                {
                    'name': '团队协作',
                    'description': '沟通能力、团队精神、协作配合',
                    'scoring_mode': 'score',
                    'max_score': 10,
                    'weight': 1.0,
                    'is_required': True
                }
            ]
        },
        'detailed_tier': {
            'name': '详细等级评分',
            'description': '使用等级标准进行细致评价',
            'icon': '⭐',
            'difficulty': '中等',
            'time_estimate': '5-8分钟',
            'suitable_for': '正式考评、晋升评估、年度考核',
            'template_type': 'structured',
            'items': [
                {
                    'name': '专业能力',
                    'description': '专业知识掌握程度和应用能力',
                    'scoring_mode': 'level',
                    'weight': 1.5,
                    'is_required': True,
                    'tiers': [
                        {'name': '优秀', 'value': 10, 'desc': '专业技能突出，能够指导他人'},
                        {'name': '良好', 'value': 8, 'desc': '专业技能熟练，能够独立完成工作'},
                        {'name': '一般', 'value': 6, 'desc': '专业技能基本满足要求'},
                        {'name': '待提升', 'value': 4, 'desc': '专业技能有待加强'}
                    ]
                },
                {
                    'name': '工作绩效',
                    'description': '工作目标达成情况和质量水平',
                    'scoring_mode': 'level',
                    'weight': 1.3,
                    'is_required': True,
                    'tiers': [
                        {'name': '超出预期', 'value': 10, 'desc': '超额完成目标，质量优秀'},
                        {'name': '达到预期', 'value': 8, 'desc': '按时完成目标，质量良好'},
                        {'name': '基本达标', 'value': 6, 'desc': '基本完成目标，质量一般'},
                        {'name': '未达预期', 'value': 4, 'desc': '未能完成预期目标'}
                    ]
                },
                {
                    'name': '创新能力',
                    'description': '创新思维和改进工作方法的能力',
                    'scoring_mode': 'level',
                    'weight': 1.0,
                    'is_required': False,
                    'tiers': [
                        {'name': '创新突出', 'value': 10, 'desc': '有重要创新成果或改进'},
                        {'name': '有所创新', 'value': 8, 'desc': '在工作中有创新想法'},
                        {'name': '按规执行', 'value': 6, 'desc': '按照既定方式工作'},
                        {'name': '缺乏创新', 'value': 4, 'desc': '工作方式较为固化'}
                    ]
                }
            ]
        },
        'comprehensive_mixed': {
            'name': '综合评价模式',
            'description': '数值+等级+文本，全面深入评价',
            'icon': '🎯',
            'difficulty': '复杂',
            'time_estimate': '10-15分钟',
            'suitable_for': '重要岗位评估、管理层考核、全面评价',
            'template_type': 'mixed',
            'items': [
                {
                    'name': '量化指标完成情况',
                    'description': '可量化的工作指标达成情况',
                    'scoring_mode': 'score',
                    'max_score': 10,
                    'weight': 1.5,
                    'is_required': True
                },
                {
                    'name': '能力素质等级',
                    'description': '综合能力素质评价',
                    'scoring_mode': 'level',
                    'weight': 1.2,
                    'is_required': True,
                    'tiers': [
                        {'name': '卓越', 'value': 10, 'desc': '各方面能力都很突出'},
                        {'name': '优秀', 'value': 8, 'desc': '大部分能力表现优秀'},
                        {'name': '良好', 'value': 6, 'desc': '能力表现符合要求'},
                        {'name': '一般', 'value': 4, 'desc': '能力有待提升'}
                    ]
                },
                {
                    'name': '改进建议和发展方向',
                    'description': '对被评价者的具体建议和发展建议',
                    'scoring_mode': 'text',
                    'weight': 0.8,
                    'is_required': False
                }
            ]
        },
        'leadership_assessment': {
            'name': '领导力评估',
            'description': '专门针对管理层的领导力评价',
            'icon': '👑',
            'difficulty': '中等',
            'time_estimate': '8-10分钟',
            'suitable_for': '管理层考核、领导力发展、晋升评估',
            'template_type': 'structured',
            'items': [
                {
                    'name': '战略思维',
                    'description': '战略规划和前瞻性思考能力',
                    'scoring_mode': 'level',
                    'weight': 1.3,
                    'is_required': True,
                    'tiers': [
                        {'name': '战略卓越', 'value': 10, 'desc': '具有出色的战略眼光'},
                        {'name': '战略清晰', 'value': 8, 'desc': '能够制定清晰的战略'},
                        {'name': '战略基本', 'value': 6, 'desc': '基本的战略思维'},
                        {'name': '战略不足', 'value': 4, 'desc': '战略思维有待提升'}
                    ]
                },
                {
                    'name': '团队管理',
                    'description': '团队建设和人员管理能力',
                    'scoring_mode': 'level',
                    'weight': 1.4,
                    'is_required': True,
                    'tiers': [
                        {'name': '管理卓越', 'value': 10, 'desc': '团队凝聚力强，管理效果显著'},
                        {'name': '管理良好', 'value': 8, 'desc': '团队管理有序，效果良好'},
                        {'name': '管理一般', 'value': 6, 'desc': '基本的团队管理能力'},
                        {'name': '管理不足', 'value': 4, 'desc': '团队管理能力需要提升'}
                    ]
                },
                {
                    'name': '决策能力',
                    'description': '分析问题和做出决策的能力',
                    'scoring_mode': 'level',
                    'weight': 1.2,
                    'is_required': True,
                    'tiers': [
                        {'name': '决策英明', 'value': 10, 'desc': '决策准确及时，效果显著'},
                        {'name': '决策合理', 'value': 8, 'desc': '决策基本正确，执行有效'},
                        {'name': '决策一般', 'value': 6, 'desc': '决策能力符合基本要求'},
                        {'name': '决策不足', 'value': 4, 'desc': '决策能力有待加强'}
                    ]
                },
                {
                    'name': '沟通协调',
                    'description': '内外部沟通和协调能力',
                    'scoring_mode': 'score',
                    'max_score': 10,
                    'weight': 1.0,
                    'is_required': True
                }
            ]
        }
    }
    
    def __init__(self, operator: Staff):
        """
        初始化模板向导服务
        
        Args:
            operator: 操作人员
        """
        self.operator = operator
    
    @classmethod
    def get_preset_info(cls, preset_name: str) -> Optional[Dict]:
        """获取预设模板信息"""
        return cls.TEMPLATE_PRESETS.get(preset_name)
    
    @classmethod
    def list_all_presets(cls) -> Dict:
        """列出所有可用的预设模板"""
        return cls.TEMPLATE_PRESETS
    
    @classmethod
    def get_presets_by_difficulty(cls, difficulty: str) -> Dict:
        """按难度筛选预设模板"""
        filtered = {}
        for name, preset in cls.TEMPLATE_PRESETS.items():
            if preset.get('difficulty', '').lower() == difficulty.lower():
                filtered[name] = preset
        return filtered
    
    @transaction.atomic
    def create_from_preset(self, preset_name: str, template_name: str, 
                          custom_description: Optional[str] = None) -> Dict:
        """
        从预设创建模板
        
        Args:
            preset_name: 预设名称
            template_name: 模板名称
            custom_description: 自定义描述
            
        Returns:
            创建结果
        """
        try:
            if preset_name not in self.TEMPLATE_PRESETS:
                return {'success': False, 'error': f'未知的模板预设: {preset_name}'}
            
            preset = self.TEMPLATE_PRESETS[preset_name]
            
            # 创建模板
            template = EvaluationTemplate.objects.create(
                name=template_name,
                description=custom_description or preset['description'],
                template_type=preset['template_type'],
                created_by=self.operator.username
            )
            
            # 创建评分项
            items_created = 0
            for i, item_data in enumerate(preset['items']):
                item = EvaluationItem.objects.create(
                    template=template,
                    name=item_data['name'],
                    description=item_data.get('description', ''),
                    scoring_mode=item_data['scoring_mode'],
                    max_score=item_data.get('max_score', 10),
                    weight=item_data.get('weight', 1.0),
                    is_required=item_data.get('is_required', True),
                    sort_order=i,
                    created_by=self.operator.username
                )
                items_created += 1
                
                # 创建等级（如果是等级评分）
                if item_data['scoring_mode'] == 'level' and 'tiers' in item_data:
                    tiers_created = 0
                    for j, tier_data in enumerate(item_data['tiers']):
                        ScoringTier.objects.create(
                            item=item,
                            tier_name=tier_data['name'],
                            tier_value=tier_data['value'],
                            description=tier_data.get('desc', ''),
                            sort_order=j,
                            created_by=self.operator.username
                        )
                        tiers_created += 1
                    
                    logger.info(f"为评分项 {item.name} 创建了 {tiers_created} 个等级")
            
            # 记录创建日志
            self._log_template_creation(template, preset_name, items_created)
            
            return {
                'success': True,
                'template': template,
                'preset_name': preset_name,
                'items_created': items_created,
                'template_id': template.id
            }
            
        except Exception as e:
            logger.error(f"从预设创建模板失败: {str(e)}")
            return {'success': False, 'error': f'创建失败: {str(e)}'}
    
    def preview_preset(self, preset_name: str) -> Dict:
        """
        预览预设模板
        
        Args:
            preset_name: 预设名称
            
        Returns:
            预览数据
        """
        if preset_name not in self.TEMPLATE_PRESETS:
            return {'error': '未知的模板预设'}
        
        preset = self.TEMPLATE_PRESETS[preset_name]
        
        # 计算预览统计
        total_items = len(preset['items'])
        scoring_modes = {}
        total_weight = 0
        
        for item in preset['items']:
            mode = item['scoring_mode']
            scoring_modes[mode] = scoring_modes.get(mode, 0) + 1
            total_weight += item.get('weight', 1.0)
        
        return {
            'preset_info': preset,
            'statistics': {
                'total_items': total_items,
                'scoring_modes': scoring_modes,
                'total_weight': total_weight,
                'avg_weight': total_weight / total_items if total_items > 0 else 0
            }
        }
    
    def customize_preset(self, preset_name: str, customizations: Dict) -> Dict:
        """
        自定义预设模板
        
        Args:
            preset_name: 预设名称
            customizations: 自定义配置
            
        Returns:
            自定义后的模板配置
        """
        if preset_name not in self.TEMPLATE_PRESETS:
            return {'error': '未知的模板预设'}
        
        preset = self.TEMPLATE_PRESETS[preset_name].copy()
        
        # 应用自定义配置
        if 'items' in customizations:
            for item_name, item_custom in customizations['items'].items():
                for item in preset['items']:
                    if item['name'] == item_name:
                        item.update(item_custom)
                        break
        
        if 'template_info' in customizations:
            preset.update(customizations['template_info'])
        
        return preset
    
    def _log_template_creation(self, template: EvaluationTemplate, preset_name: str, items_created: int):
        """记录模板创建日志"""
        from common.models import AuditLog
        
        AuditLog.objects.create(
            user=self.operator.username,
            action='create_template_from_preset',
            target_model='evaluationtemplate',
            target_id=template.id,
            description=f'从预设创建模板: {preset_name}',
            details={
                'template_name': template.name,
                'preset_name': preset_name,
                'items_created': items_created,
                'operator': self.operator.name
            }
        )


class TemplateRecommendationService:
    """模板推荐服务"""
    
    @staticmethod
    def recommend_templates(organization_type: str, evaluation_purpose: str) -> List[str]:
        """
        根据组织类型和评价目的推荐模板
        
        Args:
            organization_type: 组织类型 (startup, enterprise, government, etc.)
            evaluation_purpose: 评价目的 (daily, annual, promotion, etc.)
            
        Returns:
            推荐的模板预设名称列表
        """
        recommendations = []
        
        # 基于组织类型的推荐
        if organization_type == 'startup':
            recommendations.extend(['simple_numeric', 'comprehensive_mixed'])
        elif organization_type == 'enterprise':
            recommendations.extend(['detailed_tier', 'leadership_assessment'])
        elif organization_type == 'government':
            recommendations.extend(['detailed_tier', 'comprehensive_mixed'])
        
        # 基于评价目的的推荐
        if evaluation_purpose == 'daily':
            recommendations.insert(0, 'simple_numeric')
        elif evaluation_purpose == 'annual':
            recommendations.insert(0, 'comprehensive_mixed')
        elif evaluation_purpose == 'promotion':
            recommendations.insert(0, 'leadership_assessment')
        
        # 去重并保持顺序
        seen = set()
        unique_recommendations = []
        for item in recommendations:
            if item not in seen:
                seen.add(item)
                unique_recommendations.append(item)
        
        return unique_recommendations[:3]  # 返回前3个推荐
