# -*- coding: utf-8 -*-
"""
企业考评评分系统 URL配置
定制化管理后台，不使用Django默认admin
"""

from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.shortcuts import redirect

def redirect_to_dashboard(request):
    """根页面重定向到管理后台仪表板"""
    return redirect('organizations:admin:dashboard')

urlpatterns = [
    # 根路径重定向
    path('', redirect_to_dashboard, name='home'),
    
    # 组织管理模块（包含仪表板、认证、部门、员工管理）
    path('', include('organizations.urls')),
    
    # 考评管理模块
    path('evaluations/', include('evaluations.urls')),
    
    # 报告分析模块  
    path('reports/', include('reports.urls')),
    
    # 站内通信模块
    path('communications/', include('communications.urls')),
    
    # 通用功能模块（认证、退出、工具）
    path('', include('common.urls')),
    
    # 异常处理测试路径（仅开发环境）
    path('test/', include('common.test_urls')),
    
    # 暂时保留admin路径（避免依赖错误）
    # path('admin/', admin.site.urls),  # 不使用Django admin
]

# 开发环境下处理媒体文件
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
