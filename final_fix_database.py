#!/usr/bin/env python
"""
最终修复数据库并创建测试用户
"""
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
django.setup()

from django.db import connection
from organizations.models import Staff, Department

def check_table_structure():
    """检查表结构"""
    try:
        with connection.cursor() as cursor:
            cursor.execute("DESCRIBE organizations_staff")
            columns = cursor.fetchall()
            
            print("📋 当前表结构:")
            for column in columns:
                field_name = column[0]
                field_type = column[1]
                nullable = column[2]
                default = column[4] if column[4] is not None else 'NULL'
                print(f"   {field_name:25} {field_type:15} {'NULL' if nullable == 'YES' else 'NOT NULL':8} Default: {default}")
            
            return columns
    except Exception as e:
        print(f"❌ 检查表结构失败: {e}")
        return None

def fix_all_field_defaults():
    """修复所有字段的默认值"""
    try:
        with connection.cursor() as cursor:
            print("\n🔧 修复字段默认值...")
            
            # 修复所有可能有问题的字段
            sql_commands = [
                # 安全字段
                "ALTER TABLE organizations_staff MODIFY COLUMN failed_login_attempts INT DEFAULT 0",
                "ALTER TABLE organizations_staff MODIFY COLUMN force_password_change BOOLEAN DEFAULT FALSE",
                "ALTER TABLE organizations_staff MODIFY COLUMN enable_two_factor BOOLEAN DEFAULT FALSE",
                
                # 基本字段 - 设置为允许NULL或有默认值
                "ALTER TABLE organizations_staff MODIFY COLUMN email VARCHAR(254) DEFAULT ''",
                "ALTER TABLE organizations_staff MODIFY COLUMN phone VARCHAR(20) DEFAULT ''",
                "ALTER TABLE organizations_staff MODIFY COLUMN anonymous_code VARCHAR(50) DEFAULT ''",
                
                # 时间字段 - 允许NULL
                "ALTER TABLE organizations_staff MODIFY COLUMN hire_date DATE NULL",
                "ALTER TABLE organizations_staff MODIFY COLUMN last_login DATETIME NULL",
                "ALTER TABLE organizations_staff MODIFY COLUMN locked_until DATETIME NULL",
                "ALTER TABLE organizations_staff MODIFY COLUMN last_failed_login DATETIME NULL",
                "ALTER TABLE organizations_staff MODIFY COLUMN password_changed_at DATETIME NULL",
                "ALTER TABLE organizations_staff MODIFY COLUMN last_token_refresh DATETIME NULL",
                "ALTER TABLE organizations_staff MODIFY COLUMN last_password_change_reminder DATETIME NULL",
            ]
            
            for sql in sql_commands:
                try:
                    cursor.execute(sql)
                    field_name = sql.split("COLUMN ")[1].split()[0]
                    print(f"✅ 修复字段: {field_name}")
                except Exception as e:
                    if "Unknown column" in str(e):
                        field_name = sql.split("COLUMN ")[1].split()[0]
                        print(f"ℹ️  字段不存在: {field_name}")
                    else:
                        print(f"❌ 修复失败: {e}")
            
            return True
    except Exception as e:
        print(f"❌ 修复字段失败: {e}")
        return False

def create_user_with_all_fields():
    """创建用户时包含所有字段"""
    try:
        with connection.cursor() as cursor:
            print("\n👤 创建测试用户...")
            
            # 1. 确保部门存在
            cursor.execute("""
                INSERT IGNORE INTO organizations_department 
                (dept_code, name, is_active, sort_order, created_at, updated_at) 
                VALUES ('TEST001', '测试部门', TRUE, 1, NOW(), NOW())
            """)
            
            # 2. 获取部门ID
            cursor.execute("SELECT id FROM organizations_department WHERE dept_code = 'TEST001'")
            result = cursor.fetchone()
            if not result:
                print("❌ 部门创建失败")
                return False
            dept_id = result[0]
            
            # 3. 删除现有测试用户
            cursor.execute("DELETE FROM organizations_staff WHERE username = 'testadmin'")
            
            # 4. 生成密码
            from django.contrib.auth.hashers import make_password
            hashed_password = make_password('test123456')
            
            # 5. 创建用户，明确指定所有字段
            cursor.execute("""
                INSERT INTO organizations_staff 
                (username, password, employee_no, name, department_id, role, is_active, 
                 email, phone, anonymous_code, hire_date, last_login,
                 failed_login_attempts, locked_until, last_failed_login, 
                 password_changed_at, force_password_change, enable_two_factor,
                 last_token_refresh, last_password_change_reminder,
                 created_at, updated_at) 
                VALUES (%s, %s, %s, %s, %s, %s, %s, 
                        %s, %s, %s, %s, %s,
                        %s, %s, %s,
                        %s, %s, %s,
                        %s, %s,
                        NOW(), NOW())
            """, [
                'testadmin',           # username
                hashed_password,       # password
                'EMP001',             # employee_no
                '测试管理员',          # name
                dept_id,              # department_id
                'super_admin',        # role
                True,                 # is_active
                '<EMAIL>',   # email
                '',                   # phone
                'TEST001XX1234',      # anonymous_code
                None,                 # hire_date
                None,                 # last_login
                0,                    # failed_login_attempts
                None,                 # locked_until
                None,                 # last_failed_login
                None,                 # password_changed_at
                False,                # force_password_change
                False,                # enable_two_factor
                None,                 # last_token_refresh
                None,                 # last_password_change_reminder
            ])
            
            print("✅ 测试用户创建成功!")
            return True
            
    except Exception as e:
        print(f"❌ 创建用户失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def verify_final_user():
    """最终验证用户"""
    try:
        staff = Staff.objects.get(username='testadmin')
        print("\n🔍 最终验证:")
        print(f"   用户名: {staff.username}")
        print(f"   员工编号: {staff.employee_no}")
        print(f"   姓名: {staff.name}")
        print(f"   角色: {staff.role}")
        print(f"   是否激活: {staff.is_active}")
        print(f"   部门: {staff.department.name}")
        print(f"   邮箱: {staff.email}")
        
        # 测试密码
        from django.contrib.auth.hashers import check_password
        is_valid = check_password('test123456', staff.password)
        print(f"   密码验证: {'✅ 正确' if is_valid else '❌ 错误'}")
        
        # 测试是否管理员
        print(f"   是否管理员: {'✅ 是' if staff.is_manager else '❌ 否'}")
        
        return True
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

if __name__ == '__main__':
    print("🚀 最终修复数据库和创建测试用户...")
    print("=" * 70)
    
    # 1. 检查表结构
    columns = check_table_structure()
    
    if columns:
        print("\n" + "=" * 70)
        # 2. 修复字段默认值
        fix_success = fix_all_field_defaults()
        
        if fix_success:
            print("\n" + "=" * 70)
            # 3. 创建测试用户
            user_success = create_user_with_all_fields()
            
            if user_success:
                print("\n" + "=" * 70)
                # 4. 验证用户
                verify_success = verify_final_user()
                
                if verify_success:
                    print("\n" + "=" * 70)
                    print("🎉 所有操作完成!")
                    print("\n📝 JWT测试登录信息:")
                    print("   URL: http://127.0.0.1:8000/admin/login/")
                    print("   用户名: testadmin")
                    print("   密码: test123456")
                    print("\n🔥 现在可以开始JWT功能测试了！")
                else:
                    print("\n❌ 用户验证失败")
            else:
                print("\n❌ 用户创建失败")
        else:
            print("\n❌ 字段修复失败")
    else:
        print("\n❌ 表结构检查失败")