# -*- coding: utf-8 -*-
"""
全局异常处理中间件
统一处理系统异常，提供用户友好的错误页面
"""

import logging
import traceback
import json
import time
from typing import Callable, Optional
from django.http import JsonResponse, HttpResponse, Http404
from django.shortcuts import render
from django.conf import settings
from django.core.exceptions import PermissionDenied, ValidationError
from django.db import DatabaseError, IntegrityError
from django.template import TemplateDoesNotExist
from django.utils.deprecation import MiddlewareMixin
from django.views.defaults import page_not_found, server_error, permission_denied, bad_request

from common.exceptions import (
    BaseBusinessException,
    AuthenticationException,
    PermissionException,
    ValidationException,
    EvaluationException,
    DataImportException,
    SecurityException,
    SystemMaintenanceException,
    TokenException,
    AccountLockedException
)

logger = logging.getLogger('common.security')

class GlobalExceptionMiddleware(MiddlewareMixin):
    """
    全局异常处理中间件
    
    功能特点：
    1. 统一异常处理和错误页面展示
    2. 区分API请求和页面请求的错误响应
    3. 详细的异常日志记录
    4. 敏感信息过滤
    5. 开发/生产环境差异化处理
    """
    
    def __init__(self, get_response: Callable):
        """初始化中间件"""
        self.get_response = get_response
        super().__init__(get_response)
    
    def process_exception(self, request, exception) -> Optional[HttpResponse]:
        """
        处理视图中抛出的异常
        
        Args:
            request: HTTP请求对象
            exception: 抛出的异常
            
        Returns:
            HTTP响应对象或None（让Django默认处理）
        """
        try:
            # 记录异常信息
            self._log_exception(request, exception)
            
            # 判断是否为API请求
            is_api_request = self._is_api_request(request)
            
            # 处理不同类型的异常
            if isinstance(exception, BaseBusinessException):
                return self._handle_business_exception(request, exception, is_api_request)
            elif isinstance(exception, AuthenticationException):
                return self._handle_authentication_exception(request, exception, is_api_request)
            elif isinstance(exception, PermissionDenied):
                return self._handle_permission_denied(request, exception, is_api_request)
            elif isinstance(exception, Http404):
                return self._handle_not_found(request, exception, is_api_request)
            elif isinstance(exception, ValidationError):
                return self._handle_validation_error(request, exception, is_api_request)
            elif isinstance(exception, DatabaseError):
                return self._handle_database_error(request, exception, is_api_request)
            elif isinstance(exception, Exception):
                return self._handle_generic_exception(request, exception, is_api_request)
            
        except Exception as middleware_error:
            # 中间件本身出错时的处理
            logger.critical(f"异常处理中间件发生错误: {str(middleware_error)}\n{traceback.format_exc()}")
            
            if self._is_api_request(request):
                return JsonResponse({
                    'success': False,
                    'message': '系统错误，请稍后重试',
                    'code': 'MIDDLEWARE_ERROR'
                }, status=500)
            else:
                return self._render_error_page(request, 500, '系统错误', '服务暂时不可用，请稍后重试')
        
        return None
    
    def _is_api_request(self, request) -> bool:
        """
        判断是否为API请求
        
        Args:
            request: HTTP请求对象
            
        Returns:
            是否为API请求
        """
        # 检查Accept头
        accept_header = request.META.get('HTTP_ACCEPT', '')
        if 'application/json' in accept_header:
            return True
        
        # 检查Content-Type头
        content_type = request.META.get('CONTENT_TYPE', '')
        if 'application/json' in content_type:
            return True
        
        # 检查URL路径
        path = request.path_info
        if path.startswith('/api/') or '/api/' in path:
            return True
        
        # 检查AJAX请求
        if request.META.get('HTTP_X_REQUESTED_WITH') == 'XMLHttpRequest':
            return True
        
        return False
    
    def _log_exception(self, request, exception):
        """
        记录异常信息到日志
        
        Args:
            request: HTTP请求对象
            exception: 异常对象
        """
        try:
            # 获取用户信息
            user_info = 'Anonymous'
            if hasattr(request, 'user') and request.user.is_authenticated:
                user_info = f"User({request.user.id})"
            elif hasattr(request, 'staff') and request.staff:
                user_info = f"Staff({request.staff.id})"
            
            # 获取请求信息
            request_info = {
                'method': request.method,
                'path': request.path_info,
                'user': user_info,
                'ip': self._get_client_ip(request),
                'user_agent': request.META.get('HTTP_USER_AGENT', '')[:200]
            }
            
            # 异常详情
            exception_info = {
                'type': exception.__class__.__name__,
                'message': str(exception),
                'traceback': traceback.format_exc() if settings.DEBUG else None
            }
            
            # 根据异常类型选择日志级别
            if isinstance(exception, (BaseBusinessException, ValidationError)):
                logger.warning(f"业务异常: {exception_info['message']}", extra={
                    'request_info': request_info,
                    'exception_info': exception_info
                })
            elif isinstance(exception, (PermissionDenied, AuthenticationException)):
                logger.warning(f"权限异常: {exception_info['message']}", extra={
                    'request_info': request_info,
                    'exception_info': exception_info
                })
            elif isinstance(exception, Http404):
                logger.info(f"404错误: {request_info['path']}", extra={
                    'request_info': request_info
                })
            else:
                logger.error(f"系统异常: {exception_info['message']}", extra={
                    'request_info': request_info,
                    'exception_info': exception_info
                })
                
        except Exception as log_error:
            # 日志记录失败时的fallback
            print(f"日志记录失败: {str(log_error)}")
    
    def _get_client_ip(self, request) -> str:
        """
        获取客户端真实IP地址
        
        Args:
            request: HTTP请求对象
            
        Returns:
            客户端IP地址
        """
        # 优先使用代理头
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            return x_forwarded_for.split(',')[0].strip()
        
        # 其他代理头
        x_real_ip = request.META.get('HTTP_X_REAL_IP')
        if x_real_ip:
            return x_real_ip
        
        # 直连IP
        return request.META.get('REMOTE_ADDR', 'unknown')
    
    def _handle_business_exception(self, request, exception: BaseBusinessException, is_api: bool) -> HttpResponse:
        """
        处理业务异常
        
        Args:
            request: HTTP请求对象
            exception: 业务异常
            is_api: 是否为API请求
            
        Returns:
            HTTP响应对象
        """
        if is_api:
            return JsonResponse({
                'success': False,
                'message': exception.message,
                'code': exception.code,
                'details': exception.details
            }, status=400)
        else:
            return self._render_error_page(
                request, 
                400, 
                '业务错误', 
                exception.message,
                exception.details
            )
    
    def _handle_authentication_exception(self, request, exception: AuthenticationException, is_api: bool) -> HttpResponse:
        """
        处理认证异常
        
        Args:
            request: HTTP请求对象
            exception: 认证异常
            is_api: 是否为API请求
            
        Returns:
            HTTP响应对象
        """
        if is_api:
            return JsonResponse({
                'success': False,
                'message': '认证失败，请重新登录',
                'code': 'AUTHENTICATION_FAILED',
                'redirect_url': '/login/'
            }, status=401)
        else:
            # 重定向到登录页面
            from django.shortcuts import redirect
            return redirect('/login/')
    
    def _handle_permission_denied(self, request, exception, is_api: bool) -> HttpResponse:
        """
        处理权限拒绝异常
        
        Args:
            request: HTTP请求对象
            exception: 权限异常
            is_api: 是否为API请求
            
        Returns:
            HTTP响应对象
        """
        if is_api:
            return JsonResponse({
                'success': False,
                'message': '权限不足，无法访问此资源',
                'code': 'PERMISSION_DENIED'
            }, status=403)
        else:
            return self._render_error_page(
                request, 
                403, 
                '权限不足', 
                '您没有权限访问此页面，请联系管理员'
            )
    
    def _handle_not_found(self, request, exception, is_api: bool) -> HttpResponse:
        """
        处理404异常
        
        Args:
            request: HTTP请求对象
            exception: 404异常
            is_api: 是否为API请求
            
        Returns:
            HTTP响应对象
        """
        if is_api:
            return JsonResponse({
                'success': False,
                'message': '请求的资源不存在',
                'code': 'NOT_FOUND'
            }, status=404)
        else:
            return self._render_error_page(
                request, 
                404, 
                '页面不存在', 
                '您访问的页面不存在，可能已被删除或移动'
            )
    
    def _handle_validation_error(self, request, exception: ValidationError, is_api: bool) -> HttpResponse:
        """
        处理验证错误
        
        Args:
            request: HTTP请求对象
            exception: 验证异常
            is_api: 是否为API请求
            
        Returns:
            HTTP响应对象
        """
        # 提取验证错误信息
        if hasattr(exception, 'message_dict'):
            errors = exception.message_dict
        elif hasattr(exception, 'messages'):
            errors = {'non_field_errors': list(exception.messages)}
        else:
            errors = {'error': str(exception)}
        
        if is_api:
            return JsonResponse({
                'success': False,
                'message': '数据验证失败',
                'code': 'VALIDATION_ERROR',
                'errors': errors
            }, status=400)
        else:
            return self._render_error_page(
                request, 
                400, 
                '数据验证失败', 
                '提交的数据不符合要求，请检查后重试',
                {'validation_errors': errors}
            )
    
    def _handle_database_error(self, request, exception: DatabaseError, is_api: bool) -> HttpResponse:
        """
        处理数据库错误
        
        Args:
            request: HTTP请求对象
            exception: 数据库异常
            is_api: 是否为API请求
            
        Returns:
            HTTP响应对象
        """
        # 隐藏敏感的数据库错误信息
        user_message = '系统暂时不可用，请稍后重试'
        
        if isinstance(exception, IntegrityError):
            user_message = '数据操作失败，可能存在重复或关联数据'
        
        if is_api:
            return JsonResponse({
                'success': False,
                'message': user_message,
                'code': 'DATABASE_ERROR'
            }, status=500)
        else:
            return self._render_error_page(
                request, 
                500, 
                '系统错误', 
                user_message
            )
    
    def _handle_generic_exception(self, request, exception: Exception, is_api: bool) -> HttpResponse:
        """
        处理通用异常
        
        Args:
            request: HTTP请求对象
            exception: 通用异常
            is_api: 是否为API请求
            
        Returns:
            HTTP响应对象
        """
        if is_api:
            return JsonResponse({
                'success': False,
                'message': '系统内部错误，请稍后重试',
                'code': 'INTERNAL_ERROR',
                'debug_info': str(exception) if settings.DEBUG else None
            }, status=500)
        else:
            return self._render_error_page(
                request, 
                500, 
                '系统错误', 
                '系统遇到未知错误，请稍后重试'
            )
    
    def _render_error_page(self, request, status_code: int, title: str, message: str, context: dict = None) -> HttpResponse:
        """
        渲染错误页面
        
        Args:
            request: HTTP请求对象
            status_code: HTTP状态码
            title: 错误标题
            message: 错误信息
            context: 额外的上下文数据
            
        Returns:
            HTTP响应对象
        """
        try:
            error_context = {
                'status_code': status_code,
                'error_title': title,
                'error_message': message,
                'request_path': request.path_info,
                'debug': settings.DEBUG
            }
            
            if context:
                error_context.update(context)
            
            # 尝试渲染自定义错误页面
            response = render(request, 'common/error.html', error_context, status=status_code)
            return response
            
        except TemplateDoesNotExist:
            # 如果自定义模板不存在，使用Django默认页面
            logger.warning("自定义错误页面模板不存在，使用Django默认页面")
            
            if status_code == 404:
                return page_not_found(request, exception=None)
            elif status_code == 403:
                return permission_denied(request, exception=None)
            elif status_code == 400:
                return bad_request(request, exception=None)
            else:
                return server_error(request)
        
        except Exception as render_error:
            # 渲染错误页面失败的fallback
            logger.error(f"渲染错误页面失败: {str(render_error)}")
            
            return HttpResponse(
                f"""
                <html>
                <head><title>系统错误</title></head>
                <body>
                    <h1>{title}</h1>
                    <p>{message}</p>
                    <p>错误代码: {status_code}</p>
                </body>
                </html>
                """,
                status=status_code,
                content_type='text/html'
            )


class SecurityEventMiddleware(MiddlewareMixin):
    """
    安全事件监控中间件
    记录和监控安全相关的异常事件
    """
    
    def __init__(self, get_response: Callable):
        self.get_response = get_response
        super().__init__(get_response)
    
    def process_exception(self, request, exception) -> Optional[HttpResponse]:
        """
        监控安全相关异常
        
        Args:
            request: HTTP请求对象
            exception: 异常对象
            
        Returns:
            None（不处理响应，只记录事件）
        """
        try:
            # 只处理安全相关异常
            if isinstance(exception, (SecurityException, AuthenticationException, PermissionException, AccountLockedException)):
                self._record_security_event(request, exception)
        except Exception as e:
            logger.error(f"安全事件记录失败: {str(e)}")
        
        return None  # 不处理响应，让其他中间件处理
    
    def _record_security_event(self, request, exception):
        """
        记录安全事件
        
        Args:
            request: HTTP请求对象
            exception: 安全异常
        """
        try:
            from django.core.cache import cache
            
            # 构建安全事件记录
            event = {
                'timestamp': int(time.time()),
                'event_type': exception.__class__.__name__,
                'message': str(exception),
                'ip_address': self._get_client_ip(request),
                'user_agent': request.META.get('HTTP_USER_AGENT', '')[:200],
                'path': request.path_info,
                'method': request.method,
                'user_id': getattr(request, 'user', {}).get('id') if hasattr(request, 'user') else None,
                'staff_id': getattr(request, 'staff', {}).get('id') if hasattr(request, 'staff') else None
            }
            
            # 缓存安全事件（用于监控和告警）
            cache_key = f"security_event_{int(time.time())}"
            cache.set(cache_key, event, timeout=86400)  # 24小时
            
            # 记录到安全日志
            logger.warning(f"安全事件: {event['event_type']} - {event['message']}", extra={'security_event': event})
            
        except Exception as e:
            logger.error(f"安全事件记录失败: {str(e)}")
    
    def _get_client_ip(self, request) -> str:
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            return x_forwarded_for.split(',')[0].strip()
        return request.META.get('REMOTE_ADDR', 'unknown')