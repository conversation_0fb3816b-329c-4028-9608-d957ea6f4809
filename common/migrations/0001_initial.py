# Generated by Django 5.2.4 on 2025-07-25 04:51

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='SystemSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='记录创建时间', verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='记录最后更新时间', verbose_name='更新时间')),
                ('created_by', models.CharField(blank=True, help_text='记录创建者', max_length=50, null=True, verbose_name='创建人')),
                ('updated_by', models.CharField(blank=True, help_text='记录最后更新者', max_length=50, null=True, verbose_name='更新人')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='软删除时间，为空表示未删除', null=True, verbose_name='删除时间')),
                ('deleted_by', models.CharField(blank=True, help_text='软删除操作者', max_length=50, null=True, verbose_name='删除人')),
                ('key', models.CharField(help_text='配置项的唯一标识', max_length=100, unique=True, verbose_name='配置键')),
                ('value', models.TextField(help_text='配置项的值', verbose_name='配置值')),
                ('value_type', models.CharField(choices=[('string', '字符串'), ('integer', '整数'), ('float', '浮点数'), ('boolean', '布尔值'), ('json', 'JSON对象')], default='string', help_text='配置值的数据类型', max_length=20, verbose_name='值类型')),
                ('description', models.CharField(blank=True, help_text='配置项的描述信息', max_length=200, verbose_name='描述')),
                ('is_active', models.BooleanField(default=True, help_text='配置项是否生效', verbose_name='是否启用')),
            ],
            options={
                'verbose_name': '系统设置',
                'verbose_name_plural': '系统设置',
                'ordering': ['key'],
            },
        ),
        migrations.CreateModel(
            name='AuditLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='记录创建时间', verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='记录最后更新时间', verbose_name='更新时间')),
                ('created_by', models.CharField(blank=True, help_text='记录创建者', max_length=50, null=True, verbose_name='创建人')),
                ('updated_by', models.CharField(blank=True, help_text='记录最后更新者', max_length=50, null=True, verbose_name='更新人')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='软删除时间，为空表示未删除', null=True, verbose_name='删除时间')),
                ('deleted_by', models.CharField(blank=True, help_text='软删除操作者', max_length=50, null=True, verbose_name='删除人')),
                ('user', models.CharField(help_text='执行操作的用户标识', max_length=50, verbose_name='操作用户')),
                ('action', models.CharField(choices=[('create', '创建'), ('update', '更新'), ('delete', '删除'), ('login', '登录'), ('logout', '注销'), ('view', '查看'), ('export', '导出'), ('import', '导入')], help_text='执行的操作类型', max_length=20, verbose_name='操作类型')),
                ('target_model', models.CharField(help_text='操作的目标数据模型', max_length=50, verbose_name='目标模型')),
                ('target_id', models.PositiveIntegerField(blank=True, help_text='操作的目标记录ID', null=True, verbose_name='目标ID')),
                ('description', models.TextField(help_text='详细的操作描述', verbose_name='操作描述')),
                ('ip_address', models.GenericIPAddressField(blank=True, help_text='操作者的IP地址', null=True, verbose_name='IP地址')),
                ('user_agent', models.TextField(blank=True, help_text='浏览器用户代理信息', verbose_name='用户代理')),
                ('extra_data', models.JSONField(blank=True, help_text='额外的操作数据（JSON格式）', null=True, verbose_name='扩展数据')),
            ],
            options={
                'verbose_name': '审计日志',
                'verbose_name_plural': '审计日志',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'action'], name='common_audi_user_82151e_idx'), models.Index(fields=['target_model', 'target_id'], name='common_audi_target__6724ab_idx'), models.Index(fields=['created_at'], name='common_audi_created_5f768f_idx')],
            },
        ),
    ]
