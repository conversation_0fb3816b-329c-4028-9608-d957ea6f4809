# Generated by Django 5.2.4 on 2025-07-29 02:04

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('common', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ImportHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='记录创建时间', verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='记录最后更新时间', verbose_name='更新时间')),
                ('created_by', models.CharField(blank=True, help_text='记录创建者', max_length=50, null=True, verbose_name='创建人')),
                ('updated_by', models.CharField(blank=True, help_text='记录最后更新者', max_length=50, null=True, verbose_name='更新人')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='软删除时间，为空表示未删除', null=True, verbose_name='删除时间')),
                ('deleted_by', models.CharField(blank=True, help_text='软删除操作者', max_length=50, null=True, verbose_name='删除人')),
                ('user_id', models.PositiveIntegerField(help_text='执行导入操作的用户ID', verbose_name='操作用户ID')),
                ('import_type', models.CharField(choices=[('staff', '员工信息'), ('department', '部门信息'), ('position', '职位信息'), ('evaluation_relation', '考评关系'), ('evaluation_batch', '考评批次'), ('evaluation_template', '考评模板')], help_text='导入数据的类型', max_length=30, verbose_name='导入类型')),
                ('filename', models.CharField(help_text='导入的Excel文件名', max_length=255, verbose_name='文件名')),
                ('total_count', models.PositiveIntegerField(default=0, help_text='Excel文件中的总记录数', verbose_name='总记录数')),
                ('success_count', models.PositiveIntegerField(default=0, help_text='成功导入的记录数', verbose_name='成功记录数')),
                ('error_count', models.PositiveIntegerField(default=0, help_text='导入失败的记录数', verbose_name='错误记录数')),
                ('status', models.CharField(choices=[('processing', '处理中'), ('completed', '已完成'), ('failed', '失败'), ('partial', '部分成功')], default='processing', help_text='导入操作的状态', max_length=20, verbose_name='导入状态')),
                ('error_details', models.JSONField(blank=True, help_text='导入过程中的错误信息（JSON格式）', null=True, verbose_name='错误详情')),
                ('file_path', models.CharField(blank=True, help_text='上传文件的存储路径', max_length=500, verbose_name='文件路径')),
                ('processing_time', models.FloatField(blank=True, help_text='导入处理耗时（秒）', null=True, verbose_name='处理耗时')),
            ],
            options={
                'verbose_name': '导入历史',
                'verbose_name_plural': '导入历史',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user_id', 'import_type'], name='common_impo_user_id_c5292a_idx'), models.Index(fields=['status', 'created_at'], name='common_impo_status_cfcc88_idx')],
            },
        ),
    ]
