# -*- coding: utf-8 -*-
"""
通用API工具模块
提供标准化的API响应格式和错误处理
"""

from django.http import JsonResponse
from django.core.paginator import Paginator
from functools import wraps
import logging
import traceback

logger = logging.getLogger(__name__)


class APIResponse:
    """
    标准化API响应类
    统一所有API端点的响应格式
    """
    
    @staticmethod
    def success(data=None, message='操作成功', meta=None, status=200):
        """
        成功响应
        
        Args:
            data: 响应数据
            message: 成功消息
            meta: 元数据（如分页信息）
            status: HTTP状态码
        """
        response_data = {
            'success': True,
            'message': message,
            'data': data,
            'meta': meta or {},
            'timestamp': _get_current_timestamp()
        }
        
        # 移除空值字段
        response_data = {k: v for k, v in response_data.items() if v is not None}
        
        return JsonResponse(response_data, status=status)
    
    @staticmethod
    def error(message='操作失败', code='OPERATION_FAILED', details=None, status=400):
        """
        错误响应
        
        Args:
            message: 错误消息
            code: 错误代码
            details: 详细错误信息
            status: HTTP状态码
        """
        response_data = {
            'success': False,
            'message': message,
            'error': {
                'code': code,
                'details': details
            },
            'timestamp': _get_current_timestamp()
        }
        
        # 移除空值字段
        if not details:
            response_data['error'] = {'code': code}
        
        return JsonResponse(response_data, status=status)
    
    @staticmethod
    def validation_error(errors, message='数据验证失败'):
        """
        数据验证错误响应
        
        Args:
            errors: 验证错误字典
            message: 错误消息
        """
        return APIResponse.error(
            message=message,
            code='VALIDATION_FAILED',
            details=errors,
            status=400
        )
    
    @staticmethod
    def permission_denied(message='权限不足'):
        """权限拒绝响应"""
        return APIResponse.error(
            message=message,
            code='PERMISSION_DENIED',
            status=403
        )
    
    @staticmethod
    def not_found(message='资源不存在'):
        """资源不存在响应"""
        return APIResponse.error(
            message=message,
            code='RESOURCE_NOT_FOUND',
            status=404
        )
    
    @staticmethod
    def server_error(message='服务器内部错误', details=None):
        """服务器错误响应"""
        return APIResponse.error(
            message=message,
            code='INTERNAL_SERVER_ERROR',
            details=details,
            status=500
        )


class PaginationHelper:
    """
    分页助手类
    提供标准化的分页处理
    """
    
    @staticmethod
    def paginate(queryset, page, page_size=20, max_page_size=100):
        """
        执行分页
        
        Args:
            queryset: 查询集
            page: 页码
            page_size: 每页大小
            max_page_size: 最大每页大小
        
        Returns:
            tuple: (page_obj, pagination_meta)
        """
        # 限制页面大小
        page_size = min(int(page_size), max_page_size)
        page = max(int(page), 1)
        
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)
        
        pagination_meta = {
            'current_page': page_obj.number,
            'total_pages': paginator.num_pages,
            'total_count': paginator.count,
            'page_size': page_size,
            'has_next': page_obj.has_next(),
            'has_previous': page_obj.has_previous(),
            'next_page': page_obj.next_page_number() if page_obj.has_next() else None,
            'previous_page': page_obj.previous_page_number() if page_obj.has_previous() else None,
            'start_index': page_obj.start_index() if page_obj else 0,
            'end_index': page_obj.end_index() if page_obj else 0
        }
        
        return page_obj, pagination_meta


def api_exception_handler(view_func):
    """
    API异常处理装饰器
    统一处理API视图中的异常
    """
    @wraps(view_func)
    def wrapper(self, request, *args, **kwargs):
        try:
            return view_func(self, request, *args, **kwargs)
        except PermissionError as e:
            logger.warning(f"权限错误 in {view_func.__name__}: {str(e)}")
            return APIResponse.permission_denied(str(e))
        except ValueError as e:
            logger.warning(f"数值错误 in {view_func.__name__}: {str(e)}")
            return APIResponse.validation_error({'value': str(e)})
        except Exception as e:
            logger.error(f"未处理异常 in {view_func.__name__}: {str(e)}")
            logger.error(f"堆栈跟踪: {traceback.format_exc()}")
            
            # 在开发环境返回详细错误信息
            from django.conf import settings
            details = str(e) if settings.DEBUG else None
            
            return APIResponse.server_error(
                message='服务器内部错误，请稍后重试',
                details=details
            )
    
    return wrapper


def require_fields(required_fields):
    """
    必填字段验证装饰器
    
    Args:
        required_fields: 必填字段列表
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(self, request, *args, **kwargs):
            import json
            
            try:
                # 解析请求数据
                if request.content_type == 'application/json':
                    data = json.loads(request.body) if request.body else {}
                else:
                    data = request.POST.dict()
                
                # 检查必填字段
                missing_fields = []
                for field in required_fields:
                    if not data.get(field):
                        missing_fields.append(field)
                
                if missing_fields:
                    return APIResponse.validation_error(
                        errors={'missing_fields': missing_fields},
                        message=f'缺少必填字段: {", ".join(missing_fields)}'
                    )
                
                # 将解析后的数据添加到request对象
                request.validated_data = data
                
                return view_func(self, request, *args, **kwargs)
                
            except json.JSONDecodeError:
                return APIResponse.validation_error(
                    errors={'json': '请求数据格式错误'},
                    message='请求数据格式错误'
                )
        
        return wrapper
    return decorator


def validate_pagination_params(request):
    """
    验证分页参数
    
    Args:
        request: Django请求对象
    
    Returns:
        tuple: (page, page_size, errors)
    """
    try:
        page = int(request.GET.get('page', 1))
        if page < 1:
            page = 1
    except (ValueError, TypeError):
        return None, None, {'page': '页码必须是正整数'}
    
    try:
        page_size = int(request.GET.get('page_size', 20))
        if page_size < 1:
            page_size = 20
        elif page_size > 100:
            page_size = 100
    except (ValueError, TypeError):
        return None, None, {'page_size': '每页大小必须是1-100之间的整数'}
    
    return page, page_size, None


def format_datetime(dt):
    """
    标准化日期时间格式
    
    Args:
        dt: datetime对象
    
    Returns:
        str: 格式化后的时间字符串
    """
    if dt is None:
        return None
    return dt.strftime('%Y-%m-%d %H:%M:%S')


def _get_current_timestamp():
    """获取当前时间戳"""
    from django.utils import timezone
    return timezone.now().strftime('%Y-%m-%d %H:%M:%S')


# 常用错误代码定义
class ErrorCodes:
    """标准错误代码"""
    
    # 通用错误
    OPERATION_FAILED = 'OPERATION_FAILED'
    VALIDATION_FAILED = 'VALIDATION_FAILED'
    PERMISSION_DENIED = 'PERMISSION_DENIED'
    RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND'
    INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR'
    
    # 认证相关
    AUTHENTICATION_REQUIRED = 'AUTHENTICATION_REQUIRED'
    INVALID_CREDENTIALS = 'INVALID_CREDENTIALS'
    TOKEN_EXPIRED = 'TOKEN_EXPIRED'
    TOKEN_INVALID = 'TOKEN_INVALID'
    
    # 数据相关
    MISSING_REQUIRED_FIELD = 'MISSING_REQUIRED_FIELD'
    INVALID_DATA_FORMAT = 'INVALID_DATA_FORMAT'
    DUPLICATE_ENTRY = 'DUPLICATE_ENTRY'
    
    # 业务相关
    INSUFFICIENT_PRIVILEGES = 'INSUFFICIENT_PRIVILEGES'
    RESOURCE_ALREADY_EXISTS = 'RESOURCE_ALREADY_EXISTS'
    OPERATION_NOT_ALLOWED = 'OPERATION_NOT_ALLOWED'


# HTTP状态码常量
class StatusCodes:
    """HTTP状态码常量"""
    
    # 成功
    OK = 200
    CREATED = 201
    ACCEPTED = 202
    NO_CONTENT = 204
    
    # 客户端错误
    BAD_REQUEST = 400
    UNAUTHORIZED = 401
    FORBIDDEN = 403
    NOT_FOUND = 404
    METHOD_NOT_ALLOWED = 405
    CONFLICT = 409
    UNPROCESSABLE_ENTITY = 422
    TOO_MANY_REQUESTS = 429
    
    # 服务器错误
    INTERNAL_SERVER_ERROR = 500
    BAD_GATEWAY = 502
    SERVICE_UNAVAILABLE = 503