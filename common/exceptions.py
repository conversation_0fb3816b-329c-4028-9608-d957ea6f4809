# -*- coding: utf-8 -*-
"""
自定义异常类
统一系统异常处理
"""

class BaseBusinessException(Exception):
    """业务异常基类"""
    
    def __init__(self, message, code=None, details=None):
        self.message = message
        self.code = code or self.__class__.__name__
        self.details = details or {}
        super().__init__(message)

class AuthenticationException(BaseBusinessException):
    """认证异常"""
    pass

class PermissionException(BaseBusinessException):
    """权限异常"""
    pass

class ValidationException(BaseBusinessException):
    """验证异常"""
    pass

class EvaluationException(BaseBusinessException):
    """考评业务异常"""
    pass

class DataImportException(BaseBusinessException):
    """数据导入异常"""
    pass

class SecurityException(BaseBusinessException):
    """安全异常"""
    pass

class SystemMaintenanceException(BaseBusinessException):
    """系统维护异常"""
    pass

class TokenException(BaseBusinessException):
    """Token相关异常"""
    pass

class AccountLockedException(BaseBusinessException):
    """账户锁定异常"""
    pass