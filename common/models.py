# -*- coding: utf-8 -*-
"""
公共基础模型
包含软删除和审计字段的抽象基类
"""

from django.db import models
from django.utils import timezone


class SoftDeleteManager(models.Manager):
    """软删除管理器：只查询未删除的记录"""
    
    def get_queryset(self):
        return super().get_queryset().filter(deleted_at__isnull=True)


class BaseModel(models.Model):
    """
    抽象基础模型
    提供软删除和审计字段功能
    """
    # 审计字段
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='创建时间',
        help_text='记录创建时间'
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='更新时间',
        help_text='记录最后更新时间'
    )
    created_by = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name='创建人',
        help_text='记录创建者'
    )
    updated_by = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name='更新人',
        help_text='记录最后更新者'
    )
    
    # 软删除字段
    deleted_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='删除时间',
        help_text='软删除时间，为空表示未删除'
    )
    deleted_by = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name='删除人',
        help_text='软删除操作者'
    )
    
    # 管理器
    objects = SoftDeleteManager()  # 默认管理器：只查询未删除记录
    all_objects = models.Manager()  # 全部管理器：查询所有记录
    
    class Meta:
        abstract = True
        ordering = ['-created_at']  # 默认按创建时间倒序
    
    def soft_delete(self, deleted_by=None):
        """软删除方法"""
        self.deleted_at = timezone.now()
        self.deleted_by = deleted_by
        self.save(update_fields=['deleted_at', 'deleted_by'])
    
    def restore(self):
        """恢复软删除的记录"""
        self.deleted_at = None
        self.deleted_by = None
        self.save(update_fields=['deleted_at', 'deleted_by'])
    
    @property
    def is_deleted(self):
        """检查记录是否被软删除"""
        return self.deleted_at is not None


class AuditLog(BaseModel):
    """
    审计日志模型
    记录重要操作的日志信息
    """
    ACTION_CHOICES = [
        ('create', '创建'),
        ('update', '更新'),
        ('delete', '删除'),
        ('login', '登录'),
        ('logout', '注销'),
        ('view', '查看'),
        ('export', '导出'),
        ('import', '导入'),
    ]
    
    user = models.CharField(
        max_length=50,
        verbose_name='操作用户',
        help_text='执行操作的用户标识'
    )
    action = models.CharField(
        max_length=20,
        choices=ACTION_CHOICES,
        verbose_name='操作类型',
        help_text='执行的操作类型'
    )
    target_model = models.CharField(
        max_length=50,
        verbose_name='目标模型',
        help_text='操作的目标数据模型'
    )
    target_id = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name='目标ID',
        help_text='操作的目标记录ID'
    )
    description = models.TextField(
        verbose_name='操作描述',
        help_text='详细的操作描述'
    )
    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        verbose_name='IP地址',
        help_text='操作者的IP地址'
    )
    user_agent = models.TextField(
        blank=True,
        verbose_name='用户代理',
        help_text='浏览器用户代理信息'
    )
    extra_data = models.JSONField(
        null=True,
        blank=True,
        verbose_name='扩展数据',
        help_text='额外的操作数据（JSON格式）'
    )
    
    class Meta:
        verbose_name = '审计日志'
        verbose_name_plural = '审计日志'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'action']),
            models.Index(fields=['target_model', 'target_id']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f'{self.user} {self.get_action_display()} {self.target_model} at {self.created_at}'


class SystemSettings(BaseModel):
    """
    系统设置模型
    存储系统配置参数
    """
    SETTING_TYPES = [
        ('string', '字符串'),
        ('integer', '整数'),
        ('float', '浮点数'),
        ('boolean', '布尔值'),
        ('json', 'JSON对象'),
    ]
    
    key = models.CharField(
        max_length=100,
        unique=True,
        verbose_name='配置键',
        help_text='配置项的唯一标识'
    )
    value = models.TextField(
        verbose_name='配置值',
        help_text='配置项的值'
    )
    value_type = models.CharField(
        max_length=20,
        choices=SETTING_TYPES,
        default='string',
        verbose_name='值类型',
        help_text='配置值的数据类型'
    )
    description = models.CharField(
        max_length=200,
        blank=True,
        verbose_name='描述',
        help_text='配置项的描述信息'
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name='是否启用',
        help_text='配置项是否生效'
    )
    
    class Meta:
        verbose_name = '系统设置'
        verbose_name_plural = '系统设置'
        ordering = ['key']
    
    def __str__(self):
        return f'{self.key}: {self.value}'
    
    def get_typed_value(self):
        """获取正确类型的配置值"""
        if self.value_type == 'integer':
            return int(self.value)
        elif self.value_type == 'float':
            return float(self.value)
        elif self.value_type == 'boolean':
            return self.value.lower() in ('true', '1', 'yes', 'on')
        elif self.value_type == 'json':
            import json
            return json.loads(self.value)
        else:
            return self.value


class ImportHistory(BaseModel):
    """
    Excel导入历史记录模型
    记录每次导入操作的详细信息
    """
    IMPORT_TYPES = [
        ('staff', '员工信息'),
        ('department', '部门信息'),
        ('position', '职位信息'),
        ('evaluation_relation', '考评关系'),
        ('evaluation_batch', '考评批次'),
        ('evaluation_template', '考评模板'),
    ]
    
    STATUS_CHOICES = [
        ('processing', '处理中'),
        ('completed', '已完成'),
        ('failed', '失败'),
        ('partial', '部分成功'),
    ]
    
    user_id = models.PositiveIntegerField(
        verbose_name='操作用户ID',
        help_text='执行导入操作的用户ID'
    )
    import_type = models.CharField(
        max_length=30,
        choices=IMPORT_TYPES,
        verbose_name='导入类型',
        help_text='导入数据的类型'
    )
    filename = models.CharField(
        max_length=255,
        verbose_name='文件名',
        help_text='导入的Excel文件名'
    )
    total_count = models.PositiveIntegerField(
        default=0,
        verbose_name='总记录数',
        help_text='Excel文件中的总记录数'
    )
    success_count = models.PositiveIntegerField(
        default=0,
        verbose_name='成功记录数',
        help_text='成功导入的记录数'
    )
    error_count = models.PositiveIntegerField(
        default=0,
        verbose_name='错误记录数',
        help_text='导入失败的记录数'
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='processing',
        verbose_name='导入状态',
        help_text='导入操作的状态'
    )
    error_details = models.JSONField(
        null=True,
        blank=True,
        verbose_name='错误详情',
        help_text='导入过程中的错误信息（JSON格式）'
    )
    file_path = models.CharField(
        max_length=500,
        blank=True,
        verbose_name='文件路径',
        help_text='上传文件的存储路径'
    )
    processing_time = models.FloatField(
        null=True,
        blank=True,
        verbose_name='处理耗时',
        help_text='导入处理耗时（秒）'
    )
    
    class Meta:
        verbose_name = '导入历史'
        verbose_name_plural = '导入历史'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user_id', 'import_type']),
            models.Index(fields=['status', 'created_at']),
        ]
    
    def __str__(self):
        return f'{self.get_import_type_display()} - {self.filename} ({self.get_status_display()})'
    
    @property
    def success_rate(self):
        """计算成功率"""
        if self.total_count == 0:
            return 0
        return round((self.success_count / self.total_count) * 100, 2)
    
    def mark_completed(self):
        """标记为已完成"""
        if self.error_count == 0:
            self.status = 'completed'
        elif self.success_count > 0:
            self.status = 'partial'
        else:
            self.status = 'failed'
        self.save(update_fields=['status'])
    
    def add_error(self, error_message: str, row_number: int = None):
        """添加错误信息"""
        if not self.error_details:
            self.error_details = []
        
        error_info = {
            'message': error_message,
            'timestamp': timezone.now().isoformat()
        }
        
        if row_number:
            error_info['row'] = row_number
        
        self.error_details.append(error_info)
        self.save(update_fields=['error_details'])
