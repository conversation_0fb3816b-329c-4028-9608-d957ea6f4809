# -*- coding: utf-8 -*-
"""
通用功能URL配置
包含用户认证、退出、token管理等功能
"""

from django.urls import path
from . import views

app_name = 'common'

urlpatterns = [
    # 用户认证功能
    path('api/login/', views.login_view, name='login'),
    path('api/token/refresh/', views.refresh_token_view, name='refresh_token'),
    path('api/logout/', views.logout_view, name='logout'),
    path('api/logout/all/', views.logout_all_devices, name='logout_all'),
    path('api/token/status/', views.get_token_status, name='token_status'),
]