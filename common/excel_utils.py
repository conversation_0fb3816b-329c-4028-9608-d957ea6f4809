# -*- coding: utf-8 -*-
"""
Excel导入导出工具类
提供统一的Excel文件处理功能，包括数据导入、导出、验证等
"""

import pandas as pd
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
from django.http import HttpResponse
from django.core.exceptions import ValidationError
from io import BytesIO
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple

logger = logging.getLogger(__name__)


class ExcelExporter:
    """
    Excel导出工具类
    提供数据导出、模板生成、样式设置等功能
    """
    
    def __init__(self):
        self.workbook = openpyxl.Workbook()
        self.worksheet = self.workbook.active
        self.header_style = {
            'font': Font(bold=True, color='FFFFFF'),
            'fill': PatternFill(start_color='366092', end_color='366092', fill_type='solid'),
            'alignment': Alignment(horizontal='center', vertical='center'),
            'border': Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
        }
        self.data_style = {
            'alignment': Alignment(horizontal='left', vertical='center'),
            'border': Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
        }
    
    def create_worksheet(self, sheet_name: str = 'Sheet1') -> None:
        """创建新的工作表"""
        if sheet_name != 'Sheet1':
            self.worksheet = self.workbook.create_sheet(sheet_name)
        else:
            self.worksheet.title = sheet_name
    
    def set_headers(self, headers: List[str], row: int = 1) -> None:
        """设置表头"""
        for col, header in enumerate(headers, 1):
            cell = self.worksheet.cell(row=row, column=col)
            cell.value = header
            
            # 应用样式
            cell.font = self.header_style['font']
            cell.fill = self.header_style['fill']
            cell.alignment = self.header_style['alignment']
            cell.border = self.header_style['border']
    
    def add_data_rows(self, data: List[List[Any]], start_row: int = 2) -> None:
        """添加数据行"""
        for row_idx, row_data in enumerate(data, start_row):
            for col_idx, value in enumerate(row_data, 1):
                cell = self.worksheet.cell(row=row_idx, column=col_idx)
                cell.value = value
                cell.alignment = self.data_style['alignment']
                cell.border = self.data_style['border']
    
    def add_dataframe(self, df: pd.DataFrame, include_index: bool = False) -> None:
        """添加DataFrame数据"""
        for r in dataframe_to_rows(df, index=include_index, header=True):
            self.worksheet.append(r)
    
    def auto_adjust_columns(self) -> None:
        """自动调整列宽"""
        for column in self.worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            self.worksheet.column_dimensions[column_letter].width = adjusted_width
    
    def add_metadata(self, metadata: Dict[str, Any]) -> None:
        """添加元数据信息"""
        meta_row = self.worksheet.max_row + 2
        
        self.worksheet.cell(row=meta_row, column=1).value = "导出信息"
        self.worksheet.cell(row=meta_row, column=1).font = Font(bold=True)
        
        for key, value in metadata.items():
            meta_row += 1
            self.worksheet.cell(row=meta_row, column=1).value = f"{key}:"
            self.worksheet.cell(row=meta_row, column=2).value = str(value)
    
    def export_to_response(self, filename: str) -> HttpResponse:
        """导出为HTTP响应"""
        # 自动调整列宽
        self.auto_adjust_columns()
        
        # 创建BytesIO对象
        buffer = BytesIO()
        self.workbook.save(buffer)
        buffer.seek(0)
        
        # 创建HTTP响应
        response = HttpResponse(
            buffer.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        return response
    
    def save_to_file(self, filepath: str) -> None:
        """保存到文件"""
        self.auto_adjust_columns()
        self.workbook.save(filepath)


class ExcelImporter:
    """
    Excel导入工具类
    提供数据导入、验证、错误处理等功能
    """
    
    def __init__(self, file_path_or_buffer, sheet_name: Optional[str] = None):
        self.file_path_or_buffer = file_path_or_buffer
        self.sheet_name = sheet_name
        self.df = None
        self.errors = []
        self.warnings = []
        self.success_count = 0
        self.error_count = 0
    
    def read_excel(self, header_row: int = 0, skip_rows: Optional[List[int]] = None) -> pd.DataFrame:
        """读取Excel文件"""
        try:
            # 如果没有指定工作表名称，默认读取第一个工作表
            sheet_name = self.sheet_name if self.sheet_name is not None else 0

            result = pd.read_excel(
                self.file_path_or_buffer,
                sheet_name=sheet_name,
                header=header_row,
                skiprows=skip_rows
            )

            # 如果返回的是字典（多个工作表），取第一个
            if isinstance(result, dict):
                # 取第一个工作表
                self.df = list(result.values())[0]
            else:
                self.df = result

            # 移除空行
            self.df = self.df.dropna(how='all')

            # 移除列名中的空格
            self.df.columns = self.df.columns.str.strip()

            return self.df

        except Exception as e:
            logger.error(f"读取Excel文件失败: {e}")
            raise ValidationError(f"读取Excel文件失败: {str(e)}")
    
    def validate_headers(self, required_headers: List[str]) -> bool:
        """验证表头"""
        if self.df is None:
            raise ValueError("请先读取Excel文件")
        
        missing_headers = []
        df_headers = [str(col).strip() for col in self.df.columns]
        
        for header in required_headers:
            if header not in df_headers:
                missing_headers.append(header)
        
        if missing_headers:
            error_msg = f"缺少必需的列: {', '.join(missing_headers)}"
            self.errors.append(error_msg)
            return False
        
        return True
    
    def validate_data_types(self, column_types: Dict[str, str]) -> bool:
        """验证数据类型"""
        type_errors = []
        
        for column, expected_type in column_types.items():
            if column not in self.df.columns:
                continue
            
            try:
                if expected_type == 'int':
                    pd.to_numeric(self.df[column], errors='coerce')
                elif expected_type == 'float':
                    pd.to_numeric(self.df[column], errors='coerce')
                elif expected_type == 'datetime':
                    pd.to_datetime(self.df[column], errors='coerce')
                elif expected_type == 'string':
                    self.df[column] = self.df[column].astype(str)
                    
            except Exception as e:
                type_errors.append(f"列 '{column}' 数据类型错误: {str(e)}")
        
        if type_errors:
            self.errors.extend(type_errors)
            return False
        
        return True
    
    def validate_required_fields(self, required_fields: List[str]) -> List[int]:
        """验证必填字段"""
        error_rows = []
        
        for idx, row in self.df.iterrows():
            row_errors = []
            for field in required_fields:
                if field in self.df.columns:
                    value = row[field]
                    if pd.isna(value) or str(value).strip() == '':
                        row_errors.append(f"'{field}' 不能为空")
            
            if row_errors:
                error_rows.append(idx + 2)  # +2 因为索引从0开始，且有表头
                self.errors.append(f"第{idx + 2}行: {'; '.join(row_errors)}")
        
        return error_rows
    
    def validate_unique_fields(self, unique_fields: List[str]) -> List[int]:
        """验证唯一字段"""
        error_rows = []
        
        for field in unique_fields:
            if field not in self.df.columns:
                continue
            
            # 检查重复值
            duplicates = self.df[self.df.duplicated(subset=[field], keep=False)]
            
            if not duplicates.empty:
                duplicate_rows = duplicates.index.tolist()
                error_rows.extend([row + 2 for row in duplicate_rows])
                
                duplicate_values = duplicates[field].unique()
                self.errors.append(f"字段 '{field}' 存在重复值: {', '.join(map(str, duplicate_values))}")
        
        return list(set(error_rows))
    
    def clean_data(self) -> pd.DataFrame:
        """清理数据"""
        if self.df is None:
            raise ValueError("请先读取Excel文件")
        
        # 移除前后空格
        for col in self.df.select_dtypes(include=['object']).columns:
            self.df[col] = self.df[col].astype(str).str.strip()
        
        # 替换空字符串为NaN
        self.df = self.df.replace('', pd.NA)
        
        return self.df
    
    def get_validation_summary(self) -> Dict[str, Any]:
        """获取验证摘要"""
        return {
            'total_rows': len(self.df) if self.df is not None else 0,
            'success_count': self.success_count,
            'error_count': self.error_count,
            'errors': self.errors,
            'warnings': self.warnings,
            'has_errors': len(self.errors) > 0
        }


class ExcelTemplateGenerator:
    """
    Excel模板生成器
    用于生成各种导入模板
    """
    
    @staticmethod
    def generate_staff_template() -> HttpResponse:
        """生成员工导入模板"""
        exporter = ExcelExporter()

        headers = [
            '姓名*', '部门*', '级别*', '工号',
            '职位', '邮箱', '手机号'
        ]

        exporter.set_headers(headers)

        # 添加示例数据
        sample_data = [
            ['张三', '技术部', '6', 'EMP001', '高级工程师', '<EMAIL>', '13800138001'],
            ['李四', '人事部', '7', 'EMP002', '人事经理', '<EMAIL>', '13800138002'],
            ['王五', '技术部', '4', 'EMP003', '软件工程师', '<EMAIL>', '13800138003'],
            ['赵六', '总经理室', '8', 'EMP004', '技术总监', '<EMAIL>', '13800138004'],
        ]
        exporter.add_data_rows(sample_data)

        # 添加说明
        exporter.add_metadata({
            '导出时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            '模板说明': '员工信息导入模板',
            '注意事项': '标*字段为必填项，部门名称必须是系统中已存在的部门，职位按职位名称进行关联',
            '部门说明': '部门名称必须与系统中的部门名称完全一致',
            '级别说明': '1-4级员工，5级副主管，6级正主管，7级副经理，8级正经理，9级领导班子',
            '职位说明': '职位名称为可选项，如填写则必须与系统中的职位名称完全一致',
            '工号说明': '员工工号为可选项，如不填写系统将自动生成',
            '联系方式': '邮箱和手机号为可选项，建议填写以便联系'
        })

        return exporter.export_to_response('员工导入模板.xlsx')
    
    @staticmethod
    def generate_department_template() -> HttpResponse:
        """生成部门导入模板"""
        exporter = ExcelExporter()

        headers = [
            '部门编号*', '部门名称*', '上级部门名称', '备注'
        ]

        exporter.set_headers(headers)

        # 添加示例数据
        sample_data = [
            ['DEPT001', '总经理室', '', '公司最高管理层'],
            ['DEPT002', '技术部', '总经理室', '负责技术研发工作'],
            ['DEPT003', '人事部', '总经理室', '负责人力资源管理'],
            ['DEPT004', '前端开发组', '技术部', '负责前端开发'],
            ['DEPT005', '后端开发组', '技术部', '负责后端开发'],
        ]
        exporter.add_data_rows(sample_data)

        # 添加说明
        exporter.add_metadata({
            '导出时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            '模板说明': '部门信息导入模板',
            '注意事项': '标*字段为必填项，上级部门名称留空表示顶级部门，按部门名称关联上级部门',
            '导入顺序': '建议按组织层级顺序导入，先导入上级部门再导入下级部门',
            '备注说明': '备注字段为可选项，可以填写部门职责或其他说明信息'
        })

        return exporter.export_to_response('部门导入模板.xlsx')

    @staticmethod
    def generate_position_template() -> HttpResponse:
        """生成职位导入模板"""
        exporter = ExcelExporter()

        headers = [
            '部门*', '职位*', '编码*', '是否管理岗', '是否部门主管', '职位级别', '备注'
        ]

        exporter.set_headers(headers)

        # 添加示例数据
        sample_data = [
            ['总经理室', '总经理', 'CEO001', '是', '是', '9', '公司最高管理者'],
            ['技术部', '技术总监', 'CTO001', '是', '是', '8', '技术部门负责人'],
            ['技术部', '高级工程师', 'SE001', '否', '否', '6', '高级软件工程师'],
            ['人事部', '人事经理', 'HR001', '是', '是', '7', '人事部门负责人'],
            ['人事部', '人事专员', 'HR002', '否', '否', '4', '人事专员'],
        ]
        exporter.add_data_rows(sample_data)

        # 添加说明
        exporter.add_metadata({
            '导出时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            '模板说明': '职位信息导入模板',
            '注意事项': '标*字段为必填项，部门名称必须是系统中已存在的部门',
            '是否管理岗': '是/否，表示该职位是否为管理岗位',
            '是否部门主管': '是/否，表示该职位是否为部门主管',
            '职位级别': '1-9级，1级员工到9级领导班子',
            '级别说明': '1-4级员工，5级副主管，6级正主管，7级副经理，8级正经理，9级领导班子'
        })

        return exporter.export_to_response('职位导入模板.xlsx')

    @staticmethod
    def generate_evaluation_relation_template() -> HttpResponse:
        """生成考评关系导入模板"""
        exporter = ExcelExporter()
        
        headers = [
            '考评批次ID*', '评价者工号*', '被评价者工号*', 
            '权重系数*', '评价模板ID*', '是否分配*'
        ]
        
        exporter.set_headers(headers)
        
        # 添加示例数据
        sample_data = [
            [1, 'EMP001', 'EMP002', 1.0, 1, '是'],
            [1, 'EMP002', 'EMP001', 0.8, 1, '是'],
        ]
        exporter.add_data_rows(sample_data)
        
        # 添加说明
        exporter.add_metadata({
            '导出时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            '模板说明': '考评关系导入模板',
            '注意事项': '标*字段为必填项',
            '权重系数': '建议范围0.1-2.0',
            '是否分配': '是/否'
        })
        
        return exporter.export_to_response('考评关系导入模板.xlsx')


class ExcelProcessor:
    """
    Excel处理器
    提供完整的导入导出流程处理
    """
    
    def __init__(self, user_id: int):
        self.user_id = user_id
        self.import_history = []
    
    def process_import(self, file, import_type: str, model_class, 
                      field_mapping: Dict[str, str], 
                      validators: Optional[Dict] = None) -> Dict[str, Any]:
        """
        处理导入流程
        
        Args:
            file: 上传的文件
            import_type: 导入类型 (staff, department, evaluation_relation)
            model_class: 对应的模型类
            field_mapping: 字段映射 {'Excel列名': 'model字段名'}
            validators: 验证器配置
        """
        try:
            # 创建导入器
            importer = ExcelImporter(file)
            
            # 读取Excel
            df = importer.read_excel()
            
            # 验证表头
            required_headers = list(field_mapping.keys())
            if not importer.validate_headers(required_headers):
                return importer.get_validation_summary()
            
            # 数据验证
            if validators:
                if 'data_types' in validators:
                    importer.validate_data_types(validators['data_types'])
                
                if 'required_fields' in validators:
                    importer.validate_required_fields(validators['required_fields'])
                
                if 'unique_fields' in validators:
                    importer.validate_unique_fields(validators['unique_fields'])
            
            # 如果有错误，返回验证摘要
            if importer.errors:
                return importer.get_validation_summary()
            
            # 清理数据
            df = importer.clean_data()
            
            # 导入数据
            success_count, error_count = self._import_to_database(
                df, model_class, field_mapping
            )
            
            # 记录导入历史
            self._log_import_history(import_type, success_count, error_count)
            
            return {
                'success': True,
                'total_rows': len(df),
                'success_count': success_count,
                'error_count': error_count,
                'errors': importer.errors,
                'message': f'导入完成: 成功{success_count}条，失败{error_count}条'
            }
            
        except Exception as e:
            logger.error(f"导入处理失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '导入处理失败，请检查文件格式'
            }
    
    def _import_to_database(self, df: pd.DataFrame, model_class, 
                           field_mapping: Dict[str, str]) -> Tuple[int, int]:
        """导入数据到数据库"""
        success_count = 0
        error_count = 0
        
        for idx, row in df.iterrows():
            try:
                # 构建模型数据
                model_data = {}
                for excel_field, model_field in field_mapping.items():
                    value = row.get(excel_field)
                    if pd.notna(value):
                        model_data[model_field] = value
                
                # 创建或更新记录
                obj, created = model_class.objects.get_or_create(
                    **self._get_unique_key(model_data, model_class),
                    defaults=model_data
                )
                
                if not created:
                    # 更新现有记录
                    for key, value in model_data.items():
                        setattr(obj, key, value)
                    obj.save()
                
                success_count += 1
                
            except Exception as e:
                logger.error(f"导入第{idx+2}行数据失败: {e}")
                error_count += 1
        
        return success_count, error_count
    
    def _get_unique_key(self, data: Dict, model_class) -> Dict:
        """获取唯一键用于查重"""
        # 这里需要根据不同模型定义唯一键
        unique_fields = {
            'Staff': ['employee_no'],
            'Department': ['dept_code'],
            'EvaluationRelation': ['evaluator', 'evaluatee', 'batch']
        }
        
        model_name = model_class.__name__
        if model_name in unique_fields:
            return {field: data[field] for field in unique_fields[model_name] if field in data}
        
        return {}
    
    def _log_import_history(self, import_type: str, success_count: int, error_count: int):
        """记录导入历史"""
        from .models import ImportHistory
        
        ImportHistory.objects.create(
            user_id=self.user_id,
            import_type=import_type,
            success_count=success_count,
            error_count=error_count,
            total_count=success_count + error_count
        )
    
    def export_data(self, queryset, export_type: str, 
                   field_mapping: Dict[str, str], 
                   filename: str = None) -> HttpResponse:
        """
        导出数据
        
        Args:
            queryset: 要导出的查询集
            export_type: 导出类型
            field_mapping: 字段映射 {'model字段名': 'Excel列名'}
            filename: 文件名
        """
        try:
            exporter = ExcelExporter()
            
            # 设置表头
            headers = list(field_mapping.values())
            exporter.set_headers(headers)
            
            # 准备数据
            data_rows = []
            for obj in queryset:
                row = []
                for model_field, excel_field in field_mapping.items():
                    value = self._get_field_value(obj, model_field)
                    row.append(value)
                data_rows.append(row)
            
            # 添加数据
            exporter.add_data_rows(data_rows)
            
            # 添加元数据
            exporter.add_metadata({
                '导出时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                '导出类型': export_type,
                '数据条数': len(data_rows),
                '导出用户': f'用户ID: {self.user_id}'
            })
            
            # 生成文件名
            if not filename:
                filename = f'{export_type}_导出_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
            
            return exporter.export_to_response(filename)
            
        except Exception as e:
            logger.error(f"数据导出失败: {e}")
            raise ValidationError(f"数据导出失败: {str(e)}")
    
    def _get_field_value(self, obj, field_path: str):
        """获取对象字段值，支持点分割的嵌套字段"""
        fields = field_path.split('.')
        value = obj
        
        for field in fields:
            if hasattr(value, field):
                value = getattr(value, field)
                if callable(value):
                    value = value()
            else:
                return ''
        
        return value if value is not None else ''