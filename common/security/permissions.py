# -*- coding: utf-8 -*-
"""
RBAC权限控制系统
实现基于角色的访问控制(Role-Based Access Control)

技术特点：
- 7种角色层级：超级管理员→系统管理员→HR管理员→考评管理员→部门经理→普通管理员→员工
- 50+种权限定义：组织管理、考评管理、报告权限、系统权限、数据权限
- 装饰器支持：@require_permission、@require_role、@require_department_access
- 部门级数据隔离：支持跨部门和本部门数据访问控制
- 动态权限检查：支持运行时权限验证和角色升级

安全等级：企业级
"""

from functools import wraps
from typing import List, Dict, Set, Optional, Tuple, Any
from django.http import HttpResponseForbidden, JsonResponse
from django.shortcuts import redirect
from django.contrib import messages
from django.core.cache import cache
import logging

logger = logging.getLogger(__name__)


# ==================== 权限常量定义 ====================

class Permission:
    """权限常量定义类"""
    
    # 组织管理权限 (ORG_*)
    ORG_VIEW_DEPARTMENT = 'org.view_department'           # 查看部门
    ORG_CREATE_DEPARTMENT = 'org.create_department'       # 创建部门
    ORG_EDIT_DEPARTMENT = 'org.edit_department'           # 编辑部门
    ORG_DELETE_DEPARTMENT = 'org.delete_department'       # 删除部门
    
    ORG_VIEW_STAFF = 'org.view_staff'                     # 查看员工
    ORG_CREATE_STAFF = 'org.create_staff'                 # 创建员工
    ORG_EDIT_STAFF = 'org.edit_staff'                     # 编辑员工
    ORG_DELETE_STAFF = 'org.delete_staff'                 # 删除员工
    ORG_IMPORT_STAFF = 'org.import_staff'                 # 导入员工
    ORG_EXPORT_STAFF = 'org.export_staff'                 # 导出员工
    
    ORG_VIEW_POSITION = 'org.view_position'               # 查看职位
    ORG_CREATE_POSITION = 'org.create_position'           # 创建职位
    ORG_EDIT_POSITION = 'org.edit_position'               # 编辑职位
    ORG_DELETE_POSITION = 'org.delete_position'           # 删除职位
    
    ORG_MANAGE_STRUCTURE = 'org.manage_structure'         # 管理组织架构
    
    # 考评管理权限 (EVAL_*)
    EVAL_VIEW_TEMPLATE = 'eval.view_template'             # 查看考评模板
    EVAL_CREATE_TEMPLATE = 'eval.create_template'         # 创建模板
    EVAL_EDIT_TEMPLATE = 'eval.edit_template'             # 编辑模板
    EVAL_DELETE_TEMPLATE = 'eval.delete_template'         # 删除模板
    
    EVAL_VIEW_BATCH = 'eval.view_batch'                   # 查看考评批次
    EVAL_CREATE_BATCH = 'eval.create_batch'               # 创建批次
    EVAL_EDIT_BATCH = 'eval.edit_batch'                   # 编辑批次
    EVAL_DELETE_BATCH = 'eval.delete_batch'               # 删除批次
    EVAL_ACTIVATE_BATCH = 'eval.activate_batch'           # 激活批次
    
    EVAL_VIEW_RELATION = 'eval.view_relation'             # 查看考评关系
    EVAL_EDIT_RELATION = 'eval.edit_relation'             # 编辑关系
    EVAL_INTELLIGENT_ASSIGN = 'eval.intelligent_assign'   # 智能分配
    
    EVAL_VIEW_RECORD = 'eval.view_record'                 # 查看考评记录
    EVAL_EDIT_RECORD = 'eval.edit_record'                 # 编辑记录
    EVAL_DELETE_RECORD = 'eval.delete_record'             # 删除记录
    
    EVAL_VIEW_PROGRESS = 'eval.view_progress'             # 查看考评进度
    EVAL_MANAGE_PROGRESS = 'eval.manage_progress'         # 管理进度
    
    EVAL_VIEW_WEIGHTING = 'eval.view_weighting'           # 查看权重规则
    EVAL_MANAGE_WEIGHTING = 'eval.manage_weighting'       # 管理权重规则
    
    # 报告权限 (REPORT_*)
    REPORT_VIEW_PERSONAL = 'report.view_personal'         # 查看个人报告
    REPORT_VIEW_DEPARTMENT = 'report.view_department'     # 查看部门报告
    REPORT_VIEW_COMPANY = 'report.view_company'           # 查看公司报告
    
    REPORT_EXPORT_DATA = 'report.export_data'             # 导出报告
    REPORT_GENERATE_TALENT = 'report.generate_talent'     # 生成人才盘点
    REPORT_VIEW_ANALYTICS = 'report.view_analytics'       # 查看统计分析
    
    # 系统管理权限 (SYS_*)
    SYS_MANAGE_SETTINGS = 'sys.manage_settings'           # 系统设置
    SYS_MANAGE_USERS = 'sys.manage_users'                 # 用户管理
    SYS_MANAGE_PERMISSIONS = 'sys.manage_permissions'     # 权限管理
    SYS_VIEW_AUDIT_LOG = 'sys.view_audit_log'             # 查看审计日志
    SYS_MANAGE_AUDIT_LOG = 'sys.manage_audit_log'         # 管理审计日志
    
    SYS_VIEW_IMPORT_HISTORY = 'sys.view_import_history'   # 查看导入历史
    SYS_MANAGE_BACKUP = 'sys.manage_backup'               # 数据备份
    SYS_MONITOR_SYSTEM = 'sys.monitor_system'             # 系统监控
    
    # 数据访问权限 (DATA_*)
    DATA_VIEW_ALL = 'data.view_all'                       # 查看所有数据
    DATA_VIEW_DEPARTMENT = 'data.view_department'         # 查看本部门数据
    DATA_VIEW_SELF = 'data.view_self'                     # 查看自己数据
    DATA_CROSS_DEPARTMENT = 'data.cross_department'       # 跨部门数据访问
    
    # 特殊权限 (SPECIAL_*)
    SPECIAL_ANONYMOUS_EVAL = 'special.anonymous_eval'     # 匿名评分
    SPECIAL_REGENERATE_CODE = 'special.regenerate_code'   # 重新生成匿名编号
    SPECIAL_VIEW_MAPPING = 'special.view_mapping'         # 查看匿名编号映射
    SPECIAL_EMERGENCY_ACCESS = 'special.emergency_access' # 紧急访问权限


class Role:
    """角色常量定义类"""
    
    SUPER_ADMIN = 'super_admin'         # 超级管理员
    SYSTEM_ADMIN = 'system_admin'       # 系统管理员
    HR_ADMIN = 'hr_admin'               # HR管理员
    EVAL_ADMIN = 'eval_admin'           # 考评管理员
    DEPT_MANAGER = 'dept_manager'       # 部门经理
    ADMIN = 'admin'                     # 普通管理员
    EMPLOYEE = 'employee'               # 普通员工
    
    # 角色层级顺序（数字越小权限越高）
    HIERARCHY = {
        SUPER_ADMIN: 1,
        SYSTEM_ADMIN: 2,
        HR_ADMIN: 3,
        EVAL_ADMIN: 3,
        DEPT_MANAGER: 4,
        ADMIN: 5,
        EMPLOYEE: 6,
    }
    
    # 角色中文名称
    CHOICES = [
        (SUPER_ADMIN, '超级管理员'),
        (SYSTEM_ADMIN, '系统管理员'),
        (HR_ADMIN, 'HR管理员'),
        (EVAL_ADMIN, '考评管理员'),
        (DEPT_MANAGER, '部门经理'),
        (ADMIN, '普通管理员'),
        (EMPLOYEE, '普通员工'),
    ]


# ==================== 角色权限映射 ====================

class RolePermissionMapping:
    """角色权限映射关系"""
    
    # 超级管理员：所有权限
    SUPER_ADMIN_PERMISSIONS = {
        # 组织管理权限
        Permission.ORG_VIEW_DEPARTMENT,
        Permission.ORG_CREATE_DEPARTMENT,
        Permission.ORG_EDIT_DEPARTMENT,
        Permission.ORG_DELETE_DEPARTMENT,
        Permission.ORG_VIEW_STAFF,
        Permission.ORG_CREATE_STAFF,
        Permission.ORG_EDIT_STAFF,
        Permission.ORG_DELETE_STAFF,
        Permission.ORG_IMPORT_STAFF,
        Permission.ORG_EXPORT_STAFF,
        Permission.ORG_VIEW_POSITION,
        Permission.ORG_CREATE_POSITION,
        Permission.ORG_EDIT_POSITION,
        Permission.ORG_DELETE_POSITION,
        Permission.ORG_MANAGE_STRUCTURE,
        
        # 考评管理权限
        Permission.EVAL_VIEW_TEMPLATE,
        Permission.EVAL_CREATE_TEMPLATE,
        Permission.EVAL_EDIT_TEMPLATE,
        Permission.EVAL_DELETE_TEMPLATE,
        Permission.EVAL_VIEW_BATCH,
        Permission.EVAL_CREATE_BATCH,
        Permission.EVAL_EDIT_BATCH,
        Permission.EVAL_DELETE_BATCH,
        Permission.EVAL_ACTIVATE_BATCH,
        Permission.EVAL_VIEW_RELATION,
        Permission.EVAL_EDIT_RELATION,
        Permission.EVAL_INTELLIGENT_ASSIGN,
        Permission.EVAL_VIEW_RECORD,
        Permission.EVAL_EDIT_RECORD,
        Permission.EVAL_DELETE_RECORD,
        Permission.EVAL_VIEW_PROGRESS,
        Permission.EVAL_MANAGE_PROGRESS,
        Permission.EVAL_VIEW_WEIGHTING,
        Permission.EVAL_MANAGE_WEIGHTING,
        
        # 报告权限
        Permission.REPORT_VIEW_PERSONAL,
        Permission.REPORT_VIEW_DEPARTMENT,
        Permission.REPORT_VIEW_COMPANY,
        Permission.REPORT_EXPORT_DATA,
        Permission.REPORT_GENERATE_TALENT,
        Permission.REPORT_VIEW_ANALYTICS,
        
        # 系统管理权限
        Permission.SYS_MANAGE_SETTINGS,
        Permission.SYS_MANAGE_USERS,
        Permission.SYS_MANAGE_PERMISSIONS,
        Permission.SYS_VIEW_AUDIT_LOG,
        Permission.SYS_MANAGE_AUDIT_LOG,
        Permission.SYS_VIEW_IMPORT_HISTORY,
        Permission.SYS_MANAGE_BACKUP,
        Permission.SYS_MONITOR_SYSTEM,
        
        # 数据访问权限
        Permission.DATA_VIEW_ALL,
        Permission.DATA_VIEW_DEPARTMENT,
        Permission.DATA_VIEW_SELF,
        Permission.DATA_CROSS_DEPARTMENT,
        
        # 特殊权限
        Permission.SPECIAL_ANONYMOUS_EVAL,
        Permission.SPECIAL_REGENERATE_CODE,
        Permission.SPECIAL_VIEW_MAPPING,
        Permission.SPECIAL_EMERGENCY_ACCESS,
    }
    
    # 系统管理员：系统管理 + 部分业务权限
    SYSTEM_ADMIN_PERMISSIONS = {
        # 基础组织权限
        Permission.ORG_VIEW_DEPARTMENT,
        Permission.ORG_VIEW_STAFF,
        Permission.ORG_VIEW_POSITION,
        Permission.ORG_EXPORT_STAFF,
        
        # 基础考评权限
        Permission.EVAL_VIEW_TEMPLATE,
        Permission.EVAL_VIEW_BATCH,
        Permission.EVAL_VIEW_RELATION,
        Permission.EVAL_VIEW_RECORD,
        Permission.EVAL_VIEW_PROGRESS,
        Permission.EVAL_VIEW_WEIGHTING,
        
        # 报告权限
        Permission.REPORT_VIEW_PERSONAL,
        Permission.REPORT_VIEW_DEPARTMENT,
        Permission.REPORT_VIEW_COMPANY,
        Permission.REPORT_EXPORT_DATA,
        Permission.REPORT_VIEW_ANALYTICS,
        
        # 完整系统管理权限
        Permission.SYS_MANAGE_SETTINGS,
        Permission.SYS_MANAGE_USERS,
        Permission.SYS_MANAGE_PERMISSIONS,
        Permission.SYS_VIEW_AUDIT_LOG,
        Permission.SYS_MANAGE_AUDIT_LOG,
        Permission.SYS_VIEW_IMPORT_HISTORY,
        Permission.SYS_MANAGE_BACKUP,
        Permission.SYS_MONITOR_SYSTEM,
        
        # 数据访问权限
        Permission.DATA_VIEW_ALL,
        Permission.DATA_CROSS_DEPARTMENT,
        Permission.DATA_VIEW_SELF,
        
        # 部分特殊权限
        Permission.SPECIAL_VIEW_MAPPING,
        Permission.SPECIAL_REGENERATE_CODE,
    }
    
    # HR管理员：人事管理 + 组织架构
    HR_ADMIN_PERMISSIONS = {
        # 完整组织管理权限
        Permission.ORG_VIEW_DEPARTMENT,
        Permission.ORG_CREATE_DEPARTMENT,
        Permission.ORG_EDIT_DEPARTMENT,
        Permission.ORG_DELETE_DEPARTMENT,
        Permission.ORG_VIEW_STAFF,
        Permission.ORG_CREATE_STAFF,
        Permission.ORG_EDIT_STAFF,
        Permission.ORG_DELETE_STAFF,
        Permission.ORG_IMPORT_STAFF,
        Permission.ORG_EXPORT_STAFF,
        Permission.ORG_VIEW_POSITION,
        Permission.ORG_CREATE_POSITION,
        Permission.ORG_EDIT_POSITION,
        Permission.ORG_DELETE_POSITION,
        Permission.ORG_MANAGE_STRUCTURE,
        
        # 基础考评权限
        Permission.EVAL_VIEW_TEMPLATE,
        Permission.EVAL_VIEW_BATCH,
        Permission.EVAL_VIEW_RELATION,
        Permission.EVAL_VIEW_RECORD,
        Permission.EVAL_VIEW_PROGRESS,
        
        # 报告权限
        Permission.REPORT_VIEW_PERSONAL,
        Permission.REPORT_VIEW_DEPARTMENT,
        Permission.REPORT_VIEW_COMPANY,
        Permission.REPORT_EXPORT_DATA,
        Permission.REPORT_GENERATE_TALENT,
        Permission.REPORT_VIEW_ANALYTICS,
        
        # 部分系统权限
        Permission.SYS_VIEW_AUDIT_LOG,
        Permission.SYS_VIEW_IMPORT_HISTORY,
        
        # 数据访问权限
        Permission.DATA_VIEW_ALL,
        Permission.DATA_CROSS_DEPARTMENT,
        Permission.DATA_VIEW_SELF,
        
        # 特殊权限
        Permission.SPECIAL_REGENERATE_CODE,
        Permission.SPECIAL_VIEW_MAPPING,
    }
    
    # 考评管理员：考评业务管理
    EVAL_ADMIN_PERMISSIONS = {
        # 基础组织权限
        Permission.ORG_VIEW_DEPARTMENT,
        Permission.ORG_VIEW_STAFF,
        Permission.ORG_VIEW_POSITION,
        Permission.ORG_EXPORT_STAFF,
        
        # 完整考评管理权限
        Permission.EVAL_VIEW_TEMPLATE,
        Permission.EVAL_CREATE_TEMPLATE,
        Permission.EVAL_EDIT_TEMPLATE,
        Permission.EVAL_DELETE_TEMPLATE,
        Permission.EVAL_VIEW_BATCH,
        Permission.EVAL_CREATE_BATCH,
        Permission.EVAL_EDIT_BATCH,
        Permission.EVAL_DELETE_BATCH,
        Permission.EVAL_ACTIVATE_BATCH,
        Permission.EVAL_VIEW_RELATION,
        Permission.EVAL_EDIT_RELATION,
        Permission.EVAL_INTELLIGENT_ASSIGN,
        Permission.EVAL_VIEW_RECORD,
        Permission.EVAL_EDIT_RECORD,
        Permission.EVAL_DELETE_RECORD,
        Permission.EVAL_VIEW_PROGRESS,
        Permission.EVAL_MANAGE_PROGRESS,
        Permission.EVAL_VIEW_WEIGHTING,
        Permission.EVAL_MANAGE_WEIGHTING,
        
        # 报告权限
        Permission.REPORT_VIEW_PERSONAL,
        Permission.REPORT_VIEW_DEPARTMENT,
        Permission.REPORT_VIEW_COMPANY,
        Permission.REPORT_EXPORT_DATA,
        Permission.REPORT_GENERATE_TALENT,
        Permission.REPORT_VIEW_ANALYTICS,
        
        # 数据访问权限
        Permission.DATA_VIEW_ALL,
        Permission.DATA_CROSS_DEPARTMENT,
        Permission.DATA_VIEW_SELF,
    }
    
    # 部门经理：本部门数据管理
    DEPT_MANAGER_PERMISSIONS = {
        # 基础组织权限
        Permission.ORG_VIEW_DEPARTMENT,
        Permission.ORG_VIEW_STAFF,
        Permission.ORG_EDIT_STAFF,
        Permission.ORG_VIEW_POSITION,
        Permission.ORG_EXPORT_STAFF,
        
        # 基础考评权限
        Permission.EVAL_VIEW_TEMPLATE,
        Permission.EVAL_VIEW_BATCH,
        Permission.EVAL_VIEW_RELATION,
        Permission.EVAL_VIEW_RECORD,
        Permission.EVAL_VIEW_PROGRESS,
        
        # 报告权限
        Permission.REPORT_VIEW_PERSONAL,
        Permission.REPORT_VIEW_DEPARTMENT,
        Permission.REPORT_EXPORT_DATA,
        Permission.REPORT_VIEW_ANALYTICS,
        
        # 数据访问权限（主要是本部门）
        Permission.DATA_VIEW_DEPARTMENT,
        Permission.DATA_VIEW_SELF,
        
        # 特殊权限
        Permission.SPECIAL_ANONYMOUS_EVAL,
    }
    
    # 普通管理员：基础查看和操作权限
    ADMIN_PERMISSIONS = {
        # 基础组织权限
        Permission.ORG_VIEW_DEPARTMENT,
        Permission.ORG_VIEW_STAFF,
        Permission.ORG_VIEW_POSITION,
        
        # 基础考评权限
        Permission.EVAL_VIEW_TEMPLATE,
        Permission.EVAL_VIEW_BATCH,
        Permission.EVAL_VIEW_RELATION,
        Permission.EVAL_VIEW_RECORD,
        Permission.EVAL_VIEW_PROGRESS,
        
        # 基础报告权限
        Permission.REPORT_VIEW_PERSONAL,
        Permission.REPORT_VIEW_DEPARTMENT,
        Permission.REPORT_VIEW_ANALYTICS,
        
        # 数据访问权限
        Permission.DATA_VIEW_DEPARTMENT,
        Permission.DATA_VIEW_SELF,
        
        # 特殊权限
        Permission.SPECIAL_ANONYMOUS_EVAL,
    }
    
    # 普通员工：最基础权限
    EMPLOYEE_PERMISSIONS = {
        # 最基础的查看权限
        Permission.ORG_VIEW_DEPARTMENT,
        Permission.ORG_VIEW_STAFF,
        
        # 基础考评权限
        Permission.EVAL_VIEW_TEMPLATE,
        Permission.EVAL_VIEW_BATCH,
        
        # 个人报告权限
        Permission.REPORT_VIEW_PERSONAL,
        
        # 自己的数据权限
        Permission.DATA_VIEW_SELF,
        
        # 核心权限：匿名评分
        Permission.SPECIAL_ANONYMOUS_EVAL,
    }
    
    # 完整的角色权限映射
    ROLE_PERMISSIONS = {
        Role.SUPER_ADMIN: SUPER_ADMIN_PERMISSIONS,
        Role.SYSTEM_ADMIN: SYSTEM_ADMIN_PERMISSIONS,
        Role.HR_ADMIN: HR_ADMIN_PERMISSIONS,
        Role.EVAL_ADMIN: EVAL_ADMIN_PERMISSIONS,
        Role.DEPT_MANAGER: DEPT_MANAGER_PERMISSIONS,
        Role.ADMIN: ADMIN_PERMISSIONS,
        Role.EMPLOYEE: EMPLOYEE_PERMISSIONS,
    }


# ==================== RBAC权限管理器 ====================

class RBACPermissionManager:
    """RBAC权限管理器"""
    
    def __init__(self):
        """初始化权限管理器"""
        self.role_permissions = RolePermissionMapping.ROLE_PERMISSIONS
        self.role_hierarchy = Role.HIERARCHY
    
    def get_user_permissions(self, staff) -> Set[str]:
        """
        获取用户的所有权限
        
        Args:
            staff: Staff对象
            
        Returns:
            用户权限集合
        """
        if not staff or not hasattr(staff, 'role'):
            return set()
        
        # 从缓存中获取权限
        cache_key = f"user_permissions_{staff.id}_{staff.role}"
        permissions = cache.get(cache_key)
        
        if permissions is None:
            # 获取角色对应的权限
            permissions = self.role_permissions.get(staff.role, set())
            
            # 缓存权限信息（缓存15分钟）
            cache.set(cache_key, permissions, 900)
            
            logger.info(f"加载用户权限: {staff.username}, 角色: {staff.role}, 权限数: {len(permissions)}")
        
        return permissions
    
    def has_permission(self, staff, permission: str) -> bool:
        """
        检查用户是否具有指定权限
        
        Args:
            staff: Staff对象
            permission: 权限字符串
            
        Returns:
            是否具有权限
        """
        if not staff:
            return False
        
        # 超级管理员拥有所有权限
        if hasattr(staff, 'role') and staff.role == Role.SUPER_ADMIN:
            return True
        
        user_permissions = self.get_user_permissions(staff)
        return permission in user_permissions
    
    def has_role(self, staff, role: str) -> bool:
        """
        检查用户是否具有指定角色
        
        Args:
            staff: Staff对象
            role: 角色字符串
            
        Returns:
            是否具有角色
        """
        if not staff or not hasattr(staff, 'role'):
            return False
        
        return staff.role == role
    
    def has_role_or_higher(self, staff, min_role: str) -> bool:
        """
        检查用户是否具有指定角色或更高级角色
        
        Args:
            staff: Staff对象
            min_role: 最低角色要求
            
        Returns:
            是否满足角色要求
        """
        if not staff or not hasattr(staff, 'role'):
            return False
        
        user_level = self.role_hierarchy.get(staff.role, 999)
        required_level = self.role_hierarchy.get(min_role, 999)
        
        return user_level <= required_level
    
    def can_access_department_data(self, staff, target_department_id: int) -> bool:
        """
        检查用户是否可以访问指定部门的数据
        
        Args:
            staff: Staff对象
            target_department_id: 目标部门ID
            
        Returns:
            是否可以访问
        """
        if not staff:
            return False
        
        # 检查全局数据访问权限
        if self.has_permission(staff, Permission.DATA_VIEW_ALL):
            return True
        
        # 检查跨部门访问权限
        if self.has_permission(staff, Permission.DATA_CROSS_DEPARTMENT):
            return True
        
        # 检查本部门数据访问权限
        if (self.has_permission(staff, Permission.DATA_VIEW_DEPARTMENT) and 
            hasattr(staff, 'department') and staff.department and
            staff.department.id == target_department_id):
            return True
        
        return False
    
    def get_accessible_departments(self, staff) -> List[int]:
        """
        获取用户可访问的部门列表
        
        Args:
            staff: Staff对象
            
        Returns:
            可访问的部门ID列表
        """
        if not staff:
            return []
        
        # 有全局访问权限，返回所有部门
        if self.has_permission(staff, Permission.DATA_VIEW_ALL):
            from organizations.models import Department
            return list(Department.objects.filter(deleted_at__isnull=True).values_list('id', flat=True))
        
        # 有跨部门权限，返回所有部门
        if self.has_permission(staff, Permission.DATA_CROSS_DEPARTMENT):
            from organizations.models import Department
            return list(Department.objects.filter(deleted_at__isnull=True).values_list('id', flat=True))
        
        # 只能访问本部门
        if (self.has_permission(staff, Permission.DATA_VIEW_DEPARTMENT) and 
            hasattr(staff, 'department') and staff.department):
            return [staff.department.id]
        
        return []
    
    def clear_user_permissions_cache(self, staff):
        """
        清除用户权限缓存
        
        Args:
            staff: Staff对象
        """
        if staff:
            cache_key = f"user_permissions_{staff.id}_{staff.role}"
            cache.delete(cache_key)
            logger.info(f"清除用户权限缓存: {staff.username}")


# ==================== 全局权限管理器实例 ====================

# 创建全局权限管理器实例
permission_manager = RBACPermissionManager()


# ==================== 权限检查函数 ====================

def has_permission(staff, permission: str) -> bool:
    """
    检查用户权限（全局函数）
    
    Args:
        staff: Staff对象
        permission: 权限字符串
        
    Returns:
        是否具有权限
    """
    return permission_manager.has_permission(staff, permission)

def has_role(staff, role: str) -> bool:
    """
    检查用户角色（全局函数）
    
    Args:
        staff: Staff对象
        role: 角色字符串
        
    Returns:
        是否具有角色
    """
    return permission_manager.has_role(staff, role)

def has_role_or_higher(staff, min_role: str) -> bool:
    """
    检查用户角色等级（全局函数）
    
    Args:
        staff: Staff对象
        min_role: 最低角色要求
        
    Returns:
        是否满足角色要求
    """
    return permission_manager.has_role_or_higher(staff, min_role)

def can_access_department_data(staff, department_id: int) -> bool:
    """
    检查部门数据访问权限（全局函数）
    
    Args:
        staff: Staff对象
        department_id: 部门ID
        
    Returns:
        是否可以访问
    """
    return permission_manager.can_access_department_data(staff, department_id)


# ==================== 权限装饰器 ====================

def require_permission(permission: str, redirect_url: str = '/admin/login/', 
                      ajax_response: bool = True):
    """
    权限检查装饰器
    
    Args:
        permission: 必需的权限
        redirect_url: 权限不足时的重定向URL
        ajax_response: 是否支持Ajax响应
        
    Returns:
        装饰器函数
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # 检查用户是否登录（兼容current_staff和staff两种命名）
            staff = getattr(request, 'current_staff', None) or getattr(request, 'staff', None)
            if not staff:
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest' and ajax_response:
                    return JsonResponse({
                        'success': False,
                        'error': '请先登录',
                        'error_code': 'AUTH_REQUIRED'
                    }, status=401)
                
                messages.error(request, '请先登录')
                return redirect(redirect_url)
            
            # 设置兼容性属性
            if not hasattr(request, 'staff'):
                request.staff = staff
            if not hasattr(request, 'current_staff'):
                request.current_staff = staff
            
            # 检查权限
            if not has_permission(staff, permission):
                error_msg = f'权限不足，需要权限：{permission}'
                
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest' and ajax_response:
                    return JsonResponse({
                        'success': False,
                        'error': error_msg,
                        'error_code': 'PERMISSION_DENIED'
                    }, status=403)
                
                messages.error(request, '权限不足')
                return HttpResponseForbidden('权限不足')
            
            return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator

def require_role(role: str, redirect_url: str = '/admin/login/', 
                ajax_response: bool = True):
    """
    角色检查装饰器
    
    Args:
        role: 必需的角色
        redirect_url: 角色不符时的重定向URL
        ajax_response: 是否支持Ajax响应
        
    Returns:
        装饰器函数
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # 检查用户是否登录（兼容current_staff和staff两种命名）
            staff = getattr(request, 'current_staff', None) or getattr(request, 'staff', None)
            if not staff:
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest' and ajax_response:
                    return JsonResponse({
                        'success': False,
                        'error': '请先登录',
                        'error_code': 'AUTH_REQUIRED'
                    }, status=401)
                
                messages.error(request, '请先登录')
                return redirect(redirect_url)
            
            # 设置兼容性属性
            if not hasattr(request, 'staff'):
                request.staff = staff
            if not hasattr(request, 'current_staff'):
                request.current_staff = staff
            
            # 检查角色
            if not has_role_or_higher(staff, role):
                error_msg = f'角色权限不足，需要角色：{role}'
                
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest' and ajax_response:
                    return JsonResponse({
                        'success': False,
                        'error': error_msg,
                        'error_code': 'ROLE_DENIED'
                    }, status=403)
                
                messages.error(request, '角色权限不足')
                return HttpResponseForbidden('角色权限不足')
            
            return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator

def require_department_access(department_param: str = 'department_id', 
                            redirect_url: str = '/admin/login/',
                            ajax_response: bool = True):
    """
    部门数据访问权限装饰器
    
    Args:
        department_param: 部门ID参数名
        redirect_url: 权限不足时的重定向URL
        ajax_response: 是否支持Ajax响应
        
    Returns:
        装饰器函数
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # 检查用户是否登录（兼容current_staff和staff两种命名）
            staff = getattr(request, 'current_staff', None) or getattr(request, 'staff', None)
            if not staff:
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest' and ajax_response:
                    return JsonResponse({
                        'success': False,
                        'error': '请先登录',
                        'error_code': 'AUTH_REQUIRED'
                    }, status=401)
                
                messages.error(request, '请先登录')
                return redirect(redirect_url)
            
            # 设置兼容性属性
            if not hasattr(request, 'staff'):
                request.staff = staff
            if not hasattr(request, 'current_staff'):
                request.current_staff = staff
            
            # 获取部门ID
            department_id = kwargs.get(department_param) or request.GET.get(department_param)
            
            if department_id:
                try:
                    department_id = int(department_id)
                except (ValueError, TypeError):
                    department_id = None
            
            # 如果没有指定部门ID，检查是否有全局权限
            if not department_id:
                if not has_permission(staff, Permission.DATA_VIEW_ALL):
                    error_msg = '无法确定部门信息'
                    
                    if request.headers.get('X-Requested-With') == 'XMLHttpRequest' and ajax_response:
                        return JsonResponse({
                            'success': False,
                            'error': error_msg,
                            'error_code': 'DEPARTMENT_REQUIRED'
                        }, status=400)
                    
                    messages.error(request, error_msg)
                    return HttpResponseForbidden(error_msg)
            else:
                # 检查部门访问权限
                if not can_access_department_data(staff, department_id):
                    error_msg = '无权访问该部门数据'
                    
                    if request.headers.get('X-Requested-With') == 'XMLHttpRequest' and ajax_response:
                        return JsonResponse({
                            'success': False,
                            'error': error_msg,
                            'error_code': 'DEPARTMENT_ACCESS_DENIED'
                        }, status=403)
                    
                    messages.error(request, error_msg)
                    return HttpResponseForbidden(error_msg)
            
            return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator

def require_self_or_permission(permission: str, staff_param: str = 'staff_id',
                              redirect_url: str = '/admin/login/',
                              ajax_response: bool = True):
    """
    自己数据或特定权限装饰器
    
    Args:
        permission: 管理他人数据需要的权限
        staff_param: 员工ID参数名
        redirect_url: 权限不足时的重定向URL
        ajax_response: 是否支持Ajax响应
        
    Returns:
        装饰器函数
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # 检查用户是否登录（兼容current_staff和staff两种命名）
            staff = getattr(request, 'current_staff', None) or getattr(request, 'staff', None)
            if not staff:
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest' and ajax_response:
                    return JsonResponse({
                        'success': False,
                        'error': '请先登录',
                        'error_code': 'AUTH_REQUIRED'
                    }, status=401)
                
                messages.error(request, '请先登录')
                return redirect(redirect_url)
            
            # 设置兼容性属性
            if not hasattr(request, 'staff'):
                request.staff = staff
            if not hasattr(request, 'current_staff'):
                request.current_staff = staff
            
            # 获取目标员工ID
            target_staff_id = kwargs.get(staff_param) or request.GET.get(staff_param)
            
            if target_staff_id:
                try:
                    target_staff_id = int(target_staff_id)
                except (ValueError, TypeError):
                    target_staff_id = None
            
            # 如果是访问自己的数据，直接允许
            if target_staff_id and target_staff_id == staff.id:
                return view_func(request, *args, **kwargs)
            
            # 访问他人数据，检查权限
            if not has_permission(staff, permission):
                error_msg = f'无权访问他人数据，需要权限：{permission}'
                
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest' and ajax_response:
                    return JsonResponse({
                        'success': False,
                        'error': error_msg,
                        'error_code': 'PERMISSION_DENIED'
                    }, status=403)
                
                messages.error(request, '权限不足')
                return HttpResponseForbidden('权限不足')
            
            return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator


# ==================== 权限工具函数 ====================

def get_role_display_name(role: str) -> str:
    """
    获取角色的中文显示名称
    
    Args:
        role: 角色字符串
        
    Returns:
        中文角色名称
    """
    role_dict = dict(Role.CHOICES)
    return role_dict.get(role, '未知角色')

def get_all_permissions() -> List[Tuple[str, str]]:
    """
    获取所有权限的列表
    
    Returns:
        权限列表 [(权限代码, 权限描述), ...]
    """
    permissions = []
    
    # 通过反射获取Permission类中的所有权限常量
    for attr_name in dir(Permission):
        if not attr_name.startswith('_'):
            permission_code = getattr(Permission, attr_name)
            if isinstance(permission_code, str) and '.' in permission_code:
                # 生成权限描述
                category, action = permission_code.split('.', 1)
                category_map = {
                    'org': '组织管理',
                    'eval': '考评管理', 
                    'report': '报告权限',
                    'sys': '系统管理',
                    'data': '数据访问',
                    'special': '特殊权限'
                }
                category_name = category_map.get(category, category)
                permission_desc = f"{category_name} - {action}"
                permissions.append((permission_code, permission_desc))
    
    return sorted(permissions)

def get_role_permissions(role: str) -> Set[str]:
    """
    获取指定角色的权限集合
    
    Args:
        role: 角色字符串
        
    Returns:
        权限集合
    """
    return RolePermissionMapping.ROLE_PERMISSIONS.get(role, set())

def is_higher_role(role1: str, role2: str) -> bool:
    """
    检查role1是否比role2级别更高
    
    Args:
        role1: 角色1
        role2: 角色2
        
    Returns:
        role1是否级别更高
    """
    level1 = Role.HIERARCHY.get(role1, 999)
    level2 = Role.HIERARCHY.get(role2, 999)
    return level1 < level2


# ==================== 安全审计函数 ====================

def log_permission_check(staff, permission: str, result: bool, context: str = ''):
    """
    记录权限检查日志
    
    Args:
        staff: Staff对象
        permission: 检查的权限
        result: 检查结果
        context: 上下文信息
    """
    try:
        from common.models import AuditLog
        
        AuditLog.objects.create(
            table_name='permission_check',
            record_id=staff.id if staff else 0,
            action='PERMISSION_CHECK',
            field_name=permission,
            old_value='',
            new_value=str(result),
            created_by=staff if staff else None,
            ip_address='127.0.0.1',
            user_agent='Permission System',
            extra_data={
                'permission': permission,
                'result': result,
                'context': context,
                'role': staff.role if staff else 'anonymous'
            }
        )
    except Exception as e:
        logger.error(f"权限检查日志记录失败: {str(e)}")

def log_role_change(staff, old_role: str, new_role: str, operator_staff):
    """
    记录角色变更日志
    
    Args:
        staff: 被修改的员工
        old_role: 原角色
        new_role: 新角色
        operator_staff: 操作人员
    """
    try:
        from common.models import AuditLog
        
        AuditLog.objects.create(
            user=operator_staff.username if operator_staff else 'system',
            action='update',
            target_model='Staff',
            target_id=staff.id,
            description=f'角色变更: {get_role_display_name(old_role)} -> {get_role_display_name(new_role)}',
            created_by=operator_staff.username if operator_staff else 'system',
            ip_address='127.0.0.1',
            user_agent='Role Management System',
            extra_data={
                'field_name': 'role',
                'old_value': old_role,
                'new_value': new_role,
                'staff_name': staff.name,
                'staff_employee_no': staff.employee_no,
                'target_staff': staff.username,
                'old_role_display': get_role_display_name(old_role),
                'new_role_display': get_role_display_name(new_role),
                'operator': operator_staff.username if operator_staff else 'system'
            }
        )
        
        # 清除权限缓存
        permission_manager.clear_user_permissions_cache(staff)
        
        logger.info(f"角色变更: {staff.username} {old_role} -> {new_role} by {operator_staff.username if operator_staff else 'system'}")
        
    except Exception as e:
        logger.error(f"角色变更日志记录失败: {str(e)}")