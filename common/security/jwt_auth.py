# -*- coding: utf-8 -*-
"""
JWT认证核心实现
提供token生成、验证、刷新等功能
"""
import jwt
import uuid
import time
from datetime import datetime, timedelta
from django.conf import settings
from django.utils import timezone
from django.core.cache import cache
from common.exceptions import TokenException, AccountLockedException
import logging

logger = logging.getLogger(__name__)

class JWTAuthentication:
    """JWT认证管理器"""
    
    # Token配置
    ACCESS_TOKEN_LIFETIME = timedelta(hours=8)      # 访问token 8小时
    REFRESH_TOKEN_LIFETIME = timedelta(days=7)      # 刷新token 7天
    ALGORITHM = 'HS256'
    
    @classmethod
    def generate_tokens(cls, staff):
        """
        为用户生成访问token和刷新token
        
        Args:
            staff: Staff实例
            
        Returns:
            dict: {'access_token': str, 'refresh_token': str, 'expires_in': int}
        """
        try:
            now = timezone.now()
            
            # 生成唯一的token ID
            token_id = str(uuid.uuid4())
            
            # Access Token payload
            access_payload = {
                'staff_id': staff.id,
                'username': staff.username,
                'role': staff.role,
                'department_id': staff.department_id if staff.department else None,
                'is_manager': staff.is_manager,
                'token_type': 'access',
                'token_id': token_id,
                'iat': int(now.timestamp()),
                'exp': int((now + cls.ACCESS_TOKEN_LIFETIME).timestamp())
            }
            
            # Refresh Token payload
            refresh_payload = {
                'staff_id': staff.id,
                'token_type': 'refresh',
                'token_id': token_id,
                'iat': int(now.timestamp()),
                'exp': int((now + cls.REFRESH_TOKEN_LIFETIME).timestamp())
            }
            
            # 生成tokens
            access_token = jwt.encode(access_payload, settings.SECRET_KEY, algorithm=cls.ALGORITHM)
            refresh_token = jwt.encode(refresh_payload, settings.SECRET_KEY, algorithm=cls.ALGORITHM)
            
            # 更新用户最后刷新时间
            staff.last_token_refresh = now
            staff.save(update_fields=['last_token_refresh'])
            
            # 将token信息存储到缓存（用于token撤销）
            cache_key = f"active_token_{staff.id}_{token_id}"
            cache.set(cache_key, {
                'staff_id': staff.id,
                'token_id': token_id,
                'created_at': now.isoformat(),
                'is_active': True
            }, timeout=int(cls.REFRESH_TOKEN_LIFETIME.total_seconds()))
            
            logger.info(f"为用户 {staff.username} 生成新的JWT token")
            
            return {
                'access_token': access_token,
                'refresh_token': refresh_token,
                'expires_in': int(cls.ACCESS_TOKEN_LIFETIME.total_seconds()),
                'token_type': 'Bearer'
            }
            
        except Exception as e:
            logger.error(f"生成JWT token失败: {str(e)}")
            raise TokenException("Token生成失败")
    
    @classmethod
    def verify_token(cls, token, token_type='access'):
        """
        验证token有效性
        
        Args:
            token: JWT token字符串
            token_type: token类型 ('access' 或 'refresh')
            
        Returns:
            dict: 解码后的payload，如果失败返回None
        """
        try:
            # 解码token
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[cls.ALGORITHM])
            
            # 验证token类型
            if payload.get('token_type') != token_type:
                logger.warning(f"Token类型不匹配: 期望{token_type}, 实际{payload.get('token_type')}")
                return None
            
            # 检查token是否在缓存中（是否被撤销）
            staff_id = payload.get('staff_id')
            token_id = payload.get('token_id')
            cache_key = f"active_token_{staff_id}_{token_id}"
            
            token_cache = cache.get(cache_key)
            if token_cache and not token_cache.get('is_active'):
                # 明确被标记为非活跃状态，说明token已被撤销
                logger.warning(f"Token已被撤销: {token_id}")
                return None
            elif not token_cache:
                # 缓存不存在，可能是服务器重启导致，检查是否在黑名单中
                blacklist_key = f"blacklisted_token_{token_id}"
                if cache.get(blacklist_key):
                    logger.warning(f"Token在黑名单中: {token_id}")
                    return None
                
                # 重新创建缓存条目（只有在token本身有效且未被撤销的情况下）
                logger.debug(f"Token缓存丢失，重新创建缓存条目: {token_id}")  # 改为debug级别
                
                # 计算剩余生命周期（使用timezone.now()保持一致性）
                issued_at = datetime.fromtimestamp(payload.get('iat', 0))
                expires_at = datetime.fromtimestamp(payload.get('exp', 0))
                now = timezone.now().replace(tzinfo=None)  # 转换为naive datetime进行比较
                
                if expires_at > now:
                    # Token仍在有效期内，重新创建缓存
                    remaining_seconds = int((expires_at - now).total_seconds())
                    
                    cache.set(cache_key, {
                        'staff_id': staff_id,
                        'token_id': token_id,
                        'created_at': issued_at.isoformat(),
                        'is_active': True,
                        'recreated': True  # 标记为重新创建的缓存
                    }, timeout=remaining_seconds)
                    
                    logger.debug(f"已重新创建Token缓存，剩余时间: {remaining_seconds}秒")  # 改为debug级别
                else:
                    logger.warning(f"Token已过期，无法重新创建缓存: {token_id}")
                    return None
            
            # 验证用户是否仍然存在且激活
            try:
                from organizations.models import Staff
                staff = Staff.objects.get(id=staff_id, is_active=True, deleted_at__isnull=True)
            except Staff.DoesNotExist:
                logger.warning(f"Token对应的用户不存在或已禁用: {staff_id}")
                return None
            
            return payload
            
        except jwt.ExpiredSignatureError:
            logger.info("Token已过期")
            return None
        except jwt.InvalidTokenError as e:
            logger.warning(f"无效的token: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Token验证失败: {str(e)}")
            return None
    
    @classmethod
    def refresh_access_token(cls, refresh_token):
        """
        使用refresh token刷新access token
        
        Args:
            refresh_token: 刷新token
            
        Returns:
            dict: 新的access token信息
        """
        try:
            # 验证refresh token
            payload = cls.verify_token(refresh_token, 'refresh')
            if not payload:
                raise TokenException("Invalid refresh token")
            
            # 获取用户信息
            from organizations.models import Staff
            staff = Staff.objects.get(id=payload['staff_id'])
            
            # 生成新的access token（保持相同的token_id）
            now = timezone.now()
            new_payload = {
                'staff_id': staff.id,
                'username': staff.username,
                'role': staff.role,
                'department_id': staff.department_id if staff.department else None,
                'is_manager': staff.is_manager,
                'token_type': 'access',
                'token_id': payload['token_id'],  # 保持相同的token_id
                'iat': int(now.timestamp()),
                'exp': int((now + cls.ACCESS_TOKEN_LIFETIME).timestamp())
            }
            
            new_access_token = jwt.encode(new_payload, settings.SECRET_KEY, algorithm=cls.ALGORITHM)
            
            logger.info(f"为用户 {staff.username} 刷新access token")
            
            return {
                'access_token': new_access_token,
                'expires_in': int(cls.ACCESS_TOKEN_LIFETIME.total_seconds()),
                'token_type': 'Bearer'
            }
            
        except Exception as e:
            logger.error(f"刷新token失败: {str(e)}")
            raise TokenException("Token刷新失败")
    
    @classmethod
    def revoke_token(cls, staff_id, token_id=None):
        """
        撤销用户的token
        
        Args:
            staff_id: 用户ID
            token_id: 特定token ID，如果为None则撤销所有tokens
        """
        try:
            if token_id:
                # 撤销特定token
                cache_key = f"active_token_{staff_id}_{token_id}"
                
                # 将token设置为非活跃状态
                cache.set(cache_key, {
                    'staff_id': staff_id,
                    'token_id': token_id,
                    'revoked_at': timezone.now().isoformat(),
                    'is_active': False
                }, timeout=int(cls.REFRESH_TOKEN_LIFETIME.total_seconds()))
                
                # 同时加入黑名单
                TokenManager.add_token_to_blacklist(token_id)
                
                logger.info(f"撤销用户 {staff_id} 的token: {token_id}")
            else:
                # 撤销用户所有tokens
                # 通过修改用户的last_token_refresh时间来使所有旧token失效
                try:
                    from organizations.models import Staff
                    staff = Staff.objects.get(id=staff_id)
                    staff.last_token_refresh = timezone.now()
                    staff.save(update_fields=['last_token_refresh'])
                    logger.info(f"撤销用户 {staff_id} 的所有tokens")
                except Staff.DoesNotExist:
                    pass
                    
        except Exception as e:
            logger.error(f"撤销token失败: {str(e)}")
    
    @classmethod
    def get_token_info(cls, token):
        """
        获取token信息（不验证有效性）
        
        Args:
            token: JWT token
            
        Returns:
            dict: token信息
        """
        try:
            # 解码token但不验证过期时间
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[cls.ALGORITHM], options={"verify_exp": False})
            return {
                'staff_id': payload.get('staff_id'),
                'username': payload.get('username'),
                'token_type': payload.get('token_type'),
                'token_id': payload.get('token_id'),
                'issued_at': datetime.fromtimestamp(payload.get('iat', 0)),
                'expires_at': datetime.fromtimestamp(payload.get('exp', 0)),
                'is_expired': datetime.fromtimestamp(payload.get('exp', 0)) < timezone.now().replace(tzinfo=None)
            }
        except Exception as e:
            logger.error(f"获取token信息失败: {str(e)}")
            return None
    
    @classmethod
    def cleanup_expired_tokens(cls):
        """
        清理过期的token缓存
        这个方法可以通过定时任务调用
        """
        try:
            # 这里可以实现清理过期token的逻辑
            # 由于我们使用了Redis的TTL机制，过期的缓存会自动清理
            # 这个方法主要用于统计和日志记录
            logger.info("Token清理任务执行完成")
        except Exception as e:
            logger.error(f"Token清理失败: {str(e)}")

class TokenManager:
    """Token管理器 - 提供更高级的token管理功能"""
    
    @staticmethod
    def get_user_active_tokens(staff_id):
        """
        获取用户的所有活跃token
        
        Args:
            staff_id: 用户ID
            
        Returns:
            list: 活跃token列表
        """
        try:
            # 从缓存中查找该用户的所有token
            # 这里需要实现缓存模式匹配，简化实现
            active_tokens = []
            
            # 由于Redis的限制，这里返回空列表
            # 在实际实现中可以考虑使用Redis的SCAN命令
            
            return active_tokens
        except Exception as e:
            logger.error(f"获取用户活跃token失败: {str(e)}")
            return []
    
    @staticmethod
    def revoke_all_user_tokens(staff_id):
        """
        撤销用户的所有token
        
        Args:
            staff_id: 用户ID
        """
        try:
            JWTAuthentication.revoke_token(staff_id)
            logger.info(f"已撤销用户 {staff_id} 的所有tokens")
        except Exception as e:
            logger.error(f"撤销用户所有tokens失败: {str(e)}")
    
    @staticmethod
    def is_token_blacklisted(token_id):
        """
        检查token是否在黑名单中
        
        Args:
            token_id: token ID
            
        Returns:
            bool: 是否在黑名单中
        """
        try:
            blacklist_key = f"blacklisted_token_{token_id}"
            return cache.get(blacklist_key) is not None
        except Exception as e:
            logger.error(f"检查token黑名单失败: {str(e)}")
            return False
    
    @staticmethod
    def add_token_to_blacklist(token_id, expire_time=None):
        """
        将token添加到黑名单
        
        Args:
            token_id: token ID
            expire_time: 过期时间（秒）
        """
        try:
            blacklist_key = f"blacklisted_token_{token_id}"
            expire_time = expire_time or int(JWTAuthentication.REFRESH_TOKEN_LIFETIME.total_seconds())
            cache.set(blacklist_key, True, timeout=expire_time)
            logger.info(f"Token {token_id} 已添加到黑名单")
        except Exception as e:
            logger.error(f"添加token到黑名单失败: {str(e)}")

# JWT工具函数
def extract_token_from_header(authorization_header):
    """
    从Authorization头中提取token
    
    Args:
        authorization_header: Authorization头的值
        
    Returns:
        str: token字符串，如果格式不正确返回None
    """
    if not authorization_header:
        return None
    
    parts = authorization_header.split()
    if len(parts) != 2 or parts[0].lower() != 'bearer':
        return None
    
    return parts[1]

def create_token_response(tokens, user_info=None):
    """
    创建标准的token响应格式
    
    Args:
        tokens: token信息
        user_info: 用户信息（可选）
        
    Returns:
        dict: 标准响应格式
    """
    response = {
        'success': True,
        'tokens': tokens,
        'message': '认证成功'
    }
    
    if user_info:
        response['user'] = user_info
    
    return response