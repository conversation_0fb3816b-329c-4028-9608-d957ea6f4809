# -*- coding: utf-8 -*-
"""
JWT认证装饰器
提供基于JWT token的认证装饰器功能
"""

from functools import wraps
from django.http import JsonResponse
from common.security.jwt_auth import JWTAuthentication, extract_token_from_header
import logging

logger = logging.getLogger(__name__)

def require_auth(view_func):
    """
    JWT认证装饰器
    
    功能：
    1. 验证请求中的JWT token
    2. 解析token并设置用户信息到request对象
    3. 处理认证失败的各种情况
    
    使用方式：
    @require_auth
    def my_view(request):
        # 此时request中已经包含current_staff信息
        return JsonResponse({'success': True})
    
    返回值：
    - 认证成功：正常执行视图函数
    - 认证失败：返回401状态码的JSON响应
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        try:
            # 从请求头获取Authorization
            auth_header = request.headers.get('Authorization')
            if not auth_header:
                return JsonResponse({
                    'success': False,
                    'message': '缺少认证信息',
                    'code': 'MISSING_AUTH'
                }, status=401)
            
            # 提取token
            token = extract_token_from_header(auth_header)
            if not token:
                return JsonResponse({
                    'success': False,
                    'message': '认证信息格式错误',
                    'code': 'INVALID_AUTH_FORMAT'
                }, status=401)
            
            # 验证token
            payload = JWTAuthentication.verify_token(token)
            if not payload:
                return JsonResponse({
                    'success': False,
                    'message': 'Token无效或已过期',
                    'code': 'INVALID_TOKEN'
                }, status=401)
            
            # 获取用户信息
            from organizations.models import Staff
            try:
                staff = Staff.objects.get(
                    id=payload.get('staff_id'),
                    is_active=True,
                    deleted_at__isnull=True
                )
            except Staff.DoesNotExist:
                return JsonResponse({
                    'success': False,
                    'message': '用户不存在或已被禁用',
                    'code': 'USER_NOT_FOUND'
                }, status=401)
            
            # 设置用户信息到request对象
            request.current_staff = staff
            request.staff = staff
            request.token_payload = payload
            
            # 记录访问日志
            logger.debug(f"用户认证成功: {staff.username} (ID: {staff.id})")
            
            return view_func(request, *args, **kwargs)
            
        except Exception as e:
            logger.error(f"JWT认证异常: {str(e)}")
            return JsonResponse({
                'success': False,
                'message': '认证处理失败',
                'code': 'AUTH_ERROR'
            }, status=500)
    
    return wrapper

def require_auth_or_login(view_func):
    """
    JWT认证或登录检查装饰器（兼容Web和API）
    
    功能：
    1. 优先检查JWT token
    2. 无token时检查session登录状态
    3. 支持Web页面重定向
    
    使用方式：
    @require_auth_or_login
    def my_view(request):
        return render(request, 'template.html')
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        try:
            # 检查JWT token
            auth_header = request.headers.get('Authorization')
            if auth_header:
                token = extract_token_from_header(auth_header)
                if token:
                    payload = JWTAuthentication.verify_token(token)
                    if payload:
                        from organizations.models import Staff
                        try:
                            staff = Staff.objects.get(
                                id=payload.get('staff_id'),
                                is_active=True,
                                deleted_at__isnull=True
                            )
                            request.current_staff = staff
                            request.staff = staff
                            request.token_payload = payload
                            return view_func(request, *args, **kwargs)
                        except Staff.DoesNotExist:
                            pass
            
            # 检查session登录
            if hasattr(request, 'current_staff') and request.current_staff:
                return view_func(request, *args, **kwargs)
            
            # 检查session中的staff
            if hasattr(request, 'staff') and request.staff:
                request.current_staff = request.staff
                return view_func(request, *args, **kwargs)
            
            # 检查是否是API请求
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': False,
                    'message': '请先登录',
                    'code': 'AUTH_REQUIRED'
                }, status=401)
            
            # Web页面重定向到登录页
            from django.shortcuts import redirect
            return redirect('/login/')
            
        except Exception as e:
            logger.error(f"认证检查异常: {str(e)}")
            return JsonResponse({
                'success': False,
                'message': '认证处理失败',
                'code': 'AUTH_ERROR'
            }, status=500)
    
    return wrapper

def require_auth_api(view_func):
    """
    严格的API认证装饰器（仅支持JWT）
    
    功能：
    1. 只接受JWT token认证
    2. 拒绝session认证
    3. 专为API设计
    
    使用方式：
    @require_auth_api
    def api_view(request):
        return JsonResponse({'data': 'success'})
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        try:
            # 强制要求Authorization头
            auth_header = request.headers.get('Authorization')
            if not auth_header:
                return JsonResponse({
                    'success': False,
                    'message': '必须提供Authorization头',
                    'code': 'AUTH_HEADER_REQUIRED'
                }, status=401)
            
            # 提取token
            token = extract_token_from_header(auth_header)
            if not token:
                return JsonResponse({
                    'success': False,
                    'message': 'Authorization头格式错误，应为: Bearer <token>',
                    'code': 'INVALID_AUTH_HEADER'
                }, status=401)
            
            # 验证token
            payload = JWTAuthentication.verify_token(token)
            if not payload:
                return JsonResponse({
                    'success': False,
                    'message': 'Token无效、已过期或已被撤销',
                    'code': 'TOKEN_INVALID'
                }, status=401)
            
            # 获取用户信息
            from organizations.models import Staff
            try:
                staff = Staff.objects.get(
                    id=payload.get('staff_id'),
                    is_active=True,
                    deleted_at__isnull=True
                )
            except Staff.DoesNotExist:
                return JsonResponse({
                    'success': False,
                    'message': '用户不存在或已被禁用',
                    'code': 'USER_NOT_FOUND'
                }, status=401)
            
            # 设置用户信息
            request.current_staff = staff
            request.staff = staff
            request.token_payload = payload
            
            return view_func(request, *args, **kwargs)
            
        except Exception as e:
            logger.error(f"API认证异常: {str(e)}")
            return JsonResponse({
                'success': False,
                'message': '认证处理失败',
                'code': 'AUTH_ERROR'
            }, status=500)
    
    return wrapper