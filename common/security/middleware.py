# -*- coding: utf-8 -*-
"""
JWT认证中间件
替换原有的CustomAuthMiddleware
"""
from django.http import JsonResponse
from django.utils.deprecation import MiddlewareMixin
from django.shortcuts import redirect
from django.contrib import messages
from .jwt_auth import JWTAuthentication, extract_token_from_header
from organizations.models import Staff
from common.exceptions import TokenException
import logging

logger = logging.getLogger(__name__)

class JWTAuthenticationMiddleware(MiddlewareMixin):
    """
    JWT认证中间件
    支持JWT和Session双重认证模式（平滑过渡）
    """
    
    # 不需要认证的URL路径
    EXCLUDE_PATHS = [
        '/admin/login/',
        '/anonymous/login/',
        '/api/login/',                # JWT API登录端点
        '/api/logout/',               # JWT API登出端点
        '/api/token/refresh/',        # JWT Token刷新端点
        '/api/token/status/',         # JWT Token状态查询端点（用于测试）
        '/api/auth/login/',           # 备用登录端点
        '/api/auth/refresh/',         # 备用刷新端点
        '/static/',
        '/media/',
        '/favicon.ico',
        '/health/',
    ]
    
    def process_request(self, request):
        """处理请求，进行身份认证"""
        
        # 检查是否是排除路径
        if self._should_skip_auth(request):
            request.current_staff = None
            request.is_authenticated = False
            request.auth_method = None
            return None
        
        # 初始化用户为None
        request.current_staff = None
        request.is_authenticated = False
        
        # 1. 尝试JWT认证
        jwt_staff = self._authenticate_jwt(request)
        if jwt_staff:
            request.current_staff = jwt_staff
            request.is_authenticated = True
            request.auth_method = 'jwt'
            return None
        
        # 2. 兼容性：尝试Session认证（逐步废弃）
        session_staff = self._authenticate_session(request)
        if session_staff:
            request.current_staff = session_staff
            request.is_authenticated = True
            request.auth_method = 'session'
            return None
        
        # 3. 未认证用户
        request.auth_method = None
        return None
    
    def _should_skip_auth(self, request):
        """检查是否应该跳过认证"""
        path = request.path
        
        # 检查排除路径
        for exclude_path in self.EXCLUDE_PATHS:
            if path.startswith(exclude_path):
                return True
        
        # OPTIONS请求跳过认证
        if request.method == 'OPTIONS':
            return True
        
        return False
    
    def _authenticate_jwt(self, request):
        """JWT认证"""
        try:
            # 从Authorization头获取token
            auth_header = request.META.get('HTTP_AUTHORIZATION')
            if not auth_header:
                # 尝试从Cookie获取token（支持传统web应用）
                token = request.COOKIES.get('access_token')
                if not token:
                    logger.debug(f"JWT认证失败: 未找到access_token cookie，请求路径: {request.path}")
                    return None
            else:
                token = extract_token_from_header(auth_header)
                if not token:
                    logger.debug(f"JWT认证失败: Authorization头格式错误，请求路径: {request.path}")
                    return None
            
            # 验证token
            payload = JWTAuthentication.verify_token(token, 'access')
            if not payload:
                logger.debug(f"JWT认证失败: token验证失败，请求路径: {request.path}")
                return None
            
            # 获取用户信息
            staff = Staff.objects.select_related('department', 'position').get(
                id=payload['staff_id'],
                is_active=True,
                deleted_at__isnull=True
            )
            
            # 检查账户锁定状态
            if staff.is_account_locked():
                logger.warning(f"尝试使用锁定的账户登录: {staff.username}")
                return None
            
            logger.debug(f"JWT认证成功: 用户 {staff.username}，请求路径: {request.path}")
            return staff
            
        except (Staff.DoesNotExist, Exception) as e:
            logger.debug(f"JWT认证失败: {str(e)}，请求路径: {request.path}")
            return None
    
    def _authenticate_session(self, request):
        """Session认证（兼容性支持）"""
        try:
            staff_id = request.session.get('staff_id')
            if not staff_id:
                logger.debug(f"Session认证失败: 未找到staff_id，请求路径: {request.path}")
                return None
            
            # 检查是否是管理端登录
            is_admin = request.session.get('admin_login', False)
            is_anonymous = request.session.get('anonymous_login', False)
            
            if not (is_admin or is_anonymous):
                logger.debug(f"Session认证失败: 未找到admin_login或anonymous_login标记，请求路径: {request.path}")
                return None
            
            staff = Staff.objects.select_related('department', 'position').get(
                id=staff_id,
                is_active=True,
                deleted_at__isnull=True
            )
            
            # 管理端登录需要检查管理权限
            if is_admin and not staff.is_manager:
                logger.debug(f"Session认证失败: 用户 {staff.username} 没有管理权限，请求路径: {request.path}")
                return None
            
            # 检查账户锁定状态
            if staff.is_account_locked():
                logger.warning(f"尝试使用锁定的账户登录: {staff.username}")
                # 清除session
                request.session.flush()
                return None
            
            logger.debug(f"Session认证成功: 用户 {staff.username}，请求路径: {request.path}")
            return staff
            
        except (Staff.DoesNotExist, Exception) as e:
            logger.debug(f"Session认证失败: {str(e)}，请求路径: {request.path}")
            return None
    
    def process_response(self, request, response):
        """处理响应"""
        # 为JWT认证添加安全头
        if hasattr(request, 'auth_method') and request.auth_method == 'jwt':
            # 防止token在JavaScript中被访问
            response['X-Content-Type-Options'] = 'nosniff'
            
        return response

class SecurityHeadersMiddleware(MiddlewareMixin):
    """
    安全头中间件
    添加各种安全相关的HTTP头
    """
    
    def process_response(self, request, response):
        """添加安全头"""
        
        # XSS保护
        response['X-XSS-Protection'] = '1; mode=block'
        
        # 内容类型嗅探保护
        response['X-Content-Type-Options'] = 'nosniff'
        
        # 点击劫持保护
        response['X-Frame-Options'] = 'DENY'
        
        # 引荐者策略
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # 权限策略
        response['Permissions-Policy'] = 'geolocation=(), microphone=(), camera=()'
        
        # 内容安全策略（基础版）
        csp_policy = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdn.tailwindcss.com https://cdnjs.cloudflare.com; "
            "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdn.tailwindcss.com https://cdnjs.cloudflare.com; "
            "img-src 'self' data: https:; "
            "font-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; "
            "connect-src 'self'; "
            "frame-ancestors 'none';"
        )
        response['Content-Security-Policy'] = csp_policy
        
        return response

class AuthRequiredMiddleware(MiddlewareMixin):
    """
    认证要求中间件
    确保需要认证的页面用户已登录
    """
    
    # 需要认证的URL前缀
    PROTECTED_PREFIXES = [
        '/admin/',
        '/api/',
        '/anonymous/',
    ]
    
    # 不需要认证的具体路径
    EXCLUDE_PATHS = [
        '/admin/login/',
        '/anonymous/login/',
        '/api/login/',               # JWT登录端点
        '/api/logout/',              # JWT登出端点  
        '/api/token/refresh/',       # JWT刷新端点
        '/api/token/status/',        # JWT状态查询端点
        '/api/auth/',                # 备用认证端点
        '/admin/api/auth/',          # 管理端认证API
    ]
    
    def process_request(self, request):
        """检查认证要求"""
        
        # 检查是否是需要认证的路径
        if not self._requires_auth(request):
            return None
        
        # 检查用户是否已认证
        if not getattr(request, 'is_authenticated', False):
            return self._handle_unauthenticated(request)
        
        return None
    
    def _requires_auth(self, request):
        """检查路径是否需要认证"""
        path = request.path
        
        # 检查排除路径
        for exclude_path in self.EXCLUDE_PATHS:
            if path.startswith(exclude_path):
                return False
        
        # 检查受保护的前缀
        for protected_prefix in self.PROTECTED_PREFIXES:
            if path.startswith(protected_prefix):
                return True
        
        return False
    
    def _handle_unauthenticated(self, request):
        """处理未认证用户"""
        path = request.path
        
        # API请求返回401
        if path.startswith('/api/'):
            return JsonResponse({
                'error': '未认证',
                'message': '请先登录',
                'code': 'AUTHENTICATION_REQUIRED'
            }, status=401)
        
        # 管理端重定向到登录页
        elif path.startswith('/admin/'):
            messages.warning(request, '请先登录')
            return redirect('/admin/login/')
        
        # 匿名端重定向到匿名登录页
        elif path.startswith('/anonymous/'):
            messages.warning(request, '请输入匿名编号登录')
            return redirect('/anonymous/login/')
        
        # 其他情况重定向到主登录页
        else:
            return redirect('/admin/login/')

class TokenRefreshMiddleware(MiddlewareMixin):
    """
    Token自动刷新中间件
    在token即将过期时自动刷新
    """
    
    # token过期前多少秒开始刷新
    REFRESH_THRESHOLD = 30 * 60  # 30分钟
    
    def process_response(self, request, response):
        """检查是否需要刷新token"""
        
        # 只处理JWT认证的请求
        if getattr(request, 'auth_method', None) != 'jwt':
            return response
        
        # 只处理成功的响应
        if response.status_code >= 400:
            return response
        
        try:
            # 获取当前token
            auth_header = request.META.get('HTTP_AUTHORIZATION')
            if not auth_header:
                token = request.COOKIES.get('access_token')
            else:
                token = extract_token_from_header(auth_header)
            
            if not token:
                return response
            
            # 获取token信息
            token_info = JWTAuthentication.get_token_info(token)
            if not token_info:
                return response
            
            # 检查是否需要刷新
            from datetime import datetime
            expires_at = token_info['expires_at']
            now = datetime.now()
            
            # 如果token在阈值时间内过期，则尝试刷新
            if (expires_at - now).total_seconds() < self.REFRESH_THRESHOLD:
                refresh_token = request.COOKIES.get('refresh_token')
                if refresh_token:
                    try:
                        new_tokens = JWTAuthentication.refresh_access_token(refresh_token)
                        
                        # 更新响应头
                        response['X-New-Access-Token'] = new_tokens['access_token']
                        
                        # 如果是cookie认证，更新cookie
                        if request.COOKIES.get('access_token'):
                            response.set_cookie(
                                'access_token',
                                new_tokens['access_token'],
                                max_age=new_tokens['expires_in'],
                                httponly=True,
                                secure=request.is_secure(),
                                samesite='Lax'
                            )
                        
                        logger.info(f"自动刷新用户 {request.current_staff.username} 的token")
                        
                    except TokenException:
                        # 刷新失败，让token自然过期
                        pass
            
        except Exception as e:
            logger.error(f"Token自动刷新失败: {str(e)}")
        
        return response

# 工具函数
def get_client_ip(request):
    """获取客户端IP地址"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0].strip()
    else:
        ip = request.META.get('REMOTE_ADDR', '127.0.0.1')
    return ip

def is_ajax_request(request):
    """判断是否是AJAX请求"""
    return (
        request.headers.get('x-requested-with') == 'XMLHttpRequest' or
        request.headers.get('content-type') == 'application/json' or
        request.path.startswith('/api/')
    )