# -*- coding: utf-8 -*-
"""
安全匿名编号生成系统
实现企业考评系统中员工匿名编号的安全生成、验证和迁移功能

技术特点：
- SHA256多轮哈希加密，确保编号不可预测
- 多重熵源：员工ID、部门ID、时间戳、UUID、系统密钥
- 自定义字符集，避免混淆字符
- 格式化输出：XXXX-XXXX-XXXX
- 支持批量迁移和向后兼容

安全等级：企业级
"""

import hashlib
import secrets
import uuid
import time
import hmac
from typing import Optional, Dict, List, Tuple, Any
from datetime import datetime, timedelta
from django.conf import settings
from django.db import transaction
from django.core.cache import cache
from django.utils.crypto import constant_time_compare
import logging

logger = logging.getLogger(__name__)

class SecureAnonymousCodeGenerator:
    """
    安全匿名编号生成器
    
    采用多重安全措施：
    1. 多重熵源确保随机性
    2. 三轮SHA256哈希增强安全性
    3. 自定义字符集避免混淆
    4. 唯一性检查防止碰撞
    """
    
    # 自定义字符集：排除易混淆字符 0、O、1、I、l
    CHARSET = "23456789ABCDEFGHJKLMNPQRSTUVWXYZ"
    
    # 编号格式配置
    CODE_LENGTH = 12  # 总长度
    SEGMENT_LENGTH = 4  # 每段长度
    SEGMENTS_COUNT = 3  # 段数
    
    # 安全配置
    MAX_GENERATION_ATTEMPTS = 100  # 最大生成尝试次数
    HASH_ROUNDS = 3  # 哈希轮数
    
    def __init__(self):
        """初始化生成器"""
        self.system_salt = self._get_system_salt()
        self.charset_length = len(self.CHARSET)
    
    def _get_system_salt(self) -> str:
        """
        获取系统级密钥盐
        优先使用环境变量，否则使用默认值
        """
        return getattr(settings, 'ANONYMOUS_CODE_SALT', 'default_anonymous_salt_2025')
    
    def _generate_entropy_sources(self, staff_id: int, department_id: int) -> Tuple[str, str, str, str]:
        """
        生成多重熵源
        
        Args:
            staff_id: 员工ID
            department_id: 部门ID
            
        Returns:
            四个熵源的元组：基础数据、时间戳、随机UUID、系统盐
        """
        # 基础数据熵源
        base_data = f"staff_{staff_id}_dept_{department_id}"
        
        # 微秒级时间戳熵源
        timestamp = str(int(time.time() * 1000000))
        
        # 随机UUID熵源
        random_uuid = str(uuid.uuid4())
        
        # 系统盐熵源
        system_salt = self.system_salt
        
        return base_data, timestamp, random_uuid, system_salt
    
    def _three_round_hash(self, sources: Tuple[str, str, str, str]) -> str:
        """
        三轮SHA256哈希处理
        
        Args:
            sources: 四个熵源的元组
            
        Returns:
            最终哈希值的十六进制字符串
        """
        base_data, timestamp, random_uuid, system_salt = sources
        
        # 第一轮：基础数据哈希
        round1_input = f"{base_data}{system_salt}".encode('utf-8')
        round1_hash = hashlib.sha256(round1_input).hexdigest()
        
        # 第二轮：加入时间戳
        round2_input = f"{round1_hash}{timestamp}".encode('utf-8')
        round2_hash = hashlib.sha256(round2_input).hexdigest()
        
        # 第三轮：加入随机UUID
        round3_input = f"{round2_hash}{random_uuid}".encode('utf-8')
        final_hash = hashlib.sha256(round3_input).hexdigest()
        
        return final_hash
    
    def _hash_to_custom_base(self, hash_value: str) -> str:
        """
        将哈希值转换为自定义字符集编码
        
        Args:
            hash_value: SHA256哈希值（十六进制字符串）
            
        Returns:
            自定义字符集编码的字符串
        """
        # 将十六进制哈希转换为整数
        hash_int = int(hash_value, 16)
        
        # 转换为自定义字符集
        if hash_int == 0:
            return self.CHARSET[0]
        
        result = []
        while hash_int > 0 and len(result) < self.CODE_LENGTH:
            result.append(self.CHARSET[hash_int % self.charset_length])
            hash_int //= self.charset_length
        
        # 确保长度足够，不足时在前面补充字符
        while len(result) < self.CODE_LENGTH:
            # 使用额外的熵源生成补充字符
            extra_entropy = secrets.randbelow(self.charset_length)
            result.insert(0, self.CHARSET[extra_entropy])
        
        return ''.join(result[:self.CODE_LENGTH])
    
    def _format_code(self, code: str) -> str:
        """
        格式化编号为XXXX-XXXX-XXXX格式
        
        Args:
            code: 12位字符编号
            
        Returns:
            格式化后的编号
        """
        if len(code) != self.CODE_LENGTH:
            raise ValueError(f"编号长度必须为{self.CODE_LENGTH}位")
        
        segments = []
        for i in range(self.SEGMENTS_COUNT):
            start = i * self.SEGMENT_LENGTH
            end = start + self.SEGMENT_LENGTH
            segments.append(code[start:end])
        
        return '-'.join(segments)
    
    def generate_secure_code(self, staff_id: int, department_id: int) -> str:
        """
        生成安全匿名编号
        
        Args:
            staff_id: 员工ID
            department_id: 部门ID
            
        Returns:
            格式化的安全匿名编号 (XXXX-XXXX-XXXX)
            
        Raises:
            ValueError: 当无法生成唯一编号时
        """
        for attempt in range(self.MAX_GENERATION_ATTEMPTS):
            try:
                # 生成多重熵源
                entropy_sources = self._generate_entropy_sources(staff_id, department_id)
                
                # 三轮哈希处理
                final_hash = self._three_round_hash(entropy_sources)
                
                # 转换为自定义字符集
                code = self._hash_to_custom_base(final_hash)
                
                # 格式化编号
                formatted_code = self._format_code(code)
                
                # 检查唯一性
                if self.is_code_unique(formatted_code):
                    logger.info(f"成功生成安全匿名编号，员工ID: {staff_id}, 尝试次数: {attempt + 1}")
                    return formatted_code
                
                # 如果不唯一，稍作延迟后重试
                time.sleep(0.001)  # 1毫秒延迟
                
            except Exception as e:
                logger.warning(f"编号生成尝试失败 (第{attempt + 1}次): {str(e)}")
                continue
        
        # 如果所有尝试都失败
        error_msg = f"无法为员工 {staff_id} 生成唯一的安全匿名编号，已尝试 {self.MAX_GENERATION_ATTEMPTS} 次"
        logger.error(error_msg)
        raise ValueError(error_msg)
    
    def validate_code_format(self, code: str) -> bool:
        """
        验证编号格式是否正确
        
        Args:
            code: 待验证的编号
            
        Returns:
            格式是否正确
        """
        if not code or not isinstance(code, str):
            return False
        
        # 检查总长度 (包含连字符)
        if len(code) != self.CODE_LENGTH + self.SEGMENTS_COUNT - 1:
            return False
        
        # 检查格式
        segments = code.split('-')
        if len(segments) != self.SEGMENTS_COUNT:
            return False
        
        # 检查每段长度
        for segment in segments:
            if len(segment) != self.SEGMENT_LENGTH:
                return False
            
            # 检查字符是否在允许的字符集中
            for char in segment:
                if char not in self.CHARSET:
                    return False
        
        return True
    
    def is_code_unique(self, code: str) -> bool:
        """
        检查编号在数据库中是否唯一
        
        Args:
            code: 待检查的编号
            
        Returns:
            编号是否唯一
        """
        try:
            from organizations.models import Staff
            
            # 检查新编号字段
            exists_new = Staff.objects.filter(
                new_anonymous_code=code,
                deleted_at__isnull=True
            ).exists()
            
            # 检查旧编号字段（兼容性）
            exists_old = Staff.objects.filter(
                anonymous_code=code,
                deleted_at__isnull=True
            ).exists()
            
            return not (exists_new or exists_old)
            
        except Exception as e:
            logger.error(f"编号唯一性检查失败: {str(e)}")
            return False
    
    def batch_generate_codes(self, staff_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        批量生成安全编号
        
        Args:
            staff_data: 员工数据列表，每个元素包含 staff_id 和 department_id
            
        Returns:
            生成结果统计
        """
        results = {
            'success_count': 0,
            'failed_count': 0,
            'generated_codes': {},
            'failed_items': [],
            'start_time': datetime.now(),
            'end_time': None
        }
        
        logger.info(f"开始批量生成安全编号，总数: {len(staff_data)}")
        
        for item in staff_data:
            try:
                staff_id = item['staff_id']
                department_id = item['department_id']
                
                code = self.generate_secure_code(staff_id, department_id)
                
                results['generated_codes'][staff_id] = code
                results['success_count'] += 1
                
            except Exception as e:
                results['failed_count'] += 1
                results['failed_items'].append({
                    'staff_id': item.get('staff_id'),
                    'department_id': item.get('department_id'),
                    'error': str(e)
                })
                logger.error(f"批量生成失败，员工ID: {item.get('staff_id')}, 错误: {str(e)}")
        
        results['end_time'] = datetime.now()
        results['duration'] = (results['end_time'] - results['start_time']).total_seconds()
        
        logger.info(f"批量生成完成，成功: {results['success_count']}, 失败: {results['failed_count']}")
        
        return results


class AnonymousCodeValidator:
    """
    匿名编号验证器
    提供编号格式验证和安全检查功能
    """
    
    def __init__(self):
        self.generator = SecureAnonymousCodeGenerator()
    
    def validate_login_code(self, code: str) -> Tuple[bool, Optional[str], Optional[Any]]:
        """
        验证登录时提供的匿名编号（支持新旧格式）
        
        Args:
            code: 用户输入的匿名编号
            
        Returns:
            (是否有效, 错误信息, 匹配的员工对象)
        """
        try:
            # 输入清理
            if not code:
                return False, "匿名编号不能为空", None
            
            code = code.strip().upper()
            
            # 先尝试查找匹配的员工（支持新旧格式）
            staff = self._find_staff_by_code(code)
            if not staff:
                return False, "匿名编号不存在或已失效", None
            
            # 账户状态检查
            if not staff.is_active:
                return False, "账户已被禁用，请联系管理员", None
            
            if staff.deleted_at:
                return False, "账户不存在", None
            
            # 账户锁定检查
            if hasattr(staff, 'is_account_locked') and staff.is_account_locked():
                return False, "账户已被锁定，请稍后再试", None
            
            return True, None, staff
            
        except Exception as e:
            logger.error(f"匿名编号验证失败: {str(e)}")
            return False, "系统错误，请稍后重试", None
    
    def _find_staff_by_code(self, code: str):
        """
        根据匿名编号查找员工
        支持新旧编号格式的向后兼容
        """
        try:
            from organizations.models import Staff
            
            # 优先查找新编号
            staff = Staff.objects.filter(
                new_anonymous_code=code,
                deleted_at__isnull=True
            ).first()
            
            if staff:
                return staff
            
            # 兼容性：查找旧编号
            staff = Staff.objects.filter(
                anonymous_code=code,
                deleted_at__isnull=True
            ).first()
            
            return staff
            
        except Exception as e:
            logger.error(f"员工查找失败: {str(e)}")
            return None
    
    def check_code_strength(self, code: str) -> Dict[str, Any]:
        """
        检查编号强度和安全性
        
        Args:
            code: 待检查的编号
            
        Returns:
            安全性分析结果
        """
        result = {
            'is_strong': False,
            'strength_score': 0,
            'issues': [],
            'recommendations': []
        }
        
        if not code:
            result['issues'].append("编号为空")
            return result
        
        # 格式检查
        if not self.generator.validate_code_format(code):
            result['issues'].append("格式不符合安全标准")
            result['recommendations'].append("使用新的安全编号格式")
        else:
            result['strength_score'] += 25
        
        # 字符复杂度检查
        unique_chars = len(set(code.replace('-', '')))
        if unique_chars >= 8:
            result['strength_score'] += 25
        elif unique_chars >= 6:
            result['strength_score'] += 15
        else:
            result['issues'].append("字符重复度过高")
        
        # 模式检查
        clean_code = code.replace('-', '')
        if not self._has_obvious_pattern(clean_code):
            result['strength_score'] += 25
        else:
            result['issues'].append("存在明显的字符模式")
        
        # 字符集检查
        if all(c in self.generator.CHARSET or c == '-' for c in code):
            result['strength_score'] += 25
        else:
            result['issues'].append("包含不安全的字符")
        
        result['is_strong'] = result['strength_score'] >= 75
        
        return result
    
    def _has_obvious_pattern(self, code: str) -> bool:
        """检查编号是否有明显的模式"""
        # 检查连续字符
        for i in range(len(code) - 2):
            if code[i] == code[i+1] == code[i+2]:
                return True
        
        # 检查递增/递减序列
        charset_indices = [self.generator.CHARSET.index(c) for c in code if c in self.generator.CHARSET]
        for i in range(len(charset_indices) - 2):
            if (charset_indices[i] + 1 == charset_indices[i+1] and 
                charset_indices[i+1] + 1 == charset_indices[i+2]):
                return True
            if (charset_indices[i] - 1 == charset_indices[i+1] and 
                charset_indices[i+1] - 1 == charset_indices[i+2]):
                return True
        
        return False


class AnonymousCodeMigrator:
    """
    匿名编号迁移器
    负责将现有的匿名编号升级为新的安全编号
    """
    
    def __init__(self):
        self.generator = SecureAnonymousCodeGenerator()
        self.validator = AnonymousCodeValidator()
    
    def migrate_existing_codes(self, batch_size: int = 50) -> Dict[str, Any]:
        """
        批量迁移现有的匿名编号
        
        Args:
            batch_size: 每批处理的数量
            
        Returns:
            迁移结果统计
        """
        from organizations.models import Staff
        
        migration_result = {
            'total_count': 0,
            'migrated_count': 0,
            'skipped_count': 0,
            'failed_count': 0,
            'failed_items': [],
            'start_time': datetime.now(),
            'end_time': None,
            'migration_id': secrets.token_hex(8)
        }
        
        try:
            # 获取需要迁移的员工
            staff_list = Staff.objects.filter(
                deleted_at__isnull=True,
                is_active=True
            ).select_related('department')
            
            migration_result['total_count'] = staff_list.count()
            
            logger.info(f"开始匿名编号迁移，总数: {migration_result['total_count']}, 迁移ID: {migration_result['migration_id']}")
            
            # 分批处理
            for i in range(0, migration_result['total_count'], batch_size):
                batch = staff_list[i:i + batch_size]
                batch_result = self._migrate_batch(batch, migration_result['migration_id'])
                
                migration_result['migrated_count'] += batch_result['migrated_count']
                migration_result['skipped_count'] += batch_result['skipped_count']
                migration_result['failed_count'] += batch_result['failed_count']
                migration_result['failed_items'].extend(batch_result['failed_items'])
                
                logger.info(f"批次迁移完成: {i//batch_size + 1}, 已迁移: {migration_result['migrated_count']}")
            
            migration_result['end_time'] = datetime.now()
            migration_result['duration'] = (migration_result['end_time'] - migration_result['start_time']).total_seconds()
            
            # 记录迁移历史
            self._record_migration_history(migration_result)
            
            logger.info(f"匿名编号迁移完成，成功: {migration_result['migrated_count']}, 跳过: {migration_result['skipped_count']}, 失败: {migration_result['failed_count']}")
            
            return migration_result
            
        except Exception as e:
            logger.error(f"匿名编号迁移失败: {str(e)}")
            migration_result['end_time'] = datetime.now()
            raise
    
    @transaction.atomic
    def _migrate_batch(self, staff_batch, migration_id: str) -> Dict[str, Any]:
        """
        迁移单个批次的员工编号
        
        Args:
            staff_batch: 员工批次
            migration_id: 迁移标识
            
        Returns:
            批次迁移结果
        """
        batch_result = {
            'migrated_count': 0,
            'skipped_count': 0,
            'failed_count': 0,
            'failed_items': []
        }
        
        for staff in staff_batch:
            try:
                # 检查是否已有新编号
                if staff.new_anonymous_code:
                    batch_result['skipped_count'] += 1
                    continue
                
                # 生成新的安全编号
                new_code = self.generator.generate_secure_code(
                    staff.id, 
                    staff.department.id if staff.department else 1
                )
                
                # 更新员工记录
                staff.new_anonymous_code = new_code
                staff.save(update_fields=['new_anonymous_code', 'updated_at'])
                
                batch_result['migrated_count'] += 1
                
                # 记录审计日志
                self._log_migration_action(staff, new_code, migration_id)
                
            except Exception as e:
                batch_result['failed_count'] += 1
                batch_result['failed_items'].append({
                    'staff_id': staff.id,
                    'staff_name': staff.name,
                    'error': str(e)
                })
                logger.error(f"员工编号迁移失败 - ID: {staff.id}, 姓名: {staff.name}, 错误: {str(e)}")
        
        return batch_result
    
    def _log_migration_action(self, staff, new_code: str, migration_id: str):
        """记录迁移操作的审计日志"""
        try:
            from common.models import AuditLog
            
            AuditLog.objects.create(
                table_name='staff',
                record_id=staff.id,
                action='UPDATE',
                field_name='new_anonymous_code',
                old_value=getattr(staff, 'new_anonymous_code', None),
                new_value=new_code,
                created_by_id=1,  # 系统用户
                ip_address='127.0.0.1',
                user_agent='Anonymous Code Migrator',
                extra_data={
                    'migration_id': migration_id,
                    'migration_type': 'anonymous_code_security_upgrade',
                    'staff_name': staff.name,
                    'department': staff.department.name if staff.department else None
                }
            )
        except Exception as e:
            logger.warning(f"迁移审计日志记录失败: {str(e)}")
    
    def _record_migration_history(self, migration_result: Dict[str, Any]):
        """记录迁移历史到缓存或数据库"""
        try:
            cache_key = f"anonymous_migration_{migration_result['migration_id']}"
            cache.set(cache_key, migration_result, timeout=86400 * 30)  # 30天
            
            logger.info(f"迁移历史已缓存: {cache_key}")
        except Exception as e:
            logger.warning(f"迁移历史记录失败: {str(e)}")
    
    def get_migration_status(self) -> Dict[str, Any]:
        """
        获取迁移状态统计
        
        Returns:
            迁移状态信息
        """
        try:
            from organizations.models import Staff
            
            total_staff = Staff.objects.filter(
                deleted_at__isnull=True,
                is_active=True
            ).count()
            
            migrated_staff = Staff.objects.filter(
                deleted_at__isnull=True,
                is_active=True,
                new_anonymous_code__isnull=False
            ).count()
            
            return {
                'total_staff': total_staff,
                'migrated_staff': migrated_staff,
                'remaining_staff': total_staff - migrated_staff,
                'migration_progress': (migrated_staff / total_staff * 100) if total_staff > 0 else 0,
                'is_complete': migrated_staff == total_staff
            }
            
        except Exception as e:
            logger.error(f"获取迁移状态失败: {str(e)}")
            return {
                'total_staff': 0,
                'migrated_staff': 0,
                'remaining_staff': 0,
                'migration_progress': 0,
                'is_complete': False,
                'error': str(e)
            }
    
    def rollback_migration(self, migration_id: str) -> Dict[str, Any]:
        """
        回滚指定的迁移操作（紧急情况使用）
        
        Args:
            migration_id: 迁移标识
            
        Returns:
            回滚结果
        """
        rollback_result = {
            'rolled_back_count': 0,
            'failed_count': 0,
            'start_time': datetime.now(),
            'end_time': None
        }
        
        try:
            from organizations.models import Staff
            from common.models import AuditLog
            
            # 查找相关的审计日志
            migration_logs = AuditLog.objects.filter(
                table_name='staff',
                field_name='new_anonymous_code',
                extra_data__migration_id=migration_id
            )
            
            logger.info(f"开始回滚迁移 {migration_id}，影响记录数: {migration_logs.count()}")
            
            with transaction.atomic():
                for log in migration_logs:
                    try:
                        staff = Staff.objects.get(id=log.record_id)
                        staff.new_anonymous_code = None
                        staff.save(update_fields=['new_anonymous_code', 'updated_at'])
                        
                        rollback_result['rolled_back_count'] += 1
                        
                    except Staff.DoesNotExist:
                        rollback_result['failed_count'] += 1
                        logger.warning(f"回滚时未找到员工记录: {log.record_id}")
                    except Exception as e:
                        rollback_result['failed_count'] += 1
                        logger.error(f"回滚员工记录失败: {log.record_id}, 错误: {str(e)}")
            
            rollback_result['end_time'] = datetime.now()
            
            logger.info(f"迁移回滚完成，成功: {rollback_result['rolled_back_count']}, 失败: {rollback_result['failed_count']}")
            
            return rollback_result
            
        except Exception as e:
            logger.error(f"迁移回滚失败: {str(e)}")
            rollback_result['end_time'] = datetime.now()
            raise


# 工具函数

def generate_anonymous_code_for_staff(staff_id: int, department_id: int) -> str:
    """
    为指定员工生成安全匿名编号
    
    Args:
        staff_id: 员工ID
        department_id: 部门ID
        
    Returns:
        安全匿名编号
    """
    generator = SecureAnonymousCodeGenerator()
    return generator.generate_secure_code(staff_id, department_id)


def validate_anonymous_code(code: str) -> Tuple[bool, Optional[str]]:
    """
    验证匿名编号格式
    
    Args:
        code: 待验证的编号
        
    Returns:
        (是否有效, 错误信息)
    """
    generator = SecureAnonymousCodeGenerator()
    is_valid = generator.validate_code_format(code)
    
    if is_valid:
        return True, None
    else:
        return False, "编号格式不正确"


def check_anonymous_code_uniqueness(code: str) -> bool:
    """
    检查匿名编号是否唯一
    
    Args:
        code: 待检查的编号
        
    Returns:
        是否唯一
    """
    generator = SecureAnonymousCodeGenerator()
    return generator.is_code_unique(code)


# 安全配置检查
def check_security_configuration() -> Dict[str, Any]:
    """
    检查安全配置是否正确
    
    Returns:
        配置检查结果
    """
    result = {
        'is_secure': True,
        'warnings': [],
        'recommendations': []
    }
    
    # 检查系统盐配置
    if not hasattr(settings, 'ANONYMOUS_CODE_SALT'):
        result['warnings'].append("未配置ANONYMOUS_CODE_SALT，使用默认值")
        result['recommendations'].append("在settings.py中设置ANONYMOUS_CODE_SALT")
        result['is_secure'] = False
    
    # 检查密钥强度
    salt = getattr(settings, 'ANONYMOUS_CODE_SALT', 'default_anonymous_salt_2025')
    if len(salt) < 20:
        result['warnings'].append("系统盐长度过短")
        result['recommendations'].append("设置至少20位的系统盐")
        result['is_secure'] = False
    
    return result