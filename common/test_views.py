# -*- coding: utf-8 -*-
"""
异常处理测试视图
用于测试全局异常处理中间件的功能
"""

from django.http import JsonResponse
from django.shortcuts import render
from django.core.exceptions import ValidationError, PermissionDenied
from django.db import IntegrityError

from common.exceptions import (
    BaseBusinessException,
    AuthenticationException,
    PermissionException,
    SecurityException,
    TokenException
)

def test_404_error(request):
    """测试404错误"""
    from django.http import Http404
    raise Http404("测试页面不存在")

def test_403_error(request):
    """测试403权限错误"""
    raise PermissionDenied("测试权限不足")

def test_500_error(request):
    """测试500系统错误"""
    raise Exception("测试系统内部错误")

def test_validation_error(request):
    """测试验证错误"""
    raise ValidationError({
        'username': ['用户名不能为空'],
        'password': ['密码长度至少8位'],
        'email': ['邮箱格式不正确']
    })

def test_business_exception(request):
    """测试业务异常"""
    raise BaseBusinessException(
        message="测试业务逻辑错误",
        code="BUSINESS_ERROR",
        details={'field': 'test_field', 'value': 'invalid_value'}
    )

def test_authentication_exception(request):
    """测试认证异常"""
    raise AuthenticationException(
        message="测试认证失败",
        code="AUTH_FAILED"
    )

def test_permission_exception(request):
    """测试权限异常"""
    raise PermissionException(
        message="测试权限不足",
        code="PERMISSION_DENIED"
    )

def test_security_exception(request):
    """测试安全异常"""
    raise SecurityException(
        message="测试安全威胁检测",
        code="SECURITY_THREAT"
    )

def test_database_error(request):
    """测试数据库错误"""
    raise IntegrityError("测试数据库完整性约束错误")

def test_api_error(request):
    """测试API错误（JSON响应）"""
    if request.headers.get('Accept') == 'application/json':
        raise BaseBusinessException(
            message="API测试错误",
            code="API_ERROR"
        )
    return JsonResponse({'status': 'ok'})

def error_test_page(request):
    """错误测试页面"""
    return render(request, 'common/error_test.html')