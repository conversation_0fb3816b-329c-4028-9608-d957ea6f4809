# -*- coding: utf-8 -*-
"""
异常处理测试URL配置
"""

from django.urls import path
from . import test_views

urlpatterns = [
    # 测试页面入口
    path('error-test/', test_views.error_test_page, name='error_test_page'),
    
    # HTTP标准错误测试
    path('error/404/', test_views.test_404_error, name='test_404_error'),
    path('error/403/', test_views.test_403_error, name='test_403_error'),
    path('error/500/', test_views.test_500_error, name='test_500_error'),
    path('error/validation/', test_views.test_validation_error, name='test_validation_error'),
    
    # 业务异常测试
    path('error/business/', test_views.test_business_exception, name='test_business_exception'),
    path('error/authentication/', test_views.test_authentication_exception, name='test_authentication_exception'),
    path('error/permission/', test_views.test_permission_exception, name='test_permission_exception'),
    path('error/security/', test_views.test_security_exception, name='test_security_exception'),
    
    # 系统级错误测试
    path('error/database/', test_views.test_database_error, name='test_database_error'),
    
    # API错误测试
    path('error/api/', test_views.test_api_error, name='test_api_error'),
]