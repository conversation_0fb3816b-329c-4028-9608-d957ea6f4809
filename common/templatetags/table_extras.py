"""
表格组件相关的Django模板过滤器
"""
from django import template

register = template.Library()

@register.filter
def get_item(dictionary, key):
    """
    从字典或对象中获取指定键的值
    用于表格组件中动态获取行数据
    
    使用方式:
    {{ row|get_item:column.key }}
    """
    if hasattr(dictionary, 'get'):
        # 如果是字典类型
        return dictionary.get(key, '')
    elif hasattr(dictionary, key):
        # 如果是对象类型，有该属性
        return getattr(dictionary, key, '')
    else:
        # 尝试通过索引访问
        try:
            return dictionary[key]
        except (KeyError, TypeError, IndexError):
            return ''

@register.filter
def split(value, delimiter):
    """
    按分隔符分割字符串
    
    使用方式:
    {{ "a,b,c"|split:"," }}
    """
    if value:
        return value.split(delimiter)
    return []

@register.filter
def add(value, arg):
    """
    数值相加，支持布尔值转换
    
    使用方式:
    {{ columns|length|add:selectable|add:row_actions }}
    """
    try:
        # 将布尔值转换为数字
        if isinstance(value, bool):
            value = int(value)
        if isinstance(arg, bool):
            arg = int(arg)
        return int(value) + int(arg)
    except (ValueError, TypeError):
        return 0