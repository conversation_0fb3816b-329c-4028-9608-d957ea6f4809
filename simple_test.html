<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单布局测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 重置样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        /* 侧边栏 - 固定在左侧 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 16rem;
            height: 100vh;
            background: white;
            border-right: 1px solid #d1d5db;
            z-index: 10;
        }
        
        /* 主内容区域 - margin-left 留出侧边栏空间 */
        .main-content {
            margin-left: 16rem;
            min-height: 100vh;
            background: #f8fafc;
            display: flex;
            flex-direction: column;
        }
        
        /* 头部 */
        .header {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 2rem 1.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 5;
        }
        
        /* 内容区域 */
        .content {
            flex: 1;
            padding: 2rem 1.5rem;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar">
        <div class="p-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold">考评系统</h2>
        </div>
        <nav class="p-4">
            <div class="space-y-1">
                <a href="#" class="block px-3 py-2 rounded bg-blue-100 text-blue-700">仪表板</a>
                <a href="#" class="block px-3 py-2 rounded text-gray-700 hover:bg-gray-100">员工管理</a>
                <a href="#" class="block px-3 py-2 rounded text-gray-700 hover:bg-gray-100">部门管理</a>
            </div>
        </nav>
    </div>
    
    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 头部 -->
        <div class="header">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">仪表板</h1>
                    <p class="text-gray-600 mt-1">欢迎回来，查看最新的考评数据</p>
                </div>
                <div class="flex items-center space-x-3">
                    <button class="p-2 text-gray-400 hover:text-gray-600">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5z"/>
                        </svg>
                    </button>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                        新建考评
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 内容 -->
        <div class="content">
            <div class="card">
                <h3 class="text-lg font-semibold mb-3">测试卡片 1</h3>
                <p class="text-gray-600">这个布局应该是：左边紧贴侧边栏（16rem），右边贴合屏幕边缘，中间内容完整显示。</p>
            </div>
            
            <div class="card">
                <h3 class="text-lg font-semibold mb-3">测试卡片 2</h3>
                <p class="text-gray-600">头部应该有白色背景和阴影，与下方内容有适当间距。</p>
            </div>
            
            <div class="card">
                <h3 class="text-lg font-semibold mb-3">测试卡片 3</h3>
                <p class="text-gray-600">如果你看到这些内容都在正确的位置，那么布局就是成功的。</p>
            </div>
            
            <!-- 更多内容用于测试滚动 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="card">
                    <h4 class="font-semibold mb-2">统计卡片 1</h4>
                    <p class="text-2xl font-bold text-blue-600">156</p>
                    <p class="text-sm text-gray-500">总员工数</p>
                </div>
                <div class="card">
                    <h4 class="font-semibold mb-2">统计卡片 2</h4>
                    <p class="text-2xl font-bold text-green-600">89%</p>
                    <p class="text-sm text-gray-500">完成率</p>
                </div>
                <div class="card">
                    <h4 class="font-semibold mb-2">统计卡片 3</h4>
                    <p class="text-2xl font-bold text-purple-600">24</p>
                    <p class="text-sm text-gray-500">活跃批次</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>