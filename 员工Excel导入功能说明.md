# 员工Excel导入功能说明

## 📋 功能概述

参照部门管理和职位管理的导入功能，为员工管理新增了Excel批量导入功能。支持按照指定格式批量导入员工信息，自动处理部门关联、职位关联、用户名生成、密码设置等。

## 📊 导入格式

### Excel列格式
| 列名 | 是否必填 | 说明 |
|------|----------|------|
| 姓名* | 必填 | 员工真实姓名 |
| 部门* | 必填 | 部门名称，必须与系统中已存在的部门名称完全一致 |
| 级别* | 必填 | 员工级别，1-9级（1-4级员工，5级副主管，6级正主管，7级副经理，8级正经理，9级领导班子） |
| 工号 | 选填 | 员工工号，如不填写系统将自动生成（格式：EMP+年月日+4位随机数） |
| 职位 | 选填 | 职位名称，如填写则必须与系统中该部门的职位名称完全一致 |
| 邮箱 | 选填 | 员工邮箱地址 |
| 手机号 | 选填 | 员工手机号码 |

### 示例数据
```
姓名*    部门*    级别*  工号     职位        邮箱                手机号
张三     技术部   6      EMP001   高级工程师  <EMAIL>   13800138001
李四     人事部   7      EMP002   人事经理    <EMAIL>       13800138002
王五     技术部   4              软件工程师  <EMAIL>     13800138003
赵六     技术部   5      EMP004              <EMAIL>    
```

## 🔧 功能特性

### 自动化处理
- **工号生成**: 如未提供工号，系统自动生成唯一工号
- **用户名生成**: 基于工号自动生成用户名，确保唯一性
- **匿名编号**: 自动生成用于匿名评分的编号
- **默认密码**: 使用工号的SHA256哈希值作为默认密码
- **角色分配**: 根据级别自动分配角色（8级以上=部门经理，6-7级=管理员，1-5级=普通员工）

### 数据验证
- **必填字段检查**: 姓名、部门、级别必须填写
- **级别范围验证**: 级别必须在1-9之间
- **部门存在性验证**: 部门名称必须在系统中已存在
- **职位关联验证**: 如填写职位，必须在指定部门中存在该职位
- **工号唯一性检查**: 确保工号在系统中唯一
- **邮箱格式验证**: 如填写邮箱，必须符合邮箱格式

### 错误处理
- **详细错误报告**: 精确到行号的错误信息
- **部分导入支持**: 错误记录跳过，成功记录正常导入
- **导入历史记录**: 记录每次导入的详细信息和结果

## 🚀 使用方法

### 1. 下载模板
- 访问员工管理页面：`/organizations/admin/staff/`
- 点击"导入员工"按钮进入导入页面
- 点击"下载模板"获取最新的Excel模板

### 2. 填写数据
- 按照模板格式填写员工信息
- **重要**：部门名称必须是已存在的部门名称
- **重要**：职位名称必须是该部门中已存在的职位名称
- 建议先确保相关部门和职位已创建

### 3. 上传导入
- 选择填写好的Excel文件
- 配置导入选项（可选）
- 点击"开始导入"

### 4. 查看结果
- 系统会显示导入进度和结果
- 如有错误，会显示详细的错误信息
- 可在导入历史中查看详细记录

## 📁 技术实现

### 修改的文件
1. **common/excel_utils.py** - 更新员工模板生成器
2. **organizations/views.py** - 更新StaffImportView实现自定义导入逻辑
3. **templates/admin/staff/import.html** - 更新页面说明和验证提示
4. **templates/admin/staff/list.html** - 添加导入和导出按钮

### 核心功能
```python
class StaffImportView(View):
    """员工信息导入视图"""
    
    def post(self, request):
        # 文件验证
        # Excel解析
        # 数据验证
        # 部门和职位关联
        # 自动生成工号、用户名、匿名编号
        # 批量导入
        # 结果反馈
```

### 字段映射
```python
field_mapping = {
    '姓名*': 'name',
    '部门*': 'department__name',
    '级别*': 'level',
    '工号': 'employee_no',
    '职位': 'position__name',
    '邮箱': 'email',
    '手机号': 'phone'
}
```

## ⚠️ 注意事项

### 数据准备
1. **部门必须存在** - 导入前确保所有部门已创建
2. **职位可选但需存在** - 如填写职位，必须在对应部门中已存在
3. **级别范围** - 级别必须在1-9之间
4. **工号唯一性** - 如提供工号，必须在系统中唯一

### 安全考虑
1. **默认密码** - 新员工使用工号的哈希值作为默认密码
2. **强制修改密码** - 新员工首次登录时必须修改密码
3. **角色自动分配** - 根据级别自动分配合适的系统角色

### 性能优化
1. **批量处理** - 支持大量数据的批量导入
2. **错误跳过** - 单条记录错误不影响其他记录导入
3. **进度显示** - 实时显示导入进度和统计信息

## 🧪 测试验证

### 已验证功能
- [x] Excel模板生成正确
- [x] 字段映射一致性
- [x] 数据验证逻辑
- [x] 部门和职位关联逻辑
- [x] 自动生成功能（工号、用户名、匿名编号）
- [x] 错误处理机制
- [x] 导入历史记录

### 测试场景
- [x] 正常数据导入
- [x] 缺少必填字段处理
- [x] 部门不存在处理
- [x] 职位不存在处理
- [x] 重复工号处理
- [x] 级别范围验证
- [x] 自动工号生成

## 📈 使用建议

### 最佳实践
1. **分批导入** - 对于大量数据，建议分批导入
2. **数据验证** - 导入前先验证数据的完整性和准确性
3. **备份数据** - 导入前建议备份现有数据
4. **测试导入** - 先用少量数据测试导入流程

### 常见问题
1. **Q: 部门名称写错了怎么办？**
   A: 系统会提示找不到部门，请检查名称是否与系统中的部门名称完全一致

2. **Q: 可以不填写工号吗？**
   A: 可以，系统会自动生成唯一的工号

3. **Q: 职位不存在会影响导入吗？**
   A: 会，如果填写了职位但在对应部门中不存在，该条记录会导入失败

4. **Q: 新员工的默认密码是什么？**
   A: 默认密码是工号的SHA256哈希值，员工首次登录时需要修改密码
