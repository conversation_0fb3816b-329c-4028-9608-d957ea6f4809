# 企业考评评分系统设计方案

## 1. 系统架构设计

### 1.1 总体架构
```
┌─────────────────────────────────────────────────────────────┐
│                     系统总体架构                              │
├─────────────────────────────────────────────────────────────┤
│  匿名评分端           │           管理后台端                  │
│  ┌─────────────────┐  │  ┌─────────────────────────────────┐  │
│  │ 匿名登录         │  │  │ 管理员登录                      │  │
│  │ 考评任务列表     │  │  │ 部门管理   │ 人员管理  │ 权限管理│  │
│  │ 匿名评分界面     │  │  │ 职位管理   │ 评分管理  │ 统计分析│  │
│  │ 个人信息查看     │  │  │ 智能分配   │ 审计日志  │ 系统设置│  │
│  └─────────────────┘  │  └─────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
           │                              │
           ▼                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      业务逻辑层                              │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│ │ 认证服务     │ │ 考评服务     │ │ 统计服务     │ │ 权限服务 │ │
│ │ - 匿名认证   │ │ - 规则引擎   │ │ - 数据分析   │ │ - 角色管理│ │
│ │ - 管理员认证 │ │ - 模板管理   │ │ - 报表生成   │ │ - 权限检查│ │
│ └─────────────┘ │ - 智能分配   │ └─────────────┘ └─────────┘ │
│                 │ - 评分处理   │                             │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      数据访问层                              │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│ │ 用户数据     │ │ 考评数据     │ │ 统计数据     │ │ 系统数据 │ │
│ │ - 部门表     │ │ - 模板表     │ │ - 汇总表     │ │ - 日志表 │ │
│ │ - 人员表     │ │ - 批次表     │ │ - 分析表     │ │ - 规则表 │ │
│ │ - 匿名码表   │ │ - 评分表     │ │             │ │ - 配置表 │ │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
                        ┌─────────────┐
                        │   MySQL     │
                        │   数据库    │
                        └─────────────┘
```

### 1.2 技术栈选型

#### 1.2.1 后端技术栈
- **框架**：Django 4.2+ (稳定的企业级Web框架)
- **数据库**：MySQL 8.0+ (关系型数据库，支持复杂查询)
- **认证**：自定义认证系统 (独立于Django User)
- **缓存**：Redis (可选，用于提升性能)
- **数据处理**：Pandas (数据分析)、openpyxl (Excel处理)

#### 1.2.2 前端技术栈
- **模板引擎**：Django Templates
- **CSS框架**：Tailwind CSS 3.0+
- **JavaScript**：原生JS + jQuery (支持拖拽排序)
- **交互组件**：Sortable.js (拖拽排序)、Django Inline Formsets
- **数据可视化**：ECharts 5.0+ (强大的图表库，支持丰富的图表类型)

#### 1.2.3 部署技术栈
- **Web服务器**：Nginx
- **应用服务器**：Gunicorn
- **进程管理**：Supervisor
- **操作系统**：Linux (Ubuntu/CentOS)

#### 1.2.4 第三方集成（后期扩展）
- **企业平台**：钉钉开放平台API (组织架构同步、消息推送)
- **文件存储**：阿里云OSS / 腾讯云COS (大文件存储)
- **邮件服务**：企业邮件服务集成 (考评通知)
- **短信服务**：短信平台API (重要提醒)

## 2. 数据库设计

### 2.1 数据库设计原则
- 遵循第三范式，避免数据冗余
- 所有表包含软删除字段 `deleted_at`
- 所有表包含审计字段：`created_at`, `updated_at`, `created_by`, `updated_by`
- 合理使用索引，优化查询性能
- 外键约束确保数据完整性

### 2.2 核心数据表设计

> **最新更新**：基于新需求，数据库设计已重构支持：
> - 权重规则可配置化（默认权重=1.0）
> - 考评模板的内联表单集管理
> - 结构化评分和开放式评分双模式
> - 智能分配规则引擎
> - **考评进度管理和实时监控**
> - **报告与人才盘点功能**
> - **在线考试模块（后期扩展）**

#### 2.2.1 部门表 (Department) - 已调整
```sql
CREATE TABLE department (
    id INT PRIMARY KEY AUTO_INCREMENT,
    dept_code VARCHAR(20) UNIQUE NOT NULL COMMENT '部门编号(必填)',
    name VARCHAR(100) NOT NULL COMMENT '部门名称(必填)',
    parent_id INT DEFAULT NULL COMMENT '上级部门ID(选填)',
    manager_id INT DEFAULT NULL COMMENT '部门经理ID(选填)',
    description TEXT COMMENT '部门描述(选填)',
    sort_order INT DEFAULT 0 COMMENT '排序(选填)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT COMMENT '创建人ID',
    updated_by INT COMMENT '更新人ID',
    deleted_at TIMESTAMP NULL COMMENT '删除时间',
    FOREIGN KEY (parent_id) REFERENCES department(id),
    INDEX idx_dept_code (dept_code),
    INDEX idx_parent_id (parent_id),
    INDEX idx_deleted_at (deleted_at)
);
```

#### 2.2.2 职位表 (Position) - 已调整
```sql
CREATE TABLE position (
    id INT PRIMARY KEY AUTO_INCREMENT,
    department_id INT NOT NULL COMMENT '所属部门ID(必填)',
    position_code VARCHAR(20) UNIQUE NOT NULL COMMENT '职位编码(必填)',
    name VARCHAR(100) NOT NULL COMMENT '职位名称(必填)',
    level INT DEFAULT NULL COMMENT '职位级别(1-9)(选填)',
    description TEXT COMMENT '职位描述(选填)',
    is_manager BOOLEAN DEFAULT FALSE COMMENT '是否管理岗位(选填)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    deleted_at TIMESTAMP NULL,
    FOREIGN KEY (department_id) REFERENCES department(id),
    INDEX idx_department_id (department_id),
    INDEX idx_position_code (position_code),
    INDEX idx_level (level),
    INDEX idx_deleted_at (deleted_at)
);
```

#### 2.2.3 人员表 (Staff) - 已调整
```sql
CREATE TABLE staff (
    id INT PRIMARY KEY AUTO_INCREMENT,
    department_id INT NOT NULL COMMENT '所属部门ID(必填)',
    employee_no VARCHAR(50) UNIQUE NOT NULL COMMENT '员工编号(必填)',
    name VARCHAR(100) NOT NULL COMMENT '员工姓名(必填)',
    email VARCHAR(100) COMMENT '邮箱(选填)',
    phone VARCHAR(20) COMMENT '手机号(选填)',
    position_id INT DEFAULT NULL COMMENT '职位ID(选填)',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否在职(选填)',
    is_manager BOOLEAN DEFAULT FALSE COMMENT '是否管理人员(选填)',
    can_login_admin BOOLEAN DEFAULT FALSE COMMENT '能否登录管理后台(选填)',
    password_hash VARCHAR(255) COMMENT '密码哈希-管理员(选填)',
    last_login TIMESTAMP NULL COMMENT '最后登录时间(选填)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    deleted_at TIMESTAMP NULL,
    FOREIGN KEY (department_id) REFERENCES department(id),
    FOREIGN KEY (position_id) REFERENCES position(id),
    INDEX idx_department_id (department_id),
    INDEX idx_position_id (position_id),
    INDEX idx_employee_no (employee_no),
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_deleted_at (deleted_at)
);
```

#### 2.2.4 匿名编号表 (AnonymousCode)
```sql
CREATE TABLE anonymous_code (
    id INT PRIMARY KEY AUTO_INCREMENT,
    staff_id INT NOT NULL COMMENT '员工ID',
    anonymous_code VARCHAR(50) UNIQUE NOT NULL COMMENT '匿名编号',
    batch_id INT COMMENT '考评批次ID',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否有效',
    expire_time TIMESTAMP COMMENT '过期时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    FOREIGN KEY (staff_id) REFERENCES staff(id),
    INDEX idx_staff_id (staff_id),
    INDEX idx_anonymous_code (anonymous_code),
    INDEX idx_batch_id (batch_id)
);
```

#### 2.2.5 考评模板表 (EvaluationTemplate)
```sql
CREATE TABLE evaluation_template (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL COMMENT '模板标题',
    description TEXT COMMENT '模板描述',
    template_type ENUM('performance', 'attitude', 'ability', 'comprehensive') DEFAULT 'comprehensive' COMMENT '模板类型',
    total_score DECIMAL(5,2) DEFAULT 100.00 COMMENT '总分',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否默认模板',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    deleted_at TIMESTAMP NULL,
    INDEX idx_template_type (template_type),
    INDEX idx_is_default (is_default),
    INDEX idx_deleted_at (deleted_at)
);
```

#### 2.2.6 评分项表 (EvaluationItem)
```sql
CREATE TABLE evaluation_item (
    id INT PRIMARY KEY AUTO_INCREMENT,
    template_id INT NOT NULL COMMENT '所属模板ID',
    title VARCHAR(200) NOT NULL COMMENT '评分项标题',
    description TEXT COMMENT '评分项描述',
    item_type ENUM('score', 'choice', 'text') DEFAULT 'score' COMMENT '评分项类型',
    scoring_mode ENUM('structured', 'open') DEFAULT 'open' COMMENT '评分模式',
    max_score DECIMAL(5,2) DEFAULT 10.00 COMMENT '最高分',
    weight DECIMAL(3,2) DEFAULT 1.00 COMMENT '权重',
    sort_order INT DEFAULT 0 COMMENT '排序',
    is_required BOOLEAN DEFAULT TRUE COMMENT '是否必填',
    options JSON COMMENT '选择项(JSON格式)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    deleted_at TIMESTAMP NULL,
    FOREIGN KEY (template_id) REFERENCES evaluation_template(id),
    INDEX idx_template_id (template_id),
    INDEX idx_sort_order (sort_order),
    INDEX idx_scoring_mode (scoring_mode)
);
```

#### 2.2.7 考评批次表 (EvaluationBatch)
```sql
CREATE TABLE evaluation_batch (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL COMMENT '批次标题',
    description TEXT COMMENT '批次描述',
    default_template_id INT COMMENT '默认模板ID',
    start_time TIMESTAMP NOT NULL COMMENT '开始时间',
    end_time TIMESTAMP NOT NULL COMMENT '结束时间',
    status ENUM('draft', 'active', 'completed', 'cancelled') DEFAULT 'draft' COMMENT '批次状态',
    auto_assign BOOLEAN DEFAULT TRUE COMMENT '是否自动分配',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    deleted_at TIMESTAMP NULL,
    FOREIGN KEY (default_template_id) REFERENCES evaluation_template(id),
    INDEX idx_status (status),
    INDEX idx_default_template_id (default_template_id),
    INDEX idx_start_time (start_time),
    INDEX idx_end_time (end_time)
);
```

#### 2.2.8 考评关系表 (EvaluationRelation)
```sql
CREATE TABLE evaluation_relation (
    id INT PRIMARY KEY AUTO_INCREMENT,
    batch_id INT NOT NULL COMMENT '评分批次ID',
    evaluator_id INT NOT NULL COMMENT '考评人ID',
    evaluated_id INT NOT NULL COMMENT '被考评人ID',
    template_id INT COMMENT '使用的模板ID(可覆盖批次默认)',
    weight DECIMAL(3,2) DEFAULT 1.00 COMMENT '权重(默认为1.0)',
    relation_type ENUM('subordinate', 'superior', 'peer', 'cross_dept') COMMENT '关系类型',
    is_completed BOOLEAN DEFAULT FALSE COMMENT '是否已完成评分',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    deleted_at TIMESTAMP NULL,
    FOREIGN KEY (batch_id) REFERENCES evaluation_batch(id),
    FOREIGN KEY (evaluator_id) REFERENCES staff(id),
    FOREIGN KEY (evaluated_id) REFERENCES staff(id),
    FOREIGN KEY (template_id) REFERENCES evaluation_template(id),
    UNIQUE KEY uk_batch_evaluator_evaluated (batch_id, evaluator_id, evaluated_id),
    INDEX idx_batch_id (batch_id),
    INDEX idx_evaluator_id (evaluator_id),
    INDEX idx_evaluated_id (evaluated_id),
    INDEX idx_template_id (template_id)
);
```

#### 2.2.9 权重规则表 (WeightingRule)
```sql
CREATE TABLE weighting_rule (
    id INT PRIMARY KEY AUTO_INCREMENT,
    rule_name VARCHAR(100) NOT NULL COMMENT '规则名称',
    description TEXT COMMENT '规则描述',
    department_id INT COMMENT '适用部门ID（NULL表示全部）',
    position_id INT COMMENT '适用职位ID（NULL表示全部）',
    evaluator_dept_id INT COMMENT '考评人部门ID',
    evaluator_position_id INT COMMENT '考评人职位ID',
    relation_type ENUM('subordinate', 'superior', 'peer', 'cross_dept') COMMENT '关系类型',
    weight_value DECIMAL(3,2) DEFAULT 1.00 COMMENT '权重值',
    priority INT DEFAULT 0 COMMENT '优先级（数字越大优先级越高）',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    effective_from DATE COMMENT '生效开始日期',
    effective_to DATE COMMENT '生效结束日期',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    deleted_at TIMESTAMP NULL,
    FOREIGN KEY (department_id) REFERENCES department(id),
    FOREIGN KEY (position_id) REFERENCES position(id),
    FOREIGN KEY (evaluator_dept_id) REFERENCES department(id),
    FOREIGN KEY (evaluator_position_id) REFERENCES position(id),
    INDEX idx_department_id (department_id),
    INDEX idx_position_id (position_id),
    INDEX idx_priority (priority),
    INDEX idx_is_active (is_active),
    INDEX idx_effective_from (effective_from),
    INDEX idx_effective_to (effective_to)
);
```

#### 2.2.10 评分等级表 (ScoringTier)
```sql
CREATE TABLE scoring_tier (
    id INT PRIMARY KEY AUTO_INCREMENT,
    item_id INT NOT NULL COMMENT '关联的评分项ID',
    tier_name VARCHAR(50) NOT NULL COMMENT '等级名称（如：优秀、良好）',
    min_score DECIMAL(5,2) NOT NULL COMMENT '最低分数',
    max_score DECIMAL(5,2) NOT NULL COMMENT '最高分数',
    description TEXT COMMENT '等级描述',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    deleted_at TIMESTAMP NULL,
    FOREIGN KEY (item_id) REFERENCES evaluation_item(id),
    INDEX idx_item_id (item_id),
    INDEX idx_sort_order (sort_order),
    UNIQUE KEY uk_item_tier (item_id, tier_name)
);
```

#### 2.2.11 评分记录表 (EvaluationRecord)
```sql
CREATE TABLE evaluation_record (
    id INT PRIMARY KEY AUTO_INCREMENT,
    relation_id INT NOT NULL COMMENT '考评关系ID',
    item_id INT NOT NULL COMMENT '评分项ID',
    score DECIMAL(5,2) COMMENT '得分',
    choice_value VARCHAR(500) COMMENT '选择值',
    text_value TEXT COMMENT '文本值',
    submit_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (relation_id) REFERENCES evaluation_relation(id),
    FOREIGN KEY (item_id) REFERENCES evaluation_item(id),
    UNIQUE KEY uk_relation_item (relation_id, item_id),
    INDEX idx_relation_id (relation_id),
    INDEX idx_item_id (item_id),
    INDEX idx_submit_time (submit_time)
);
```

#### 2.2.12 审计日志表 (AuditLog)
```sql
CREATE TABLE audit_log (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT COMMENT '操作用户ID',
    user_type ENUM('admin', 'staff') COMMENT '用户类型',
    action VARCHAR(100) NOT NULL COMMENT '操作类型',
    table_name VARCHAR(100) COMMENT '操作表名',
    record_id INT COMMENT '操作记录ID',
    old_values JSON COMMENT '修改前数据',
    new_values JSON COMMENT '修改后数据',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_table_name (table_name),
    INDEX idx_created_at (created_at)
);
```

### 2.3 新增功能模块数据表

#### 2.3.1 考评进度统计表 (EvaluationProgress)
```sql
CREATE TABLE evaluation_progress (
    id INT PRIMARY KEY AUTO_INCREMENT,
    batch_id INT NOT NULL COMMENT '考评批次ID',
    total_relations INT DEFAULT 0 COMMENT '总考评关系数',
    completed_relations INT DEFAULT 0 COMMENT '已完成考评关系数',
    completion_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '完成率(%)',
    total_evaluators INT DEFAULT 0 COMMENT '总考评人数',
    completed_evaluators INT DEFAULT 0 COMMENT '已完成考评的考评人数',
    evaluator_completion_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '考评人完成率(%)',
    total_evaluated INT DEFAULT 0 COMMENT '总被考评人数',
    completed_evaluated INT DEFAULT 0 COMMENT '已完成考评的被考评人数',
    evaluated_completion_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '被考评人完成率(%)',
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (batch_id) REFERENCES evaluation_batch(id),
    UNIQUE KEY uk_batch_id (batch_id),
    INDEX idx_completion_rate (completion_rate),
    INDEX idx_last_updated (last_updated)
);
```

#### 2.3.2 人才盘点数据表 (TalentAssessment)
```sql
CREATE TABLE talent_assessment (
    id INT PRIMARY KEY AUTO_INCREMENT,
    staff_id INT NOT NULL COMMENT '员工ID',
    batch_id INT NOT NULL COMMENT '考评批次ID',
    overall_score DECIMAL(5,2) COMMENT '综合得分',
    performance_score DECIMAL(5,2) COMMENT '绩效得分',
    potential_score DECIMAL(5,2) COMMENT '潜力得分',
    talent_category ENUM('high_performer', 'high_potential', 'core_talent', 'improvement_needed', 'solid_performer') COMMENT '人才分类',
    strengths TEXT COMMENT '优势分析',
    improvement_areas TEXT COMMENT '改进领域',
    development_suggestions TEXT COMMENT '发展建议',
    succession_readiness ENUM('ready_now', 'ready_1_year', 'ready_2_years', 'not_ready') COMMENT '继任准备度',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    FOREIGN KEY (staff_id) REFERENCES staff(id),
    FOREIGN KEY (batch_id) REFERENCES evaluation_batch(id),
    UNIQUE KEY uk_staff_batch (staff_id, batch_id),
    INDEX idx_overall_score (overall_score),
    INDEX idx_talent_category (talent_category),
    INDEX idx_succession_readiness (succession_readiness)
);
```

#### 2.3.3 考评报告表 (EvaluationReport)
```sql
CREATE TABLE evaluation_report (
    id INT PRIMARY KEY AUTO_INCREMENT,
    batch_id INT NOT NULL COMMENT '考评批次ID',
    report_type ENUM('individual', 'department', 'company', 'talent_matrix') COMMENT '报告类型',
    target_id INT COMMENT '目标ID(个人/部门ID)',
    title VARCHAR(200) NOT NULL COMMENT '报告标题',
    content LONGTEXT COMMENT '报告内容(JSON格式)',
    file_path VARCHAR(500) COMMENT '报告文件路径',
    status ENUM('generating', 'completed', 'failed') DEFAULT 'generating' COMMENT '生成状态',
    generated_at TIMESTAMP NULL COMMENT '生成完成时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT,
    FOREIGN KEY (batch_id) REFERENCES evaluation_batch(id),
    INDEX idx_batch_id (batch_id),
    INDEX idx_report_type (report_type),
    INDEX idx_target_id (target_id),
    INDEX idx_status (status),
    INDEX idx_generated_at (generated_at)
);
```

### 2.4 在线考试模块数据表（后期扩展）

#### 2.4.1 题目分类表 (QuestionCategory)
```sql
CREATE TABLE question_category (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    parent_id INT DEFAULT NULL COMMENT '父分类ID',
    description TEXT COMMENT '分类描述',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    deleted_at TIMESTAMP NULL,
    FOREIGN KEY (parent_id) REFERENCES question_category(id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_sort_order (sort_order)
);
```

#### 2.4.2 题目表 (Question)
```sql
CREATE TABLE question (
    id INT PRIMARY KEY AUTO_INCREMENT,
    category_id INT NOT NULL COMMENT '题目分类ID',
    question_type ENUM('single_choice', 'multiple_choice', 'true_false', 'short_answer', 'essay') COMMENT '题目类型',
    title TEXT NOT NULL COMMENT '题目标题',
    content TEXT COMMENT '题目内容',
    options JSON COMMENT '选择项(JSON格式)',
    correct_answer TEXT COMMENT '正确答案',
    explanation TEXT COMMENT '解析',
    difficulty ENUM('easy', 'medium', 'hard') DEFAULT 'medium' COMMENT '难度',
    score DECIMAL(5,2) DEFAULT 1.00 COMMENT '分值',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    deleted_at TIMESTAMP NULL,
    FOREIGN KEY (category_id) REFERENCES question_category(id),
    INDEX idx_category_id (category_id),
    INDEX idx_question_type (question_type),
    INDEX idx_difficulty (difficulty),
    INDEX idx_is_active (is_active)
);
```

#### 2.4.3 试卷模板表 (PaperTemplate)
```sql
CREATE TABLE paper_template (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL COMMENT '试卷标题',
    description TEXT COMMENT '试卷描述',
    total_score DECIMAL(5,2) DEFAULT 100.00 COMMENT '总分',
    duration_minutes INT DEFAULT 60 COMMENT '考试时长(分钟)',
    question_config JSON COMMENT '题目配置(JSON格式)',
    is_random BOOLEAN DEFAULT FALSE COMMENT '是否随机抽题',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    deleted_at TIMESTAMP NULL,
    INDEX idx_is_active (is_active)
);
```

#### 2.4.4 考试批次表 (ExamBatch)
```sql
CREATE TABLE exam_batch (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL COMMENT '考试批次标题',
    paper_template_id INT NOT NULL COMMENT '试卷模板ID',
    start_time TIMESTAMP NOT NULL COMMENT '开始时间',
    end_time TIMESTAMP NOT NULL COMMENT '结束时间',
    duration_minutes INT NOT NULL COMMENT '考试时长(分钟)',
    max_attempts INT DEFAULT 1 COMMENT '最大考试次数',
    status ENUM('draft', 'active', 'completed', 'cancelled') DEFAULT 'draft' COMMENT '批次状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    deleted_at TIMESTAMP NULL,
    FOREIGN KEY (paper_template_id) REFERENCES paper_template(id),
    INDEX idx_status (status),
    INDEX idx_start_time (start_time),
    INDEX idx_end_time (end_time)
);
```

#### 2.4.5 考试记录表 (ExamRecord)
```sql
CREATE TABLE exam_record (
    id INT PRIMARY KEY AUTO_INCREMENT,
    exam_batch_id INT NOT NULL COMMENT '考试批次ID',
    staff_id INT NOT NULL COMMENT '考试人员ID',
    start_time TIMESTAMP NOT NULL COMMENT '开始考试时间',
    end_time TIMESTAMP NULL COMMENT '结束考试时间',
    duration_seconds INT COMMENT '实际考试时长(秒)',
    total_score DECIMAL(5,2) COMMENT '总得分',
    status ENUM('in_progress', 'completed', 'timeout', 'abandoned') DEFAULT 'in_progress' COMMENT '考试状态',
    attempt_number INT DEFAULT 1 COMMENT '考试次数',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (exam_batch_id) REFERENCES exam_batch(id),
    FOREIGN KEY (staff_id) REFERENCES staff(id),
    INDEX idx_exam_batch_id (exam_batch_id),
    INDEX idx_staff_id (staff_id),
    INDEX idx_status (status),
    INDEX idx_start_time (start_time)
);
```

## 3. 智能分配算法设计（重构版）

> **重要更新**：智能分配算法已从固定权重模式升级为规则引擎驱动的可配置模式

### 3.1 新算法流程图
```
开始批次分配
    ↓
获取批次配置（默认模板ID等）
    ↓
获取所有需要被考评的人员
    ↓
遍历每个被考评人
    ↓
╔═══════════════════════════════════════════════════════════════╗
║                    规则引擎处理流程                            ║
╚═══════════════════════════════════════════════════════════════╝
    ↓
第一优先级：检查特殊权重规则
┌─────────────────────────────────────────────────────────────┐
│ 查询WeightingRule表，匹配条件：                              │
│ - 被考评人部门/职位                                          │
│ - 考评人部门/职位                                            │
│ - 关系类型                                                  │
│ - 生效日期                                                  │
└─────────────────────────────────────────────────────────────┘
    ↓
第二优先级：批次默认模板
┌─────────────────────────────────────────────────────────────┐
│ 使用batch.default_template_id                                │
│ 为考评关系分配默认模板                                         │
└─────────────────────────────────────────────────────────────┘
    ↓
第三优先级：系统默认权重
┌─────────────────────────────────────────────────────────────┐
│ 权重 = 1.0 (系统默认值)                                      │
│ 模板 = 系统默认模板                                          │
└─────────────────────────────────────────────────────────────┘
    ↓
生成考评关系记录
┌─────────────────────────────────────────────────────────────┐
│ evaluation_relation表记录：                                   │
│ - evaluator_id + evaluated_id                                │
│ - template_id (动态分配)                                      │
│ - weight (规则计算结果)                                       │
│ - relation_type                                              │
└─────────────────────────────────────────────────────────────┘
    ↓
提供管理界面手动调整
    ↓
完成智能分配
```

### 3.2 新算法实现伪代码

```python
def intelligent_assign_evaluators(batch_id):
    """
    智能分配考评关系算法（规则引擎版）
    """
    batch = get_evaluation_batch(batch_id)
    all_staff = get_active_staff()
    senior_management = get_senior_management()  # 总经理室人员
    rule_engine = WeightingRuleEngine()
    
    for evaluated_staff in all_staff:
        if evaluated_staff in senior_management:
            continue  # 总经理室人员不被考评
            
        evaluators = get_potential_evaluators(evaluated_staff)
        
        for evaluator in evaluators:
            # 步骤1：确定关系类型
            relation_type = determine_relation_type(evaluator, evaluated_staff)
            
            # 步骤2：规则引擎计算权重和模板
            weight, template_id = rule_engine.calculate_weight_and_template(
                evaluator=evaluator,
                evaluated=evaluated_staff,
                relation_type=relation_type,
                batch_default_template=batch.default_template_id
            )
            
            # 步骤3：创建考评关系记录
            create_evaluation_relation(
                batch_id=batch_id,
                evaluator_id=evaluator.id,
                evaluated_id=evaluated_staff.id,
                template_id=template_id,
                weight=weight,
                relation_type=relation_type
            )

class WeightingRuleEngine:
    """权重规则引擎"""
    
    def calculate_weight_and_template(self, evaluator, evaluated, relation_type, batch_default_template):
        """
        计算权重和模板ID
        优先级：特殊规则 > 批次默认 > 系统默认
        """
        # 第一优先级：查找特殊权重规则
        special_rule = self.find_special_rule(
            evaluator_dept=evaluator.department_id,
            evaluator_position=evaluator.position_id,
            evaluated_dept=evaluated.department_id,
            evaluated_position=evaluated.position_id,
            relation_type=relation_type
        )
        
        if special_rule:
            return special_rule.weight_value, special_rule.template_id
        
        # 第二优先级：使用批次默认模板
        if batch_default_template:
            return 1.0, batch_default_template  # 默认权重1.0
        
        # 第三优先级：系统默认
        default_template = get_system_default_template()
        return 1.0, default_template.id
    
    def find_special_rule(self, evaluator_dept, evaluator_position, 
                         evaluated_dept, evaluated_position, relation_type):
        """查找匹配的特殊权重规则"""
        rules = WeightingRule.objects.filter(
            is_active=True,
            effective_from__lte=timezone.now().date(),
            effective_to__gte=timezone.now().date()
        ).filter(
            Q(department_id__isnull=True) | Q(department_id=evaluated_dept),
            Q(position_id__isnull=True) | Q(position_id=evaluated_position),
            Q(evaluator_dept_id__isnull=True) | Q(evaluator_dept_id=evaluator_dept),
            Q(evaluator_position_id__isnull=True) | Q(evaluator_position_id=evaluator_position),
            Q(relation_type__isnull=True) | Q(relation_type=relation_type)
        ).order_by('-priority')
        
        return rules.first()  # 返回优先级最高的规则
```

### 3.3 新权重配置机制

#### 3.3.1 默认权重策略
| 配置层级 | 权重值 | 适用条件 | 说明 |
|---------|--------|----------|------|
| 系统默认 | 1.0 | 无特殊规则时 | 确保系统稳定运行的基础权重 |
| 批次默认 | 1.0 | 使用批次默认模板 | 可通过批次配置统一调整 |
| 特殊规则 | 可配置 | 匹配WeightingRule条件 | 最高优先级，支持精细化配置 |

#### 3.3.2 可配置规则示例
| 规则名称 | 条件 | 权重值 | 说明 |
|---------|------|--------|------|
| 下级评上级 | relation_type='subordinate' | 0.8 | 传统的下级评上级权重 |
| 上级评下级 | relation_type='superior' | 1.2 | 上级权威性体现 |
| 同级互评 | relation_type='peer' | 1.0 | 平等关系 |
| 跨部门协作 | relation_type='cross_dept' | 1.0 | 跨部门公平性 |
| 特定部门强权重 | department_id=财务科 | 1.5 | 特殊业务需求 |
| 高层评价权重 | evaluator_position_id=9级 | 1.2 | 高层权威性 |

#### 3.3.3 规则优先级处理
1. **优先级字段**：WeightingRule.priority，数字越大优先级越高
2. **冲突处理**：同一考评关系匹配多个规则时，选择priority最高的
3. **默认兜底**：无匹配规则时使用默认权重1.0
4. **生效时间**：支持规则的生效和失效时间控制

## 4. 系统模块设计

### 4.1 认证模块设计

#### 4.1.1 双重认证机制
```python
# 管理员认证
class AdminAuthView:
    def authenticate(self, username, password):
        staff = Staff.objects.filter(
            employee_no=username,
            can_login_admin=True,
            is_active=True,
            deleted_at__isnull=True
        ).first()
        
        if staff and verify_password(password, staff.password_hash):
            return staff
        return None

# 匿名认证
class AnonymousAuthView:
    def authenticate(self, anonymous_code):
        code_obj = AnonymousCode.objects.filter(
            anonymous_code=anonymous_code,
            is_active=True,
            expire_time__gt=timezone.now()
        ).first()
        
        if code_obj:
            return code_obj.staff
        return None
```

#### 4.1.2 权限控制装饰器
```python
def admin_required(view_func):
    """管理员权限检查装饰器"""
    def wrapper(request, *args, **kwargs):
        if not request.user.can_login_admin:
            return redirect('admin_login')
        return view_func(request, *args, **kwargs)
    return wrapper

def department_permission_required(view_func):
    """部门权限检查装饰器"""
    def wrapper(request, *args, **kwargs):
        # 检查用户是否只能访问自己部门的数据
        if not is_super_admin(request.user):
            # 限制部门访问权限
            pass
        return view_func(request, *args, **kwargs)
    return wrapper
```

### 4.2 考评模板管理服务设计

```python
class EvaluationTemplateService:
    """考评模板管理服务（新增）"""
    
    def create_template_with_items(self, template_data, items_data):
        """创建模板及其评分项（支持内联表单集）"""
        with transaction.atomic():
            template = EvaluationTemplate.objects.create(**template_data)
            
            for item_data in items_data:
                item_data['template_id'] = template.id
                item = EvaluationItem.objects.create(**item_data)
                
                # 如果是结构化评分，创建评分等级
                if item.scoring_mode == 'structured' and 'scoring_tiers' in item_data:
                    for tier_data in item_data['scoring_tiers']:
                        tier_data['item_id'] = item.id
                        ScoringTier.objects.create(**tier_data)
            
            return template
    
    def update_items_order(self, template_id, items_order):
        """更新评分项排序（支持拖拽排序）"""
        for order, item_id in enumerate(items_order):
            EvaluationItem.objects.filter(
                id=item_id, 
                template_id=template_id
            ).update(sort_order=order)

### 4.3 权重规则管理服务设计

```python
class WeightingRuleService:
    """权重规则管理服务（新增）"""
    
    def create_rule(self, rule_data):
        """创建权重规则"""
        # 验证规则冲突
        conflicts = self.check_rule_conflicts(rule_data)
        if conflicts:
            raise ValidationError(f"规则冲突：{conflicts}")
        
        return WeightingRule.objects.create(**rule_data)
    
    def check_rule_conflicts(self, rule_data):
        """检查规则冲突"""
        existing_rules = WeightingRule.objects.filter(
            is_active=True,
            department_id=rule_data.get('department_id'),
            position_id=rule_data.get('position_id'),
            relation_type=rule_data.get('relation_type')
        )
        return existing_rules.exists()
    
    def get_applicable_rules(self, evaluator, evaluated, relation_type):
        """获取适用的权重规则"""
        return WeightingRule.objects.filter(
            is_active=True,
            effective_from__lte=timezone.now().date(),
            effective_to__gte=timezone.now().date()
        ).filter(
            Q(department_id__isnull=True) | Q(department_id=evaluated.department_id),
            Q(position_id__isnull=True) | Q(position_id=evaluated.position_id),
            Q(evaluator_dept_id__isnull=True) | Q(evaluator_dept_id=evaluator.department_id),
            Q(evaluator_position_id__isnull=True) | Q(evaluator_position_id=evaluator.position_id),
            Q(relation_type__isnull=True) | Q(relation_type=relation_type)
        ).order_by('-priority')

### 4.4 智能分配服务设计（重构版）

```python
class IntelligentAssignmentService:
    """智能分配服务类（重构版）"""
    
    def __init__(self):
        self.rule_service = WeightingRuleService()
        self.template_service = EvaluationTemplateService()
    
    def assign_batch_relations(self, batch_id):
        """为整个批次分配考评关系"""
        batch = EvaluationBatch.objects.get(id=batch_id)
        all_staff = self.get_evaluable_staff()
        
        for staff in all_staff:
            evaluators = self.get_evaluators_for_staff(staff)
            self.create_relations_with_rules(batch, staff, evaluators)
    
    def create_relations_with_rules(self, batch, evaluated_staff, evaluators):
        """使用规则引擎创建考评关系"""
        for evaluator in evaluators:
            relation_type = self.determine_relation_type(evaluator, evaluated_staff)
            
            # 查找适用的权重规则
            applicable_rules = self.rule_service.get_applicable_rules(
                evaluator, evaluated_staff, relation_type
            )
            
            # 确定权重和模板
            if applicable_rules.exists():
                rule = applicable_rules.first()
                weight = rule.weight_value
                template_id = rule.template_id or batch.default_template_id
            else:
                weight = 1.0  # 默认权重
                template_id = batch.default_template_id
            
            # 创建考评关系
            EvaluationRelation.objects.create(
                batch_id=batch.id,
                evaluator_id=evaluator.id,
                evaluated_id=evaluated_staff.id,
                template_id=template_id,
                weight=weight,
                relation_type=relation_type
            )
    
    def calculate_weighted_score(self, evaluated_staff_id, batch_id):
        """计算加权平均分（保持不变）"""
        relations = EvaluationRelation.objects.filter(
            evaluated_id=evaluated_staff_id,
            batch_id=batch_id,
            is_completed=True
        )
        
        total_weighted_score = 0
        total_weight = 0
        
        for relation in relations:
            score = self.get_relation_total_score(relation)
            total_weighted_score += score * relation.weight
            total_weight += relation.weight
            
        return total_weighted_score / total_weight if total_weight > 0 else 0
```

### 4.5 考评进度管理服务设计

```python
class EvaluationProgressService:
    """考评进度管理服务（新增）"""
    
    def update_batch_progress(self, batch_id):
        """更新批次进度统计"""
        # 获取基础数据
        total_relations = EvaluationRelation.objects.filter(
            batch_id=batch_id, deleted_at__isnull=True
        ).count()
        
        completed_relations = EvaluationRelation.objects.filter(
            batch_id=batch_id, is_completed=True, deleted_at__isnull=True
        ).count()
        
        # 计算考评人完成情况
        evaluator_stats = self.calculate_evaluator_completion(batch_id)
        
        # 计算被考评人完成情况  
        evaluated_stats = self.calculate_evaluated_completion(batch_id)
        
        # 更新或创建进度记录
        progress, created = EvaluationProgress.objects.update_or_create(
            batch_id=batch_id,
            defaults={
                'total_relations': total_relations,
                'completed_relations': completed_relations,
                'completion_rate': (completed_relations / total_relations * 100) if total_relations > 0 else 0,
                'total_evaluators': evaluator_stats['total'],
                'completed_evaluators': evaluator_stats['completed'],
                'evaluator_completion_rate': evaluator_stats['rate'],
                'total_evaluated': evaluated_stats['total'],
                'completed_evaluated': evaluated_stats['completed'],
                'evaluated_completion_rate': evaluated_stats['rate']
            }
        )
        
        return progress
    
    def get_incomplete_evaluators(self, batch_id):
        """获取未完成考评的考评人列表"""
        incomplete_evaluators = []
        
        # 查找有未完成考评任务的考评人
        evaluators_with_incomplete = EvaluationRelation.objects.filter(
            batch_id=batch_id,
            is_completed=False,
            deleted_at__isnull=True
        ).values('evaluator_id').annotate(
            incomplete_count=Count('id'),
            total_count=Count('id')
        ).select_related('evaluator')
        
        for item in evaluators_with_incomplete:
            evaluator = Staff.objects.get(id=item['evaluator_id'])
            total_tasks = EvaluationRelation.objects.filter(
                batch_id=batch_id,
                evaluator_id=item['evaluator_id'],
                deleted_at__isnull=True
            ).count()
            
            incomplete_evaluators.append({
                'evaluator': evaluator,
                'incomplete_count': item['incomplete_count'],
                'total_count': total_tasks,
                'completion_rate': ((total_tasks - item['incomplete_count']) / total_tasks * 100) if total_tasks > 0 else 0
            })
        
        return incomplete_evaluators
    
    def get_evaluated_scores(self, batch_id):
        """获取被考评人分数统计"""
        evaluated_scores = []
        
        # 获取所有被考评人
        evaluated_staff = EvaluationRelation.objects.filter(
            batch_id=batch_id,
            deleted_at__isnull=True
        ).values_list('evaluated_id', flat=True).distinct()
        
        assignment_service = IntelligentAssignmentService()
        
        for staff_id in evaluated_staff:
            staff = Staff.objects.get(id=staff_id)
            
            # 计算加权平均分
            weighted_score = assignment_service.calculate_weighted_score(staff_id, batch_id)
            
            # 统计评分详情
            completed_relations = EvaluationRelation.objects.filter(
                batch_id=batch_id,
                evaluated_id=staff_id,
                is_completed=True,
                deleted_at__isnull=True
            ).count()
            
            total_relations = EvaluationRelation.objects.filter(
                batch_id=batch_id,
                evaluated_id=staff_id,
                deleted_at__isnull=True
            ).count()
            
            evaluated_scores.append({
                'staff': staff,
                'weighted_score': weighted_score,
                'completed_evaluations': completed_relations,
                'total_evaluations': total_relations,
                'completion_rate': (completed_relations / total_relations * 100) if total_relations > 0 else 0
            })
        
        return sorted(evaluated_scores, key=lambda x: x['weighted_score'], reverse=True)
    
    def get_real_time_dashboard_data(self, batch_id):
        """获取实时仪表板数据"""
        progress = EvaluationProgress.objects.filter(batch_id=batch_id).first()
        
        if not progress:
            progress = self.update_batch_progress(batch_id)
        
        return {
            'progress': progress,
            'incomplete_evaluators': self.get_incomplete_evaluators(batch_id)[:10],  # 前10个
            'top_scores': self.get_evaluated_scores(batch_id)[:10],  # 前10名
            'recent_completions': self.get_recent_completions(batch_id, limit=5)
        }

### 4.6 报告与人才盘点服务设计

```python
class TalentAssessmentService:
    """人才盘点服务（新增）"""
    
    def generate_talent_matrix(self, batch_id):
        """生成人才九宫格矩阵"""
        # 获取所有被考评人的评分数据
        evaluated_scores = EvaluationProgressService().get_evaluated_scores(batch_id)
        
        talent_matrix = {
            'high_performer_high_potential': [],  # 明星人才
            'high_performer_medium_potential': [],  # 核心人才  
            'high_performer_low_potential': [],  # 专业人才
            'medium_performer_high_potential': [],  # 潜力人才
            'medium_performer_medium_potential': [],  # 稳定人才
            'medium_performer_low_potential': [],  # 基础人才
            'low_performer_high_potential': [],  # 待发展人才
            'low_performer_medium_potential': [],  # 改进人才
            'low_performer_low_potential': []  # 流失风险人才
        }
        
        for item in evaluated_scores:
            staff = item['staff']
            performance_score = item['weighted_score']
            
            # 计算潜力得分（基于多维度评价）
            potential_score = self.calculate_potential_score(staff.id, batch_id)
            
            # 分类逻辑
            performance_level = self.categorize_performance(performance_score)
            potential_level = self.categorize_potential(potential_score)
            
            category_key = f"{performance_level}_performer_{potential_level}_potential"
            talent_matrix[category_key].append({
                'staff': staff,
                'performance_score': performance_score,
                'potential_score': potential_score
            })
            
            # 保存人才评估记录
            self.save_talent_assessment(staff.id, batch_id, performance_score, potential_score)
        
        return talent_matrix
    
    def generate_succession_plan(self, department_id=None):
        """生成继任计划"""
        # 识别关键岗位
        key_positions = Position.objects.filter(
            is_manager=True,
            department_id=department_id if department_id else None
        )
        
        succession_plan = {}
        
        for position in key_positions:
            # 现任者
            current_holders = Staff.objects.filter(
                position_id=position.id,
                is_active=True
            )
            
            # 继任候选人（高潜力人才）
            candidates = TalentAssessment.objects.filter(
                talent_category__in=['high_potential', 'high_performer'],
                succession_readiness__in=['ready_now', 'ready_1_year']
            ).select_related('staff')
            
            succession_plan[position.name] = {
                'position': position,
                'current_holders': current_holders,
                'ready_now': [c for c in candidates if c.succession_readiness == 'ready_now'],
                'ready_1_year': [c for c in candidates if c.succession_readiness == 'ready_1_year']
            }
        
        return succession_plan
    
    def generate_development_recommendations(self, staff_id, batch_id):
        """生成个人发展建议"""
        talent_assessment = TalentAssessment.objects.filter(
            staff_id=staff_id,
            batch_id=batch_id
        ).first()
        
        if not talent_assessment:
            return None
        
        # 基于人才分类生成建议
        recommendations = {
            'high_performer': {
                'focus': '领导力发展和跨部门经验',
                'actions': ['参与高层决策', '轮岗锻炼', '外部培训'],
                'timeline': '6-12个月'
            },
            'high_potential': {
                'focus': '技能提升和绩效改进',
                'actions': ['导师制', '项目挑战', '专业认证'],
                'timeline': '3-6个月'  
            },
            # 更多分类...
        }
        
        return recommendations.get(talent_assessment.talent_category, {})

class ReportGenerationService:
    """报告生成服务（新增）"""
    
    def generate_comprehensive_report(self, batch_id, report_type, target_id=None):
        """生成综合报告"""
        report_data = {}
        
        if report_type == 'individual':
            report_data = self.generate_individual_report(target_id, batch_id)
        elif report_type == 'department':
            report_data = self.generate_department_report(target_id, batch_id)
        elif report_type == 'company':
            report_data = self.generate_company_report(batch_id)
        elif report_type == 'talent_matrix':
            report_data = TalentAssessmentService().generate_talent_matrix(batch_id)
        
        # 保存报告记录
        report = EvaluationReport.objects.create(
            batch_id=batch_id,
            report_type=report_type,
            target_id=target_id,
            title=f"{report_type.title()} Report - {timezone.now().strftime('%Y-%m-%d')}",
            content=json.dumps(report_data, cls=DjangoJSONEncoder),
            status='completed',
            generated_at=timezone.now()
        )
        
        # 生成PDF文件（可选）
        if self.should_generate_pdf(report_type):
            pdf_path = self.generate_pdf_report(report_data, report.id)
            report.file_path = pdf_path
            report.save()
        
        return report

### 4.7 统计分析模块设计

```python
class EvaluationStatisticsService:
    """考评统计分析服务"""
    
    def generate_staff_report(self, staff_id, batch_id):
        """生成员工个人考评报告"""
        relations = EvaluationRelation.objects.filter(
            evaluated_id=staff_id,
            batch_id=batch_id,
            is_completed=True
        )
        
        report = {
            'staff_info': self.get_staff_info(staff_id),
            'overall_score': self.calculate_weighted_score(staff_id, batch_id),
            'evaluation_count': relations.count(),
            'score_distribution': self.get_score_distribution(relations),
            'strength_areas': self.get_strength_areas(relations),
            'improvement_areas': self.get_improvement_areas(relations)
        }
        
        return report
    
    def generate_department_report(self, department_id, batch_id):
        """生成部门考评报告"""
        staff_list = Staff.objects.filter(
            department_id=department_id,
            is_active=True,
            deleted_at__isnull=True
        )
        
        department_report = {
            'department_info': self.get_department_info(department_id),
            'staff_scores': [],
            'average_score': 0,
            'score_ranking': [],
            'evaluation_completion_rate': 0
        }
        
        for staff in staff_list:
            staff_score = self.calculate_weighted_score(staff.id, batch_id)
            department_report['staff_scores'].append({
                'staff': staff,
                'score': staff_score
            })
        
        return department_report
```

### 4.8 Excel数据处理服务设计

```python
import pandas as pd
import openpyxl
from django.http import HttpResponse
from django.core.exceptions import ValidationError

class ExcelDataService:
    """Excel数据处理服务（新增）"""
    
    def import_departments_from_excel(self, file):
        """从Excel导入部门数据"""
        try:
            # 读取Excel文件
            df = pd.read_excel(file, engine='openpyxl')
            
            # 验证必要列是否存在
            required_columns = ['部门名称', '员工数量']
            if not all(col in df.columns for col in required_columns):
                raise ValidationError(f"Excel文件缺少必要列：{required_columns}")
            
            # 数据处理和导入
            import_results = {
                'success_count': 0,
                'error_count': 0,
                'errors': []
            }
            
            for index, row in df.iterrows():
                try:
                    # 生成部门编号
                    dept_code = self.generate_dept_code(row['部门名称'])
                    
                    # 创建或更新部门
                    department, created = Department.objects.get_or_create(
                        name=row['部门名称'],
                        defaults={
                            'dept_code': dept_code,
                            'description': f"预估员工数量：{row.get('员工数量', 0)}人"
                        }
                    )
                    
                    if created:
                        import_results['success_count'] += 1
                    else:
                        import_results['errors'].append(f"第{index+2}行：部门'{row['部门名称']}'已存在")
                        import_results['error_count'] += 1
                        
                except Exception as e:
                    import_results['errors'].append(f"第{index+2}行：{str(e)}")
                    import_results['error_count'] += 1
            
            return import_results
            
        except Exception as e:
            raise ValidationError(f"Excel文件处理失败：{str(e)}")
    
    def import_staff_from_excel(self, file):
        """从Excel导入员工数据"""
        try:
            df = pd.read_excel(file, engine='openpyxl')
            
            # 验证必要列
            required_columns = ['姓名', '部门名称', '职位名称', '是否为管理人员']
            if not all(col in df.columns for col in required_columns):
                raise ValidationError(f"Excel文件缺少必要列：{required_columns}")
            
            import_results = {
                'success_count': 0,
                'error_count': 0,
                'errors': []
            }
            
            for index, row in df.iterrows():
                try:
                    # 查找部门
                    department = Department.objects.filter(
                        name=row['部门名称']
                    ).first()
                    
                    if not department:
                        import_results['errors'].append(f"第{index+2}行：找不到部门'{row['部门名称']}'")
                        import_results['error_count'] += 1
                        continue
                    
                    # 查找职位
                    position = Position.objects.filter(
                        name=row['职位名称']
                    ).first()
                    
                    # 生成员工编号
                    employee_no = self.generate_employee_no(department.dept_code)
                    
                    # 创建员工记录
                    staff, created = Staff.objects.get_or_create(
                        name=row['姓名'],
                        department=department,
                        defaults={
                            'employee_no': employee_no,
                            'position': position,
                            'is_manager': str(row.get('是否为管理人员', '否')).lower() in ['是', 'yes', 'true', '1'],
                            'email': row.get('邮箱', ''),
                            'phone': row.get('手机号', '')
                        }
                    )
                    
                    if created:
                        import_results['success_count'] += 1
                    else:
                        import_results['errors'].append(f"第{index+2}行：员工'{row['姓名']}'在该部门已存在")
                        import_results['error_count'] += 1
                        
                except Exception as e:
                    import_results['errors'].append(f"第{index+2}行：{str(e)}")
                    import_results['error_count'] += 1
            
            return import_results
            
        except Exception as e:
            raise ValidationError(f"Excel文件处理失败：{str(e)}")
    
    def export_evaluation_report_to_excel(self, batch_id, report_type='comprehensive'):
        """导出考评报告到Excel"""
        # 获取考评数据
        progress_service = EvaluationProgressService()
        evaluated_scores = progress_service.get_evaluated_scores(batch_id)
        
        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "考评结果报告"
        
        # 设置表头
        headers = ['员工姓名', '部门', '职位', '综合得分', '完成评价数', '总评价数', '完成率']
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
        
        # 填充数据
        for row_idx, item in enumerate(evaluated_scores, 2):
            staff = item['staff']
            ws.cell(row=row_idx, column=1, value=staff.name)
            ws.cell(row=row_idx, column=2, value=staff.department.name)
            ws.cell(row=row_idx, column=3, value=staff.position.name if staff.position else '')
            ws.cell(row=row_idx, column=4, value=float(item['weighted_score']))
            ws.cell(row=row_idx, column=5, value=item['completed_evaluations'])
            ws.cell(row=row_idx, column=6, value=item['total_evaluations'])
            ws.cell(row=row_idx, column=7, value=f"{item['completion_rate']:.1f}%")
        
        # 生成HTTP响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="evaluation_report_{batch_id}.xlsx"'
        
        wb.save(response)
        return response

### 4.9 钉钉集成服务设计

```python
import requests
import json
from django.conf import settings
from django.utils import timezone

class DingTalkIntegrationService:
    """钉钉集成服务（新增）"""
    
    def __init__(self):
        self.app_key = getattr(settings, 'DINGTALK_APP_KEY', '')
        self.app_secret = getattr(settings, 'DINGTALK_APP_SECRET', '')
        self.access_token = None
        self.token_expires_at = None
    
    def get_access_token(self):
        """获取钉钉访问令牌"""
        if self.access_token and self.token_expires_at > timezone.now():
            return self.access_token
        
        url = "https://oapi.dingtalk.com/gettoken"
        params = {
            'appkey': self.app_key,
            'appsecret': self.app_secret
        }
        
        response = requests.get(url, params=params)
        result = response.json()
        
        if result.get('errcode') == 0:
            self.access_token = result['access_token']
            # 钉钉token有效期2小时，提前10分钟刷新
            self.token_expires_at = timezone.now() + timezone.timedelta(seconds=result['expires_in'] - 600)
            return self.access_token
        else:
            raise Exception(f"获取钉钉access_token失败：{result}")
    
    def sync_organization_structure(self):
        """同步钉钉组织架构"""
        access_token = self.get_access_token()
        
        # 获取部门列表
        departments = self.get_department_list(access_token)
        
        # 同步部门数据
        sync_results = {
            'departments_synced': 0,
            'staff_synced': 0,
            'errors': []
        }
        
        for dept_info in departments:
            try:
                # 创建或更新部门
                department, created = Department.objects.update_or_create(
                    dept_code=f"DD_{dept_info['id']}",  # 钉钉部门ID作为编号
                    defaults={
                        'name': dept_info['name'],
                        'description': f"钉钉同步部门，ID：{dept_info['id']}"
                    }
                )
                
                if created:
                    sync_results['departments_synced'] += 1
                
                # 获取部门成员
                staff_list = self.get_department_staff(access_token, dept_info['id'])
                
                for staff_info in staff_list:
                    staff, staff_created = Staff.objects.update_or_create(
                        employee_no=staff_info.get('jobnumber', f"DD_{staff_info['userid']}"),
                        defaults={
                            'name': staff_info['name'],
                            'department': department,
                            'email': staff_info.get('email', ''),
                            'phone': staff_info.get('mobile', ''),
                            'is_active': staff_info.get('active', True)
                        }
                    )
                    
                    if staff_created:
                        sync_results['staff_synced'] += 1
                        
            except Exception as e:
                sync_results['errors'].append(f"同步部门{dept_info['name']}失败：{str(e)}")
        
        return sync_results
    
    def send_evaluation_notification(self, staff_ids, message, batch_id):
        """发送考评通知消息"""
        access_token = self.get_access_token()
        
        # 构造消息内容
        notification_message = {
            "msgtype": "text",
            "text": {
                "content": message
            }
        }
        
        # 批量发送消息
        send_results = {
            'success_count': 0,
            'error_count': 0,
            'errors': []
        }
        
        for staff_id in staff_ids:
            try:
                staff = Staff.objects.get(id=staff_id)
                
                # 通过工号查找钉钉用户ID
                dingtalk_userid = self.get_dingtalk_userid_by_employee_no(
                    access_token, staff.employee_no
                )
                
                if dingtalk_userid:
                    # 发送工作通知
                    self.send_work_notification(
                        access_token, 
                        dingtalk_userid, 
                        notification_message
                    )
                    send_results['success_count'] += 1
                else:
                    send_results['errors'].append(f"未找到员工{staff.name}的钉钉账号")
                    send_results['error_count'] += 1
                    
            except Exception as e:
                send_results['errors'].append(f"发送通知给员工{staff_id}失败：{str(e)}")
                send_results['error_count'] += 1
        
        return send_results
    
    def create_evaluation_calendar_event(self, batch_id):
        """创建考评日程事件"""
        access_token = self.get_access_token()
        batch = EvaluationBatch.objects.get(id=batch_id)
        
        # 创建日程事件
        event_data = {
            "summary": f"考评批次：{batch.title}",
            "description": f"考评说明：{batch.description}",
            "start": {
                "dateTime": batch.start_time.isoformat(),
                "timeZone": "Asia/Shanghai"
            },
            "end": {
                "dateTime": batch.end_time.isoformat(),
                "timeZone": "Asia/Shanghai"
            }
        }
        
        # 调用钉钉日历API创建事件
        # 具体实现依据钉钉API文档
        return self.create_calendar_event(access_token, event_data)

## 5. 前端界面设计

### 5.1 设计风格指导

#### 5.1.1 色彩方案
- **主色调**：#3B82F6 (蓝色) - 专业、信任
- **辅助色**：#10B981 (绿色) - 成功、完成
- **警告色**：#F59E0B (橙色) - 注意、提醒  
- **错误色**：#EF4444 (红色) - 错误、危险
- **中性色**：#6B7280 (灰色) - 文本、边框

#### 5.1.2 布局原则
- 采用响应式栅格系统
- 左侧导航 + 主内容区域布局
- 卡片式设计，清晰分区
- 充足的留白，提升可读性

### 5.2 管理后台界面设计

#### 5.2.1 整体布局
```html
<!-- 管理后台主布局 -->
<div class="min-h-screen bg-gray-50">
    <!-- 顶部导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="px-6 py-4">
            <div class="flex justify-between items-center">
                <h1 class="text-xl font-semibold text-gray-900">企业考评管理系统</h1>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-gray-600">欢迎，{{ user.name }}</span>
                    <a href="{% url 'admin_logout' %}" class="text-sm text-blue-600 hover:text-blue-800">退出</a>
                </div>
            </div>
        </div>
    </nav>
    
    <div class="flex">
        <!-- 左侧导航菜单 -->
        <aside class="w-64 bg-white shadow-sm h-screen sticky top-0">
            <nav class="mt-8">
                <div class="px-4 space-y-2">
                    <!-- 菜单项 -->
                    <a href="#" class="flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100">
                        <span class="ml-3">部门管理</span>
                    </a>
                    <!-- 更多菜单项... -->
                </div>
            </nav>
        </aside>
        
        <!-- 主内容区域 -->
        <main class="flex-1 p-8">
            <div class="max-w-7xl mx-auto">
                {% block content %}{% endblock %}
            </div>
        </main>
    </div>
</div>
```

#### 5.2.2 数据表格设计
```html
<!-- 响应式数据表格 -->
<div class="bg-white shadow-sm rounded-lg overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex justify-between items-center">
            <h2 class="text-lg font-medium text-gray-900">人员列表</h2>
            <div class="flex space-x-3">
                <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                    添加人员
                </button>
                <button class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                    批量导入
                </button>
            </div>
        </div>
    </div>
    
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        姓名
                    </th>
                    <!-- 更多表头... -->
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for staff in staff_list %}
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {{ staff.name }}
                    </td>
                    <!-- 更多列... -->
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
```

#### 5.2.3 考评模板管理界面（新增）
```html
<!-- 考评模板编辑页面（内联表单集） -->
<div class="bg-white shadow-sm rounded-lg">
    <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-medium text-gray-900">编辑考评模板</h2>
    </div>
    
    <form id="templateForm" class="p-6">
        <!-- 模板基本信息 -->
        <div class="grid grid-cols-2 gap-6 mb-8">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">模板名称</label>
                <input type="text" name="title" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">模板类型</label>
                <select name="template_type" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                    <option value="comprehensive">综合评价</option>
                    <option value="performance">绩效评价</option>
                    <option value="attitude">态度评价</option>
                    <option value="ability">能力评价</option>
                </select>
            </div>
        </div>

        <!-- 评分项管理（内联表单集 + 拖拽排序） -->
        <div class="mb-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-md font-medium text-gray-900">评分项列表</h3>
                <button type="button" id="addItemBtn" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                    添加评分项
                </button>
            </div>
            
            <!-- 可拖拽的评分项列表 -->
            <div id="evaluationItems" class="space-y-4">
                <!-- 动态生成的评分项 -->
                <div class="evaluation-item bg-gray-50 p-4 rounded-lg border-l-4 border-blue-500" data-item-id="1">
                    <div class="flex items-start space-x-4">
                        <!-- 拖拽手柄 -->
                        <div class="drag-handle cursor-move text-gray-400 hover:text-gray-600 mt-2">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"></path>
                            </svg>
                        </div>
                        
                        <!-- 评分项内容 -->
                        <div class="flex-1 grid grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">评分项标题</label>
                                <input type="text" name="items[0][title]" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">评分模式</label>
                                <select name="items[0][scoring_mode]" class="scoring-mode-select w-full px-3 py-2 border border-gray-300 rounded-lg">
                                    <option value="open">开放式评分</option>
                                    <option value="structured">结构化评分</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">最高分</label>
                                <input type="number" name="items[0][max_score]" class="w-full px-3 py-2 border border-gray-300 rounded-lg" value="10">
                            </div>
                        </div>
                        
                        <!-- 删除按钮 -->
                        <button type="button" class="remove-item text-red-600 hover:text-red-800 mt-2">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                    </div>
                    
                    <!-- 结构化评分等级设置（条件显示） -->
                    <div class="scoring-tiers mt-4 hidden">
                        <h4 class="text-sm font-medium text-gray-700 mb-2">评分等级设置</h4>
                        <div class="grid grid-cols-4 gap-3">
                            <div>
                                <input type="text" placeholder="等级名称" class="w-full px-2 py-1 text-sm border border-gray-300 rounded">
                            </div>
                            <div>
                                <input type="number" placeholder="最低分" class="w-full px-2 py-1 text-sm border border-gray-300 rounded">
                            </div>
                            <div>
                                <input type="number" placeholder="最高分" class="w-full px-2 py-1 text-sm border border-gray-300 rounded">
                            </div>
                            <div>
                                <input type="text" placeholder="描述" class="w-full px-2 py-1 text-sm border border-gray-300 rounded">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 保存按钮 -->
        <div class="flex justify-end space-x-3">
            <button type="button" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                取消
            </button>
            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                保存模板
            </button>
        </div>
    </form>
</div>

<script>
// 拖拽排序功能
new Sortable(document.getElementById('evaluationItems'), {
    handle: '.drag-handle',
    animation: 150,
    onEnd: function (evt) {
        updateItemOrder();
    }
});

// 评分模式切换
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('scoring-mode-select')) {
        const tiersDiv = e.target.closest('.evaluation-item').querySelector('.scoring-tiers');
        if (e.target.value === 'structured') {
            tiersDiv.classList.remove('hidden');
        } else {
            tiersDiv.classList.add('hidden');
        }
    }
});
</script>
```

#### 5.2.4 权重规则管理界面（新增）
```html
<!-- 权重规则管理页面 -->
<div class="bg-white shadow-sm rounded-lg">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex justify-between items-center">
            <h2 class="text-lg font-medium text-gray-900">权重规则管理</h2>
            <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                添加权重规则
            </button>
        </div>
    </div>
    
    <div class="p-6">
        <!-- 规则列表 -->
        <div class="space-y-4">
            <div class="border border-gray-200 rounded-lg p-4">
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <h3 class="font-medium text-gray-900">下级评上级权重规则</h3>
                        <div class="mt-2 grid grid-cols-2 gap-4 text-sm text-gray-600">
                            <div>关系类型: 下级对上级</div>
                            <div>权重值: 0.8</div>
                            <div>适用部门: 全部</div>
                            <div>优先级: 10</div>
                        </div>
                        <div class="mt-2 flex items-center space-x-4">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                启用中
                            </span>
                            <span class="text-xs text-gray-500">生效期: 2025-01-01 至 2025-12-31</span>
                        </div>
                    </div>
                    <div class="flex space-x-2">
                        <button class="text-blue-600 hover:text-blue-800">编辑</button>
                        <button class="text-red-600 hover:text-red-800">删除</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
```

#### 5.2.5 数据可视化界面（ECharts集成）
```html
<!-- 考评数据统计仪表板 -->
<div class="bg-white shadow-sm rounded-lg">
    <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-medium text-gray-900">考评数据分析</h2>
    </div>
    
    <div class="p-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 考评进度饼图 -->
            <div class="bg-gray-50 p-4 rounded-lg">
                <h3 class="text-md font-medium text-gray-900 mb-4">考评完成进度</h3>
                <div id="progressPieChart" style="width: 100%; height: 300px;"></div>
            </div>
            
            <!-- 部门分数对比柱状图 -->
            <div class="bg-gray-50 p-4 rounded-lg">
                <h3 class="text-md font-medium text-gray-900 mb-4">部门评分对比</h3>
                <div id="departmentBarChart" style="width: 100%; height: 300px;"></div>
            </div>
            
            <!-- 人才分布散点图 -->
            <div class="bg-gray-50 p-4 rounded-lg lg:col-span-2">
                <h3 class="text-md font-medium text-gray-900 mb-4">人才九宫格分布</h3>
                <div id="talentMatrixChart" style="width: 100%; height: 400px;"></div>
            </div>
            
            <!-- 评分趋势折线图 -->
            <div class="bg-gray-50 p-4 rounded-lg lg:col-span-2">
                <h3 class="text-md font-medium text-gray-900 mb-4">历史评分趋势</h3>
                <div id="trendLineChart" style="width: 100%; height: 300px;"></div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
<script>
// 考评进度饼图
const progressChart = echarts.init(document.getElementById('progressPieChart'));
const progressOption = {
    title: {
        text: '考评完成状态',
        subtext: '实时统计',
        left: 'center'
    },
    tooltip: {
        trigger: 'item'
    },
    legend: {
        orient: 'vertical',
        left: 'left'
    },
    series: [
        {
            name: '考评状态',
            type: 'pie',
            radius: '50%',
            data: [
                { value: {{ completed_count }}, name: '已完成', itemStyle: { color: '#10B981' } },
                { value: {{ pending_count }}, name: '进行中', itemStyle: { color: '#F59E0B' } },
                { value: {{ not_started_count }}, name: '未开始', itemStyle: { color: '#6B7280' } }
            ],
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            }
        }
    ]
};
progressChart.setOption(progressOption);

// 部门分数对比柱状图
const departmentChart = echarts.init(document.getElementById('departmentBarChart'));
const departmentOption = {
    title: {
        text: '部门平均分对比'
    },
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'shadow'
        }
    },
    grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
    },
    xAxis: [
        {
            type: 'category',
            data: {{ department_names|safe }},
            axisTick: {
                alignWithLabel: true
            }
        }
    ],
    yAxis: [
        {
            type: 'value',
            name: '平均分',
            min: 0,
            max: 100
        }
    ],
    series: [
        {
            name: '平均分',
            type: 'bar',
            barWidth: '60%',
            data: {{ department_scores|safe }},
            itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: '#83bff6' },
                    { offset: 0.5, color: '#188df0' },
                    { offset: 1, color: '#188df0' }
                ])
            }
        }
    ]
};
departmentChart.setOption(departmentOption);

// 人才九宫格散点图
const talentChart = echarts.init(document.getElementById('talentMatrixChart'));
const talentOption = {
    title: {
        text: '人才分布矩阵',
        subtext: '绩效 vs 潜力'
    },
    tooltip: {
        trigger: 'item',
        formatter: function (params) {
            return `${params.data.name}<br/>绩效得分: ${params.data.value[0]}<br/>潜力得分: ${params.data.value[1]}`;
        }
    },
    grid: {
        left: '3%',
        right: '7%',
        bottom: '3%',
        containLabel: true
    },
    xAxis: {
        type: 'value',
        name: '绩效得分',
        min: 0,
        max: 100,
        splitLine: {
            lineStyle: {
                type: 'dashed'
            }
        }
    },
    yAxis: {
        type: 'value',
        name: '潜力得分',
        min: 0,
        max: 100,
        splitLine: {
            lineStyle: {
                type: 'dashed'
            }
        }
    },
    series: [
        {
            name: '人才分布',
            type: 'scatter',
            symbolSize: function (data) {
                return Math.sqrt(data[2]) * 2; // 第三个维度作为圆点大小
            },
            data: {{ talent_matrix_data|safe }},
            itemStyle: {
                shadowBlur: 10,
                shadowColor: 'rgba(120, 36, 50, 0.5)',
                shadowOffsetY: 5,
                color: function(params) {
                    // 根据象限设置颜色
                    const x = params.data.value[0];
                    const y = params.data.value[1];
                    if (x >= 70 && y >= 70) return '#FF6B6B'; // 明星人才
                    if (x >= 70 && y >= 40) return '#4ECDC4'; // 核心人才
                    if (x >= 40 && y >= 70) return '#45B7D1'; // 潜力人才
                    return '#96CEB4'; // 其他
                }
            }
        }
    ]
};
talentChart.setOption(talentOption);

// 历史趋势折线图
const trendChart = echarts.init(document.getElementById('trendLineChart'));
const trendOption = {
    title: {
        text: '历史评分趋势'
    },
    tooltip: {
        trigger: 'axis'
    },
    legend: {
        data: ['公司平均分', '部门平均分', '个人得分']
    },
    grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
    },
    xAxis: {
        type: 'category',
        boundaryGap: false,
        data: {{ trend_dates|safe }}
    },
    yAxis: {
        type: 'value',
        name: '得分',
        min: 0,
        max: 100
    },
    series: [
        {
            name: '公司平均分',
            type: 'line',
            data: {{ company_trend|safe }},
            itemStyle: { color: '#5470C6' }
        },
        {
            name: '部门平均分',
            type: 'line',
            data: {{ department_trend|safe }},
            itemStyle: { color: '#91CC75' }
        },
        {
            name: '个人得分',
            type: 'line',
            data: {{ personal_trend|safe }},
            itemStyle: { color: '#FAC858' }
        }
    ]
};
trendChart.setOption(trendOption);

// 响应式处理
window.addEventListener('resize', function() {
    progressChart.resize();
    departmentChart.resize();
    talentChart.resize();
    trendChart.resize();
});
</script>
```

### 5.3 匿名评分端界面设计

#### 5.3.1 简洁登录界面
```html
<!-- 匿名登录页面 -->
<div class="min-h-screen flex items-center justify-center bg-gray-50">
    <div class="max-w-md w-full space-y-8">
        <div class="text-center">
            <h2 class="mt-6 text-3xl font-extrabold text-gray-900">匿名考评系统</h2>
            <p class="mt-2 text-sm text-gray-600">请输入您的匿名编号进行登录</p>
        </div>
        <form class="mt-8 space-y-6" method="post">
            {% csrf_token %}
            <div>
                <label for="anonymous_code" class="sr-only">匿名编号</label>
                <input id="anonymous_code" name="anonymous_code" type="text" required 
                       class="relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-lg focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm" 
                       placeholder="请输入匿名编号">
            </div>
            <div>
                <button type="submit" 
                        class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    登录考评系统
                </button>
            </div>
        </form>
    </div>
</div>
```

#### 5.3.2 考评任务界面
```html
<!-- 考评任务列表 -->
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4">
        <div class="bg-white shadow-sm rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h1 class="text-xl font-semibold text-gray-900">我的考评任务</h1>
                <p class="mt-1 text-sm text-gray-600">请完成以下人员的考评，您的身份是匿名的</p>
            </div>
            
            <div class="p-6">
                <div class="grid gap-4">
                    {% for task in evaluation_tasks %}
                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div class="flex justify-between items-center">
                            <div>
                                <h3 class="font-medium text-gray-900">考评对象：{{ task.evaluated_anonymous_code }}</h3>
                                <p class="text-sm text-gray-600">部门：{{ task.department_name }}</p>
                                <p class="text-sm text-gray-600">职位：{{ task.position_name }}</p>
                            </div>
                            <div>
                                {% if task.is_completed %}
                                <span class="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full">
                                    已完成
                                </span>
                                {% else %}
                                <a href="{% url 'evaluation_form' task.id %}" 
                                   class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                                    开始评分
                                </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
```

## 6. 安全性设计

### 6.1 数据安全
- **加密存储**：敏感数据使用AES加密
- **SQL注入防护**：使用Django ORM和参数化查询
- **XSS防护**：模板自动转义和CSP策略
- **CSRF保护**：Django内置CSRF中间件

### 6.2 访问控制
- **多层权限验证**：视图层、服务层、数据层
- **会话管理**：安全的session配置
- **IP白名单**：管理员访问IP限制（可选）
- **操作审计**：所有关键操作记录日志

### 6.3 匿名保护
- **编号生成**：使用加密算法生成不可逆匿名编号
- **数据隔离**：考评过程中完全隔离真实身份
- **权限分离**：普通用户无法查看身份映射关系

## 7. 性能优化

### 7.1 数据库优化
- **索引策略**：为频繁查询字段创建合适索引
- **查询优化**：使用select_related和prefetch_related
- **分页查询**：大数据量列表使用分页
- **连接池**：数据库连接池配置

### 7.2 缓存策略
- **页面缓存**：静态内容页面缓存
- **查询缓存**：频繁查询结果缓存
- **会话缓存**：Redis会话存储
- **文件缓存**：静态文件CDN加速

### 7.3 前端优化
- **资源压缩**：CSS/JS文件压缩合并
- **图片优化**：WebP格式和懒加载
- **异步加载**：非关键资源异步加载
- **浏览器缓存**：合理的缓存策略

## 8. 部署架构

### 8.1 生产环境架构
```
                    ┌─────────────────┐
                    │   Nginx (反向代理)  │
                    │   SSL终端        │
                    └─────────────────┘
                              │
                              ▼
    ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
    │  Django App1    │  │  Django App2    │  │  Django App3    │
    │  (Gunicorn)     │  │  (Gunicorn)     │  │  (Gunicorn)     │
    └─────────────────┘  └─────────────────┘  └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │   MySQL 主从    │
                    │   Master-Slave  │
                    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │   Redis 缓存    │
                    │   会话存储      │
                    └─────────────────┘
```

### 8.2 容器化部署
```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["gunicorn", "--bind", "0.0.0.0:8000", "project.wsgi:application"]
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  web:
    build: .
    ports:
      - "8000:8000"
    depends_on:
      - db
      - redis
    environment:
      - DEBUG=False
      - DATABASE_URL=mysql://user:pass@db:3306/evaldb
      
  db:
    image: mysql:8.0
    environment:
      MYSQL_DATABASE: evaldb
      MYSQL_ROOT_PASSWORD: rootpass
    volumes:
      - mysql_data:/var/lib/mysql
      
  redis:
    image: redis:alpine
    
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      
volumes:
  mysql_data:
```

## 9. 开发规范

### 9.1 代码规范
- **命名规范**：使用有意义的变量和函数名
- **注释规范**：所有函数必须有docstring
- **错误处理**：完善的异常捕获和处理
- **日志记录**：关键操作记录详细日志

### 9.2 Git规范
- **分支策略**：Git Flow工作流
- **提交规范**：Conventional Commits格式
- **代码审查**：Pull Request代码审查
- **版本管理**：语义化版本控制

### 9.3 测试规范
- **单元测试**：覆盖率≥80%
- **集成测试**：关键业务流程测试
- **性能测试**：负载测试和压力测试
- **安全测试**：安全漏洞扫描

---

**文档版本**：v1.0  
**创建时间**：2025-07-25  
**设计负责人**：Claude Code Assistant