#!/usr/bin/env python3
"""
测试权限管理页面分页功能
"""

import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')

import django
django.setup()

from django.test import RequestFactory

from organizations.models import Staff
from organizations.views_permissions import PermissionManagementView

def test_permissions_pagination():
    """测试权限管理分页功能"""
    print("=== 权限管理页面分页功能测试 ===\n")
    
    # 1. 检查数据总量
    total_staff = Staff.objects.filter(deleted_at__isnull=True).count()
    print(f"[OK] 总员工数: {total_staff}")
    
    # 2. 创建请求工厂
    factory = RequestFactory()
    
    # 模拟登录用户
    class MockUser:
        def __init__(self):
            self.id = 1
            self.is_authenticated = True
    
    # 3. 测试第一页
    print("\n--- 测试第一页 ---")
    request = factory.get('/admin/permissions/')
    request.user = MockUser()
    
    view = PermissionManagementView()
    view.setup(request)
    
    queryset = view.get_queryset()
    print(f"[OK] 查询集总数: {queryset.count()}")
    print(f"[OK] 每页显示数量: {view.paginate_by}")
    
    expected_pages = (total_staff + view.paginate_by - 1) // view.paginate_by
    print(f"[OK] 预期总页数: {expected_pages}")
    
    # 4. 测试分页器
    from django.core.paginator import Paginator
    paginator = Paginator(queryset, view.paginate_by)
    print(f"[OK] 实际总页数: {paginator.num_pages}")
    
    # 测试第一页
    page1 = paginator.page(1)
    print(f"[OK] 第1页项目数: {len(page1.object_list)}")
    print(f"[OK] 第1页起始索引: {page1.start_index()}")
    print(f"[OK] 第1页结束索引: {page1.end_index()}")
    print(f"[OK] 第1页是否有下一页: {page1.has_next()}")
    print(f"[OK] 第1页是否有上一页: {page1.has_previous()}")
    
    # 如果有多页，测试第二页
    if paginator.num_pages > 1:
        print("\n--- 测试第二页 ---")
        page2 = paginator.page(2)
        print(f"[OK] 第2页项目数: {len(page2.object_list)}")
        print(f"[OK] 第2页起始索引: {page2.start_index()}")
        print(f"[OK] 第2页结束索引: {page2.end_index()}")
        print(f"[OK] 第2页是否有下一页: {page2.has_next()}")
        print(f"[OK] 第2页是否有上一页: {page2.has_previous()}")
    
    # 5. 测试搜索功能分页
    print("\n--- 测试搜索功能分页 ---")
    search_request = factory.get('/admin/permissions/?search=admin')
    search_request.user = MockUser()
    
    search_view = PermissionManagementView()
    search_view.setup(search_request)
    search_view.request = search_request  # 手动设置request属性
    
    search_queryset = search_view.get_queryset()
    print(f"[OK] 搜索'admin'的结果数: {search_queryset.count()}")
    
    # 6. 测试角色筛选分页
    print("\n--- 测试角色筛选分页 ---")
    filter_request = factory.get('/admin/permissions/?role=admin')
    filter_request.user = MockUser()
    
    filter_view = PermissionManagementView()
    filter_view.setup(filter_request)
    filter_view.request = filter_request
    
    filter_queryset = filter_view.get_queryset()
    print(f"[OK] 筛选'admin'角色的结果数: {filter_queryset.count()}")
    
    # 7. 测试部门筛选分页
    print("\n--- 测试部门筛选分页 ---")
    dept_filter_request = factory.get('/admin/permissions/?department=1')
    dept_filter_request.user = MockUser()
    
    dept_filter_view = PermissionManagementView()
    dept_filter_view.setup(dept_filter_request)
    dept_filter_view.request = dept_filter_request
    
    dept_filter_queryset = dept_filter_view.get_queryset()
    print(f"[OK] 筛选部门ID=1的结果数: {dept_filter_queryset.count()}")
    
    print("\n=== 测试完成 ===")
    
    # 8. 生成测试报告
    return {
        'total_staff': total_staff,
        'paginate_by': view.paginate_by,
        'expected_pages': expected_pages,
        'actual_pages': paginator.num_pages,
        'first_page_items': len(page1.object_list),
        'search_results': search_queryset.count(),
        'role_filter_results': filter_queryset.count(),
        'dept_filter_results': dept_filter_queryset.count(),
        'pagination_working': True
    }

if __name__ == '__main__':
    try:
        result = test_permissions_pagination()
        
        print("\n=== 测试结果摘要 ===")
        print(f"总员工数: {result['total_staff']}")
        print(f"每页显示: {result['paginate_by']} 项")
        print(f"总页数: {result['actual_pages']}")
        print(f"分页功能: {'[OK] 正常' if result['pagination_working'] else '[ERROR] 异常'}")
        
        if result['total_staff'] > result['paginate_by']:
            print("[OK] 数据量足够，分页功能将正常展示")
        else:
            print("[INFO] 数据量较少，所有数据将在一页内显示")
            
    except Exception as e:
        print(f"[ERROR] 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()