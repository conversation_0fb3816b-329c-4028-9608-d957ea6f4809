#!/usr/bin/env python
"""
JWT认证修复验证脚本
测试修复后的JWT认证流程是否正常工作
"""

import os
import sys
import django
import requests
import json
from datetime import datetime

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
django.setup()

def test_jwt_auth_flow():
    """测试完整的JWT认证流程"""
    base_url = 'http://127.0.0.1:8000'
    
    print("=" * 60)
    print("JWT认证修复验证测试")
    print("=" * 60)
    
    # 1. 测试API登录端点
    print("\n1. 测试API登录端点 (/api/login/)")
    try:
        login_data = {
            'username': 'admin',
            'password': '123456'
        }
        
        response = requests.post(
            f'{base_url}/api/login/',
            json=login_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("   ✅ API登录成功")
                print(f"   用户: {data['user']['name']} ({data['user']['username']})")
                print(f"   角色: {data['user']['role_display']}")
                
                # 保存token用于后续测试
                access_token = data['tokens']['access_token']
                refresh_token = data['tokens']['refresh_token']
                
                # 2. 测试token验证
                print("\n2. 测试token验证 (/api/token/status/)")
                headers = {'Authorization': f'Bearer {access_token}'}
                
                token_response = requests.get(
                    f'{base_url}/api/token/status/',
                    headers=headers
                )
                
                print(f"   状态码: {token_response.status_code}")
                if token_response.status_code == 200:
                    token_data = token_response.json()
                    print("   ✅ Token验证通过")
                    print(f"   Token状态: {token_data.get('data', {}).get('valid', 'unknown')}")
                else:
                    print("   ❌ Token验证失败")
                
                # 3. 测试token刷新
                print("\n3. 测试token刷新 (/api/token/refresh/)")
                refresh_data = {'refresh_token': refresh_token}
                
                refresh_response = requests.post(
                    f'{base_url}/api/token/refresh/',
                    json=refresh_data,
                    headers={'Content-Type': 'application/json'}
                )
                
                print(f"   状态码: {refresh_response.status_code}")
                if refresh_response.status_code == 200:
                    refresh_result = refresh_response.json()
                    if refresh_result.get('success'):
                        print("   ✅ Token刷新成功")
                        new_access_token = refresh_result['tokens']['access_token']
                        print(f"   新access_token已生成")
                    else:
                        print("   ❌ Token刷新失败")
                else:
                    print("   ❌ Token刷新请求失败")
                
                # 4. 测试管理后台访问
                print("\n4. 测试管理后台访问 (/admin/)")
                admin_response = requests.get(
                    f'{base_url}/admin/',
                    headers={'Authorization': f'Bearer {access_token}'},
                    allow_redirects=False
                )
                
                print(f"   状态码: {admin_response.status_code}")
                if admin_response.status_code == 200:
                    print("   ✅ 管理后台访问成功")
                elif admin_response.status_code == 302:
                    print(f"   ⚠️ 重定向到: {admin_response.headers.get('Location', 'unknown')}")
                else:
                    print("   ❌ 管理后台访问失败")
                
                # 5. 测试退出登录
                print("\n5. 测试退出登录 (/api/logout/)")
                logout_response = requests.post(
                    f'{base_url}/api/logout/',
                    headers={'Authorization': f'Bearer {access_token}'}
                )
                
                print(f"   状态码: {logout_response.status_code}")
                if logout_response.status_code == 200:
                    logout_data = logout_response.json()
                    if logout_data.get('success'):
                        print("   ✅ 退出登录成功")
                    else:
                        print("   ❌ 退出登录失败")
                else:
                    print("   ❌ 退出登录请求失败")
                
            else:
                print(f"   ❌ API登录失败: {data.get('message', '未知错误')}")
        else:
            print(f"   ❌ API登录请求失败: HTTP {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("   ❌ 无法连接到服务器，请确保Django服务器正在运行")
        return False
    except Exception as e:
        print(f"   ❌ 测试过程中发生错误: {e}")
        return False
    
    # 6. 测试前端登录页面
    print("\n6. 测试前端登录页面 (/admin/login/)")
    try:
        login_page_response = requests.get(f'{base_url}/admin/login/')
        print(f"   状态码: {login_page_response.status_code}")
        if login_page_response.status_code == 200:
            print("   ✅ 登录页面加载成功")
            
            # 检查页面是否包含AJAX登录代码
            page_content = login_page_response.text
            if 'fetch(\'/api/login/\'' in page_content:
                print("   ✅ 页面包含AJAX登录代码")
            else:
                print("   ❌ 页面缺少AJAX登录代码")
                
            if 'localStorage.setItem(\'access_token\'' in page_content:
                print("   ✅ 页面包含token存储代码")
            else:
                print("   ❌ 页面缺少token存储代码")
        else:
            print("   ❌ 登录页面加载失败")
    except Exception as e:
        print(f"   ❌ 测试登录页面时发生错误: {e}")
    
    print("\n" + "=" * 60)
    print("JWT认证修复验证完成")
    print("=" * 60)
    
    return True

def test_database_connection():
    """测试数据库连接和基础数据"""
    print("\n[检查] 数据库连接和基础数据...")
    
    try:
        from organizations.models import Staff
        
        # 检查管理员用户
        admin_user = Staff.objects.filter(username='admin').first()
        if admin_user:
            print(f"   ✅ 找到管理员用户: {admin_user.name}")
            print(f"   - 用户名: {admin_user.username}")
            print(f"   - 角色: {admin_user.get_role_display()}")
            print(f"   - 是否激活: {admin_user.is_active}")
            print(f"   - 是否管理员: {admin_user.is_manager}")
            
            # 检查JWT相关字段
            jwt_fields_status = []
            if hasattr(admin_user, 'last_token_refresh'):
                jwt_fields_status.append("✅ last_token_refresh字段存在")
            else:
                jwt_fields_status.append("❌ last_token_refresh字段缺失")
                
            if hasattr(admin_user, 'failed_login_attempts'):
                jwt_fields_status.append("✅ failed_login_attempts字段存在")
            else:
                jwt_fields_status.append("❌ failed_login_attempts字段缺失")
                
            if hasattr(admin_user, 'account_locked_until'):
                jwt_fields_status.append("✅ account_locked_until字段存在")
            else:
                jwt_fields_status.append("❌ account_locked_until字段缺失")
            
            print("   JWT安全字段检查:")
            for status in jwt_fields_status:
                print(f"     {status}")
                
        else:
            print("   ❌ 未找到管理员用户，请先创建管理员")
            return False
            
        return True
        
    except Exception as e:
        print(f"   ❌ 数据库检查失败: {e}")
        return False

if __name__ == '__main__':
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 首先检查数据库
    if test_database_connection():
        # 然后测试JWT认证流程
        test_jwt_auth_flow()
    else:
        print("数据库检查失败，跳过JWT认证测试")