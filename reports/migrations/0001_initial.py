# Generated by Django 5.2.4 on 2025-07-25 04:58

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('evaluations', '0001_initial'),
        ('organizations', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='EvaluationProgress',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='记录创建时间', verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='记录最后更新时间', verbose_name='更新时间')),
                ('created_by', models.CharField(blank=True, help_text='记录创建者', max_length=50, null=True, verbose_name='创建人')),
                ('updated_by', models.CharField(blank=True, help_text='记录最后更新者', max_length=50, null=True, verbose_name='更新人')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='软删除时间，为空表示未删除', null=True, verbose_name='删除时间')),
                ('deleted_by', models.CharField(blank=True, help_text='软删除操作者', max_length=50, null=True, verbose_name='删除人')),
                ('total_relations', models.PositiveIntegerField(default=0, help_text='该批次的总考评关系数量', verbose_name='总关系数')),
                ('completed_relations', models.PositiveIntegerField(default=0, help_text='已完成评价的关系数量', verbose_name='已完成关系数')),
                ('completion_rate', models.DecimalField(decimal_places=2, default=0.0, help_text='考评完成的百分比', max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='完成率(%)')),
                ('total_participants', models.PositiveIntegerField(default=0, help_text='参与该批次考评的总人数', verbose_name='总参与人数')),
                ('active_evaluators', models.PositiveIntegerField(default=0, help_text='已进行评价的人数', verbose_name='活跃评价者')),
                ('pending_evaluators', models.PositiveIntegerField(default=0, help_text='尚未完成评价的人数', verbose_name='待评价者')),
                ('average_score', models.DecimalField(blank=True, decimal_places=2, help_text='当前批次的平均得分', max_digits=5, null=True, verbose_name='平均分')),
                ('last_updated', models.DateTimeField(auto_now=True, help_text='进度数据最后更新时间', verbose_name='最后更新时间')),
                ('batch', models.OneToOneField(help_text='关联的考评批次', on_delete=django.db.models.deletion.CASCADE, to='evaluations.evaluationbatch', verbose_name='考评批次')),
            ],
            options={
                'verbose_name': '考评进度',
                'verbose_name_plural': '考评进度',
                'ordering': ['-last_updated'],
            },
        ),
        migrations.CreateModel(
            name='EvaluationReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='记录创建时间', verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='记录最后更新时间', verbose_name='更新时间')),
                ('created_by', models.CharField(blank=True, help_text='记录创建者', max_length=50, null=True, verbose_name='创建人')),
                ('updated_by', models.CharField(blank=True, help_text='记录最后更新者', max_length=50, null=True, verbose_name='更新人')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='软删除时间，为空表示未删除', null=True, verbose_name='删除时间')),
                ('deleted_by', models.CharField(blank=True, help_text='软删除操作者', max_length=50, null=True, verbose_name='删除人')),
                ('name', models.CharField(help_text='报告的标题名称', max_length=200, verbose_name='报告名称')),
                ('report_type', models.CharField(choices=[('individual', '个人报告'), ('department', '部门报告'), ('company', '公司报告'), ('comparison', '对比报告'), ('trend', '趋势报告')], help_text='报告的类型分类', max_length=20, verbose_name='报告类型')),
                ('status', models.CharField(choices=[('generating', '生成中'), ('completed', '已完成'), ('failed', '生成失败')], default='generating', help_text='报告的生成状态', max_length=20, verbose_name='生成状态')),
                ('content', models.JSONField(blank=True, help_text='报告的详细数据（JSON格式）', null=True, verbose_name='报告内容')),
                ('file_path', models.CharField(blank=True, help_text='生成的PDF/Excel文件路径', max_length=500, verbose_name='文件路径')),
                ('generation_time', models.DateTimeField(blank=True, help_text='报告生成完成的时间', null=True, verbose_name='生成时间')),
                ('download_count', models.PositiveIntegerField(default=0, help_text='报告被下载的次数', verbose_name='下载次数')),
                ('batch', models.ForeignKey(help_text='报告基于的考评批次', on_delete=django.db.models.deletion.CASCADE, to='evaluations.evaluationbatch', verbose_name='关联批次')),
                ('generated_by', models.ForeignKey(help_text='生成报告的用户', on_delete=django.db.models.deletion.CASCADE, related_name='generated_reports', to='organizations.staff', verbose_name='生成者')),
                ('target_department', models.ForeignKey(blank=True, help_text='部门报告的目标部门', null=True, on_delete=django.db.models.deletion.CASCADE, to='organizations.department', verbose_name='目标部门')),
                ('target_staff', models.ForeignKey(blank=True, help_text='个人报告的目标员工', null=True, on_delete=django.db.models.deletion.CASCADE, to='organizations.staff', verbose_name='目标员工')),
            ],
            options={
                'verbose_name': '考评报告',
                'verbose_name_plural': '考评报告',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='TalentMatrix',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='记录创建时间', verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='记录最后更新时间', verbose_name='更新时间')),
                ('created_by', models.CharField(blank=True, help_text='记录创建者', max_length=50, null=True, verbose_name='创建人')),
                ('updated_by', models.CharField(blank=True, help_text='记录最后更新者', max_length=50, null=True, verbose_name='更新人')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='软删除时间，为空表示未删除', null=True, verbose_name='删除时间')),
                ('deleted_by', models.CharField(blank=True, help_text='软删除操作者', max_length=50, null=True, verbose_name='删除人')),
                ('performance_high_threshold', models.DecimalField(decimal_places=2, default=80.0, help_text='划分高绩效的分数线', max_digits=5, verbose_name='高绩效阈值')),
                ('performance_low_threshold', models.DecimalField(decimal_places=2, default=60.0, help_text='划分低绩效的分数线', max_digits=5, verbose_name='低绩效阈值')),
                ('potential_high_threshold', models.DecimalField(decimal_places=2, default=80.0, help_text='划分高潜力的分数线', max_digits=5, verbose_name='高潜力阈值')),
                ('potential_low_threshold', models.DecimalField(decimal_places=2, default=60.0, help_text='划分低潜力的分数线', max_digits=5, verbose_name='低潜力阈值')),
                ('matrix_data', models.JSONField(blank=True, help_text='九宫格的统计数据（JSON格式）', null=True, verbose_name='矩阵数据')),
                ('total_assessed', models.PositiveIntegerField(default=0, help_text='参与人才盘点的总人数', verbose_name='总盘点人数')),
                ('last_updated', models.DateTimeField(auto_now=True, help_text='矩阵数据最后更新时间', verbose_name='最后更新时间')),
                ('batch', models.OneToOneField(help_text='九宫格对应的考评批次', on_delete=django.db.models.deletion.CASCADE, to='evaluations.evaluationbatch', verbose_name='关联批次')),
            ],
            options={
                'verbose_name': '人才矩阵',
                'verbose_name_plural': '人才矩阵',
                'ordering': ['-last_updated'],
            },
        ),
        migrations.CreateModel(
            name='TalentAssessment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='记录创建时间', verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='记录最后更新时间', verbose_name='更新时间')),
                ('created_by', models.CharField(blank=True, help_text='记录创建者', max_length=50, null=True, verbose_name='创建人')),
                ('updated_by', models.CharField(blank=True, help_text='记录最后更新者', max_length=50, null=True, verbose_name='更新人')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='软删除时间，为空表示未删除', null=True, verbose_name='删除时间')),
                ('deleted_by', models.CharField(blank=True, help_text='软删除操作者', max_length=50, null=True, verbose_name='删除人')),
                ('performance_score', models.DecimalField(decimal_places=2, help_text='员工的综合绩效得分', max_digits=5, verbose_name='绩效得分')),
                ('performance_level', models.CharField(choices=[('high', '高绩效'), ('medium', '中绩效'), ('low', '低绩效')], help_text='根据得分划分的绩效等级', max_length=20, verbose_name='绩效水平')),
                ('potential_score', models.DecimalField(blank=True, decimal_places=2, help_text='员工的潜力评估得分', max_digits=5, null=True, verbose_name='潜力得分')),
                ('potential_level', models.CharField(blank=True, choices=[('high', '高潜力'), ('medium', '中潜力'), ('low', '低潜力')], help_text='根据评估划分的潜力等级', max_length=20, null=True, verbose_name='潜力水平')),
                ('talent_category', models.CharField(choices=[('star', '明星员工'), ('core', '核心员工'), ('expert', '专业员工'), ('potential', '潜力员工'), ('backbone', '骨干员工'), ('stable', '稳定员工'), ('trainee', '培训员工'), ('question', '问题员工'), ('risk', '风险员工')], help_text='基于九宫格的人才分类', max_length=20, verbose_name='人才类别')),
                ('development_suggestions', models.TextField(blank=True, help_text='针对该员工的发展建议', verbose_name='发展建议')),
                ('career_path', models.CharField(blank=True, help_text='建议的职业发展路径', max_length=200, verbose_name='职业路径')),
                ('risk_factors', models.TextField(blank=True, help_text='需要关注的风险因素', verbose_name='风险因素')),
                ('strengths', models.TextField(blank=True, help_text='员工的优势和特长', verbose_name='优势特长')),
                ('improvement_areas', models.TextField(blank=True, help_text='需要改进的方面', verbose_name='改进领域')),
                ('assessment_date', models.DateTimeField(help_text='人才盘点的日期', verbose_name='盘点日期')),
                ('assessed_by', models.ForeignKey(help_text='执行人才盘点的人员', on_delete=django.db.models.deletion.CASCADE, related_name='talent_assessments', to='organizations.staff', verbose_name='盘点人')),
                ('batch', models.ForeignKey(help_text='人才盘点基于的考评批次', on_delete=django.db.models.deletion.CASCADE, to='evaluations.evaluationbatch', verbose_name='关联批次')),
                ('staff', models.ForeignKey(help_text='被盘点的员工', on_delete=django.db.models.deletion.CASCADE, to='organizations.staff', verbose_name='员工')),
            ],
            options={
                'verbose_name': '人才盘点',
                'verbose_name_plural': '人才盘点',
                'ordering': ['-assessment_date'],
                'unique_together': {('batch', 'staff')},
            },
        ),
    ]
