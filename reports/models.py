# -*- coding: utf-8 -*-
"""
报告和分析模型
包含统计报告、人才盘点、进度管理等功能
"""

from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from common.models import BaseModel
import json


class EvaluationProgress(BaseModel):
    """
    考评进度统计模型
    实时跟踪考评批次的进度信息
    """
    batch = models.OneToOneField(
        'evaluations.EvaluationBatch',
        on_delete=models.CASCADE,
        verbose_name='考评批次',
        help_text='关联的考评批次'
    )
    total_relations = models.PositiveIntegerField(
        default=0,
        verbose_name='总关系数',
        help_text='该批次的总考评关系数量'
    )
    completed_relations = models.PositiveIntegerField(
        default=0,
        verbose_name='已完成关系数',
        help_text='已完成评价的关系数量'
    )
    completion_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0.00,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name='完成率(%)',
        help_text='考评完成的百分比'
    )
    total_participants = models.PositiveIntegerField(
        default=0,
        verbose_name='总参与人数',
        help_text='参与该批次考评的总人数'
    )
    active_evaluators = models.PositiveIntegerField(
        default=0,
        verbose_name='活跃评价者',
        help_text='已进行评价的人数'
    )
    pending_evaluators = models.PositiveIntegerField(
        default=0,
        verbose_name='待评价者',
        help_text='尚未完成评价的人数'
    )
    average_score = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name='平均分',
        help_text='当前批次的平均得分'
    )
    last_updated = models.DateTimeField(
        auto_now=True,
        verbose_name='最后更新时间',
        help_text='进度数据最后更新时间'
    )
    
    class Meta:
        verbose_name = '考评进度'
        verbose_name_plural = '考评进度'
        ordering = ['-last_updated']
    
    def __str__(self):
        return f'{self.batch.name} - 进度: {self.completion_rate}%'
    
    def update_progress(self):
        """更新进度统计信息"""
        from evaluations.models import EvaluationRelation, EvaluationRecord
        
        # 统计关系数量
        relations = EvaluationRelation.objects.filter(batch=self.batch)
        self.total_relations = relations.count()
        
        # 统计完成情况
        completed = relations.filter(evaluationrecord__isnull=False)
        self.completed_relations = completed.count()
        
        # 计算完成率
        if self.total_relations > 0:
            self.completion_rate = round(
                (self.completed_relations / self.total_relations) * 100, 2
            )
        else:
            self.completion_rate = 0.00
        
        # 统计参与人数
        evaluators = relations.values_list('evaluator', flat=True).distinct()
        evaluatees = relations.values_list('evaluatee', flat=True).distinct()
        self.total_participants = len(set(evaluators) | set(evaluatees))
        
        # 统计活跃评价者
        active_evaluators = relations.filter(
            evaluationrecord__isnull=False
        ).values_list('evaluator', flat=True).distinct()
        self.active_evaluators = len(active_evaluators)
        self.pending_evaluators = len(evaluators) - self.active_evaluators
        
        # 计算平均分
        records = EvaluationRecord.objects.filter(relation__batch=self.batch)
        if records.exists():
            self.average_score = records.aggregate(
                avg_score=models.Avg('total_score')
            )['avg_score']
        
        self.save()


class EvaluationReport(BaseModel):
    """
    考评报告模型
    生成和管理各类考评报告
    """
    REPORT_TYPES = [
        ('individual', '个人报告'),
        ('department', '部门报告'),
        ('company', '公司报告'),
        ('comparison', '对比报告'),
        ('trend', '趋势报告'),
    ]
    
    REPORT_STATUS = [
        ('generating', '生成中'),
        ('completed', '已完成'),
        ('failed', '生成失败'),
    ]
    
    name = models.CharField(
        max_length=200,
        verbose_name='报告名称',
        help_text='报告的标题名称'
    )
    report_type = models.CharField(
        max_length=20,
        choices=REPORT_TYPES,
        verbose_name='报告类型',
        help_text='报告的类型分类'
    )
    batch = models.ForeignKey(
        'evaluations.EvaluationBatch',
        on_delete=models.CASCADE,
        verbose_name='关联批次',
        help_text='报告基于的考评批次'
    )
    target_staff = models.ForeignKey(
        'organizations.Staff',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name='目标员工',
        help_text='个人报告的目标员工'
    )
    target_department = models.ForeignKey(
        'organizations.Department',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name='目标部门',
        help_text='部门报告的目标部门'
    )
    generated_by = models.ForeignKey(
        'organizations.Staff',
        on_delete=models.CASCADE,
        related_name='generated_reports',
        verbose_name='生成者',
        help_text='生成报告的用户'
    )
    status = models.CharField(
        max_length=20,
        choices=REPORT_STATUS,
        default='generating',
        verbose_name='生成状态',
        help_text='报告的生成状态'
    )
    content = models.JSONField(
        null=True,
        blank=True,
        verbose_name='报告内容',
        help_text='报告的详细数据（JSON格式）'
    )
    file_path = models.CharField(
        max_length=500,
        blank=True,
        verbose_name='文件路径',
        help_text='生成的PDF/Excel文件路径'
    )
    generation_time = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='生成时间',
        help_text='报告生成完成的时间'
    )
    download_count = models.PositiveIntegerField(
        default=0,
        verbose_name='下载次数',
        help_text='报告被下载的次数'
    )
    
    class Meta:
        verbose_name = '考评报告'
        verbose_name_plural = '考评报告'
        ordering = ['-created_at']
    
    def __str__(self):
        return f'{self.name} ({self.get_report_type_display()})'
    
    def generate_content(self):
        """生成报告内容"""
        from evaluations.models import EvaluationRecord
        
        try:
            self.status = 'generating'
            self.save(update_fields=['status'])
            
            if self.report_type == 'individual':
                content = self._generate_individual_report()
            elif self.report_type == 'department':
                content = self._generate_department_report()
            elif self.report_type == 'company':
                content = self._generate_company_report()
            else:
                content = {}
            
            self.content = content
            self.status = 'completed'
            self.generation_time = timezone.now()
            self.save(update_fields=['content', 'status', 'generation_time'])
            
        except Exception as e:
            self.status = 'failed'
            self.save(update_fields=['status'])
            raise e
    
    def _generate_individual_report(self):
        """生成个人报告内容"""
        from evaluations.models import EvaluationRecord
        
        records = EvaluationRecord.objects.filter(
            relation__batch=self.batch,
            relation__evaluatee=self.target_staff
        )
        
        if not records.exists():
            return {'error': '暂无评价数据'}
        
        # 基础统计
        total_evaluators = records.count()
        avg_score = records.aggregate(avg=models.Avg('total_score'))['avg']
        max_score = records.aggregate(max=models.Max('total_score'))['max']
        min_score = records.aggregate(min=models.Min('total_score'))['min']
        
        # 按关系类型分组统计
        relation_stats = {}
        for record in records:
            rel_type = record.relation.get_relation_type()
            if rel_type not in relation_stats:
                relation_stats[rel_type] = {'count': 0, 'scores': []}
            relation_stats[rel_type]['count'] += 1
            relation_stats[rel_type]['scores'].append(float(record.total_score))
        
        # 计算各关系类型的平均分
        for rel_type, stats in relation_stats.items():
            stats['avg_score'] = sum(stats['scores']) / len(stats['scores'])
        
        return {
            'staff_info': {
                'name': self.target_staff.name,
                'employee_no': self.target_staff.employee_no,
                'department': self.target_staff.department.name,
                'position': self.target_staff.position.name if self.target_staff.position else '未设置',
            },
            'summary': {
                'total_evaluators': total_evaluators,
                'average_score': round(float(avg_score), 2) if avg_score else 0,
                'max_score': float(max_score) if max_score else 0,
                'min_score': float(min_score) if min_score else 0,
            },
            'relation_analysis': relation_stats,
            'generated_at': timezone.now().isoformat(),
        }
    
    def _generate_department_report(self):
        """生成部门报告内容"""
        from evaluations.models import EvaluationRecord
        from organizations.models import Staff
        
        dept_staff = Staff.objects.filter(department=self.target_department, is_active=True)
        records = EvaluationRecord.objects.filter(
            relation__batch=self.batch,
            relation__evaluatee__in=dept_staff
        )
        
        if not records.exists():
            return {'error': '暂无评价数据'}
        
        # 部门整体统计
        dept_avg = records.aggregate(avg=models.Avg('total_score'))['avg']
        dept_count = records.count()
        
        # 个人排名
        staff_scores = {}
        for record in records:
            staff = record.relation.evaluatee
            if staff.id not in staff_scores:
                staff_scores[staff.id] = {
                    'staff': staff,
                    'scores': [],
                    'total_score': 0,
                    'count': 0
                }
            staff_scores[staff.id]['scores'].append(float(record.total_score))
            staff_scores[staff.id]['count'] += 1
        
        # 计算每个人的平均分
        for staff_id, data in staff_scores.items():
            data['avg_score'] = sum(data['scores']) / len(data['scores'])
        
        # 排序
        ranking = sorted(staff_scores.values(), key=lambda x: x['avg_score'], reverse=True)
        
        return {
            'department_info': {
                'name': self.target_department.name,
                'code': self.target_department.dept_code,
                'staff_count': dept_staff.count(),
            },
            'summary': {
                'department_average': round(float(dept_avg), 2) if dept_avg else 0,
                'total_evaluations': dept_count,
                'evaluated_staff': len(staff_scores),
            },
            'ranking': [
                {
                    'rank': idx + 1,
                    'name': item['staff'].name,
                    'employee_no': item['staff'].employee_no,
                    'avg_score': round(item['avg_score'], 2),
                    'evaluation_count': item['count'],
                }
                for idx, item in enumerate(ranking)
            ],
            'generated_at': timezone.now().isoformat(),
        }
    
    def _generate_company_report(self):
        """生成公司整体报告内容"""
        from evaluations.models import EvaluationRecord
        from organizations.models import Department
        
        all_records = EvaluationRecord.objects.filter(relation__batch=self.batch)
        
        if not all_records.exists():
            return {'error': '暂无评价数据'}
        
        # 公司整体统计
        company_avg = all_records.aggregate(avg=models.Avg('total_score'))['avg']
        total_evaluations = all_records.count()
        
        # 按部门统计
        dept_stats = {}
        for record in all_records:
            dept = record.relation.evaluatee.department
            if dept.id not in dept_stats:
                dept_stats[dept.id] = {
                    'department': dept,
                    'scores': [],
                    'count': 0
                }
            dept_stats[dept.id]['scores'].append(float(record.total_score))
            dept_stats[dept.id]['count'] += 1
        
        # 计算各部门平均分
        for dept_id, data in dept_stats.items():
            data['avg_score'] = sum(data['scores']) / len(data['scores'])
        
        # 部门排名
        dept_ranking = sorted(dept_stats.values(), key=lambda x: x['avg_score'], reverse=True)
        
        return {
            'batch_info': {
                'name': self.batch.name,
                'start_date': self.batch.start_date.isoformat(),
                'end_date': self.batch.end_date.isoformat(),
            },
            'summary': {
                'company_average': round(float(company_avg), 2) if company_avg else 0,
                'total_evaluations': total_evaluations,
                'departments_count': len(dept_stats),
            },
            'department_ranking': [
                {
                    'rank': idx + 1,
                    'name': item['department'].name,
                    'code': item['department'].dept_code,
                    'avg_score': round(item['avg_score'], 2),
                    'evaluation_count': item['count'],
                }
                for idx, item in enumerate(dept_ranking)
            ],
            'generated_at': timezone.now().isoformat(),
        }


class TalentAssessment(BaseModel):
    """
    人才盘点模型
    基于考评结果进行人才分析和九宫格评估
    """
    PERFORMANCE_LEVELS = [
        ('high', '高绩效'),
        ('medium', '中绩效'),
        ('low', '低绩效'),
    ]
    
    POTENTIAL_LEVELS = [
        ('high', '高潜力'),
        ('medium', '中潜力'),
        ('low', '低潜力'),
    ]
    
    TALENT_CATEGORIES = [
        ('star', '明星员工'),      # 高绩效 + 高潜力
        ('core', '核心员工'),      # 高绩效 + 中潜力
        ('expert', '专业员工'),    # 高绩效 + 低潜力
        ('potential', '潜力员工'), # 中绩效 + 高潜力
        ('backbone', '骨干员工'),  # 中绩效 + 中潜力
        ('stable', '稳定员工'),    # 中绩效 + 低潜力
        ('trainee', '培训员工'),   # 低绩效 + 高潜力
        ('question', '问题员工'),  # 低绩效 + 中潜力
        ('risk', '风险员工'),      # 低绩效 + 低潜力
    ]
    
    batch = models.ForeignKey(
        'evaluations.EvaluationBatch',
        on_delete=models.CASCADE,
        verbose_name='关联批次',
        help_text='人才盘点基于的考评批次'
    )
    staff = models.ForeignKey(
        'organizations.Staff',
        on_delete=models.CASCADE,
        verbose_name='员工',
        help_text='被盘点的员工'
    )
    performance_score = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        verbose_name='绩效得分',
        help_text='员工的综合绩效得分'
    )
    performance_level = models.CharField(
        max_length=20,
        choices=PERFORMANCE_LEVELS,
        verbose_name='绩效水平',
        help_text='根据得分划分的绩效等级'
    )
    potential_score = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name='潜力得分',
        help_text='员工的潜力评估得分'
    )
    potential_level = models.CharField(
        max_length=20,
        choices=POTENTIAL_LEVELS,
        null=True,
        blank=True,
        verbose_name='潜力水平',
        help_text='根据评估划分的潜力等级'
    )
    talent_category = models.CharField(
        max_length=20,
        choices=TALENT_CATEGORIES,
        verbose_name='人才类别',
        help_text='基于九宫格的人才分类'
    )
    development_suggestions = models.TextField(
        blank=True,
        verbose_name='发展建议',
        help_text='针对该员工的发展建议'
    )
    career_path = models.CharField(
        max_length=200,
        blank=True,
        verbose_name='职业路径',
        help_text='建议的职业发展路径'
    )
    risk_factors = models.TextField(
        blank=True,
        verbose_name='风险因素',
        help_text='需要关注的风险因素'
    )
    strengths = models.TextField(
        blank=True,
        verbose_name='优势特长',
        help_text='员工的优势和特长'
    )
    improvement_areas = models.TextField(
        blank=True,
        verbose_name='改进领域',
        help_text='需要改进的方面'
    )
    assessment_date = models.DateTimeField(
        verbose_name='盘点日期',
        help_text='人才盘点的日期'
    )
    assessed_by = models.ForeignKey(
        'organizations.Staff',
        on_delete=models.CASCADE,
        related_name='talent_assessments',
        verbose_name='盘点人',
        help_text='执行人才盘点的人员'
    )
    
    class Meta:
        verbose_name = '人才盘点'
        verbose_name_plural = '人才盘点'
        unique_together = [['batch', 'staff']]
        ordering = ['-assessment_date']
    
    def __str__(self):
        return f'{self.staff.name} - {self.get_talent_category_display()}'
    
    def calculate_talent_category(self):
        """根据绩效和潜力水平计算人才类别"""
        if self.performance_level == 'high':
            if self.potential_level == 'high':
                return 'star'
            elif self.potential_level == 'medium':
                return 'core'
            else:  # low potential
                return 'expert'
        elif self.performance_level == 'medium':
            if self.potential_level == 'high':
                return 'potential'
            elif self.potential_level == 'medium':
                return 'backbone'
            else:  # low potential
                return 'stable'
        else:  # low performance
            if self.potential_level == 'high':
                return 'trainee'
            elif self.potential_level == 'medium':
                return 'question'
            else:  # low potential
                return 'risk'
    
    def generate_development_suggestions(self):
        """根据人才类别生成发展建议"""
        suggestions = {
            'star': '重点培养，考虑晋升或关键岗位轮岗，提供更大的发展平台。',
            'core': '稳定发展，适当增加挑战性工作，培养领导能力。',
            'expert': '专业深化，在专业领域继续提升，发挥技术专家作用。',
            'potential': '重点关注，提供更多学习机会和实践平台，加速成长。',
            'backbone': '稳步发展，保持现有工作水平，适当拓展技能。',
            'stable': '基础培训，提升基本技能，保持工作积极性。',
            'trainee': '加强指导，制定详细的培养计划，发掘潜力。',
            'question': '重点帮扶，分析问题原因，制定改进措施。',
            'risk': '警示管理，严格考核，必要时考虑岗位调整。',
        }
        return suggestions.get(self.talent_category, '请根据具体情况制定发展计划。')
    
    def save(self, *args, **kwargs):
        """重写保存方法，自动计算人才类别和生成建议"""
        if self.performance_level and self.potential_level:
            self.talent_category = self.calculate_talent_category()
        
        if not self.development_suggestions:
            self.development_suggestions = self.generate_development_suggestions()
        
        super().save(*args, **kwargs)


class TalentMatrix(BaseModel):
    """
    人才九宫格矩阵模型
    管理九宫格的配置和统计信息
    """
    batch = models.OneToOneField(
        'evaluations.EvaluationBatch',
        on_delete=models.CASCADE,
        verbose_name='关联批次',
        help_text='九宫格对应的考评批次'
    )
    performance_high_threshold = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=80.00,
        verbose_name='高绩效阈值',
        help_text='划分高绩效的分数线'
    )
    performance_low_threshold = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=60.00,
        verbose_name='低绩效阈值',
        help_text='划分低绩效的分数线'
    )
    potential_high_threshold = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=80.00,
        verbose_name='高潜力阈值',
        help_text='划分高潜力的分数线'
    )
    potential_low_threshold = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=60.00,
        verbose_name='低潜力阈值',
        help_text='划分低潜力的分数线'
    )
    matrix_data = models.JSONField(
        null=True,
        blank=True,
        verbose_name='矩阵数据',
        help_text='九宫格的统计数据（JSON格式）'
    )
    total_assessed = models.PositiveIntegerField(
        default=0,
        verbose_name='总盘点人数',
        help_text='参与人才盘点的总人数'
    )
    last_updated = models.DateTimeField(
        auto_now=True,
        verbose_name='最后更新时间',
        help_text='矩阵数据最后更新时间'
    )
    
    class Meta:
        verbose_name = '人才矩阵'
        verbose_name_plural = '人才矩阵'
        ordering = ['-last_updated']
    
    def __str__(self):
        return f'{self.batch.name} - 人才矩阵'
    
    def update_matrix_data(self):
        """更新九宫格矩阵数据"""
        assessments = TalentAssessment.objects.filter(batch=self.batch)
        
        # 统计各类别人数
        category_counts = {}
        for category, _ in TalentAssessment.TALENT_CATEGORIES:
            category_counts[category] = assessments.filter(talent_category=category).count()
        
        # 计算百分比
        total_count = assessments.count()
        category_percentages = {}
        if total_count > 0:
            for category, count in category_counts.items():
                category_percentages[category] = round((count / total_count) * 100, 2)
        
        # 生成矩阵数据
        matrix_grid = [
            ['star', 'potential', 'trainee'],      # 高潜力行
            ['core', 'backbone', 'question'],     # 中潜力行
            ['expert', 'stable', 'risk']          # 低潜力行
        ]
        
        self.matrix_data = {
            'category_counts': category_counts,
            'category_percentages': category_percentages,
            'matrix_grid': matrix_grid,
            'total_count': total_count,
            'thresholds': {
                'performance_high': float(self.performance_high_threshold),
                'performance_low': float(self.performance_low_threshold),
                'potential_high': float(self.potential_high_threshold),
                'potential_low': float(self.potential_low_threshold),
            }
        }
        
        self.total_assessed = total_count
        self.save(update_fields=['matrix_data', 'total_assessed'])
