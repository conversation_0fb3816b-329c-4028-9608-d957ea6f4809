# -*- coding: utf-8 -*-
"""
报告应用视图
包含报告生成、人才盘点和数据分析功能
"""

from django.shortcuts import render, redirect, get_object_or_404
from django.http import JsonResponse, HttpResponse
from django.contrib import messages
from django.views.generic import View, ListView, DetailView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy, reverse
from django.db import transaction
from django.core.paginator import Paginator
from django.db.models import Q, Count, Avg, Max, Min
from django.utils import timezone
from django.http import FileResponse
import json
import logging
import pandas as pd

from .models import EvaluationProgress, EvaluationReport, TalentAssessment, TalentMatrix
from evaluations.models import EvaluationBatch, EvaluationRelation, EvaluationRecord
from organizations.models import Staff, Department, Position
from organizations.middleware import require_admin_permission
from common.models import AuditLog
from common.excel_utils import ExcelProcessor

logger = logging.getLogger(__name__)


# 报告管理视图类
class ReportListView(ListView):
    """报告列表视图"""
    model = EvaluationReport
    template_name = 'admin/report/list.html'
    context_object_name = 'reports'
    paginate_by = 20
    
    def get_queryset(self):
        """获取报告列表数据"""
        queryset = EvaluationReport.objects.filter(deleted_at__isnull=True).order_by('-created_at')
        
        # 报告类型筛选
        report_type = self.request.GET.get('report_type')
        if report_type:
            queryset = queryset.filter(report_type=report_type)
            
        return queryset


class ReportGenerateView(View):
    """报告生成视图"""
    template_name = 'admin/report/generate.html'
    
    def get(self, request):
        """显示报告生成页面"""
        # 获取可用的考评批次
        available_batches = EvaluationBatch.objects.filter(
            status__in=['active', 'completed'], deleted_at__isnull=True
        )
        
        # 获取部门列表
        departments = Department.objects.filter(deleted_at__isnull=True)
        
        context = {
            'available_batches': available_batches,
            'departments': departments,
        }
        
        return render(request, self.template_name, context)
        
    def post(self, request):
        """处理报告生成请求"""
        try:
            report_type = request.POST.get('report_type')
            batch_id = request.POST.get('batch_id')
            department_id = request.POST.get('department_id')
            
            # 开始生成报告
            report = self._generate_report(request, report_type, batch_id, department_id)
            
            messages.success(request, '报告生成成功！')
            return redirect('reports:admin:report_detail', pk=report.id)
            
        except Exception as e:
            logger.error(f"报告生成失败: {e}")
            messages.error(request, '报告生成失败，请重试')
            return redirect('reports:admin:report_generate')
            
    def _generate_report(self, request, report_type, batch_id, department_id):
        """生成报告"""
        batch = get_object_or_404(EvaluationBatch, id=batch_id)
        department = get_object_or_404(Department, id=department_id) if department_id else None
        
        # 创建报告记录
        report = EvaluationReport.objects.create(
            batch=batch,
            department=department,
            report_type=report_type,
            title=self._generate_report_title(report_type, batch, department),
            status='generating',
            created_by=request.current_staff
        )
        
        # 生成报告数据（异步处理）
        self._process_report_data(report)
        
        return report
        
    def _generate_report_title(self, report_type, batch, department):
        """生成报告标题"""
        type_map = {
            'individual': '个人考评报告',
            'department': '部门考评报告', 
            'company': '公司整体考评报告'
        }
        
        title = type_map.get(report_type, '考评报告')
        if department:
            title += f' - {department.name}'
        title += f' - {batch.name}'
        
        return title
        
    def _process_report_data(self, report):
        """处理报告数据"""
        try:
            # 获取考评数据
            relations = EvaluationRelation.objects.filter(
                batch=report.batch,
                status='completed',
                deleted_at__isnull=True
            )
            
            if report.department:
                relations = relations.filter(evaluatee__department=report.department)
                
            # 生成统计数据
            total_evaluations = relations.count()
            avg_score = relations.aggregate(avg_score=Avg('evaluationrecord__total_score'))['avg_score'] or 0
            
            # 更新报告状态和数据
            report.data = {
                'total_evaluations': total_evaluations,
                'average_score': round(avg_score, 2),
                'generated_at': timezone.now().isoformat(),
            }
            report.status = 'completed'
            report.save(update_fields=['data', 'status', 'updated_at'])
            
        except Exception as e:
            logger.error(f"处理报告数据失败: {e}")
            report.status = 'failed'
            report.error_message = str(e)
            report.save(update_fields=['status', 'error_message', 'updated_at'])


class ReportDetailView(DetailView):
    """报告详情视图"""
    model = EvaluationReport
    template_name = 'admin/report/detail.html'
    context_object_name = 'report'
    
    def get_context_data(self, **kwargs):
        """添加额外的上下文数据"""
        context = super().get_context_data(**kwargs)
        report = self.get_object()
        
        # 获取详细的考评数据
        if report.status == 'completed':
            context['evaluation_data'] = self._get_evaluation_data(report)
            
        return context
        
    def _get_evaluation_data(self, report):
        """获取详细的考评数据"""
        try:
            relations = EvaluationRelation.objects.filter(
                batch=report.batch,
                status='completed',
                deleted_at__isnull=True
            ).select_related('evaluatee', 'evaluator')
            
            if report.department:
                relations = relations.filter(evaluatee__department=report.department)
                
            return relations[:10]  # 限制显示数量
        except Exception as e:
            logger.error(f"获取考评数据失败: {e}")
            return []


# 人才盘点视图类
class TalentAssessmentListView(ListView):
    """人才盘点列表视图"""
    model = TalentAssessment
    template_name = 'admin/talent/list.html'
    context_object_name = 'assessments'
    paginate_by = 20
    
    def get_queryset(self):
        """获取人才盘点数据"""
        return TalentAssessment.objects.filter(
            deleted_at__isnull=True
        ).select_related('staff', 'batch').order_by('-created_at')


class TalentAssessmentView(View):
    """人才盘点评估视图"""
    template_name = 'admin/talent/assess.html'
    
    def get(self, request, batch_id):
        """显示人才盘点页面"""
        batch = get_object_or_404(EvaluationBatch, id=batch_id)
        
        # 获取批次相关的员工评分数据
        evaluation_data = self._get_batch_evaluation_data(batch)
        
        context = {
            'batch': batch,
            'evaluation_data': evaluation_data,
        }
        
        return render(request, self.template_name, context)
        
    def _get_batch_evaluation_data(self, batch):
        """获取批次的评分数据"""
        try:
            relations = EvaluationRelation.objects.filter(
                batch=batch,
                status='completed',
                deleted_at__isnull=True
            ).select_related('evaluatee').prefetch_related('evaluationrecord_set')
            
            data = []
            for relation in relations:
                record = relation.evaluationrecord_set.first()
                if record:
                    data.append({
                        'staff': relation.evaluatee,
                        'score': record.total_score,
                        'relation': relation,
                    })
                    
            return data
        except Exception as e:
            logger.error(f"获取批次评分数据失败: {e}")
            return []


class TalentMatrixView(View):
    """人才九宫格矩阵视图"""
    template_name = 'admin/talent/matrix.html'
    
    def get(self, request, batch_id):
        """显示人才矩阵页面"""
        batch = get_object_or_404(EvaluationBatch, id=batch_id)
        
        # 获取人才矩阵数据
        matrix_data = self._get_talent_matrix_data(batch)
        
        context = {
            'batch': batch,
            'matrix_data': matrix_data,
        }
        
        return render(request, self.template_name, context)
        
    def _get_talent_matrix_data(self, batch):
        """获取人才矩阵数据"""
        try:
            # 获取九宫格配置
            matrix = TalentMatrix.objects.filter(
                batch=batch, deleted_at__isnull=True
            ).first()
            
            if not matrix:
                # 创建默认矩阵
                matrix = TalentMatrix.objects.create(
                    batch=batch,
                    matrix_data=self._generate_default_matrix(),
                    created_by=self.request.current_staff
                )
                
            return matrix.matrix_data
        except Exception as e:
            logger.error(f"获取人才矩阵数据失败: {e}")
            return {}
            
    def _generate_default_matrix(self):
        """生成默认九宫格矩阵"""
        return {
            'high_performance_high_potential': [],
            'high_performance_medium_potential': [],
            'high_performance_low_potential': [],
            'medium_performance_high_potential': [],
            'medium_performance_medium_potential': [],
            'medium_performance_low_potential': [],
            'low_performance_high_potential': [],
            'low_performance_medium_potential': [],
            'low_performance_low_potential': [],
        }


# 数据分析视图类
class AnalyticsView(View):
    """统计分析视图"""
    template_name = 'admin/analytics/overview.html'
    
    def get(self, request):
        """显示统计分析页面"""
        # 获取统计数据
        analytics_data = self._get_analytics_data()
        
        context = {
            'analytics_data': analytics_data,
        }
        
        return render(request, self.template_name, context)
        
    def _get_analytics_data(self):
        """获取统计分析数据"""
        try:
            # 基础统计
            total_batches = EvaluationBatch.objects.filter(deleted_at__isnull=True).count()
            active_batches = EvaluationBatch.objects.filter(status='active', deleted_at__isnull=True).count()
            completed_evaluations = EvaluationRelation.objects.filter(is_assigned=True, deleted_at__isnull=True).count()
            
            return {
                'total_batches': total_batches,
                'active_batches': active_batches,
                'completed_evaluations': completed_evaluations,
            }
        except Exception as e:
            logger.error(f"获取统计数据失败: {e}")
            return {}


# API视图类（用于ECharts数据可视化）
class AnalyticsAPIView(View):
    """统计分析数据API"""
    
    def get(self, request):
        """获取统计分析数据"""
        try:
            days = int(request.GET.get('days', 30))
            
            # 计算时间范围
            from datetime import datetime, timedelta
            end_date = timezone.now()
            start_date = end_date - timedelta(days=days)
            
            # 获取数据
            data = {
                'trend': self._get_completion_trend(start_date, end_date),
                'distribution': self._get_score_distribution(start_date, end_date),
                'departments': self._get_department_comparison(start_date, end_date),
                'positions': self._get_position_analysis(start_date, end_date),
                'activity': self._get_activity_heatmap(start_date, end_date),
                'metrics': self._get_key_metrics(start_date, end_date)
            }
            
            return JsonResponse({
                'success': True,
                'data': data,
                'message': '数据获取成功',
                'timestamp': timezone.now().isoformat()
            })
        except Exception as e:
            logger.error(f"获取统计分析数据失败: {e}")
            return JsonResponse({'error': '获取数据失败'}, status=500)
    
    def _get_completion_trend(self, start_date, end_date):
        """获取完成趋势数据"""
        from datetime import datetime, timedelta
        
        # 生成日期范围
        current_date = start_date.date()
        end_date_only = end_date.date()
        dates = []
        completion_rates = []
        
        while current_date <= end_date_only:
            dates.append(current_date.strftime('%m-%d'))
            
            # 计算该日期的累计完成率
            total_relations = EvaluationRelation.objects.filter(
                created_at__date__lte=current_date,
                deleted_at__isnull=True
            ).count()
            
            completed_relations = EvaluationRecord.objects.filter(
                created_at__date__lte=current_date,
                status='completed',
                deleted_at__isnull=True
            ).count()
            
            completion_rate = (completed_relations / total_relations * 100) if total_relations > 0 else 0
            completion_rates.append(round(completion_rate, 1))
            
            current_date += timedelta(days=1)
        
        return {
            'dates': dates,
            'completion': completion_rates
        }
    
    def _get_score_distribution(self, start_date, end_date):
        """获取评分分布数据"""
        records = EvaluationRecord.objects.filter(
            created_at__range=[start_date, end_date],
            status='completed',
            deleted_at__isnull=True
        )
        
        # 按分数段统计
        excellent = records.filter(total_score__gte=90).count()
        good = records.filter(total_score__gte=80, total_score__lt=90).count()
        average = records.filter(total_score__gte=70, total_score__lt=80).count()
        below_average = records.filter(total_score__gte=60, total_score__lt=70).count()
        poor = records.filter(total_score__lt=60).count()
        
        return [
            {'value': excellent, 'name': '优秀(90+)'},
            {'value': good, 'name': '良好(80-89)'},
            {'value': average, 'name': '中等(70-79)'},
            {'value': below_average, 'name': '待改进(60-69)'},
            {'value': poor, 'name': '不合格(<60)'}
        ]
    
    def _get_department_comparison(self, start_date, end_date):
        """获取部门对比数据"""
        departments = Department.objects.filter(deleted_at__isnull=True)
        names = []
        scores = []
        completion_rates = []
        
        for dept in departments:
            names.append(dept.name)
            
            # 计算平均分
            avg_score = EvaluationRecord.objects.filter(
                relation__evaluatee__department=dept,
                created_at__range=[start_date, end_date],
                status='completed',
                deleted_at__isnull=True
            ).aggregate(avg_score=Avg('total_score'))['avg_score'] or 0
            scores.append(round(avg_score, 1))
            
            # 计算完成率
            total_relations = EvaluationRelation.objects.filter(
                evaluator__department=dept,
                created_at__range=[start_date, end_date],
                deleted_at__isnull=True
            ).count()
            
            completed_relations = EvaluationRecord.objects.filter(
                relation__evaluator__department=dept,
                created_at__range=[start_date, end_date],
                status='completed',
                deleted_at__isnull=True
            ).count()
            
            completion_rate = (completed_relations / total_relations * 100) if total_relations > 0 else 0
            completion_rates.append(round(completion_rate, 1))
        
        return {
            'names': names,
            'scores': scores,
            'completion': completion_rates
        }
    
    def _get_position_analysis(self, start_date, end_date):
        """获取职位层级分析数据"""
        positions = Position.objects.filter(deleted_at__isnull=True).order_by('level')
        levels = []
        counts = []
        
        for pos in positions:
            levels.append(f"{pos.level}级")
            
            # 统计该职位层级的员工数量
            staff_count = Staff.objects.filter(
                position=pos,
                deleted_at__isnull=True,
                is_active=True
            ).count()
            counts.append(staff_count)
        
        return {
            'levels': levels,
            'counts': counts
        }
    
    def _get_activity_heatmap(self, start_date, end_date):
        """获取活动热力图数据"""
        # 模拟活动热力图数据（按小时和星期统计）
        data = []
        hours = ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00']
        days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        
        for day_idx, day in enumerate(days):
            for hour_idx, hour in enumerate(hours):
                # 这里可以根据实际需求从数据库查询活动数据
                # 暂时使用模拟数据
                import random
                activity_count = random.randint(0, 100)
                data.append([day_idx, hour_idx, activity_count])
        
        return data
    
    def _get_key_metrics(self, start_date, end_date):
        """获取关键指标数据"""
        # 总完成数
        total_completed = EvaluationRecord.objects.filter(
            created_at__range=[start_date, end_date],
            status='completed',
            deleted_at__isnull=True
        ).count()
        
        # 平均评分
        avg_score = EvaluationRecord.objects.filter(
            created_at__range=[start_date, end_date],
            status='completed',
            deleted_at__isnull=True
        ).aggregate(avg_score=Avg('total_score'))['avg_score'] or 0
        
        # 参与人数
        participant_count = EvaluationRecord.objects.filter(
            created_at__range=[start_date, end_date],
            status='completed',
            deleted_at__isnull=True
        ).values('relation__evaluator').distinct().count()
        
        # 活跃批次数
        active_batches = EvaluationBatch.objects.filter(
            status='in_progress',
            deleted_at__isnull=True
        ).count()
        
        # 总完成率
        total_relations = EvaluationRelation.objects.filter(
            created_at__range=[start_date, end_date],
            deleted_at__isnull=True
        ).count()
        
        completion_rate = (total_completed / total_relations * 100) if total_relations > 0 else 0
        
        return {
            'completion_rate': round(completion_rate, 1),
            'average_score': round(avg_score, 1),
            'participant_count': participant_count,
            'active_batches': active_batches,
            'total_completed': total_completed,
            'total_relations': total_relations
        }


class ProgressDataView(View):
    """进度数据API"""
    
    def get(self, request, batch_id):
        """获取批次进度数据"""
        try:
            batch = get_object_or_404(EvaluationBatch, id=batch_id)
            progress = EvaluationProgress.objects.filter(
                batch=batch, deleted_at__isnull=True
            ).first()
            
            if progress:
                data = {
                    'total_relations': progress.total_relations,
                    'completed_relations': progress.completed_relations,
                    'completion_rate': progress.completion_rate,
                }
            else:
                data = {
                    'total_relations': 0,
                    'completed_relations': 0,
                    'completion_rate': 0,
                }
                
            return JsonResponse(data)
        except Exception as e:
            logger.error(f"获取进度数据失败: {e}")
            return JsonResponse({'error': '获取数据失败'}, status=500)


class ScoreDataView(View):
    """分数数据API"""
    
    def get(self, request, batch_id):
        """获取批次分数分布数据"""
        try:
            batch = get_object_or_404(EvaluationBatch, id=batch_id)
            
            # 获取分数分布
            records = EvaluationRecord.objects.filter(
                relation__batch=batch,
                deleted_at__isnull=True
            ).values_list('total_score', flat=True)
            
            # 分组统计
            score_ranges = {
                '90-100': 0,
                '80-89': 0,
                '70-79': 0,
                '60-69': 0,
                '60以下': 0,
            }
            
            for score in records:
                if score >= 90:
                    score_ranges['90-100'] += 1
                elif score >= 80:
                    score_ranges['80-89'] += 1
                elif score >= 70:
                    score_ranges['70-79'] += 1
                elif score >= 60:
                    score_ranges['60-69'] += 1
                else:
                    score_ranges['60以下'] += 1
                    
            return JsonResponse(score_ranges)
        except Exception as e:
            logger.error(f"获取分数数据失败: {e}")
            return JsonResponse({'error': '获取数据失败'}, status=500)


# 其他占位视图类（后续实现详细功能）
class ReportDownloadView(View):
    """评估报告下载视图"""
    
    def get(self, request, pk):
        """下载评估报告Excel文件"""
        try:
            report = get_object_or_404(EvaluationReport, id=pk)
            
            if report.status != 'completed':
                messages.error(request, '报告尚未生成完成，无法下载')
                return redirect('reports:admin:report_detail', pk=pk)
            
            # 获取报告相关的评估数据
            relations = EvaluationRelation.objects.filter(
                batch=report.batch,
                status='completed',
                deleted_at__isnull=True
            ).select_related('evaluatee', 'evaluator', 'evaluatee__department', 'evaluatee__position')
            
            if report.department:
                relations = relations.filter(evaluatee__department=report.department)
            
            # 构建详细数据查询集
            detailed_data = []
            for relation in relations:
                records = EvaluationRecord.objects.filter(
                    relation=relation,
                    deleted_at__isnull=True
                ).first()
                
                if records:
                    detailed_data.append({
                        'evaluatee_employee_no': relation.evaluatee.employee_no,
                        'evaluatee_name': relation.evaluatee.name,
                        'evaluatee_department': relation.evaluatee.department.name if relation.evaluatee.department else '',
                        'evaluatee_position': relation.evaluatee.position.name if relation.evaluatee.position else '',
                        'evaluator_name': relation.evaluator.name,
                        'evaluation_score': records.total_score,
                        'dimension_scores': records.dimension_scores,
                        'comments': records.comments or '',
                        'created_at': relation.created_at
                    })
            
            # 创建Excel处理器
            processor = ExcelProcessor(request.current_staff.id)
            
            # 定义字段映射
            field_mapping = {
                'evaluatee_employee_no': '被评价员工编号',
                'evaluatee_name': '被评价员工姓名',
                'evaluatee_department': '所属部门',
                'evaluatee_position': '职位',
                'evaluator_name': '评价者',
                'evaluation_score': '总分',
                'dimension_scores': '维度评分',
                'comments': '评价意见',
                'created_at': '评价时间'
            }
            
            # 将字典数据转换为对象形式以便导出
            from types import SimpleNamespace
            export_data = [SimpleNamespace(**item) for item in detailed_data]
            
            # 生成文件名
            filename = f'{report.title}_详细数据_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
            
            # 记录审计日志
            AuditLog.objects.create(
                user=request.current_staff.name,
                action='export',
                target_model='EvaluationReport',
                description=f'下载评估报告: {report.title}, {len(detailed_data)}条记录',
                ip_address=request.META.get('REMOTE_ADDR'),
                user_agent=request.META.get('HTTP_USER_AGENT', '')
            )
            
            return processor.export_data(export_data, 'evaluation_report', field_mapping, filename)
            
        except Exception as e:
            logger.error(f"报告下载失败: {e}")
            messages.error(request, f'报告下载失败: {str(e)}')
            return redirect('reports:admin:report_detail', pk=pk)

class ReportDeleteView(DeleteView):
    model = EvaluationReport
    success_url = reverse_lazy('reports:admin:report_list')

class TalentAssessmentUpdateView(UpdateView):
    model = TalentAssessment
    template_name = 'admin/talent/update.html'
    fields = '__all__'

class TalentExportView(View):
    """人才盘点导出视图"""
    
    def get(self, request, batch_id):
        """导出人才盘点数据"""
        try:
            batch = get_object_or_404(EvaluationBatch, id=batch_id)
            
            # 获取人才评估数据
            assessments = TalentAssessment.objects.filter(
                batch=batch,
                deleted_at__isnull=True
            ).select_related('staff', 'staff__department', 'staff__position')
            
            # 创建Excel处理器
            processor = ExcelProcessor(request.current_staff.id)
            
            # 定义字段映射
            field_mapping = {
                'staff.employee_no': '员工编号',
                'staff.name': '姓名',
                'staff.department.name': '所属部门',
                'staff.position.name': '职位',
                'performance_score': '绩效得分',
                'potential_score': '潜力得分',
                'talent_category': '人才类别',
                'development_plan': '发展计划',
                'risk_level': '流失风险',
                'created_at': '评估时间'
            }
            
            # 生成文件名
            filename = f'人才盘点数据_{batch.name}_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
            
            # 记录审计日志
            AuditLog.objects.create(
                user=request.current_staff.name,
                action='export',
                target_model='TalentAssessment',
                description=f'导出人才盘点数据: 批次{batch.name}, {assessments.count()}条记录',
                ip_address=request.META.get('REMOTE_ADDR'),
                user_agent=request.META.get('HTTP_USER_AGENT', '')
            )
            
            return processor.export_data(assessments, 'talent_assessment', field_mapping, filename)
            
        except Exception as e:
            logger.error(f"人才盘点数据导出失败: {e}")
            messages.error(request, f'导出失败: {str(e)}')
            return redirect('reports:admin:talent_assessment_list')

class DepartmentAnalyticsView(View):
    def get(self, request, dept_id):
        return render(request, 'admin/analytics/department.html')

class ComparisonAnalyticsView(View):
    def get(self, request):
        return render(request, 'admin/analytics/comparison.html')

class MatrixDataView(View):
    def get(self, request, batch_id):
        return JsonResponse({'matrix': '人才矩阵数据开发中'})

class TrendDataView(View):
    def get(self, request):
        return JsonResponse({'trends': '趋势数据开发中'})


class AnalyticsExportView(View):
    """统计分析数据导出视图"""
    
    def get(self, request):
        """导出统计分析数据"""
        try:
            # 获取筛选参数
            days = int(request.GET.get('days', 30))
            department_id = request.GET.get('department')
            
            # 计算时间范围
            from datetime import datetime, timedelta
            end_date = timezone.now()
            start_date = end_date - timedelta(days=days)
            
            # 构建查询集
            relations = EvaluationRelation.objects.filter(
                created_at__range=[start_date, end_date],
                deleted_at__isnull=True
            ).select_related('evaluatee', 'evaluator', 'batch', 'evaluatee__department', 'evaluatee__position')
            
            if department_id:
                relations = relations.filter(evaluatee__department_id=department_id)
            
            # 构建导出数据
            export_data = []
            for relation in relations:
                # 获取评估记录
                record = EvaluationRecord.objects.filter(
                    relation=relation,
                    deleted_at__isnull=True
                ).first()
                
                score = record.total_score if record else 0
                completion_status = '已完成' if record else '未完成'
                
                export_data.append({
                    'batch_name': relation.batch.name,
                    'evaluatee_employee_no': relation.evaluatee.employee_no,
                    'evaluatee_name': relation.evaluatee.name,
                    'evaluatee_department': relation.evaluatee.department.name if relation.evaluatee.department else '',
                    'evaluatee_position': relation.evaluatee.position.name if relation.evaluatee.position else '',
                    'evaluator_name': relation.evaluator.name,
                    'completion_status': completion_status,
                    'total_score': score,
                    'score_level': self._get_score_level(score),
                    'created_at': relation.created_at,
                    'completed_at': record.created_at if record else None
                })
            
            # 创建Excel处理器
            processor = ExcelProcessor(request.current_staff.id)
            
            # 定义字段映射
            field_mapping = {
                'batch_name': '考评批次',
                'evaluatee_employee_no': '被评价员工编号',
                'evaluatee_name': '被评价员工姓名',
                'evaluatee_department': '所属部门',
                'evaluatee_position': '职位',
                'evaluator_name': '评价者',
                'completion_status': '完成状态',
                'total_score': '总分',
                'score_level': '得分等级',
                'created_at': '创建时间',
                'completed_at': '完成时间'
            }
            
            # 将字典数据转换为对象形式以便导出
            from types import SimpleNamespace
            formatted_data = [SimpleNamespace(**item) for item in export_data]
            
            # 生成文件名
            filename = f'统计分析数据_{days}天_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
            
            # 记录审计日志
            AuditLog.objects.create(
                user=request.current_staff.name,
                action='export',
                target_model='AnalyticsData',
                description=f'导出统计分析数据: {days}天数据, {len(export_data)}条记录',
                ip_address=request.META.get('REMOTE_ADDR'),
                user_agent=request.META.get('HTTP_USER_AGENT', '')
            )
            
            return processor.export_data(formatted_data, 'analytics_data', field_mapping, filename)
            
        except Exception as e:
            logger.error(f"统计分析数据导出失败: {e}")
            messages.error(request, f'导出失败: {str(e)}')
            return redirect('reports:admin:analytics')
    
    def _get_score_level(self, score):
        """获取分数等级"""
        if score >= 90:
            return '优秀'
        elif score >= 80:
            return '良好'
        elif score >= 70:
            return '中等'
        elif score >= 60:
            return '待改进'
        else:
            return '不合格'
