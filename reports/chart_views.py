# -*- coding: utf-8 -*-
"""
图表数据API视图
为ECharts提供JSON格式的数据接口
"""

import json
from datetime import datetime, timedelta
from django.http import JsonResponse
from django.views import View
from django.db.models import Count, Avg, Q, F
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt

from organizations.models import Department, Staff
from evaluations.models import EvaluationBatch, EvaluationRecord, EvaluationRelation
from reports.models import EvaluationProgress, TalentAssessment, TalentMatrix
from organizations.middleware import require_admin_permission


class BaseChartDataView(View):
    """图表数据基础视图类"""
    
    def get_response_data(self, success=True, data=None, message=''):
        """统一的响应数据格式"""
        return {
            'success': success,
            'data': data or {},
            'message': message,
            'timestamp': datetime.now().isoformat()
        }
    
    def handle_exception(self, e):
        """统一的异常处理"""
        return JsonResponse(self.get_response_data(
            success=False,
            message=f'数据获取失败: {str(e)}'
        ), status=500)


@method_decorator(require_admin_permission, name='dispatch')
class DashboardOverviewChartView(BaseChartDataView):
    """仪表板总览图表数据"""
    
    def get(self, request):
        """获取仪表板总览数据"""
        try:
            # 基础统计数据
            stats = self._get_basic_stats()
            
            # 部门分布数据
            department_data = self._get_department_distribution()
            
            # 考评进度数据
            progress_data = self._get_evaluation_progress()
            
            # 最近30天活动趋势
            activity_trend = self._get_activity_trend()
            
            return JsonResponse(self.get_response_data(data={
                'overview': stats,
                'departments': department_data,
                'progress': progress_data,
                'trend': activity_trend
            }))
            
        except Exception as e:
            return self.handle_exception(e)
    
    def _get_basic_stats(self):
        """获取基础统计数据"""
        total_staff = Staff.objects.filter(deleted_at__isnull=True, is_active=True).count()
        total_departments = Department.objects.filter(deleted_at__isnull=True).count()
        active_batches = EvaluationBatch.objects.filter(status='in_progress').count()
        completed_evaluations = EvaluationRecord.objects.filter(
            status='completed',
            created_at__gte=datetime.now() - timedelta(days=30)
        ).count()
        
        return {
            'total_staff': total_staff,
            'total_departments': total_departments,
            'active_batches': active_batches,
            'completed_evaluations': completed_evaluations
        }
    
    def _get_department_distribution(self):
        """获取部门人员分布数据"""
        departments = Department.objects.filter(
            deleted_at__isnull=True
        ).annotate(
            staff_count=Count('staff', filter=Q(staff__deleted_at__isnull=True, staff__is_active=True))
        ).order_by('-staff_count')
        
        return [{
            'name': dept.name,
            'value': dept.staff_count,
            'code': dept.dept_code
        } for dept in departments if dept.staff_count > 0]
    
    def _get_evaluation_progress(self):
        """获取考评进度数据"""
        # 获取进行中的批次
        active_batches = EvaluationBatch.objects.filter(status='in_progress')
        
        progress_data = []
        for batch in active_batches:
            total_relations = EvaluationRelation.objects.filter(batch=batch).count()
            completed_relations = EvaluationRecord.objects.filter(
                relation__batch=batch,
                status='completed'
            ).count()
            
            progress_rate = (completed_relations / total_relations * 100) if total_relations > 0 else 0
            
            progress_data.append({
                'batch_name': batch.name,
                'total': total_relations,
                'completed': completed_relations,
                'progress_rate': round(progress_rate, 1)
            })
        
        return progress_data
    
    def _get_activity_trend(self):
        """获取最近30天活动趋势"""
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=29)  # 30天数据
        
        # 生成日期范围
        date_range = []
        current_date = start_date
        while current_date <= end_date:
            date_range.append(current_date)
            current_date += timedelta(days=1)
        
        # 获取每日评分记录数
        daily_records = EvaluationRecord.objects.filter(
            created_at__date__gte=start_date,
            created_at__date__lte=end_date
        ).extra(
            select={'date': 'DATE(created_at)'}
        ).values('date').annotate(
            count=Count('id')
        ).order_by('date')
        
        # 构建日期-数量映射
        record_map = {item['date']: item['count'] for item in daily_records}
        
        return [{
            'date': date.strftime('%Y-%m-%d'),
            'count': record_map.get(date, 0)
        } for date in date_range]


@method_decorator(require_admin_permission, name='dispatch')
class EvaluationProgressChartView(BaseChartDataView):
    """考评进度图表数据"""
    
    def get(self, request):
        """获取考评进度图表数据"""
        try:
            batch_id = request.GET.get('batch_id')
            
            if batch_id:
                # 特定批次的详细进度数据
                data = self._get_batch_progress_detail(batch_id)
            else:
                # 所有批次的概览数据
                data = self._get_all_batches_progress()
            
            return JsonResponse(self.get_response_data(data=data))
            
        except Exception as e:
            return self.handle_exception(e)
    
    def _get_batch_progress_detail(self, batch_id):
        """获取特定批次的详细进度"""
        try:
            batch = EvaluationBatch.objects.get(id=batch_id)
        except EvaluationBatch.DoesNotExist:
            raise ValueError("批次不存在")
        
        # 按部门统计进度
        departments = Department.objects.filter(deleted_at__isnull=True)
        department_progress = []
        
        for dept in departments:
            # 该部门参与该批次的关系数
            total_relations = EvaluationRelation.objects.filter(
                batch=batch,
                evaluator__department=dept
            ).count()
            
            if total_relations == 0:
                continue
            
            # 该部门已完成的评价数
            completed_relations = EvaluationRecord.objects.filter(
                relation__batch=batch,
                relation__evaluator__department=dept,
                status='completed'
            ).count()
            
            progress_rate = (completed_relations / total_relations * 100) if total_relations > 0 else 0
            
            department_progress.append({
                'department': dept.name,
                'total': total_relations,
                'completed': completed_relations,
                'pending': total_relations - completed_relations,
                'progress_rate': round(progress_rate, 1)
            })
        
        # 按评价关系类型统计
        relation_types = ['peer', 'subordinate_to_superior', 'superior_to_subordinate', 'self']
        relation_type_names = {
            'peer': '同级评价',
            'subordinate_to_superior': '下级评上级',
            'superior_to_subordinate': '上级评下级',
            'self': '自我评价'
        }
        
        type_progress = []
        for rel_type in relation_types:
            total = EvaluationRelation.objects.filter(
                batch=batch,
                relation_type=rel_type
            ).count()
            
            if total == 0:
                continue
            
            completed = EvaluationRecord.objects.filter(
                relation__batch=batch,
                relation__relation_type=rel_type,
                status='completed'
            ).count()
            
            type_progress.append({
                'type': relation_type_names[rel_type],
                'total': total,
                'completed': completed,
                'progress_rate': round((completed / total * 100), 1)
            })
        
        return {
            'batch_info': {
                'id': batch.id,
                'name': batch.name,
                'status': batch.get_status_display(),
                'start_date': batch.start_date.isoformat() if batch.start_date else None,
                'end_date': batch.end_date.isoformat() if batch.end_date else None
            },
            'department_progress': department_progress,
            'type_progress': type_progress
        }
    
    def _get_all_batches_progress(self):
        """获取所有批次的概览进度"""
        batches = EvaluationBatch.objects.all().order_by('-created_at')
        
        batch_data = []
        for batch in batches:
            total_relations = EvaluationRelation.objects.filter(batch=batch).count()
            completed_relations = EvaluationRecord.objects.filter(
                relation__batch=batch,
                status='completed'
            ).count()
            
            progress_rate = (completed_relations / total_relations * 100) if total_relations > 0 else 0
            
            batch_data.append({
                'id': batch.id,
                'name': batch.name,
                'status': batch.status,
                'status_display': batch.get_status_display(),
                'total': total_relations,
                'completed': completed_relations,
                'progress_rate': round(progress_rate, 1),
                'created_at': batch.created_at.isoformat()
            })
        
        return {
            'batches': batch_data
        }


@method_decorator(require_admin_permission, name='dispatch')
class DepartmentComparisonChartView(BaseChartDataView):
    """部门对比分析图表数据"""
    
    def get(self, request):
        """获取部门对比分析数据"""
        try:
            batch_id = request.GET.get('batch_id')
            metric = request.GET.get('metric', 'average_score')  # average_score, participation_rate, completion_time
            
            if batch_id:
                data = self._get_department_comparison_by_batch(batch_id, metric)
            else:
                data = self._get_department_overall_comparison(metric)
            
            return JsonResponse(self.get_response_data(data=data))
            
        except Exception as e:
            return self.handle_exception(e)
    
    def _get_department_comparison_by_batch(self, batch_id, metric):
        """按批次的部门对比数据"""
        try:
            batch = EvaluationBatch.objects.get(id=batch_id)
        except EvaluationBatch.DoesNotExist:
            raise ValueError("批次不存在")
        
        departments = Department.objects.filter(deleted_at__isnull=True)
        comparison_data = []
        
        for dept in departments:
            if metric == 'average_score':
                # 平均分对比
                avg_score = EvaluationRecord.objects.filter(
                    relation__batch=batch,
                    relation__evaluatee__department=dept,
                    status='completed'
                ).aggregate(avg_score=Avg('total_score'))['avg_score'] or 0
                
                comparison_data.append({
                    'department': dept.name,
                    'value': round(avg_score, 2),
                    'metric': '平均分'
                })
                
            elif metric == 'participation_rate':
                # 参与率对比
                total_staff = Staff.objects.filter(
                    department=dept,
                    deleted_at__isnull=True,
                    is_active=True
                ).count()
                
                participated_staff = EvaluationRecord.objects.filter(
                    relation__batch=batch,
                    relation__evaluator__department=dept,
                    status='completed'
                ).values('relation__evaluator').distinct().count()
                
                participation_rate = (participated_staff / total_staff * 100) if total_staff > 0 else 0
                
                comparison_data.append({
                    'department': dept.name,
                    'value': round(participation_rate, 1),
                    'metric': '参与率(%)'
                })
        
        return {
            'batch_info': {
                'id': batch.id,
                'name': batch.name
            },
            'metric': metric,
            'data': comparison_data
        }
    
    def _get_department_overall_comparison(self, metric):
        """整体部门对比数据"""
        departments = Department.objects.filter(deleted_at__isnull=True)
        comparison_data = []
        
        for dept in departments:
            if metric == 'average_score':
                # 历史平均分
                avg_score = EvaluationRecord.objects.filter(
                    relation__evaluatee__department=dept,
                    status='completed'
                ).aggregate(avg_score=Avg('total_score'))['avg_score'] or 0
                
                comparison_data.append({
                    'department': dept.name,
                    'value': round(avg_score, 2),
                    'metric': '历史平均分'
                })
                
            elif metric == 'participation_rate':
                # 整体参与率
                total_evaluations = EvaluationRelation.objects.filter(
                    evaluator__department=dept
                ).count()
                
                completed_evaluations = EvaluationRecord.objects.filter(
                    relation__evaluator__department=dept,
                    status='completed'
                ).count()
                
                participation_rate = (completed_evaluations / total_evaluations * 100) if total_evaluations > 0 else 0
                
                comparison_data.append({
                    'department': dept.name,
                    'value': round(participation_rate, 1),
                    'metric': '整体参与率(%)'
                })
        
        return {
            'metric': metric,
            'data': comparison_data
        }


@method_decorator(require_admin_permission, name='dispatch')
class TalentMatrixChartView(BaseChartDataView):
    """人才九宫格图表数据"""
    
    def get(self, request):
        """获取人才九宫格数据"""
        try:
            department_id = request.GET.get('department_id')
            
            if department_id:
                data = self._get_department_talent_matrix(department_id)
            else:
                data = self._get_overall_talent_matrix()
            
            return JsonResponse(self.get_response_data(data=data))
            
        except Exception as e:
            return self.handle_exception(e)
    
    def _get_department_talent_matrix(self, department_id):
        """获取特定部门的人才九宫格数据"""
        try:
            department = Department.objects.get(id=department_id)
        except Department.DoesNotExist:
            raise ValueError("部门不存在")
        
        # 获取该部门的人才评估数据
        assessments = TalentAssessment.objects.filter(
            staff__department=department,
            deleted_at__isnull=True
        )
        
        # 按九宫格分类统计
        matrix_data = []
        categories = [
            ('high_performance_high_potential', '明星员工', '#22c55e'),
            ('high_performance_medium_potential', '核心骨干', '#3b82f6'),
            ('high_performance_low_potential', '专业专家', '#8b5cf6'),
            ('medium_performance_high_potential', '潜力股', '#f59e0b'),
            ('medium_performance_medium_potential', '稳定员工', '#6b7280'),
            ('medium_performance_low_potential', '普通员工', '#94a3b8'),
            ('low_performance_high_potential', '问题员工', '#ef4444'),
            ('low_performance_medium_potential', '待提升', '#f97316'),
            ('low_performance_low_potential', '不匹配', '#dc2626')
        ]
        
        for category, name, color in categories:
            count = assessments.filter(talent_category=category).count()
            if count > 0:
                # 获取该分类的员工详情
                staff_list = assessments.filter(talent_category=category).values(
                    'staff__name',
                    'staff__employee_no',
                    'performance_score',
                    'potential_score'
                )
                
                matrix_data.append({
                    'category': category,
                    'name': name,
                    'count': count,
                    'color': color,
                    'staff': list(staff_list)
                })
        
        return {
            'department': {
                'id': department.id,
                'name': department.name
            },
            'matrix_data': matrix_data,
            'total_staff': assessments.count()
        }
    
    def _get_overall_talent_matrix(self):
        """获取整体人才九宫格数据"""
        # 获取所有人才评估数据
        assessments = TalentAssessment.objects.filter(deleted_at__isnull=True)
        
        # 九宫格散点图数据
        scatter_data = []
        for assessment in assessments:
            scatter_data.append({
                'name': assessment.staff.name,
                'value': [assessment.performance_score, assessment.potential_score],
                'category': assessment.talent_category,
                'department': assessment.staff.department.name if assessment.staff.department else '未分配'
            })
        
        # 统计数据
        category_stats = {}
        categories = [
            ('high_performance_high_potential', '明星员工'),
            ('high_performance_medium_potential', '核心骨干'),
            ('high_performance_low_potential', '专业专家'),
            ('medium_performance_high_potential', '潜力股'),
            ('medium_performance_medium_potential', '稳定员工'),
            ('medium_performance_low_potential', '普通员工'),
            ('low_performance_high_potential', '问题员工'),
            ('low_performance_medium_potential', '待提升'),
            ('low_performance_low_potential', '不匹配')
        ]
        
        for category, name in categories:
            count = assessments.filter(talent_category=category).count()
            category_stats[category] = {
                'name': name,
                'count': count,
                'percentage': round((count / assessments.count() * 100), 1) if assessments.count() > 0 else 0
            }
        
        return {
            'scatter_data': scatter_data,
            'category_stats': category_stats,
            'total_staff': assessments.count()
        }


@method_decorator(require_admin_permission, name='dispatch')
class ActivityTrendChartView(BaseChartDataView):
    """活动趋势图表数据"""
    
    def get(self, request):
        """获取活动趋势数据"""
        try:
            days = int(request.GET.get('days', 30))  # 默认30天
            metric = request.GET.get('metric', 'evaluation_records')  # evaluation_records, login_activity
            
            data = self._get_trend_data(days, metric)
            
            return JsonResponse(self.get_response_data(data=data))
            
        except Exception as e:
            return self.handle_exception(e)
    
    def _get_trend_data(self, days, metric):
        """获取趋势数据"""
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=days-1)
        
        # 生成日期范围
        date_range = []
        current_date = start_date
        while current_date <= end_date:
            date_range.append(current_date)
            current_date += timedelta(days=1)
        
        if metric == 'evaluation_records':
            # 评价记录趋势
            daily_records = EvaluationRecord.objects.filter(
                created_at__date__gte=start_date,
                created_at__date__lte=end_date
            ).extra(
                select={'date': 'DATE(created_at)'}
            ).values('date').annotate(
                count=Count('id')
            ).order_by('date')
            
            record_map = {item['date']: item['count'] for item in daily_records}
            
            trend_data = [{
                'date': date.strftime('%Y-%m-%d'),
                'value': record_map.get(date, 0),
                'label': date.strftime('%m-%d')
            } for date in date_range]
            
        elif metric == 'login_activity':
            # 登录活动趋势
            from organizations.models import StaffLoginLog
            
            daily_logins = StaffLoginLog.objects.filter(
                login_time__date__gte=start_date,
                login_time__date__lte=end_date,
                login_success=True
            ).extra(
                select={'date': 'DATE(login_time)'}
            ).values('date').annotate(
                count=Count('staff', distinct=True)  # 去重计算独立用户数
            ).order_by('date')
            
            login_map = {item['date']: item['count'] for item in daily_logins}
            
            trend_data = [{
                'date': date.strftime('%Y-%m-%d'),
                'value': login_map.get(date, 0),
                'label': date.strftime('%m-%d')
            } for date in date_range]
        
        return {
            'metric': metric,
            'days': days,
            'data': trend_data,
            'summary': {
                'total': sum(item['value'] for item in trend_data),
                'average': round(sum(item['value'] for item in trend_data) / len(trend_data), 1),
                'max': max(item['value'] for item in trend_data),
                'min': min(item['value'] for item in trend_data)
            }
        }