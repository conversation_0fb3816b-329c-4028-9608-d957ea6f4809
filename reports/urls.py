# -*- coding: utf-8 -*-
"""
报告应用URL配置
包含报告生成和人才盘点相关的路由
"""

from django.urls import path, include
from . import views, chart_views

app_name = 'reports'

# 管理端URL配置
admin_urlpatterns = [
    # 报告管理
    path('', views.ReportListView.as_view(), name='report_list'),
    path('generate/', views.ReportGenerateView.as_view(), name='report_generate'),
    path('<int:pk>/', views.ReportDetailView.as_view(), name='report_detail'),
    path('<int:pk>/download/', views.ReportDownloadView.as_view(), name='report_download'),
    path('<int:pk>/delete/', views.ReportDeleteView.as_view(), name='report_delete'),
    
    # 人才盘点
    path('talent/', views.TalentAssessmentListView.as_view(), name='talent_assessment_list'),
    path('talent/<int:batch_id>/assess/', views.TalentAssessmentView.as_view(), name='talent_assess'),
    path('talent/<int:pk>/edit/', views.TalentAssessmentUpdateView.as_view(), name='talent_update'),
    path('talent/<int:batch_id>/matrix/', views.TalentMatrixView.as_view(), name='talent_matrix'),
    path('talent/<int:batch_id>/export/', views.TalentExportView.as_view(), name='talent_export'),
    
    # 统计分析
    path('analytics/', views.AnalyticsView.as_view(), name='analytics'),
    path('analytics/export/', views.AnalyticsExportView.as_view(), name='analytics_export'),
    path('analytics/department/<int:dept_id>/', views.DepartmentAnalyticsView.as_view(), name='dept_analytics'),
    path('analytics/comparison/', views.ComparisonAnalyticsView.as_view(), name='comparison_analytics'),
    
    # 数据可视化API（原有）
    path('api/analytics/', views.AnalyticsAPIView.as_view(), name='analytics_api'),
    path('api/progress/<int:batch_id>/', views.ProgressDataView.as_view(), name='progress_data'),
    path('api/scores/<int:batch_id>/', views.ScoreDataView.as_view(), name='score_data'),
    path('api/matrix/<int:batch_id>/', views.MatrixDataView.as_view(), name='matrix_data'),
    path('api/trends/', views.TrendDataView.as_view(), name='trend_data'),
    
    # ECharts图表数据API（新增）
    path('api/charts/dashboard-overview/', chart_views.DashboardOverviewChartView.as_view(), name='dashboard_overview_chart'),
    path('api/charts/evaluation-progress/', chart_views.EvaluationProgressChartView.as_view(), name='evaluation_progress_chart'),
    path('api/charts/department-comparison/', chart_views.DepartmentComparisonChartView.as_view(), name='department_comparison_chart'),
    path('api/charts/talent-matrix/', chart_views.TalentMatrixChartView.as_view(), name='talent_matrix_chart'),
    path('api/charts/activity-trend/', chart_views.ActivityTrendChartView.as_view(), name='activity_trend_chart'),
]

urlpatterns = [
    path('admin/', include((admin_urlpatterns, 'admin'))),
]