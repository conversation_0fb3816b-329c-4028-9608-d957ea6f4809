# 职位导入功能说明

## 📋 功能概述

参考部门管理导入模块，为职位管理新增了完整的导入功能，支持批量导入职位信息和层级结构。

## 🎯 导入格式

### 必填字段
- **部门*** - 职位所属部门名称（必须是系统中已存在的部门）
- **职位*** - 职位名称
- **编码*** - 职位编码（在同一部门内必须唯一）

### 可选字段
- **是否管理岗** - 填写"是"或"否"，不填默认为"否"
- **是否部门主管** - 填写"是"或"否"，不填默认为"否"
- **职位级别** - 1-9级，对应不同层级
- **备注** - 职位描述或其他说明信息

### 职位级别说明
- 1-4级：普通员工
- 5级：副主管
- 6级：正主管（柜组长）
- 7级：副经理
- 8级：正经理
- 9级：领导班子

## 🚀 功能特点

### ✨ 智能导入
- **部门关联** - 通过部门名称自动关联部门ID
- **数据验证** - 自动验证必填字段和数据格式
- **重复处理** - 支持更新已存在的职位信息
- **错误提示** - 详细的错误信息和行号定位

### 🔄 导入流程
1. **模板下载** - 提供标准Excel模板
2. **数据填写** - 按模板格式填写职位信息
3. **文件上传** - 支持拖拽上传和文件选择
4. **实时反馈** - 显示导入进度和结果统计

### 🛡️ 安全保障
- **权限控制** - 需要职位创建权限
- **数据校验** - 多层次数据验证
- **错误恢复** - 部分失败不影响成功记录
- **操作日志** - 完整的导入历史记录

## 📁 实现文件

### 后端实现
- `organizations/views.py` - 新增 `PositionImportView` 视图类
- `organizations/urls.py` - 添加职位导入路由
- `common/excel_utils.py` - 新增职位模板生成方法

### 前端实现
- `templates/admin/position/import.html` - 职位导入页面
- `templates/admin/position/list.html` - 添加导入按钮（卡片视图）
- `templates/admin/position/list_table.html` - 添加导入按钮（表格视图）

## 🔧 技术实现

### 核心功能
```python
class PositionImportView(View):
    """职位信息导入视图"""
    
    def post(self, request):
        # 文件验证
        # Excel解析
        # 数据验证
        # 部门关联
        # 批量导入
        # 结果反馈
```

### 字段映射
```python
field_mapping = {
    '部门*': 'department__name',
    '职位*': 'name', 
    '编码*': 'position_code',
    '是否管理岗': 'is_management',
    '是否部门主管': 'is_department_manager',
    '职位级别': 'level',
    '备注': 'description'
}
```

### 数据验证
```python
validators = {
    'required_fields': ['部门*', '职位*', '编码*'],
    'unique_fields': ['编码*'],
    'data_types': {
        '部门*': 'string',
        '职位*': 'string', 
        '编码*': 'string',
        '是否管理岗': 'boolean',
        '是否部门主管': 'boolean',
        '职位级别': 'integer',
        '备注': 'string'
    }
}
```

## 📊 使用示例

### 1. 访问导入页面
```
http://127.0.0.1:8000/admin/positions/ → 点击"导入"按钮
```

### 2. 下载模板
点击"下载模板"获取标准Excel文件，包含：
- 示例数据
- 字段说明
- 注意事项

### 3. 填写数据
按照模板格式填写职位信息：
```
部门*    | 职位*     | 编码*   | 是否管理岗 | 是否部门主管 | 职位级别 | 备注
总经理室  | 总经理    | CEO001  | 是        | 是          | 9       | 公司最高管理者
技术部   | 技术总监   | CTO001  | 是        | 是          | 8       | 技术部门负责人
技术部   | 高级工程师 | SE001   | 否        | 否          | 6       | 高级软件工程师
```

### 4. 上传导入
- 选择或拖拽Excel文件
- 点击"开始导入"
- 查看导入结果和错误信息

## ⚠️ 注意事项

### 数据要求
1. **部门存在性** - 部门名称必须在系统中已存在
2. **编码唯一性** - 职位编码在同一部门内必须唯一
3. **级别范围** - 职位级别必须在1-9之间
4. **布尔值格式** - 是否管理岗/部门主管支持多种格式：是/否、True/False、1/0

### 最佳实践
1. **先导入部门** - 确保相关部门已存在
2. **检查模板** - 使用最新的模板文件
3. **分批导入** - 大量数据建议分批处理
4. **备份数据** - 导入前建议备份现有数据

## 🧪 测试验证

已创建测试脚本 `test_position_import.py`，包含：
- 测试部门创建
- 测试Excel文件生成
- 数据验证测试
- 导入流程模拟

运行测试：
```bash
python test_position_import.py
```

## 📈 功能扩展

未来可考虑的扩展功能：
- 批量编辑职位信息
- 职位层级关系导入
- 职位权限批量配置
- 导入模板自定义
- 数据导入预览功能
