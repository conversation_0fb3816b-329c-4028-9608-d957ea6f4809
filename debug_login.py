#!/usr/bin/env python
"""
调试登录问题
"""
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
django.setup()

from organizations.models import Staff
from django.contrib.auth.hashers import check_password

def debug_login_issue():
    """调试登录问题"""
    try:
        print("🔍 调试登录问题...")
        print("=" * 50)
        
        # 1. 检查用户是否存在
        print("1. 检查用户存在性:")
        staff = Staff.objects.filter(username='testadmin').first()
        if not staff:
            print("❌ 用户不存在！")
            return
        
        print(f"✅ 用户存在: {staff.username}")
        print(f"   ID: {staff.id}")
        print(f"   员工编号: {staff.employee_no}")
        print(f"   姓名: {staff.name}")
        print(f"   角色: {staff.role}")
        print(f"   是否激活: {staff.is_active}")
        print(f"   是否管理员: {staff.is_manager}")
        print(f"   部门: {staff.department.name if staff.department else '无'}")
        
        # 2. 检查密码
        print("\n2. 检查密码:")
        is_valid = check_password('test123456', staff.password)
        print(f"   密码验证: {'✅ 正确' if is_valid else '❌ 错误'}")
        
        if not is_valid:
            print("   尝试重置密码...")
            staff.set_password('test123456')
            staff.save()
            print("   密码已重置")
            
            # 再次验证
            is_valid = check_password('test123456', staff.password)
            print(f"   重新验证: {'✅ 正确' if is_valid else '❌ 仍然错误'}")
        
        # 3. 检查账户状态
        print("\n3. 检查账户状态:")
        print(f"   is_active: {staff.is_active}")
        print(f"   deleted_at: {staff.deleted_at if hasattr(staff, 'deleted_at') else '无此字段'}")
        
        # 4. 检查管理员权限
        print("\n4. 检查管理员权限:")
        print(f"   role: {staff.role}")
        print(f"   is_manager: {staff.is_manager}")
        
        # 5. 模拟登录验证逻辑
        print("\n5. 模拟登录验证:")
        
        # 检查用户名查找
        found_by_username = Staff.objects.filter(
            username='testadmin',
            deleted_at__isnull=True,
            is_active=True
        ).first()
        print(f"   通过用户名查找: {'✅ 找到' if found_by_username else '❌ 未找到'}")
        
        # 检查员工编号查找
        found_by_employee_no = Staff.objects.filter(
            employee_no='testadmin', 
            deleted_at__isnull=True,
            is_active=True
        ).first()
        print(f"   通过员工编号查找: {'✅ 找到' if found_by_employee_no else '❌ 未找到'}")
        
        # 检查组合查找（LoginView使用的逻辑）
        from django.db.models import Q
        found_combined = Staff.objects.filter(
            Q(username='testadmin') | Q(employee_no='testadmin'),
            deleted_at__isnull=True,
            is_active=True
        ).first()
        print(f"   组合查找(Q): {'✅ 找到' if found_combined else '❌ 未找到'}")
        
        # 6. 检查token生成能力
        print("\n6. 检查token生成:")
        can_generate, message = staff.can_generate_token()
        print(f"   can_generate_token: {'✅ 可以' if can_generate else '❌ 不可以'}")
        print(f"   消息: {message}")
        
        # 7. 尝试生成token
        if can_generate:
            try:
                from common.security.jwt_auth import JWTAuthentication
                tokens = JWTAuthentication.generate_tokens(staff)
                print(f"   token生成: ✅ 成功")
                print(f"   access_token长度: {len(tokens['access_token'])}")
            except Exception as e:
                print(f"   token生成: ❌ 失败 - {e}")
        
        return staff
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return None

def test_login_view():
    """测试登录视图逻辑"""
    try:
        print("\n" + "=" * 50)
        print("🧪 测试登录视图逻辑:")
        
        from django.test import RequestFactory
        from organizations.views import LoginView
        
        # 创建测试请求
        factory = RequestFactory()
        request = factory.post('/admin/login/', {
            'username': 'testadmin',
            'password': 'test123456',
            'use_jwt': 'true'
        })
        
        # 添加session（Django需要）
        from django.contrib.sessions.middleware import SessionMiddleware
        middleware = SessionMiddleware(lambda x: None)
        middleware.process_request(request)
        request.session.save()
        
        # 添加messages
        from django.contrib.messages.middleware import MessageMiddleware
        from django.contrib.messages.storage.fallback import FallbackStorage
        request._messages = FallbackStorage(request)
        
        # 测试登录视图
        view = LoginView()
        response = view.post(request)
        
        print(f"   响应状态码: {response.status_code}")
        print(f"   响应类型: {type(response).__name__}")
        
        if hasattr(response, 'url'):
            print(f"   重定向URL: {response.url}")
        
        if hasattr(response, 'content'):
            content = response.content.decode('utf-8')[:200]
            print(f"   响应内容（前200字符）: {content}")
            
    except Exception as e:
        print(f"❌ 视图测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == '__main__':
    print("🚀 开始调试登录问题...")
    
    # 调试用户和密码
    user = debug_login_issue()
    
    # 测试登录视图
    if user:
        test_login_view()
    
    print("\n" + "=" * 50)
    print("🔍 调试完成")
    print("\n💡 建议检查:")
    print("1. 浏览器开发者工具的Console是否有JavaScript错误")
    print("2. Django服务器控制台是否有错误日志")
    print("3. 确认用户名和密码输入正确")