#!/usr/bin/env python
"""
Communications模块API测试脚本
测试所有已实现的API端点
"""

import os
import sys
import django
import requests
import json
from datetime import datetime

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
django.setup()

class CommunicationsAPITester:
    def __init__(self):
        self.base_url = 'http://127.0.0.1:8000'
        self.access_token = None
        self.headers = {'Content-Type': 'application/json'}
        
    def login(self):
        """登录获取访问令牌"""
        print("=== 登录测试 ===")
        login_data = {
            'username': 'admin',
            'password': '123456'
        }
        
        try:
            response = requests.post(
                f'{self.base_url}/api/login/',
                json=login_data,
                headers=self.headers,
                timeout=10
            )
            
            print(f"登录状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.access_token = data['tokens']['access_token']
                    self.headers['Authorization'] = f'Bearer {self.access_token}'
                    print("✅ 登录成功")
                    return True
                else:
                    print(f"❌ 登录失败: {data.get('message')}")
            else:
                print(f"❌ 登录失败: HTTP {response.status_code}")
                print(f"响应: {response.text}")
                
        except Exception as e:
            print(f"❌ 登录异常: {e}")
            
        return False
    
    def test_message_apis(self):
        """测试消息相关API"""
        print("\n=== 消息API测试 ===")
        
        # 1. 测试消息列表API
        print("\n1. 测试消息列表API")
        try:
            response = requests.get(
                f'{self.base_url}/communications/api/messages/',
                headers=self.headers,
                params={'page': 1, 'page_size': 5},
                timeout=10
            )
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 消息列表获取成功，共 {len(data.get('messages', []))} 条消息")
                if data.get('pagination'):
                    print(f"分页信息: {data['pagination']}")
            else:
                print(f"❌ 消息列表获取失败: {response.text}")
                
        except Exception as e:
            print(f"❌ 消息列表API异常: {e}")
        
        # 2. 测试未读消息数量API
        print("\n2. 测试未读消息数量API")
        try:
            response = requests.get(
                f'{self.base_url}/communications/api/messages/unread-count/',
                headers=self.headers,
                timeout=10
            )
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 未读消息数量: {data.get('count', 0)}")
            else:
                print(f"❌ 未读消息数量获取失败: {response.text}")
                
        except Exception as e:
            print(f"❌ 未读消息数量API异常: {e}")
        
        # 3. 测试最近消息API
        print("\n3. 测试最近消息API")
        try:
            response = requests.get(
                f'{self.base_url}/communications/api/messages/recent/',
                headers=self.headers,
                timeout=10
            )
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 最近消息获取成功，共 {len(data.get('messages', []))} 条消息")
            else:
                print(f"❌ 最近消息获取失败: {response.text}")
                
        except Exception as e:
            print(f"❌ 最近消息API异常: {e}")
    
    def test_announcement_apis(self):
        """测试公告相关API"""
        print("\n=== 公告API测试 ===")
        
        # 1. 测试公告列表API
        print("\n1. 测试公告列表API")
        try:
            response = requests.get(
                f'{self.base_url}/communications/api/announcements/',
                headers=self.headers,
                params={'page': 1, 'page_size': 5},
                timeout=10
            )
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 公告列表获取成功，共 {len(data.get('announcements', []))} 条公告")
                if data.get('pagination'):
                    print(f"分页信息: {data['pagination']}")
                if data.get('stats'):
                    print(f"统计信息: {data['stats']}")
            else:
                print(f"❌ 公告列表获取失败: {response.text}")
                
        except Exception as e:
            print(f"❌ 公告列表API异常: {e}")
        
        # 2. 测试公告创建API
        print("\n2. 测试公告创建API")
        try:
            announcement_data = {
                'title': f'测试公告 - {datetime.now().strftime("%Y%m%d_%H%M%S")}',
                'content': '这是一个由API测试脚本创建的测试公告。请忽略此内容。',
                'announcement_type': 'SYSTEM',
                'is_published': True,
                'is_pinned': False
            }
            
            response = requests.post(
                f'{self.base_url}/communications/api/announcements/create/',
                json=announcement_data,
                headers=self.headers,
                timeout=10
            )
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 公告创建成功: {data.get('message')}")
                announcement_id = data.get('data', {}).get('announcement_id')
                
                # 3. 测试公告详情API
                if announcement_id:
                    print(f"\n3. 测试公告详情API (ID: {announcement_id})")
                    try:
                        response = requests.get(
                            f'{self.base_url}/communications/api/announcements/{announcement_id}/',
                            headers=self.headers,
                            timeout=10
                        )
                        print(f"状态码: {response.status_code}")
                        
                        if response.status_code == 200:
                            data = response.json()
                            announcement = data.get('announcement', {})
                            print(f"✅ 公告详情获取成功: {announcement.get('title')}")
                            print(f"查看次数: {announcement.get('content_info', {}).get('view_count', 0)}")
                        else:
                            print(f"❌ 公告详情获取失败: {response.text}")
                            
                    except Exception as e:
                        print(f"❌ 公告详情API异常: {e}")
                        
            else:
                print(f"❌ 公告创建失败: {response.text}")
                
        except Exception as e:
            print(f"❌ 公告创建API异常: {e}")
    
    def test_staff_data_api(self):
        """测试员工数据API"""
        print("\n=== 员工数据API测试 ===")
        
        try:
            response = requests.get(
                f'{self.base_url}/communications/api/staff-data/',
                headers=self.headers,
                timeout=10
            )
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                departments = data.get('departments', {})
                total_staff = sum(len(staff_list) for staff_list in departments.values())
                print(f"✅ 员工数据获取成功")
                print(f"部门数量: {len(departments)}")
                print(f"员工总数: {total_staff}")
                
                # 显示前几个部门的信息
                for dept_name, staff_list in list(departments.items())[:3]:
                    print(f"  - {dept_name}: {len(staff_list)}人")
                    
            else:
                print(f"❌ 员工数据获取失败: {response.text}")
                
        except Exception as e:
            print(f"❌ 员工数据API异常: {e}")
    
    def test_message_operations(self):
        """测试消息操作功能"""
        print("\n=== 消息操作测试 ===")
        
        # 测试批量标记已读API
        print("\n1. 测试批量标记已读API")
        try:
            mark_data = {
                'action': 'mark_all_read',
                'message_type': '',
                'priority': ''
            }
            
            response = requests.post(
                f'{self.base_url}/communications/api/messages/batch-read/',
                json=mark_data,
                headers=self.headers,
                timeout=10
            )
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 批量标记成功: {data.get('message')}")
                print(f"更新数量: {data.get('data', {}).get('updated_count', 0)}")
                print(f"剩余未读: {data.get('data', {}).get('remaining_unread', 0)}")
            else:
                print(f"❌ 批量标记失败: {response.text}")
                
        except Exception as e:
            print(f"❌ 批量标记API异常: {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("开始Communications模块API测试")
        print("=" * 60)
        
        # 首先登录
        if not self.login():
            print("登录失败，无法继续测试")
            return
        
        # 执行各项测试
        self.test_message_apis()
        self.test_announcement_apis()
        self.test_staff_data_api()
        self.test_message_operations()
        
        print("\n" + "=" * 60)
        print("Communications模块API测试完成")

def main():
    """主函数"""
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tester = CommunicationsAPITester()
    tester.run_all_tests()

if __name__ == '__main__':
    main()