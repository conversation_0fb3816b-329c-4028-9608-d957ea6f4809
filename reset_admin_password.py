#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
重置管理员密码
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
django.setup()

from organizations.models import Staff

def reset_admin_password():
    """重置管理员密码为123456"""
    print("=== 重置管理员密码 ===")
    
    # 查找admin用户
    admin = Staff.objects.filter(username='admin').first()
    if not admin:
        print("❌ 未找到admin用户")
        return
    
    print(f"找到用户: {admin.username} - {admin.name}")
    print(f"当前密码哈希: {admin.password}")
    
    # 测试当前密码
    test_passwords = ['123456', 'admin', 'password', '']
    for pwd in test_passwords:
        if admin.check_password(pwd):
            print(f"✅ 当前密码是: '{pwd}'")
            break
    else:
        print("❌ 无法确定当前密码")
    
    # 重置密码
    print("\n重置密码为: 123456")
    admin.set_password('123456')
    admin.save()
    
    print(f"新密码哈希: {admin.password}")
    print(f"验证新密码: {admin.check_password('123456')}")
    
    if admin.check_password('123456'):
        print("✅ 密码重置成功！")
    else:
        print("❌ 密码重置失败！")

if __name__ == '__main__':
    reset_admin_password()
