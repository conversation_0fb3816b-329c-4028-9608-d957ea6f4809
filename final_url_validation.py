#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
最终URL命名空间验证脚本
检查所有修复是否完成
"""

import os
import sys

# 处理Windows编码问题
if sys.platform.startswith('win'):
    import io
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

def check_for_remaining_errors():
    """检查是否还有剩余的URL错误"""
    print("=== 检查剩余的URL命名空间错误 ===")
    
    # 检查的文件类型和路径
    check_paths = [
        ('templates', '*.html'),
        ('organizations', '*.py'),
        ('evaluations', '*.py'),
        ('reports', '*.py'),
    ]
    
    # 错误的URL模式
    error_patterns = [
        "'organizations:staff_list'",
        "'organizations:department_list'",
        "'organizations:permissions_manage'",
        "'organizations:anonymous_codes_manage'",
        "'organizations:dashboard'",
        "'organizations:login'",
        "'organizations:logout'",
        "'evaluations:template_list'",
        "'evaluations:batch_list'",
        "'reports:report_list'",
        "redirect('organizations:staff_list')",
        "redirect('organizations:department_list')",
        "redirect('admin:permissions:",
    ]
    
    errors_found = []
    
    for directory, pattern in check_paths:
        dir_path = os.path.join(os.path.dirname(__file__), directory)
        if os.path.exists(dir_path):
            for root, dirs, files in os.walk(dir_path):
                for file in files:
                    if (pattern == '*.html' and file.endswith('.html')) or \
                       (pattern == '*.py' and file.endswith('.py')):
                        
                        file_path = os.path.join(root, file)
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                                
                                for error_pattern in error_patterns:
                                    if error_pattern in content:
                                        rel_path = os.path.relpath(file_path, os.path.dirname(__file__))
                                        errors_found.append(f"{rel_path}: {error_pattern}")
                                        
                        except Exception as e:
                            print(f"⚠️  无法读取文件 {file_path}: {str(e)}")
    
    if errors_found:
        print("❌ 发现剩余的URL错误:")
        for error in errors_found:
            print(f"   - {error}")
        return False
    else:
        print("✅ 没有发现剩余的URL命名空间错误")
        return True

def check_specific_fixes():
    """检查具体的修复是否已应用"""
    print("\n=== 检查具体修复 ===")
    
    fixes_to_check = [
        {
            'file': 'organizations/views_permissions.py',
            'should_contain': "redirect('organizations:admin:permissions_manage')",
            'description': '权限管理重定向修复'
        },
        {
            'file': 'organizations/views_permissions.py', 
            'should_contain': "redirect('organizations:admin:staff_role_edit'",
            'description': '员工角色编辑重定向修复'
        },
        {
            'file': 'templates/admin/permissions/manage.html',
            'should_contain': "{% url 'organizations:admin:staff_list' %}",
            'description': '权限管理模板员工列表链接修复'
        },
        {
            'file': 'templates/admin/permissions/manage.html',
            'should_contain': "{% url 'organizations:admin:anonymous_codes_manage' %}",
            'description': '权限管理模板匿名编号链接修复'
        },
        {
            'file': 'templates/admin/anonymous/manage.html',
            'should_contain': "{% url 'organizations:admin:dashboard' %}",
            'description': '匿名编号管理模板仪表板链接修复'
        },
    ]
    
    all_fixed = True
    
    for fix in fixes_to_check:
        file_path = os.path.join(os.path.dirname(__file__), fix['file'])
        
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                    if fix['should_contain'] in content:
                        print(f"✅ {fix['description']}")
                    else:
                        print(f"❌ {fix['description']}")
                        all_fixed = False
                        
            except Exception as e:
                print(f"❌ 无法检查 {fix['description']}: {str(e)}")
                all_fixed = False
        else:
            print(f"❌ 文件不存在: {fix['file']}")
            all_fixed = False
    
    return all_fixed

def create_fix_summary():
    """创建修复总结"""
    print("\n=== 修复总结 ===")
    
    fixes_applied = [
        "✅ 修复了权限管理页面 'set' object is not subscriptable 错误",
        "✅ 修复了匿名编号管理页面 'headers' 属性错误", 
        "✅ 修复了 'admin' is not a registered namespace 错误",
        "✅ 修复了 Reverse for 'staff_list' not found 错误",
        "✅ 修复了 Reverse for 'anonymous_codes_manage' not found 错误",
        "✅ 系统性修复了所有URL命名空间错误",
    ]
    
    for fix in fixes_applied:
        print(fix)
    
    print("\n🎯 主要修复内容:")
    print("   1. 将集合转换为列表以支持切片操作")
    print("   2. 正确使用 @method_decorator 装饰类视图方法")
    print("   3. 统一所有URL命名空间为 'app:admin:view_name' 格式")
    print("   4. 修复了模板文件中的错误URL引用")
    print("   5. 修复了视图文件中的错误重定向URL")

def main():
    """主函数"""
    print("最终URL命名空间验证")
    print("=" * 50)
    
    # 检查剩余错误
    no_remaining_errors = check_for_remaining_errors()
    
    # 检查具体修复
    fixes_verified = check_specific_fixes()
    
    # 创建总结
    create_fix_summary()
    
    # 最终结果
    print("\n" + "=" * 50)
    print("验证结果")
    print("=" * 50)
    
    if no_remaining_errors and fixes_verified:
        print("🎉 所有URL命名空间问题已完全修复！")
        print("\n💡 现在可以安全地重启服务器并测试以下页面：")
        print("   - /admin/permissions/ (权限管理)")
        print("   - /admin/anonymous-codes/ (匿名编号管理)")
        print("   - 所有页面间的导航链接")
        return True
    else:
        print("⚠️  仍有部分问题需要解决")
        if not no_remaining_errors:
            print("   - 仍有URL命名空间错误存在")
        if not fixes_verified:
            print("   - 部分具体修复未能验证成功")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)