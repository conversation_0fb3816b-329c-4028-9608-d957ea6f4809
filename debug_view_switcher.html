<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视图切换器调试页面</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <!-- 我们的CSS -->
    <link rel="stylesheet" href="static/css/view-switcher.css">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">视图切换器测试</h1>
        
        <!-- 搜索和筛选 -->
        <div class="bg-white rounded-lg shadow mb-6 p-6">
            <div class="mb-4">
                <input type="text" id="searchInput" placeholder="搜索员工..." class="w-full px-4 py-2 border border-gray-300 rounded-md">
            </div>
            
            <!-- 视图切换器 -->
            <div class="flex items-center justify-between pt-4 border-t border-gray-200">
                <div class="view-switcher flex items-center space-x-3">
                    <span class="text-sm text-gray-600 font-medium">显示方式：</span>
                    <div class="flex border border-gray-300 rounded-md overflow-hidden bg-white">
                        <button 
                            id="list-view-btn" 
                            class="view-switcher-btn px-3 py-2 text-sm font-medium bg-blue-500 text-white flex items-center space-x-2">
                            <i data-lucide="list" class="w-4 h-4"></i>
                            <span>列表</span>
                        </button>
                        <button 
                            id="card-view-btn" 
                            class="view-switcher-btn px-3 py-2 text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 flex items-center space-x-2">
                            <i data-lucide="grid" class="w-4 h-4"></i>
                            <span>卡片</span>
                        </button>
                    </div>
                </div>
                
                <div class="text-sm text-gray-500">
                    <span id="search-result-count">共 3 名员工</span>
                </div>
            </div>
        </div>
        
        <!-- 视图容器 -->
        <div id="data-container" class="view-container">
            <!-- 列表视图 -->
            <div id="list-view" class="list-view">
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">员工列表</h3>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">员工信息</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">部门</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <!-- 测试数据 -->
                                <tr class="staff-row" 
                                    data-department="1" 
                                    data-position="5"
                                    data-item='{
                                        "id": "1",
                                        "name": "张三",
                                        "employee_no": "E001",
                                        "email": "<EMAIL>",
                                        "department_name": "技术部",
                                        "department_code": "TECH",
                                        "position_name": "高级开发工程师",
                                        "position_level": 5,
                                        "is_active": true,
                                        "is_admin": false,
                                        "is_manager": false,
                                        "anonymous_code": "ABC123",
                                        "anonymous_code_type": "secure"
                                    }'>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                                <span class="text-sm font-medium">张</span>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900">张三</div>
                                                <div class="text-sm text-gray-500">E001</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">技术部</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">在职</span>
                                    </td>
                                </tr>
                                
                                <tr class="staff-row" 
                                    data-department="2" 
                                    data-position="3"
                                    data-item='{
                                        "id": "2",
                                        "name": "李四",
                                        "employee_no": "E002",
                                        "email": "<EMAIL>",
                                        "department_name": "销售部",
                                        "department_code": "SALES",
                                        "position_name": "销售经理",
                                        "position_level": 3,
                                        "is_active": true,
                                        "is_admin": false,
                                        "is_manager": true,
                                        "anonymous_code": "DEF456",
                                        "anonymous_code_type": "legacy"
                                    }'>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                                <span class="text-sm font-medium">李</span>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900">李四</div>
                                                <div class="text-sm text-gray-500">E002</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">销售部</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">在职</span>
                                    </td>
                                </tr>
                                
                                <tr class="staff-row" 
                                    data-department="1" 
                                    data-position="2"
                                    data-item='{
                                        "id": "3",
                                        "name": "王五",
                                        "employee_no": "E003",
                                        "email": "<EMAIL>",
                                        "department_name": "技术部",
                                        "department_code": "TECH",
                                        "position_name": "初级开发工程师",
                                        "position_level": 2,
                                        "is_active": false,
                                        "is_admin": false,
                                        "is_manager": false,
                                        "anonymous_code": "",
                                        "anonymous_code_type": "none"
                                    }'>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                                <span class="text-sm font-medium">王</span>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900">王五</div>
                                                <div class="text-sm text-gray-500">E003</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">技术部</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">离职</span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- 卡片视图 -->
            <div id="card-view" class="card-view" style="display: none;">
                <!-- 卡片内容将由JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <!-- 我们的JavaScript -->
    <script src="static/js/view-switcher.js"></script>
    
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('页面加载完成');
        
        // 初始化Lucide图标
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
            console.log('Lucide图标初始化成功');
        } else {
            console.error('Lucide未加载');
        }
        
        // 测试配置
        window.viewSwitcherConfig = {
            containerSelector: '#data-container',
            listViewSelector: '#list-view',
            cardViewSelector: '#card-view',
            listBtnSelector: '#list-view-btn',
            cardBtnSelector: '#card-view-btn',
            searchSelector: '#searchInput',
            
            cardConfig: {
                template: 'staff',
                extractDataFromRows: true
            },
            
            onViewChange: function(oldView, newView) {
                console.log(`视图切换: ${oldView} -> ${newView}`);
            }
        };
        
        // 初始化视图切换器
        try {
            window.testViewSwitcher = new ViewSwitcher(window.viewSwitcherConfig);
            console.log('ViewSwitcher初始化成功');
        } catch (error) {
            console.error('ViewSwitcher初始化失败:', error);
        }
    });
    
    // 全局函数
    function viewStaffDetail(id) {
        alert('查看员工: ' + id);
    }
    
    function editStaff(id) {
        alert('编辑员工: ' + id);
    }
    </script>
</body>
</html>