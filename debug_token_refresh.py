#!/usr/bin/env python
"""
调试Token刷新功能
"""
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
django.setup()

from organizations.models import Staff
from common.security.jwt_auth import JWTAuthentication
import requests
import json

def debug_token_refresh():
    """调试token刷新功能"""
    try:
        print("🔍 调试Token刷新功能...")
        print("=" * 50)
        
        # 1. 获取测试用户
        staff = Staff.objects.get(username='testadmin')
        print(f"✅ 找到用户: {staff.name}")
        
        # 2. 生成初始tokens
        print("\n📝 生成初始tokens...")
        initial_tokens = JWTAuthentication.generate_tokens(staff)
        print(f"   Access Token: {initial_tokens['access_token'][:50]}...")
        print(f"   Refresh Token: {initial_tokens['refresh_token'][:50]}...")
        
        # 3. 直接测试refresh方法
        print("\n🔄 直接测试refresh方法...")
        refresh_token = initial_tokens['refresh_token']
        
        try:
            new_tokens = JWTAuthentication.refresh_access_token(refresh_token)
            print("✅ 直接refresh成功!")
            print(f"   新Access Token: {new_tokens['access_token'][:50]}...")
            print(f"   过期时间: {new_tokens['expires_in']}秒")
        except Exception as e:
            print(f"❌ 直接refresh失败: {e}")
            return False
        
        # 4. 测试Token刷新视图
        print("\n🌐 测试Token刷新视图...")
        refresh_url = "http://127.0.0.1:8000/admin/api/auth/refresh/"
        refresh_data = {"refresh_token": refresh_token}
        
        try:
            response = requests.post(
                refresh_url,
                json=refresh_data,
                headers={"Content-Type": "application/json"}
            )
            
            print(f"   状态码: {response.status_code}")
            print(f"   Content-Type: {response.headers.get('Content-Type')}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   JSON响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
                    
                    if data.get('success'):
                        print("✅ 视图refresh成功!")
                        return True
                    else:
                        print(f"❌ 视图refresh失败: {data.get('error')}")
                except json.JSONDecodeError:
                    print(f"   响应不是JSON: {response.text[:200]}...")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"   响应内容: {response.text[:200]}...")
                
        except Exception as e:
            print(f"❌ 视图测试失败: {e}")
            
        return False
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_middleware_exclusion():
    """测试中间件排除路径"""
    print("\n🛡️ 测试中间件排除路径...")
    print("=" * 50)
    
    from common.security.middleware import AuthRequiredMiddleware
    
    middleware = AuthRequiredMiddleware(lambda x: None)
    
    # 创建模拟请求
    class MockRequest:
        def __init__(self, path):
            self.path = path
    
    test_paths = [
        '/admin/api/auth/refresh/',
        '/admin/login/',
        '/admin/',
        '/admin/api/auth/token/',
    ]
    
    for path in test_paths:
        request = MockRequest(path)
        requires_auth = middleware._requires_auth(request)
        print(f"   {path}: {'需要认证' if requires_auth else '免认证'}")

if __name__ == '__main__':
    print("🚀 开始调试Token刷新功能...")
    
    # 测试中间件排除
    test_middleware_exclusion()
    
    # 调试refresh功能
    success = debug_token_refresh()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Token刷新功能正常!")
    else:
        print("⚠️  Token刷新功能需要进一步修复")