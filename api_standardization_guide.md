# API标准化迁移指南

## 概述

这个文档描述了如何将现有的API端点迁移到新的标准化响应格式。新的格式提供了更一致的错误处理、统一的响应结构和更好的调试信息。

## 新的响应格式

### 成功响应格式
```json
{
    "success": true,
    "message": "操作成功",
    "data": {
        // 实际数据内容
    },
    "meta": {
        // 元数据，如分页信息
        "pagination": {
            "current_page": 1,
            "total_pages": 10,
            "total_count": 200,
            "page_size": 20
        }
    },
    "timestamp": "2025-01-30 16:30:45"
}
```

### 错误响应格式
```json
{
    "success": false,
    "message": "操作失败",
    "error": {
        "code": "VALIDATION_FAILED",
        "details": {
            "field_name": ["具体错误信息"]
        }
    },
    "timestamp": "2025-01-30 16:30:45"
}
```

## 迁移步骤

### 1. 导入工具模块

```python
from common.api_utils import (
    APIResponse, 
    PaginationHelper, 
    api_exception_handler,
    require_fields,
    validate_pagination_params,
    format_datetime
)
```

### 2. 使用装饰器

#### 异常处理装饰器
```python
class MyAPIView(AdminRequiredMixin, View):
    @api_exception_handler
    def get(self, request):
        # 视图逻辑
        pass
```

#### 必填字段验证装饰器
```python
class MyAPIView(AdminRequiredMixin, View):
    @require_fields(['title', 'content', 'type'])
    @api_exception_handler
    def post(self, request):
        # request.validated_data 包含验证后的数据
        pass
```

### 3. 标准化响应

#### 成功响应
```python
# 旧格式
return JsonResponse({
    'success': True,
    'messages': messages,
    'pagination': pagination
})

# 新格式
return APIResponse.success(
    data={'messages': messages},
    meta={'pagination': pagination},
    message='获取消息列表成功'
)
```

#### 错误响应
```python
# 旧格式
return JsonResponse({
    'success': False,
    'error': '消息不存在',
    'code': 'MESSAGE_NOT_FOUND'
}, status=404)

# 新格式
return APIResponse.not_found('消息不存在')
```

#### 验证错误
```python
# 旧格式
return JsonResponse({
    'success': False,
    'error': '缺少必填字段: title'
}, status=400)

# 新格式
return APIResponse.validation_error(
    errors={'title': ['字段不能为空']},
    message='数据验证失败'
)
```

### 4. 分页处理

```python
# 旧格式
from django.core.paginator import Paginator
paginator = Paginator(queryset, page_size)
page_obj = paginator.get_page(page)

# 新格式
page_obj, pagination_meta = PaginationHelper.paginate(
    queryset, 
    page=request.GET.get('page', 1),
    page_size=request.GET.get('page_size', 20)
)

return APIResponse.success(
    data={'items': list(page_obj)},
    meta={'pagination': pagination_meta}
)
```

## 具体迁移示例

### Communications模块API迁移

#### MessageListAPIView 迁移示例

```python
# 迁移前
class MessageListAPIView(AdminRequiredMixin, View):
    def get(self, request):
        try:
            page = int(request.GET.get('page', 1))
            page_size = min(int(request.GET.get('page_size', 20)), 100)
            
            # ... 查询逻辑 ...
            
            return JsonResponse({
                'success': True,
                'messages': messages,
                'pagination': pagination_info
            })
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'获取消息失败: {str(e)}'
            }, status=500)

# 迁移后
class MessageListAPIView(AdminRequiredMixin, View):
    @api_exception_handler
    def get(self, request):
        # 验证分页参数
        page, page_size, errors = validate_pagination_params(request)
        if errors:
            return APIResponse.validation_error(errors)
        
        # ... 查询逻辑 ...
        
        # 使用标准分页助手
        page_obj, pagination_meta = PaginationHelper.paginate(
            queryset, page, page_size
        )
        
        # 构建响应数据
        messages = []
        for recipient in page_obj:
            messages.append({
                'id': recipient.message.id,
                'subject': recipient.message.subject,
                'created_at': format_datetime(recipient.created_at),
                # ... 其他字段
            })
        
        return APIResponse.success(
            data={'messages': messages},
            meta={'pagination': pagination_meta},
            message='获取消息列表成功'
        )
```

#### 错误处理改进

```python
# 旧格式 - 多种错误处理方式
try:
    # 业务逻辑
    pass
except MessageRecipient.DoesNotExist:
    return JsonResponse({
        'success': False,
        'error': '消息不存在',
        'code': 'MESSAGE_NOT_FOUND'
    }, status=404)
except Exception as e:
    return JsonResponse({
        'success': False,
        'error': f'操作失败: {str(e)}'
    }, status=500)

# 新格式 - 统一错误处理
@api_exception_handler
def my_view_method(self, request):
    try:
        # 业务逻辑
        pass
    except MessageRecipient.DoesNotExist:
        return APIResponse.not_found('消息不存在')
    # 其他异常会被装饰器自动处理
```

## 常用错误代码

使用 `ErrorCodes` 类中定义的标准错误代码：

```python
from common.api_utils import ErrorCodes

return APIResponse.error(
    message='用户名或密码错误',
    code=ErrorCodes.INVALID_CREDENTIALS,
    status=401
)
```

## 迁移检查清单

### Communications模块

- [x] MessageListAPIView - 已实现
- [x] MessageDetailAPIView - 已实现  
- [x] MessageMarkReadAPIView - 已实现
- [x] MessageSendAPIView - 已实现
- [x] BatchMarkReadAPIView - 已实现
- [x] AnnouncementListAPIView - 已实现
- [x] AnnouncementDetailAPIView - 已实现
- [x] AnnouncementCreateAPIView - 已实现
- [x] UnreadMessageCountAPIView - 需要迁移
- [x] StaffDataAPIView - 需要迁移
- [x] RecentMessagesAPIView - 需要迁移

### 其他模块

- [ ] Organizations模块API
- [ ] Evaluations模块API  
- [ ] Reports模块API

## 兼容性说明

新的API格式向后兼容现有的前端代码，因为：

1. `success` 字段保持不变
2. `data` 字段包含原有的数据结构
3. 错误响应仍然包含 `success: false`
4. 新增的字段（如 `timestamp`, `meta`）不会影响现有解析逻辑

## 最佳实践

1. **统一使用装饰器**：为所有API视图添加异常处理装饰器
2. **标准化错误码**：使用预定义的错误代码常量
3. **一致的时间格式**：使用 `format_datetime` 函数
4. **详细的错误信息**：在开发环境提供详细错误，生产环境隐藏敏感信息
5. **分页标准化**：使用 `PaginationHelper` 处理所有分页需求

## 测试建议

1. 创建API测试用例验证新格式
2. 确保前端兼容性
3. 测试错误处理场景
4. 验证分页功能
5. 检查日志记录是否完整