#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试数据初始化脚本
直接运行此脚本来创建企业考评系统的基础测试数据
适用于Windows环境下的Python执行
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    django.setup()
except Exception as e:
    print(f"Django环境设置失败: {e}")
    print("请确保在项目根目录下运行此脚本")
    sys.exit(1)

from organizations.models import Department, Position, Staff
from django.contrib.auth.hashers import make_password
from django.utils import timezone
import random
import string

def clear_existing_data():
    """清除现有测试数据"""
    print("清除现有数据...")
    
    # 按依赖关系顺序删除
    Staff.objects.all().delete()
    Position.objects.all().delete()
    Department.objects.all().delete()
    
    print("现有数据已清除")

def create_departments():
    """创建部门数据"""
    print("创建部门数据...")
    
    # 创建总经理室（顶级部门）
    gm_office, created = Department.objects.get_or_create(
        dept_code='GM',
        defaults={
            'name': '总经理室',
            'parent_department': None,
            'description': '企业最高管理机构，负责公司整体战略决策',
            'is_active': True,
            'sort_order': 1,
            'created_by': 'system'
        }
    )
    if created:
        print(f"  ✓ 创建部门: {gm_office.name}")

    # 创建6个业务部门
    departments_data = [
        {
            'code': 'HR', 'name': '人力资源部', 
            'desc': '负责人力资源管理、员工发展和企业文化建设'
        },
        {
            'code': 'IT', 'name': '信息技术部', 
            'desc': '负责信息系统建设、技术支持和数字化转型'
        },
        {
            'code': 'FIN', 'name': '财务部', 
            'desc': '负责财务管理、会计核算和资金运营'
        },
        {
            'code': 'MKT', 'name': '市场部', 
            'desc': '负责市场营销、品牌推广和客户关系管理'
        },
        {
            'code': 'OPS', 'name': '运营部', 
            'desc': '负责业务运营、流程管理和质量控制'
        },
        {
            'code': 'SALES', 'name': '销售部', 
            'desc': '负责销售业务、客户开发和业绩管理'
        },
    ]

    created_departments = [gm_office]
    
    for i, dept_data in enumerate(departments_data, 2):
        dept, created = Department.objects.get_or_create(
            dept_code=dept_data['code'],
            defaults={
                'name': dept_data['name'],
                'parent_department': gm_office,
                'description': dept_data['desc'],
                'is_active': True,
                'sort_order': i,
                'created_by': 'system'
            }
        )
        if created:
            print(f"  ✓ 创建部门: {dept.name}")
        created_departments.append(dept)
    
    return created_departments

def create_positions(departments):
    """创建职位数据"""
    print("创建职位数据...")
    
    # 职位层级数据（1-9级）
    positions_data = [
        {'level': 9, 'name': '总经理', 'is_mgmt': True, 'desc': '企业最高管理者'},
        {'level': 8, 'name': '部门经理', 'is_mgmt': True, 'desc': '部门最高负责人'},
        {'level': 7, 'name': '副经理', 'is_mgmt': True, 'desc': '部门副职管理者'},
        {'level': 6, 'name': '主管', 'is_mgmt': True, 'desc': '团队负责人'},
        {'level': 5, 'name': '副主管', 'is_mgmt': True, 'desc': '团队副职负责人'},
        {'level': 4, 'name': '高级专员', 'is_mgmt': False, 'desc': '资深专业人员'},
        {'level': 3, 'name': '专员', 'is_mgmt': False, 'desc': '专业技术人员'},
        {'level': 2, 'name': '助理专员', 'is_mgmt': False, 'desc': '初级专业人员'},
        {'level': 1, 'name': '实习生', 'is_mgmt': False, 'desc': '实习培训人员'},
    ]

    created_positions = []
    
    for dept in departments:
        for pos_data in positions_data:
            # 总经理室只有总经理职位
            if dept.dept_code == 'GM' and pos_data['level'] != 9:
                continue
            # 其他部门不设总经理职位
            if dept.dept_code != 'GM' and pos_data['level'] == 9:
                continue
                
            pos, created = Position.objects.get_or_create(
                department=dept,
                position_code=f"{dept.dept_code}_{pos_data['level']}",
                defaults={
                    'name': pos_data['name'],
                    'level': pos_data['level'],
                    'is_management': pos_data['is_mgmt'],
                    'description': f"{dept.name}{pos_data['name']} - {pos_data['desc']}",
                    'is_active': True,
                    'sort_order': pos_data['level'],
                    'created_by': 'system'
                }
            )
            if created:
                print(f"  ✓ 创建职位: {dept.name} - {pos.name}")
            created_positions.append(pos)
    
    return created_positions

def create_staff(departments):
    """创建员工数据"""
    print("创建员工数据...")
    
    # 核心管理员工数据
    core_staff_data = [
        {
            'username': 'admin', 'name': '系统管理员', 'dept': 'GM', 
            'employee_no': 'GM001', 'role': 'super_admin', 'level': 9,
            'email': '<EMAIL>', 'phone': '13800000001'
        },
        {
            'username': 'hr_manager', 'name': '张人事', 'dept': 'HR', 
            'employee_no': 'HR001', 'role': 'dept_manager', 'level': 8,
            'email': '<EMAIL>', 'phone': '13800000002'
        },
        {
            'username': 'it_manager', 'name': '李技术', 'dept': 'IT', 
            'employee_no': 'IT001', 'role': 'dept_manager', 'level': 8,
            'email': '<EMAIL>', 'phone': '13800000003'
        },
        {
            'username': 'fin_manager', 'name': '王会计', 'dept': 'FIN', 
            'employee_no': 'FIN001', 'role': 'dept_manager', 'level': 8,
            'email': '<EMAIL>', 'phone': '13800000004'
        },
        {
            'username': 'test_user', 'name': '测试用户', 'dept': 'IT', 
            'employee_no': 'IT002', 'role': 'employee', 'level': 4,
            'email': '<EMAIL>', 'phone': '13800000005'
        },
    ]

    created_staff = []
    
    # 创建核心员工
    for staff_info in core_staff_data:
        staff = create_single_staff(staff_info, departments)
        if staff:
            created_staff.append(staff)

    return created_staff

def create_single_staff(staff_info, departments):
    """创建单个员工"""
    try:
        # 找到对应部门
        department = next((d for d in departments if d.dept_code == staff_info['dept']), None)
        if not department:
            print(f"  ⚠ 找不到部门: {staff_info['dept']}")
            return None
        
        # 找到对应职位
        position = Position.objects.filter(
            department=department, 
            level=staff_info['level']
        ).first()
        
        if not position:
            print(f"  ⚠ 找不到职位: {department.name} - 级别{staff_info['level']}")
            return None
        
        staff, created = Staff.objects.get_or_create(
            username=staff_info['username'],
            defaults={
                'employee_no': staff_info['employee_no'],
                'name': staff_info['name'],
                'department': department,
                'position': position,
                'role': staff_info['role'],
                'password': make_password('123456'),  # 默认密码
                'is_active': True,
                'email': staff_info.get('email', f"{staff_info['username']}@company.com"),
                'phone': staff_info.get('phone', ''),
                'hire_date': timezone.now().date(),
                'created_by': 'system'
            }
        )
        
        if created:
            print(f"  ✓ 创建员工: {staff.name} ({staff.username})")
            # 生成匿名编号
            generate_anonymous_code(staff)
            
        return staff
        
    except Exception as e:
        print(f"  ✗ 创建员工失败 {staff_info['username']}: {e}")
        return None

def generate_anonymous_code(staff):
    """为员工生成匿名编号"""
    if not staff.anonymous_code:
        # 生成格式：部门代码(3位) + 职位代码(2位) + 随机数字(4位)
        dept_code = staff.department.dept_code[:3].upper()
        pos_code = str(staff.position.level).zfill(2) if staff.position else 'XX'
        random_num = ''.join(random.choices(string.digits, k=4))
        
        # 确保匿名编号唯一
        anonymous_code = f'{dept_code}{pos_code}{random_num}'
        counter = 0
        while Staff.objects.filter(anonymous_code=anonymous_code).exists() and counter < 100:
            random_num = ''.join(random.choices(string.digits, k=4))
            anonymous_code = f'{dept_code}{pos_code}{random_num}'
            counter += 1
        
        staff.anonymous_code = anonymous_code
        staff.save(update_fields=['anonymous_code'])
        
        print(f"    → 匿名编号: {anonymous_code}")

def assign_department_managers(departments, staff_list):
    """设置部门经理"""
    print("设置部门经理...")
    
    # 需要设置经理的部门和对应的用户名
    manager_assignments = [
        ('HR', 'hr_manager'),
        ('IT', 'it_manager'),
        ('FIN', 'fin_manager'),
    ]
    
    for dept_code, manager_username in manager_assignments:
        try:
            dept = next((d for d in departments if d.dept_code == dept_code), None)
            manager = next((s for s in staff_list if s.username == manager_username), None)
            
            if dept and manager:
                dept.manager = manager
                dept.save(update_fields=['manager'])
                print(f"  ✓ 设置 {dept.name} 经理: {manager.name}")
            else:
                print(f"  ⚠ 设置部门经理失败: {dept_code}-{manager_username}")
                
        except Exception as e:
            print(f"  ✗ 设置部门经理出错 {dept_code}: {e}")

def display_test_accounts():
    """显示测试账号信息"""
    print('\n' + '='*60)
    print('测试账号信息')
    print('='*60)
    
    test_accounts = [
        ('admin', '系统管理员', '超级管理员权限'),
        ('hr_manager', '人力资源经理', '部门管理权限'),
        ('it_manager', '信息技术经理', '部门管理权限'),
        ('fin_manager', '财务经理', '部门管理权限'),
        ('test_user', '测试用户', '普通员工权限'),
    ]
    
    for username, name, permission in test_accounts:
        print(f'用户名: {username:<15} 密码: 123456    ({name} - {permission})')
    
    print('\n登录地址:')
    print('管理端: http://localhost:8000/admin/login/')
    print('匿名端: http://localhost:8000/anonymous/login/')
    
    # 显示匿名编号示例
    try:
        sample_staff = Staff.objects.filter(anonymous_code__isnull=False).first()
        if sample_staff:
            print(f'\n匿名编号示例: {sample_staff.anonymous_code} (用于匿名端登录)')
    except:
        pass
    
    print('='*60)

def main():
    """主函数"""
    print("开始创建企业考评系统测试数据...")
    print(f"Django版本: {django.get_version()}")
    
    try:
        # 清除现有数据（可选）
        # clear_existing_data()
        
        # 创建基础数据
        departments = create_departments()
        positions = create_positions(departments)
        staff_list = create_staff(departments)
        
        # 设置部门经理
        assign_department_managers(departments, staff_list)
        
        print("\n测试数据创建完成！")
        display_test_accounts()
        
        # 显示统计信息
        print(f"\n统计信息:")
        print(f"部门数量: {Department.objects.count()}")
        print(f"职位数量: {Position.objects.count()}")
        print(f"员工数量: {Staff.objects.count()}")
        
    except Exception as e:
        print(f"\n创建测试数据时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()