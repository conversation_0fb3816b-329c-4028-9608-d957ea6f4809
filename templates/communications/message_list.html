{% extends 'admin_base.html' %}
{% load static %}
{% load table_extras %}

{% block title %}消息中心 - 管理后台{% endblock %}

{% block breadcrumb %}
<a href="{% url 'organizations:admin:dashboard' %}" class="text-blue-600 hover:text-blue-800">首页</a>
<i data-lucide="chevron-right" class="w-4 h-4 breadcrumb-separator"></i>
<span class="text-gray-900">消息中心</span>
{% endblock %}

{% block breadcrumb_current %}消息中心{% endblock %}

{% block content %}
<div class="communications-container">
    <!-- 页面头部 -->
    <div class="page-header">
        <div class="page-header-content">
            <h1 class="page-title">消息中心</h1>
            <p class="page-description">查看和管理您的消息通知</p>
        </div>
        <div class="page-header-actions">
            {% include 'components/base/button.html' with type='primary' size='md' text='撰写消息' icon='plus' onclick='openComposeModal()' %}
            {% include 'components/base/button.html' with type='secondary' size='md' text='批量操作' icon='check-square' onclick='toggleBatchMode()' %}
        </div>
    </div>

    <!-- 消息统计卡片 -->
    <div class="message-stats">
        <div class="stats-grid">
            {% include 'components/base/card.html' with variant='default' extra_classes='stats-card' %}
                <div class="stats-content">
                    <div class="stats-icon stats-icon-total">
                        <i data-lucide="mail" class="w-6 h-6"></i>
                    </div>
                    <div class="stats-info">
                        <div class="stats-value" id="total-messages">{{ stats.total|default:0 }}</div>
                        <div class="stats-label">全部消息</div>
                    </div>
                </div>
            {% endinclude %}

            {% include 'components/base/card.html' with variant='primary' extra_classes='stats-card' %}
                <div class="stats-content">
                    <div class="stats-icon stats-icon-unread">
                        <i data-lucide="mail-open" class="w-6 h-6"></i>
                    </div>
                    <div class="stats-info">
                        <div class="stats-value" id="unread-messages">{{ stats.unread|default:0 }}</div>
                        <div class="stats-label">未读消息</div>
                    </div>
                </div>
            {% endinclude %}

            {% include 'components/base/card.html' with variant='success' extra_classes='stats-card' %}
                <div class="stats-content">
                    <div class="stats-icon stats-icon-read">
                        <i data-lucide="check-circle" class="w-6 h-6"></i>
                    </div>
                    <div class="stats-info">
                        <div class="stats-value" id="read-messages">{{ stats.read|default:0 }}</div>
                        <div class="stats-label">已读消息</div>
                    </div>
                </div>
            {% endinclude %}

            {% include 'components/base/card.html' with variant='warning' extra_classes='stats-card' %}
                <div class="stats-content">
                    <div class="stats-icon stats-icon-starred">
                        <i data-lucide="star" class="w-6 h-6"></i>
                    </div>
                    <div class="stats-info">
                        <div class="stats-value" id="starred-messages">{{ stats.starred|default:0 }}</div>
                        <div class="stats-label">星标消息</div>
                    </div>
                </div>
            {% endinclude %}
        </div>
    </div>

    <!-- 消息列表表格 -->
    {% include 'components/data/table.html' with id='messageTable' columns=columns data=messages selectable=True search=search_config filters=filters actions=table_actions row_actions=row_actions pagination=pagination info=table_info empty_text='暂无消息' empty_icon='mail-x' %}

    <!-- 批量操作工具栏 -->
    <div id="batch-toolbar" class="batch-toolbar hidden">
        <div class="batch-toolbar-content">
            <div class="batch-info">
                已选择 <span id="batch-count">0</span> 条消息
            </div>
            <div class="batch-actions">
                {% include 'components/base/button.html' with type='primary' size='sm' text='标记已读' icon='check' onclick='batchMarkRead()' %}
                {% include 'components/base/button.html' with type='secondary' size='sm' text='标记未读' icon='mail' onclick='batchMarkUnread()' %}
                {% include 'components/base/button.html' with type='warning' size='sm' text='加星标' icon='star' onclick='batchStar()' %}
                {% include 'components/base/button.html' with type='danger' size='sm' text='删除' icon='trash-2' onclick='batchDelete()' %}
                {% include 'components/base/button.html' with type='ghost' size='sm' text='取消' onclick='cancelBatchMode()' %}
            </div>
        </div>
    </div>
</div>

<!-- 撰写消息模态框 -->
{% include 'components/overlays/modal.html' with id='composeModal' title='撰写消息' size='lg' %}
    <form id="composeForm" class="compose-form">
        {% include 'components/base/input.html' with type='text' name='recipient' label='收件人' placeholder='请输入收件人姓名或工号' required=True prefix_icon='user' %}
        
        {% include 'components/base/input.html' with type='text' name='subject' label='主题' placeholder='请输入消息主题' required=True prefix_icon='mail' %}
        
        {% include 'components/base/input.html' with type='select' name='priority' label='优先级' options=priority_options %}
        
        {% include 'components/base/input.html' with type='textarea' name='content' label='消息内容' placeholder='请输入消息内容...' rows=6 required=True %}
        
        <div class="form-actions">
            {% include 'components/base/button.html' with type='primary' size='md' text='发送' icon='send' onclick='sendMessage()' %}
            {% include 'components/base/button.html' with type='secondary' size='md' text='保存草稿' icon='save' onclick='saveDraft()' %}
            {% include 'components/base/button.html' with type='ghost' size='md' text='取消' onclick='closeModal("composeModal")' %}
        </div>
    </form>
{% endinclude %}

<!-- 消息详情模态框 -->
{% include 'components/overlays/modal.html' with id='messageDetailModal' title='消息详情' size='xl' %}
    <div id="messageDetailContent" class="message-detail-content">
        <!-- 消息详情内容将通过JavaScript动态加载 -->
    </div>
{% endinclude %}

<!-- Toast通知容器 -->
{% include 'components/notifications/toast.html' %}

<style>
/* 通信模块样式 */
.communications-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8;
}

/* 页面头部 */
.page-header {
    @apply flex items-center justify-between mb-8;
}

.page-header-content h1 {
    @apply text-2xl font-bold text-gray-900;
}

.page-header-content p {
    @apply text-gray-600 mt-1;
}

.page-header-actions {
    @apply flex items-center space-x-3;
}

/* 消息统计 */
.message-stats {
    @apply mb-8;
}

.stats-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6;
}

.stats-card {
    @apply cursor-pointer hover:shadow-md transition-shadow;
}

.stats-content {
    @apply flex items-center space-x-4 p-6;
}

.stats-icon {
    @apply flex-shrink-0 p-3 rounded-lg;
}

.stats-icon-total {
    @apply bg-gray-100 text-gray-600;
}

.stats-icon-unread {
    @apply bg-blue-100 text-blue-600;
}

.stats-icon-read {
    @apply bg-green-100 text-green-600;
}

.stats-icon-starred {
    @apply bg-yellow-100 text-yellow-600;
}

.stats-info {
    @apply flex-1;
}

.stats-value {
    @apply text-2xl font-bold text-gray-900;
}

.stats-label {
    @apply text-sm text-gray-600 mt-1;
}

/* 批量操作工具栏 */
.batch-toolbar {
    @apply fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-40;
    animation: slideUp 0.3s ease-out;
}

.batch-toolbar.hidden {
    @apply transform translate-y-full;
}

.batch-toolbar-content {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex items-center justify-between;
}

.batch-info {
    @apply text-sm font-medium text-gray-700;
}

.batch-actions {
    @apply flex items-center space-x-3;
}

/* 撰写表单 */
.compose-form {
    @apply space-y-6;
}

.form-actions {
    @apply flex items-center justify-end space-x-3 pt-6 border-t border-gray-200;
}

/* 消息详情 */
.message-detail-content {
    @apply space-y-6;
}

/* 响应式调整 */
@media (max-width: 640px) {
    .page-header {
        @apply flex-col items-start space-y-4;
    }
    
    .page-header-actions {
        @apply w-full justify-center;
    }
    
    .batch-toolbar-content {
        @apply flex-col space-y-3;
    }
    
    .batch-actions {
        @apply w-full justify-center flex-wrap;
    }
}

/* 动画 */
@keyframes slideUp {
    from {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}
</style>

<script>
// 消息管理类
class MessageManager {
    constructor() {
        this.batchMode = false;
        this.selectedMessages = new Set();
        this.currentPage = 1;
        this.pageSize = 20;
        this.searchQuery = '';
        this.filters = {};
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadMessages();
    }
    
    bindEvents() {
        // 表格事件监听
        document.addEventListener('table:selection-change', (e) => {
            this.selectedMessages = new Set(e.detail.selectedRows);
            this.updateBatchToolbar();
        });
        
        document.addEventListener('table:search', (e) => {
            this.searchQuery = e.detail.query;
            this.loadMessages();
        });
        
        document.addEventListener('table:filter', (e) => {
            this.filters = e.detail.filters;
            this.loadMessages();
        });
        
        document.addEventListener('table:page-change', (e) => {
            this.currentPage = e.detail.page;
            this.loadMessages();
        });
        
        document.addEventListener('table:page-size-change', (e) => {
            this.pageSize = e.detail.size;
            this.currentPage = 1;
            this.loadMessages();
        });
    }
    
    async loadMessages() {
        try {
            const params = new URLSearchParams({
                page: this.currentPage,
                page_size: this.pageSize,
                search: this.searchQuery,
                ...this.filters
            });
            
            const response = await fetch(`/api/messages/?${params}`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                }
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.updateTable(data.data);
                this.updateStats(data.meta.stats);
            } else {
                showError('加载失败', data.message);
            }
        } catch (error) {
            console.error('加载消息失败:', error);
            showError('加载失败', '网络错误，请稍后重试');
        }
    }
    
    updateTable(data) {
        // 更新表格数据的逻辑
        // 这里需要根据实际的表格更新方式来实现
        location.reload(); // 临时方案，后续优化为动态更新
    }
    
    updateStats(stats) {
        if (stats) {
            document.getElementById('total-messages').textContent = stats.total || 0;
            document.getElementById('unread-messages').textContent = stats.unread || 0;
            document.getElementById('read-messages').textContent = stats.read || 0;
            document.getElementById('starred-messages').textContent = stats.starred || 0;
        }
    }
    
    updateBatchToolbar() {
        const count = this.selectedMessages.size;
        document.getElementById('batch-count').textContent = count;
        
        if (count > 0 && this.batchMode) {
            document.getElementById('batch-toolbar').classList.remove('hidden');
        } else {
            document.getElementById('batch-toolbar').classList.add('hidden');
        }
    }
    
    async viewMessage(messageId) {
        try {
            const response = await fetch(`/api/messages/${messageId}/`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                }
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showMessageDetail(data.data);
                
                // 标记为已读
                if (!data.data.is_read) {
                    this.markAsRead(messageId);
                }
            } else {
                showError('加载失败', data.message);
            }
        } catch (error) {
            console.error('查看消息失败:', error);
            showError('加载失败', '网络错误，请稍后重试');
        }
    }
    
    showMessageDetail(message) {
        const content = document.getElementById('messageDetailContent');
        content.innerHTML = `
            <div class="message-header">
                <div class="message-meta">
                    <div class="message-sender">
                        <img src="${message.sender.avatar || '/static/images/default-avatar.png'}" 
                             alt="${message.sender.name}" class="sender-avatar">
                        <div class="sender-info">
                            <div class="sender-name">${message.sender.name}</div>
                            <div class="sender-title">${message.sender.title || ''}</div>
                        </div>
                    </div>
                    <div class="message-time">
                        <span class="text-sm text-gray-500">${this.formatDate(message.created_at)}</span>
                    </div>
                </div>
                <h3 class="message-subject">${message.subject}</h3>
                <div class="message-priority priority-${message.priority}">
                    ${this.getPriorityText(message.priority)}
                </div>
            </div>
            <div class="message-body">
                <div class="message-content">${message.content}</div>
            </div>
            <div class="message-actions">
                <button type="button" class="btn btn-primary btn-sm" onclick="replyMessage(${message.id})">
                    <i data-lucide="reply" class="w-4 h-4 mr-2"></i>回复
                </button>
                <button type="button" class="btn btn-secondary btn-sm" onclick="forwardMessage(${message.id})">
                    <i data-lucide="forward" class="w-4 h-4 mr-2"></i>转发
                </button>
                <button type="button" class="btn btn-warning btn-sm" onclick="toggleStar(${message.id})">
                    <i data-lucide="${message.is_starred ? 'star-off' : 'star'}" class="w-4 h-4 mr-2"></i>
                    ${message.is_starred ? '取消星标' : '加星标'}
                </button>
                <button type="button" class="btn btn-danger btn-sm" onclick="deleteMessage(${message.id})">
                    <i data-lucide="trash-2" class="w-4 h-4 mr-2"></i>删除
                </button>
            </div>
        `;
        
        // 初始化Lucide图标
        if (window.lucide) {
            lucide.createIcons({ nameAttr: 'data-lucide' });
        }
        
        openModal('messageDetailModal');
    }
    
    formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) { // 1分钟内
            return '刚刚';
        } else if (diff < 3600000) { // 1小时内
            return `${Math.floor(diff / 60000)}分钟前`;
        } else if (diff < 86400000) { // 1天内
            return `${Math.floor(diff / 3600000)}小时前`;
        } else {
            return date.toLocaleDateString('zh-CN');
        }
    }
    
    getPriorityText(priority) {
        const priorityMap = {
            'low': '低',
            'normal': '普通',
            'high': '高',
            'urgent': '紧急'
        };
        return priorityMap[priority] || '普通';
    }
    
    async markAsRead(messageId) {
        try {
            const response = await fetch(`/api/messages/${messageId}/mark-read/`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
                    'Content-Type': 'application/json'
                }
            });
            
            const data = await response.json();
            
            if (data.success) {
                // 更新统计信息
                this.loadMessages();
            }
        } catch (error) {
            console.error('标记已读失败:', error);
        }
    }
    
    async batchMarkRead() {
        if (this.selectedMessages.size === 0) return;
        
        try {
            const response = await fetch('/api/messages/batch-mark-read/', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message_ids: Array.from(this.selectedMessages)
                })
            });
            
            const data = await response.json();
            handleApiResponse(data, '批量标记已读成功');
            
            if (data.success) {
                this.loadMessages();
                this.cancelBatchMode();
            }
        } catch (error) {
            console.error('批量标记已读失败:', error);
            showError('操作失败', '网络错误，请稍后重试');
        }
    }
    
    toggleBatchMode() {
        this.batchMode = !this.batchMode;
        
        if (this.batchMode) {
            showInfo('批量模式', '请选择要操作的消息');
        } else {
            this.cancelBatchMode();
        }
    }
    
    cancelBatchMode() {
        this.batchMode = false;
        this.selectedMessages.clear();
        
        // 清除所有选择
        const table = document.querySelector('#messageTable');
        if (table && table.dataTable) {
            table.dataTable.clearSelection();
        }
        
        document.getElementById('batch-toolbar').classList.add('hidden');
    }
}

// 全局函数
function openComposeModal() {
    openModal('composeModal');
}

function toggleBatchMode() {
    if (window.messageManager) {
        window.messageManager.toggleBatchMode();
    }
}

function cancelBatchMode() {
    if (window.messageManager) {
        window.messageManager.cancelBatchMode();
    }
}

function batchMarkRead() {
    if (window.messageManager) {
        window.messageManager.batchMarkRead();
    }
}

function viewMessage(messageId) {
    if (window.messageManager) {
        window.messageManager.viewMessage(messageId);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.messageManager = new MessageManager();
    
    // 初始化Lucide图标
    if (window.lucide) {
        lucide.createIcons({ nameAttr: 'data-lucide' });
    }
});
</script>
{% endblock %}