<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JWT登录 - 企业考评系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
    </style>
</head>
<body>
    <div class="login-card p-8 rounded-lg shadow-2xl w-full max-w-md">
        <div class="text-center mb-8">
            <div class="w-16 h-16 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <h1 class="text-2xl font-bold text-gray-900">企业考评系统</h1>
            <p class="text-gray-600 mt-2">JWT认证登录</p>
        </div>

        <form id="loginForm" class="space-y-6">
            <div>
                <label for="username" class="block text-sm font-medium text-gray-700">用户名</label>
                <input type="text" id="username" name="username" required
                       class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                       placeholder="请输入用户名" value="admin">
            </div>

            <div>
                <label for="password" class="block text-sm font-medium text-gray-700">密码</label>
                <input type="password" id="password" name="password" required
                       class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                       placeholder="请输入密码" value="123456">
            </div>

            <button type="submit" 
                    class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <span id="loginButtonText">登录</span>
                <svg id="loadingSpinner" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white hidden" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            </button>
        </form>

        <div id="errorMessage" class="mt-4 hidden">
            <div class="bg-red-50 border border-red-200 rounded-md p-3">
                <p class="text-sm text-red-600" id="errorText"></p>
            </div>
        </div>

        <div id="successMessage" class="mt-4 hidden">
            <div class="bg-green-50 border border-green-200 rounded-md p-3">
                <p class="text-sm text-green-600" id="successText"></p>
            </div>
        </div>

        <div class="mt-6 text-center">
            <p class="text-sm text-gray-600">
                测试账号：<br>
                <span class="font-mono">admin / 123456</span>
            </p>
        </div>

        <div class="mt-4 text-center">
            <a href="/jwt-test/" class="text-sm text-blue-600 hover:text-blue-800">→ 前往JWT功能测试页</a>
        </div>
    </div>

    <script src="/static/js/jwt-auth.js"></script>
    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const loginButton = document.querySelector('button[type="submit"]');
            const loginButtonText = document.getElementById('loginButtonText');
            const loadingSpinner = document.getElementById('loadingSpinner');
            const errorMessage = document.getElementById('errorMessage');
            const successMessage = document.getElementById('successMessage');
            
            // 隐藏之前的消息
            errorMessage.classList.add('hidden');
            successMessage.classList.add('hidden');
            
            // 显示加载状态
            loginButton.disabled = true;
            loginButtonText.textContent = '登录中...';
            loadingSpinner.classList.remove('hidden');
            
            try {
                const result = await authManager.login(username, password);
                
                if (result.success) {
                    // 显示成功消息
                    document.getElementById('successText').textContent = 
                        `登录成功！欢迎 ${result.user.name} (${result.user.role_display})`;
                    successMessage.classList.remove('hidden');
                    
                    // 2秒后跳转到管理后台
                    setTimeout(() => {
                        window.location.href = '/admin/';
                    }, 2000);
                } else {
                    // 显示错误消息
                    document.getElementById('errorText').textContent = result.message;
                    errorMessage.classList.remove('hidden');
                }
            } catch (error) {
                document.getElementById('errorText').textContent = '网络错误，请检查网络连接后重试';
                errorMessage.classList.remove('hidden');
            } finally {
                // 恢复按钮状态
                loginButton.disabled = false;
                loginButtonText.textContent = '登录';
                loadingSpinner.classList.add('hidden');
            }
        });

        // 如果已经登录，直接跳转到管理后台
        if (authManager.isAuthenticated()) {
            window.location.href = '/admin/';
        }
    </script>
</body>
</html>