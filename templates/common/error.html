<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ error_title }} - 通用员工考评系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .error-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            padding: 60px 40px;
            text-align: center;
            max-width: 600px;
            width: 90%;
            position: relative;
            overflow: hidden;
        }

        .error-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .error-icon {
            font-size: 80px;
            margin-bottom: 30px;
            display: block;
        }

        .error-404 .error-icon::before { content: '🔍'; }
        .error-403 .error-icon::before { content: '🔒'; }
        .error-500 .error-icon::before { content: '⚠️'; }
        .error-400 .error-icon::before { content: '❌'; }
        .error-default .error-icon::before { content: '❓'; }

        .status-code {
            font-size: 120px;
            font-weight: 700;
            color: #667eea;
            line-height: 1;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .error-title {
            font-size: 32px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 20px;
        }

        .error-message {
            font-size: 18px;
            color: #718096;
            line-height: 1.6;
            margin-bottom: 40px;
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
        }

        .error-details {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: left;
        }

        .error-details h4 {
            color: #2d3748;
            margin-bottom: 15px;
            font-size: 16px;
            font-weight: 600;
        }

        .error-details pre {
            background: #1a202c;
            color: #a0aec0;
            padding: 15px;
            border-radius: 8px;
            font-size: 12px;
            overflow-x: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .validation-errors {
            list-style: none;
            padding: 0;
        }

        .validation-errors li {
            background: #fed7d7;
            color: #c53030;
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 6px;
            font-size: 14px;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            text-decoration: none;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #f7fafc;
            color: #4a5568;
            border: 2px solid #e2e8f0;
        }

        .btn-secondary:hover {
            background: #edf2f7;
            border-color: #cbd5e0;
        }

        .help-info {
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid #e2e8f0;
            font-size: 14px;
            color: #718096;
        }

        .help-info a {
            color: #667eea;
            text-decoration: none;
        }

        .help-info a:hover {
            text-decoration: underline;
        }

        .request-info {
            background: #f0fff4;
            border: 1px solid #c6f6d5;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            font-size: 12px;
            color: #2f855a;
        }

        @media (max-width: 768px) {
            .error-container {
                padding: 40px 20px;
                margin: 20px;
            }

            .status-code {
                font-size: 80px;
            }

            .error-title {
                font-size: 24px;
            }

            .error-message {
                font-size: 16px;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 280px;
            }
        }

        /* 动画效果 */
        .error-container {
            animation: slideIn 0.6s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .error-icon {
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
    </style>
</head>
<body>
    <div class="error-container error-{{ status_code }}">
        <div class="error-icon"></div>
        
        <div class="status-code">{{ status_code }}</div>
        
        <h1 class="error-title">{{ error_title }}</h1>
        
        <p class="error-message">{{ error_message }}</p>

        {% if validation_errors %}
        <div class="error-details">
            <h4>验证错误详情：</h4>
            <ul class="validation-errors">
                {% for field, errors in validation_errors.items %}
                    {% for error in errors %}
                        <li>
                            {% if field != 'non_field_errors' and field != 'error' %}
                                <strong>{{ field }}:</strong> 
                            {% endif %}
                            {{ error }}
                        </li>
                    {% endfor %}
                {% endfor %}
            </ul>
        </div>
        {% endif %}

        {% if debug and status_code == 500 %}
        <div class="error-details">
            <h4>调试信息：</h4>
            <pre>{{ exception_info.traceback|default:"无详细信息" }}</pre>
        </div>
        {% endif %}

        <div class="action-buttons">
            <a href="javascript:history.back()" class="btn btn-secondary">
                ← 返回上页
            </a>
            
            <a href="/" class="btn btn-primary">
                🏠 返回首页
            </a>
            
            {% if status_code == 401 %}
            <a href="/login/" class="btn btn-primary">
                🔐 重新登录
            </a>
            {% endif %}
        </div>

        <div class="help-info">
            <p>
                如果问题持续存在，请联系系统管理员或 
                <a href="mailto:<EMAIL>">技术支持</a>
            </p>
            
            {% if debug %}
            <div class="request-info">
                <strong>请求信息：</strong><br>
                路径: {{ request_path }}<br>
                时间: {% now "Y-m-d H:i:s" %}<br>
                错误代码: {{ status_code }}
            </div>
            {% endif %}
        </div>
    </div>

    <script>
        // 自动刷新功能（适用于临时性错误）
        {% if status_code == 500 or status_code == 503 %}
        let refreshCount = parseInt(localStorage.getItem('errorRefreshCount') || '0');
        if (refreshCount < 3) {
            setTimeout(() => {
                localStorage.setItem('errorRefreshCount', (refreshCount + 1).toString());
                window.location.reload();
            }, 5000); // 5秒后自动刷新
        } else {
            localStorage.removeItem('errorRefreshCount');
        }
        {% endif %}

        // 按键快捷操作
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                history.back();
            } else if (e.key === 'Enter' || e.key === ' ') {
                window.location.href = '/';
            }
        });

        // 统计错误信息（用于改进系统）
        if (window.fetch && !window.location.hostname.includes('localhost')) {
            fetch('/api/error-report/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token }}'
                },
                body: JSON.stringify({
                    status_code: {{ status_code }},
                    error_title: '{{ error_title|escapejs }}',
                    request_path: '{{ request_path|escapejs }}',
                    user_agent: navigator.userAgent,
                    timestamp: new Date().toISOString()
                })
            }).catch(() => {}); // 静默处理错误报告失败
        }
    </script>
</body>
</html>