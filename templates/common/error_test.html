<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>异常处理测试 - 通用员工考评系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }

        .test-section {
            margin-bottom: 30px;
        }

        .test-section h3 {
            color: #555;
            margin-bottom: 15px;
            border-bottom: 2px solid #eee;
            padding-bottom: 5px;
        }

        .test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
        }

        .test-btn {
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            text-align: center;
            font-size: 14px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .test-btn.danger {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        }

        .test-btn.warning {
            background: linear-gradient(135deg, #feca57, #ff9ff3);
        }

        .test-btn.info {
            background: linear-gradient(135deg, #48dbfb, #0abde3);
        }

        .description {
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .api-test {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
        }

        .api-test button {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }

        #api-result {
            margin-top: 10px;
            padding: 10px;
            background: #f1f3f4;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 异常处理测试页面</h1>
        
        <div class="test-section">
            <h3>HTTP 标准错误测试</h3>
            <p class="description">测试标准HTTP错误状态码的处理和页面展示效果。</p>
            <div class="test-buttons">
                <a href="/test/error/404/" class="test-btn warning">404 页面不存在</a>
                <a href="/test/error/403/" class="test-btn danger">403 权限不足</a>
                <a href="/test/error/500/" class="test-btn danger">500 系统错误</a>
                <a href="/test/error/validation/" class="test-btn warning">验证错误</a>
            </div>
        </div>

        <div class="test-section">
            <h3>业务异常测试</h3>
            <p class="description">测试自定义业务异常的处理，包括不同类型的业务错误。</p>
            <div class="test-buttons">
                <a href="/test/error/business/" class="test-btn info">业务异常</a>
                <a href="/test/error/authentication/" class="test-btn danger">认证异常</a>
                <a href="/test/error/permission/" class="test-btn danger">权限异常</a>
                <a href="/test/error/security/" class="test-btn danger">安全异常</a>
            </div>
        </div>

        <div class="test-section">
            <h3>系统级错误测试</h3>
            <p class="description">测试数据库错误等系统级异常的处理。</p>
            <div class="test-buttons">
                <a href="/test/error/database/" class="test-btn danger">数据库错误</a>
            </div>
        </div>

        <div class="test-section">
            <h3>API 错误测试</h3>
            <p class="description">测试API请求的错误响应格式（JSON格式）。</p>
            <div class="api-test">
                <button onclick="testApiError()">测试 API 错误响应</button>
                <button onclick="testApiSuccess()">测试 API 正常响应</button>
                <div id="api-result"></div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px;">
            <a href="/" class="test-btn">返回首页</a>
        </div>
    </div>

    <script>
        async function testApiError() {
            const resultDiv = document.getElementById('api-result');
            try {
                const response = await fetch('/test/error/api/', {
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                const data = await response.json();
                resultDiv.textContent = `状态码: ${response.status}\n响应内容:\n${JSON.stringify(data, null, 2)}`;
                resultDiv.style.background = response.ok ? '#d4edda' : '#f8d7da';
            } catch (error) {
                resultDiv.textContent = `请求失败: ${error.message}`;
                resultDiv.style.background = '#f8d7da';
            }
        }

        async function testApiSuccess() {
            const resultDiv = document.getElementById('api-result');
            try {
                const response = await fetch('/test/error/api/', {
                    headers: {
                        'Accept': 'text/html'
                    }
                });
                const data = await response.json();
                resultDiv.textContent = `状态码: ${response.status}\n响应内容:\n${JSON.stringify(data, null, 2)}`;
                resultDiv.style.background = '#d4edda';
            } catch (error) {
                resultDiv.textContent = `请求失败: ${error.message}`;
                resultDiv.style.background = '#f8d7da';
            }
        }
    </script>
</body>
</html>