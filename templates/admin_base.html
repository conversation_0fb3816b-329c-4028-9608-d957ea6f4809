<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}管理后台 - 通用员工评价系统{% endblock %}</title>
    
    <!-- 设计系统 -->
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/design-system.css' %}">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <!-- 性能优化脚本 -->
    <script src="{% static 'js/performance-optimizer.js' %}"></script>
    <script src="{% static 'js/table-optimizer.js' %}"></script>
    
    <!-- 自定义样式 -->
    <style>
        /* 全局样式 */
        * {
            box-sizing: border-box;
        }
        
        body {
            font-family: var(--font-family-sans);
            line-height: var(--leading-normal);
            color: var(--gray-900);
            background-color: var(--gray-50);
        }
        
        /* 侧边栏样式 */
        .sidebar {
            transition: transform 0.3s ease-in-out;
        }
        
        .sidebar-collapsed {
            transform: translateX(-100%);
        }
        
        /* 主内容区域自适应 */
        .main-content {
            transition: margin-left 0.3s ease-in-out;
        }
        
        .main-content-expanded {
            margin-left: 0;
        }
        
        /* 导航项悬停效果 */
        .nav-item {
            transition: var(--transition-default);
            border-radius: var(--radius-md);
        }
        
        .nav-item:hover {
            background-color: var(--primary-50);
        }
        
        .nav-item.active {
            background-color: var(--primary-500);
            color: white;
        }
        
        .nav-item.active:hover {
            background-color: var(--primary-600);
        }
        
        /* 子菜单样式 */
        .nav-submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-in-out;
        }
        
        .nav-submenu.expanded {
            max-height: 500px;
        }
        
        /* 面包屑导航 */
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            color: var(--gray-600);
            font-size: var(--text-sm);
        }
        
        .breadcrumb-separator {
            color: var(--gray-400);
        }
        
        /* 用户下拉菜单 */
        .user-dropdown {
            transition: all 0.2s ease-in-out;
        }
        
        /* 响应式调整 */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                z-index: 50;
            }
            
            .main-content {
                margin-left: 0 !important;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body class="bg-gray-50">
    <!-- 侧边栏 -->
    <div id="sidebar" class="sidebar fixed inset-y-0 left-0 z-40 w-64 bg-white shadow-lg">
        <!-- 侧边栏头部 -->
        <div class="flex items-center justify-between p-4 border-b border-gray-200">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                    <i data-lucide="users" class="w-5 h-5 text-white"></i>
                </div>
                <span class="text-lg font-semibold text-gray-900">评价系统</span>
            </div>
            <button id="sidebarToggle" class="p-1 text-gray-500 hover:text-gray-700 md:hidden">
                <i data-lucide="x" class="w-5 h-5"></i>
            </button>
        </div>
        
        <!-- 导航菜单 -->
        <nav class="mt-4 px-4">
            <ul class="space-y-2">
                <!-- 仪表板 -->
                <li>
                    <a href="{% url 'organizations:admin:dashboard' %}" 
                       class="nav-item flex items-center px-3 py-2 text-sm font-medium text-gray-700 {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}">
                        <i data-lucide="home" class="w-5 h-5 mr-3"></i>
                        仪表板
                    </a>
                </li>
                
                <!-- 组织管理 -->
                <li>
                    <div class="nav-item-group">
                        <button onclick="toggleSubmenu('organization-menu')" 
                                class="nav-item flex items-center justify-between w-full px-3 py-2 text-sm font-medium text-gray-700">
                            <div class="flex items-center">
                                <i data-lucide="building" class="w-5 h-5 mr-3"></i>
                                组织管理
                            </div>
                            <i data-lucide="chevron-right" class="w-4 h-4 transform transition-transform" id="organization-menu-icon"></i>
                        </button>
                        <ul id="organization-menu" class="nav-submenu mt-1 ml-8 space-y-1">
                            <li>
                                <a href="{% url 'organizations:admin:department_list' %}" 
                                   class="nav-item flex items-center px-3 py-2 text-sm text-gray-600 {% if request.resolver_match.url_name == 'department_list' %}active{% endif %}">
                                    <i data-lucide="layers" class="w-4 h-4 mr-2"></i>
                                    部门管理
                                </a>
                            </li>
                            <li>
                                <a href="{% url 'organizations:admin:position_list' %}" 
                                   class="nav-item flex items-center px-3 py-2 text-sm text-gray-600 {% if request.resolver_match.url_name == 'position_list' %}active{% endif %}">
                                    <i data-lucide="badge" class="w-4 h-4 mr-2"></i>
                                    职位管理
                                </a>
                            </li>
                            <li>
                                <a href="{% url 'organizations:admin:staff_list' %}" 
                                   class="nav-item flex items-center px-3 py-2 text-sm text-gray-600 {% if request.resolver_match.url_name == 'staff_list' %}active{% endif %}">
                                    <i data-lucide="users" class="w-4 h-4 mr-2"></i>
                                    员工管理
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>
                
                <!-- 评价管理 -->
                <li>
                    <div class="nav-item-group">
                        <button onclick="toggleSubmenu('evaluation-menu')" 
                                class="nav-item flex items-center justify-between w-full px-3 py-2 text-sm font-medium text-gray-700">
                            <div class="flex items-center">
                                <i data-lucide="clipboard-list" class="w-5 h-5 mr-3"></i>
                                评价管理
                            </div>
                            <i data-lucide="chevron-right" class="w-4 h-4 transform transition-transform" id="evaluation-menu-icon"></i>
                        </button>
                        <ul id="evaluation-menu" class="nav-submenu mt-1 ml-8 space-y-1">
                            <li>
                                <a href="#" class="nav-item flex items-center px-3 py-2 text-sm text-gray-600">
                                    <i data-lucide="file-text" class="w-4 h-4 mr-2"></i>
                                    评价表管理
                                </a>
                            </li>
                            <li>
                                <a href="#" class="nav-item flex items-center px-3 py-2 text-sm text-gray-600">
                                    <i data-lucide="calendar" class="w-4 h-4 mr-2"></i>
                                    评价批次
                                </a>
                            </li>
                            <li>
                                <a href="#" class="nav-item flex items-center px-3 py-2 text-sm text-gray-600">
                                    <i data-lucide="user-check" class="w-4 h-4 mr-2"></i>
                                    匿名编号
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>
                
                <!-- 数据分析 -->
                <li>
                    <div class="nav-item-group">
                        <button onclick="toggleSubmenu('analytics-menu')" 
                                class="nav-item flex items-center justify-between w-full px-3 py-2 text-sm font-medium text-gray-700">
                            <div class="flex items-center">
                                <i data-lucide="bar-chart-3" class="w-5 h-5 mr-3"></i>
                                数据分析
                            </div>
                            <i data-lucide="chevron-right" class="w-4 h-4 transform transition-transform" id="analytics-menu-icon"></i>
                        </button>
                        <ul id="analytics-menu" class="nav-submenu mt-1 ml-8 space-y-1">
                            <li>
                                <a href="#" class="nav-item flex items-center px-3 py-2 text-sm text-gray-600">
                                    <i data-lucide="trending-up" class="w-4 h-4 mr-2"></i>
                                    评价统计
                                </a>
                            </li>
                            <li>
                                <a href="#" class="nav-item flex items-center px-3 py-2 text-sm text-gray-600">
                                    <i data-lucide="pie-chart" class="w-4 h-4 mr-2"></i>
                                    部门对比
                                </a>
                            </li>
                            <li>
                                <a href="#" class="nav-item flex items-center px-3 py-2 text-sm text-gray-600">
                                    <i data-lucide="download" class="w-4 h-4 mr-2"></i>
                                    数据导出
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>
                
                <!-- 通信中心 -->
                <li>
                    <a href="{% url 'communications:admin:message_center' %}" 
                       class="nav-item flex items-center px-3 py-2 text-sm font-medium text-gray-700 {% if 'communications' in request.resolver_match.namespace %}active{% endif %}">
                        <i data-lucide="mail" class="w-5 h-5 mr-3"></i>
                        通信中心
                        <span id="unread-badge" class="hidden ml-auto bg-red-500 text-white text-xs rounded-full px-2 py-0.5">0</span>
                    </a>
                </li>
                
                <!-- 系统设置 -->
                <li>
                    <div class="nav-item-group">
                        <button onclick="toggleSubmenu('system-menu')" 
                                class="nav-item flex items-center justify-between w-full px-3 py-2 text-sm font-medium text-gray-700">
                            <div class="flex items-center">
                                <i data-lucide="settings" class="w-5 h-5 mr-3"></i>
                                系统设置
                            </div>
                            <i data-lucide="chevron-right" class="w-4 h-4 transform transition-transform" id="system-menu-icon"></i>
                        </button>
                        <ul id="system-menu" class="nav-submenu mt-1 ml-8 space-y-1">
                            <li>
                                <a href="#" class="nav-item flex items-center px-3 py-2 text-sm text-gray-600">
                                    <i data-lucide="shield" class="w-4 h-4 mr-2"></i>
                                    权限管理
                                </a>
                            </li>
                            <li>
                                <a href="#" class="nav-item flex items-center px-3 py-2 text-sm text-gray-600">
                                    <i data-lucide="database" class="w-4 h-4 mr-2"></i>
                                    数据备份
                                </a>
                            </li>
                            <li>
                                <a href="#" class="nav-item flex items-center px-3 py-2 text-sm text-gray-600">
                                    <i data-lucide="activity" class="w-4 h-4 mr-2"></i>
                                    系统日志
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>
            </ul>
        </nav>
        
        <!-- 侧边栏底部 -->
        <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200">
            <div class="text-xs text-gray-500 text-center">
                版本 v1.0.0
            </div>
        </div>
    </div>
    
    <!-- 主内容区域 -->
    <div id="mainContent" class="main-content ml-64">
        <!-- 顶部导航栏 -->
        <header class="bg-white shadow-sm border-b border-gray-200">
            <div class="flex items-center justify-between px-6 py-4">
                <!-- 左侧：菜单切换和面包屑 -->
                <div class="flex items-center space-x-4">
                    <button id="menuToggle" class="p-2 text-gray-500 hover:text-gray-700 md:hidden">
                        <i data-lucide="menu" class="w-5 h-5"></i>
                    </button>
                    
                    <!-- 面包屑导航 -->
                    <nav class="breadcrumb">
                        {% block breadcrumb %}
                        <a href="{% url 'organizations:admin:dashboard' %}" class="text-blue-600 hover:text-blue-800">首页</a>
                        <i data-lucide="chevron-right" class="w-4 h-4 breadcrumb-separator"></i>
                        <span class="text-gray-900">{% block breadcrumb_current %}仪表板{% endblock %}</span>
                        {% endblock %}
                    </nav>
                </div>
                
                <!-- 右侧：搜索和用户菜单 -->
                <div class="flex items-center space-x-4">
                    <!-- 全局搜索 -->
                    <div class="hidden md:block">
                        <div class="relative">
                            <input type="text" 
                                   placeholder="搜索..." 
                                   class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <i data-lucide="search" class="absolute left-3 top-2.5 w-5 h-5 text-gray-400"></i>
                        </div>
                    </div>
                    
                    <!-- 通知图标 -->
                    <div class="relative">
                        <button class="p-2 text-gray-500 hover:text-gray-700 relative">
                            <i data-lucide="bell" class="w-5 h-5"></i>
                            <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center hidden" id="notification-badge">0</span>
                        </button>
                    </div>
                    
                    <!-- 用户菜单 -->
                    <div class="relative">
                        <button onclick="toggleUserMenu()" class="flex items-center space-x-3 p-2 text-gray-700 hover:text-gray-900">
                            <img src="/static/images/default-avatar.png" alt="用户头像" class="w-8 h-8 rounded-full">
                            <div class="hidden md:block text-left">
                                <div class="text-sm font-medium">{{ request.current_staff.name|default:'管理员' }}</div>
                                <div class="text-xs text-gray-500">{{ request.current_staff.position.name|default:'系统管理员' }}</div>
                            </div>
                            <i data-lucide="chevron-down" class="w-4 h-4"></i>
                        </button>
                        
                        <div id="userMenu" class="user-dropdown absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 hidden z-50">
                            <div class="py-1">
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i data-lucide="user" class="w-4 h-4 inline mr-2"></i>
                                    个人资料
                                </a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i data-lucide="settings" class="w-4 h-4 inline mr-2"></i>
                                    偏好设置
                                </a>
                                <div class="border-t border-gray-100"></div>
                                <a href="{% url 'organizations:admin:logout' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i data-lucide="log-out" class="w-4 h-4 inline mr-2"></i>
                                    退出登录
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>
        
        <!-- 页面内容 -->
        <main class="p-6">
            {% block content %}
            <div class="text-center">
                <h1 class="text-2xl font-bold text-gray-900 mb-4">欢迎使用通用员工评价系统</h1>
                <p class="text-gray-600">请从左侧菜单选择功能模块</p>
            </div>
            {% endblock %}
        </main>
    </div>
    
    <!-- 移动端遮罩层 -->
    <div id="overlay" class="fixed inset-0 bg-black bg-opacity-50 z-30 hidden md:hidden"></div>
    
    <!-- JavaScript -->
    <script>
        // 全局变量
        let sidebarOpen = true;
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化Lucide图标
            if (window.lucide) {
                lucide.createIcons({ nameAttr: 'data-lucide' });
            }
            
            // 绑定事件
            bindEvents();
            
            // 加载未读消息数量
            loadUnreadCount();
            
            // 每30秒检查一次未读消息
            setInterval(loadUnreadCount, 30000);
        });
        
        // 绑定事件
        function bindEvents() {
            // 侧边栏切换
            document.getElementById('menuToggle').addEventListener('click', toggleSidebar);
            document.getElementById('sidebarToggle').addEventListener('click', toggleSidebar);
            
            // 遮罩层点击关闭侧边栏
            document.getElementById('overlay').addEventListener('click', closeSidebar);
            
            // 点击外部关闭用户菜单
            document.addEventListener('click', function(event) {
                const userMenu = document.getElementById('userMenu');
                const userButton = event.target.closest('button');
                
                if (!userButton || userButton.getAttribute('onclick') !== 'toggleUserMenu()') {
                    userMenu.classList.add('hidden');
                }
            });
            
            // 响应式处理
            window.addEventListener('resize', function() {
                if (window.innerWidth >= 768) {
                    document.getElementById('overlay').classList.add('hidden');
                    document.getElementById('sidebar').classList.remove('sidebar-collapsed');
                }
            });
        }
        
        // 切换侧边栏
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            const overlay = document.getElementById('overlay');
            
            if (window.innerWidth < 768) {
                // 移动端
                sidebar.classList.toggle('sidebar-collapsed');
                overlay.classList.toggle('hidden');
            } else {
                // 桌面端
                sidebarOpen = !sidebarOpen;
                if (sidebarOpen) {
                    sidebar.classList.remove('sidebar-collapsed');
                    mainContent.classList.remove('main-content-expanded');
                } else {
                    sidebar.classList.add('sidebar-collapsed');
                    mainContent.classList.add('main-content-expanded');
                }
            }
        }
        
        // 关闭侧边栏
        function closeSidebar() {
            document.getElementById('sidebar').classList.add('sidebar-collapsed');
            document.getElementById('overlay').classList.add('hidden');
        }
        
        // 切换子菜单
        function toggleSubmenu(menuId) {
            const submenu = document.getElementById(menuId);
            const icon = document.getElementById(menuId + '-icon');
            
            submenu.classList.toggle('expanded');
            icon.classList.toggle('rotate-90');
        }
        
        // 切换用户菜单
        function toggleUserMenu() {
            const userMenu = document.getElementById('userMenu');
            userMenu.classList.toggle('hidden');
        }
        
        // 加载未读消息数量
        async function loadUnreadCount() {
            try {
                const response = await fetch('/api/communications/messages/unread-count/', {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                    }
                });
                
                const data = await response.json();
                
                if (data.success && data.data.count > 0) {
                    // 更新侧边栏徽章
                    const sidebarBadge = document.getElementById('unread-badge');
                    if (sidebarBadge) {
                        sidebarBadge.textContent = data.data.count;
                        sidebarBadge.classList.remove('hidden');
                    }
                    
                    // 更新通知图标徽章
                    const notificationBadge = document.getElementById('notification-badge');
                    if (notificationBadge) {
                        notificationBadge.textContent = data.data.count;
                        notificationBadge.classList.remove('hidden');
                    }
                } else {
                    // 隐藏徽章
                    const badges = document.querySelectorAll('#unread-badge, #notification-badge');
                    badges.forEach(badge => badge.classList.add('hidden'));
                }
            } catch (error) {
                console.error('加载未读消息数量失败:', error);
            }
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>