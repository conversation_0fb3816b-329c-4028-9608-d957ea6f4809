{% extends "anonymous/base_anonymous.html" %}

{% block page_title %}首页{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- 欢迎信息 -->
    <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-lg text-white p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold">欢迎，{{ staff.name }}！</h1>
                <p class="mt-2 text-blue-100">
                    您的匿名编号：<span class="font-mono font-medium">{{ staff.anonymous_code }}</span>
                </p>
                <p class="text-sm text-blue-100">
                    所属部门：{{ staff.department.name|default:"未设置" }}　|　当前时间：<span id="welcome-time"></span>
                </p>
            </div>
            <div class="hidden md:block">
                <svg class="h-16 w-16 text-blue-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- 任务统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- 待完成任务 -->
        <div class="bg-white overflow-hidden shadow-sm rounded-lg border-l-4 border-yellow-400">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">待完成任务</dt>
                            <dd class="text-2xl font-bold text-gray-900">{{ pending_count }}</dd>
                        </dl>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="{% url 'evaluations:anonymous:task_list' %}" 
                       class="text-sm font-medium text-yellow-600 hover:text-yellow-500">
                        查看详情 →
                    </a>
                </div>
            </div>
        </div>

        <!-- 已完成任务 -->
        <div class="bg-white overflow-hidden shadow-sm rounded-lg border-l-4 border-green-400">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">已完成任务</dt>
                            <dd class="text-2xl font-bold text-gray-900">{{ completed_count }}</dd>
                        </dl>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="{% url 'evaluations:anonymous:results' %}" 
                       class="text-sm font-medium text-green-600 hover:text-green-500">
                        查看结果 →
                    </a>
                </div>
            </div>
        </div>

        <!-- 完成进度 -->
        <div class="bg-white overflow-hidden shadow-sm rounded-lg border-l-4 border-blue-400">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">完成进度</dt>
                            <dd class="text-2xl font-bold text-gray-900">
                                {% if pending_count > 0 or completed_count > 0 %}
                                    {{ completed_count|add:pending_count|floatformat:0|default:0 }}分之{{ completed_count }}
                                {% else %}
                                    0%
                                {% endif %}
                            </dd>
                        </dl>
                    </div>
                </div>
                <div class="mt-4">
                    {% if pending_count > 0 or completed_count > 0 %}
                        {% widthratio completed_count completed_count|add:pending_count 100 as progress_percent %}
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                                 style="width: {{ progress_percent }}%"></div>
                        </div>
                        <p class="text-sm text-gray-500 mt-1">{{ progress_percent }}% 已完成</p>
                    {% else %}
                        <p class="text-sm text-gray-500">暂无考评任务</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 待办任务列表 -->
        <div class="bg-white shadow-sm rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">待完成任务</h3>
                    {% if pending_count > 0 %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            {{ pending_count }} 个任务
                        </span>
                    {% endif %}
                </div>
            </div>
            <div class="p-6">
                {% if pending_tasks %}
                    <div class="space-y-4">
                        {% for task in pending_tasks|slice:":5" %}
                            <div class="task-card p-4 cursor-pointer" onclick="window.location.href='{% url 'evaluations:anonymous:evaluate' task.id %}'">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <h4 class="text-sm font-medium text-gray-900">
                                            考评对象：{{ task.evaluatee.name }}
                                        </h4>
                                        <p class="text-sm text-gray-500 mt-1">
                                            {{ task.evaluatee.department.name }} - {{ task.evaluatee.position.name }}
                                        </p>
                                        <p class="text-xs text-gray-400 mt-1">
                                            批次：{{ task.batch.name }}
                                        </p>
                                    </div>
                                    <div class="flex items-center">
                                        <span class="status-pending">待完成</span>
                                        <svg class="ml-2 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                        {% if pending_tasks|length > 5 %}
                            <div class="text-center pt-4 border-t border-gray-200">
                                <a href="{% url 'evaluations:anonymous:task_list' %}" 
                                   class="text-sm font-medium text-blue-600 hover:text-blue-500">
                                    查看全部 {{ pending_count }} 个任务 →
                                </a>
                            </div>
                        {% endif %}
                    </div>
                {% else %}
                    <div class="text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">暂无待完成任务</h3>
                        <p class="mt-1 text-sm text-gray-500">您已完成所有分配的考评任务。</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- 最近完成任务 -->
        <div class="bg-white shadow-sm rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">最近完成</h3>
                    {% if completed_count > 0 %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            {{ completed_count }} 个任务
                        </span>
                    {% endif %}
                </div>
            </div>
            <div class="p-6">
                {% if completed_tasks %}
                    <div class="space-y-4">
                        {% for task in completed_tasks %}
                            <div class="border-l-4 border-green-400 pl-4">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <h4 class="text-sm font-medium text-gray-900">
                                            考评对象：{{ task.evaluatee.name }}
                                        </h4>
                                        <p class="text-sm text-gray-500 mt-1">
                                            {{ task.evaluatee.department.name }} - {{ task.evaluatee.position.name }}
                                        </p>
                                        <p class="text-xs text-gray-400 mt-1">
                                            完成时间：{{ task.completion_time|date:"Y-m-d H:i" }}
                                        </p>
                                    </div>
                                    <div class="flex items-center">
                                        <span class="status-completed">已完成</span>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                        {% if completed_count > 5 %}
                            <div class="text-center pt-4 border-t border-gray-200">
                                <a href="{% url 'evaluations:anonymous:results' %}" 
                                   class="text-sm font-medium text-green-600 hover:text-green-500">
                                    查看全部结果 →
                                </a>
                            </div>
                        {% endif %}
                    </div>
                {% else %}
                    <div class="text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">暂无完成记录</h3>
                        <p class="mt-1 text-sm text-gray-500">完成考评任务后，记录将在这里显示。</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 操作提示 -->
    {% if pending_count > 0 %}
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800">温馨提示</h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <p>您还有 <strong>{{ pending_count }}</strong> 个考评任务需要完成，请及时处理。</p>
                        <div class="mt-3">
                            <a href="{% url 'evaluations:anonymous:task_list' %}" 
                               class="inline-flex items-center px-3 py-2 border border-blue-300 rounded-md text-sm font-medium text-blue-700 bg-white hover:bg-blue-50 transition duration-200">
                                立即开始考评
                                <svg class="ml-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 更新欢迎页面的时间
    function updateWelcomeTime() {
        const now = new Date();
        const timeString = now.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
        const welcomeTimeElement = document.getElementById('welcome-time');
        if (welcomeTimeElement) {
            welcomeTimeElement.textContent = timeString;
        }
    }
    
    // 每分钟更新时间
    setInterval(updateWelcomeTime, 60000);
    updateWelcomeTime();
    
    // 页面加载动画
    document.addEventListener('DOMContentLoaded', function() {
        // 统计卡片动画
        const cards = document.querySelectorAll('.task-card');
        cards.forEach((card, index) => {
            setTimeout(() => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'all 0.3s ease';
                
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 100);
            }, index * 100);
        });
        
        // 进度条动画
        const progressBars = document.querySelectorAll('.bg-blue-600');
        progressBars.forEach(bar => {
            if (bar.style.width) {
                const targetWidth = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = targetWidth;
                }, 500);
            }
        });
    });
    
    // 任务提醒功能
    function showTaskReminder() {
        const pendingCount = {{ pending_count }};
        if (pendingCount > 0) {
            // 可以在这里添加定时提醒功能
            console.log(`您还有 ${pendingCount} 个待完成任务`);
        }
    }
    
    // 每30分钟提醒一次
    setInterval(showTaskReminder, 30 * 60 * 1000);
</script>
{% endblock %}