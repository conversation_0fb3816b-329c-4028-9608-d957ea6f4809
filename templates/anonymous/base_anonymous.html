{% extends "common/base.html" %}

{% block title %}{{ title|default:"匿名考评" }} - 企业考评评分系统{% endblock %}

{% block sidebar %}
<!-- 左侧简化导航栏 -->
<div class="bg-blue-800 text-white w-64 flex-shrink-0">
    <!-- Logo区域 -->
    <div class="flex items-center justify-center h-16 bg-blue-900">
        <h1 class="text-xl font-bold">匿名考评系统</h1>
    </div>
    
    <!-- 用户信息 -->
    <div class="px-4 py-3 bg-blue-700">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                    </svg>
                </div>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium">{{ request.current_staff.name|default:"匿名用户" }}</p>
                <p class="text-xs text-blue-300">{{ request.current_staff.department.name|default:"" }}</p>
            </div>
        </div>
    </div>
    
    <!-- 导航菜单 -->
    <nav class="mt-4">
        <div class="px-2 space-y-1">
            <!-- 首页 -->
            <a href="{% url 'evaluations:anonymous:home' %}" 
               class="nav-item flex items-center px-2 py-2 text-sm font-medium rounded-md hover:bg-blue-700 transition duration-150">
                <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                </svg>
                首页
            </a>
            
            <!-- 考评任务 -->
            <a href="{% url 'evaluations:anonymous:task_list' %}"
               class="nav-item flex items-center px-2 py-2 text-sm font-medium rounded-md hover:bg-blue-700 transition duration-150">
                <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                </svg>
                考评任务
            </a>
            
            <!-- 评分结果 -->
            <a href="{% url 'evaluations:anonymous:results' %}"
               class="nav-item flex items-center px-2 py-2 text-sm font-medium rounded-md hover:bg-blue-700 transition duration-150">
                <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                评分结果
            </a>
            
            <!-- 个人信息 -->
            <a href="{% url 'organizations:anonymous:anonymous_profile' %}"
               class="nav-item flex items-center px-2 py-2 text-sm font-medium rounded-md hover:bg-blue-700 transition duration-150">
                <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                个人信息
            </a>
        </div>
    </nav>
    
    <!-- 底部退出按钮 -->
    <div class="absolute bottom-0 w-full p-4">
        <a href="{% url 'organizations:anonymous:anonymous_logout' %}"
           class="flex items-center w-full px-2 py-2 text-sm font-medium text-center bg-blue-900 hover:bg-red-600 rounded-md transition duration-150">
            <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
            </svg>
            退出系统
        </a>
    </div>
</div>
{% endblock %}

{% block header %}
<!-- 顶部导航栏 -->
<header class="bg-white shadow-sm border-b border-gray-200">
    <div class="flex items-center justify-between px-6 py-4">
        <div class="flex items-center">
            <h2 class="text-2xl font-bold text-gray-900">
                {% block page_title %}匿名考评{% endblock %}
            </h2>
            {% block breadcrumb %}{% endblock %}
        </div>
        
        <!-- 右侧信息栏 -->
        <div class="flex items-center space-x-4">
            <!-- 当前时间 -->
            <div class="text-sm text-gray-500">
                <span id="current-time"></span>
            </div>
            
            <!-- 匿名编号 -->
            <div class="text-sm text-blue-600 font-medium">
                匿名编号: {{ request.current_staff.anonymous_code|default:"未设置" }}
            </div>
        </div>
    </div>
</header>
{% endblock %}

{% block extra_css %}
<style>
    .nav-item.active {
        @apply bg-blue-600 text-white;
    }
    
    .card {
        @apply bg-white rounded-lg shadow-sm border border-gray-200;
    }
    
    .task-card {
        @apply bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition duration-200;
    }
    
    .task-card:hover {
        transform: translateY(-2px);
    }
    
    .status-pending {
        @apply bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium;
    }
    
    .status-completed {
        @apply bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium;
    }
    
    .status-expired {
        @apply bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium;
    }
    
    .evaluation-form {
        @apply bg-white rounded-lg shadow-sm p-6 space-y-6;
    }
    
    .form-group {
        @apply space-y-2;
    }
    
    .form-label {
        @apply block text-sm font-medium text-gray-700;
    }
    
    .form-input {
        @apply mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500;
    }
    
    .form-textarea {
        @apply mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-none;
    }
    
    .score-input {
        @apply text-center font-medium text-lg;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // 更新当前时间
    function updateTime() {
        const now = new Date();
        const timeString = now.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        document.getElementById('current-time').textContent = timeString;
    }
    
    // 每秒更新时间
    setInterval(updateTime, 1000);
    updateTime();
    
    // 设置当前活动菜单项
    document.addEventListener('DOMContentLoaded', function() {
        const currentPath = window.location.pathname;
        const navItems = document.querySelectorAll('.nav-item');
        
        navItems.forEach(item => {
            const href = item.getAttribute('href');
            if (href && currentPath.startsWith(href)) {
                item.classList.add('active');
            }
        });
    });
    
    // 评分表单验证
    function validateEvaluationForm() {
        const requiredFields = document.querySelectorAll('[required]');
        let isValid = true;
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('border-red-500');
                isValid = false;
            } else {
                field.classList.remove('border-red-500');
            }
        });
        
        if (!isValid) {
            alert('请填写所有必填项目');
        }
        
        return isValid;
    }
    
    // 分数输入验证
    function validateScore(input, maxScore) {
        const value = parseFloat(input.value);
        if (isNaN(value) || value < 0 || value > maxScore) {
            input.classList.add('border-red-500');
            alert(`分数必须在0-${maxScore}之间`);
            return false;
        }
        input.classList.remove('border-red-500');
        return true;
    }
    
    // 自动保存草稿（每30秒）
    let autoSaveTimer;
    function startAutoSave() {
        autoSaveTimer = setInterval(function() {
            const form = document.querySelector('.evaluation-form form');
            if (form) {
                saveDraft(form);
            }
        }, 30000);
    }
    
    function saveDraft(form) {
        const formData = new FormData(form);
        const data = {};
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }
        
        localStorage.setItem('evaluation_draft_' + form.dataset.relationId, JSON.stringify(data));
        
        // 显示保存提示
        showMessage('草稿已自动保存', 'info');
    }
    
    function showMessage(message, type) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `fixed bottom-4 right-4 px-4 py-2 rounded-lg text-white z-50 ${
            type === 'success' ? 'bg-green-500' : 
            type === 'error' ? 'bg-red-500' : 
            type === 'info' ? 'bg-blue-500' : 'bg-gray-500'
        }`;
        messageDiv.textContent = message;
        
        document.body.appendChild(messageDiv);
        
        setTimeout(() => {
            messageDiv.remove();
        }, 3000);
    }
</script>
{% endblock %}