{% extends "anonymous/base_anonymous.html" %}

{% block page_title %}考评打分{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- 评价对象信息 -->
    <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-lg text-white p-6 mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold mb-2">考评打分</h1>
                <div class="text-blue-100 space-y-1">
                    <p class="flex items-center">
                        <i data-lucide="user" class="w-4 h-4 mr-2"></i>
                        评价对象：<span class="font-medium ml-1">{{ evaluatee.name }}</span>
                    </p>
                    <p class="flex items-center">
                        <i data-lucide="building" class="w-4 h-4 mr-2"></i>
                        所属部门：<span class="font-medium ml-1">{{ evaluatee.department.name }}</span>
                    </p>
                    <p class="flex items-center">
                        <i data-lucide="briefcase" class="w-4 h-4 mr-2"></i>
                        职位：<span class="font-medium ml-1">{{ evaluatee.position.name }}</span>
                    </p>
                </div>
            </div>
            <div class="hidden md:flex items-center">
                <div class="text-right">
                    <p class="text-blue-100">考评批次</p>
                    <p class="font-semibold">{{ relation.batch.name }}</p>
                    <p class="text-sm text-blue-200">{{ relation.batch.start_date|date:"Y-m-d" }} ~ {{ relation.batch.end_date|date:"Y-m-d" }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 考评说明 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <i data-lucide="info" class="w-5 h-5 text-blue-500 mt-0.5"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-gray-900 mb-2">考评说明</h3>
                <div class="text-sm text-gray-600 space-y-1">
                    <p>• 请根据该员工的实际工作表现客观公正地进行评分</p>
                    <p>• 您的评分将完全匿名，被评价者无法知晓评分来源</p>
                    <p>• 评分完成后请务必点击"提交评分"按钮保存</p>
                    <p>• 如有疑问，请联系HR部门或系统管理员</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 评分表单 -->
    <form id="evaluationForm" method="post" action="{% url 'evaluations:anonymous:submit' relation.id %}">
        {% csrf_token %}
        
        <!-- 模板信息 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 flex items-center">
                            <i data-lucide="file-text" class="w-5 h-5 mr-2 text-gray-500"></i>
                            {{ template.name }}
                        </h3>
                        {% if template.description %}
                            <p class="mt-1 text-sm text-gray-600">{{ template.description }}</p>
                        {% endif %}
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-500">评分模式</p>
                        <p class="font-medium">
                            {% if template.template_type == 'structured' %}结构化评分
                            {% elif template.template_type == 'open' %}开放式评分
                            {% elif template.template_type == 'mixed' %}混合式评分
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 评分项列表 -->
        <div class="space-y-6" id="evaluationItems">
            {% for item in evaluation_items %}
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 evaluation-item" data-item-id="{{ item.id }}">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h4 class="text-base font-medium text-gray-900 mb-1">
                                    {{ item.name }}
                                    {% if item.is_required %}
                                        <span class="text-red-500 ml-1">*</span>
                                    {% endif %}
                                </h4>
                                {% if item.description %}
                                    <p class="text-sm text-gray-600">{{ item.description }}</p>
                                {% endif %}
                            </div>
                            <div class="ml-4">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
                                    {% if item.scoring_mode == 'numeric' %}bg-blue-100 text-blue-800
                                    {% elif item.scoring_mode == 'tier' %}bg-green-100 text-green-800
                                    {% elif item.scoring_mode == 'text' %}bg-purple-100 text-purple-800
                                    {% endif %}">
                                    {% if item.scoring_mode == 'numeric' %}数值评分
                                    {% elif item.scoring_mode == 'tier' %}等级评分
                                    {% elif item.scoring_mode == 'text' %}文本评价
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="px-6 py-6">
                        {% if item.scoring_mode == 'numeric' %}
                            <!-- 数值评分 -->
                            <div class="numeric-scoring" data-max-score="{{ item.max_score }}">
                                <div class="flex items-center space-x-4 mb-4">
                                    <label class="text-sm font-medium text-gray-700">评分：</label>
                                    <input type="number" name="items[{{ item.id }}][score]" 
                                           min="0" max="{{ item.max_score }}" step="0.1"
                                           class="w-24 px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="0">
                                    <span class="text-sm text-gray-500">/ {{ item.max_score }} 分</span>
                                </div>
                                
                                <!-- 评分滑块 -->
                                <div class="mb-4">
                                    <input type="range" min="0" max="{{ item.max_score }}" step="0.1" value="0"
                                           class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                                           data-item-id="{{ item.id }}">
                                    <div class="flex justify-between text-xs text-gray-500 mt-1">
                                        <span>0</span>
                                        <span>{{ item.max_score|floatformat:0 }}</span>
                                    </div>
                                </div>
                            </div>
                            
                        {% elif item.scoring_mode == 'tier' %}
                            <!-- 等级评分 -->
                            <div class="tier-scoring">
                                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
                                    {% for tier in item.scoringtier_set.all %}
                                        <label class="relative flex cursor-pointer rounded-lg border border-gray-200 p-4 hover:bg-gray-50 focus:outline-none">
                                            <input type="radio" name="items[{{ item.id }}][tier]" value="{{ tier.id }}"
                                                   class="sr-only peer" data-score="{{ tier.tier_value }}">
                                            <div class="flex flex-1 flex-col">
                                                <div class="flex items-center justify-between">
                                                    <span class="block text-sm font-medium text-gray-900">{{ tier.tier_name }}</span>
                                                    <span class="ml-2 text-sm font-semibold text-blue-600">{{ tier.tier_value }}分</span>
                                                </div>
                                                {% if tier.description %}
                                                    <span class="mt-1 block text-xs text-gray-500">{{ tier.description }}</span>
                                                {% endif %}
                                            </div>
                                            <div class="absolute -inset-px rounded-lg border-2 pointer-events-none peer-checked:border-blue-500"></div>
                                        </label>
                                    {% endfor %}
                                </div>
                                <input type="hidden" name="items[{{ item.id }}][score]" value="0">
                            </div>
                            
                        {% elif item.scoring_mode == 'text' %}
                            <!-- 文本评价 -->
                            <div class="text-scoring">
                                <textarea name="items[{{ item.id }}][text]" rows="4"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                          placeholder="请输入您的评价和建议..."></textarea>
                                <input type="hidden" name="items[{{ item.id }}][score]" value="0">
                            </div>
                        {% endif %}
                    </div>
                </div>
            {% endfor %}
        </div>

        <!-- 总体评价 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mt-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i data-lucide="message-circle" class="w-5 h-5 mr-2 text-gray-500"></i>
                    总体评价
                </h3>
                <p class="mt-1 text-sm text-gray-600">请对该员工的整体表现进行综合评价（可选）</p>
            </div>
            <div class="px-6 py-6">
                <textarea name="comment" rows="4"
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="请输入您的整体评价、建议或意见..."></textarea>
            </div>
        </div>

        <!-- 评分汇总 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mt-6" id="scoreSummary">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i data-lucide="calculator" class="w-5 h-5 mr-2 text-gray-500"></i>
                    评分汇总
                </h3>
            </div>
            <div class="px-6 py-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center">
                        <p class="text-sm text-gray-600 mb-1">当前总分</p>
                        <p class="text-2xl font-bold text-blue-600" id="currentScore">0</p>
                    </div>
                    <div class="text-center">
                        <p class="text-sm text-gray-600 mb-1">满分</p>
                        <p class="text-2xl font-bold text-gray-900" id="maxScore">{{ template.calculate_total_score }}</p>
                    </div>
                    <div class="text-center">
                        <p class="text-sm text-gray-600 mb-1">完成度</p>
                        <p class="text-2xl font-bold text-green-600" id="completionRate">0%</p>
                    </div>
                </div>
                
                <!-- 进度条 -->
                <div class="mt-6">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-gray-700">评分进度</span>
                        <span class="text-sm text-gray-600" id="progressText">0 / {{ evaluation_items|length }} 项</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                             style="width: 0%" id="progressBar"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 提交按钮 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mt-6">
            <div class="px-6 py-6">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-600">
                        <i data-lucide="shield-check" class="w-4 h-4 inline mr-1"></i>
                        您的评分将完全匿名提交，无法追溯评分来源
                    </div>
                    <div class="flex items-center space-x-4">
                        <button type="button" id="saveDraftBtn"
                                class="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
                            保存草稿
                        </button>
                        <button type="submit" id="submitBtn"
                                class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
                            <i data-lucide="send" class="w-4 h-4"></i>
                            <span>提交评分</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let maxTotalScore = {{ template.calculate_total_score }};
    let totalItems = {{ evaluation_items|length }};

    // 初始化事件监听
    document.addEventListener('DOMContentLoaded', function() {
        initializeEventListeners();
        updateScoreSummary();
        
        // 加载草稿数据（如果存在）
        loadDraftData();
    });

    function initializeEventListeners() {
        // 数值评分输入框
        document.querySelectorAll('input[type="number"]').forEach(input => {
            input.addEventListener('input', function() {
                updateSlider(this);
                updateScoreSummary();
            });
        });

        // 滑块
        document.querySelectorAll('.slider').forEach(slider => {
            slider.addEventListener('input', function() {
                updateNumberInput(this);
                updateScoreSummary();
            });
        });

        // 等级评分单选框
        document.querySelectorAll('input[type="radio"]').forEach(radio => {
            radio.addEventListener('change', function() {
                updateTierScore(this);
                updateScoreSummary();
            });
        });

        // 文本评价
        document.querySelectorAll('textarea[name*="[text]"]').forEach(textarea => {
            textarea.addEventListener('input', function() {
                updateScoreSummary();
            });
        });
    }

    function updateSlider(numberInput) {
        const itemId = numberInput.name.match(/\[(\d+)\]/)[1];
        const slider = document.querySelector(`.slider[data-item-id="${itemId}"]`);
        if (slider) {
            slider.value = numberInput.value;
        }
    }

    function updateNumberInput(slider) {
        const itemId = slider.dataset.itemId;
        const numberInput = document.querySelector(`input[name="items[${itemId}][score]"]`);
        if (numberInput) {
            numberInput.value = slider.value;
        }
    }

    function updateTierScore(radio) {
        const score = radio.dataset.score;
        const itemId = radio.name.match(/\[(\d+)\]/)[1];
        const hiddenInput = document.querySelector(`input[name="items[${itemId}][score]"]`);
        if (hiddenInput) {
            hiddenInput.value = score;
        }
    }

    function updateScoreSummary() {
        let currentScore = 0;
        let completedItems = 0;

        // 计算当前总分和完成项数
        document.querySelectorAll('.evaluation-item').forEach(item => {
            const itemId = item.dataset.itemId;
            const scoreInput = item.querySelector(`input[name="items[${itemId}][score]"]`);
            const textInput = item.querySelector(`textarea[name="items[${itemId}][text]"]`);
            
            if (scoreInput) {
                const score = parseFloat(scoreInput.value) || 0;
                currentScore += score;
                
                if (score > 0 || (textInput && textInput.value.trim())) {
                    completedItems++;
                }
            } else if (textInput && textInput.value.trim()) {
                completedItems++;
            }
        });

        // 更新显示
        document.getElementById('currentScore').textContent = currentScore.toFixed(1);
        document.getElementById('completionRate').textContent = 
            maxTotalScore > 0 ? Math.round((currentScore / maxTotalScore) * 100) + '%' : '0%';
        
        document.getElementById('progressText').textContent = `${completedItems} / ${totalItems} 项`;
        
        const progressPercent = totalItems > 0 ? (completedItems / totalItems) * 100 : 0;
        document.getElementById('progressBar').style.width = progressPercent + '%';
    }

    // 保存草稿
    document.getElementById('saveDraftBtn').addEventListener('click', function() {
        const formData = new FormData(document.getElementById('evaluationForm'));
        formData.append('save_draft', 'true');
        
        fetch('{% url "evaluations:anonymous:submit" relation.id %}', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('草稿已保存', 'success');
                localStorage.setItem('draft_{{ relation.id }}', JSON.stringify(getFormData()));
            } else {
                showNotification('保存失败：' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('保存失败，请重试', 'error');
        });
    });

    // 表单提交
    document.getElementById('evaluationForm').addEventListener('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
            return;
        }
        
        // 显示确认对话框
        if (!confirm('确定要提交评分吗？提交后将无法修改。')) {
            e.preventDefault();
            return;
        }
        
        // 显示加载状态
        const submitBtn = document.getElementById('submitBtn');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i data-lucide="loader-2" class="w-4 h-4 mr-2 animate-spin"></i>提交中...';
        
        // 清除草稿
        localStorage.removeItem('draft_{{ relation.id }}');
    });

    function validateForm() {
        let isValid = true;
        let errors = [];

        // 检查必填项
        document.querySelectorAll('.evaluation-item').forEach(item => {
            const itemId = item.dataset.itemId;
            const itemName = item.querySelector('h4').textContent.trim();
            const isRequired = item.querySelector('span.text-red-500');
            
            if (isRequired) {
                const scoreInput = item.querySelector(`input[name="items[${itemId}][score]"]`);
                const textInput = item.querySelector(`textarea[name="items[${itemId}][text]"]`);
                
                let hasValue = false;
                if (scoreInput && parseFloat(scoreInput.value) > 0) {
                    hasValue = true;
                }
                if (textInput && textInput.value.trim()) {
                    hasValue = true;
                }
                
                if (!hasValue) {
                    errors.push(`"${itemName}" 为必填项，请完成评分`);
                    isValid = false;
                }
            }
        });

        if (!isValid) {
            alert('请完成以下必填项：\n\n' + errors.join('\n'));
        }

        return isValid;
    }

    function getFormData() {
        const data = {};
        const formData = new FormData(document.getElementById('evaluationForm'));
        
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }
        
        return data;
    }

    function loadDraftData() {
        // 从服务器加载草稿数据
        fetch(`{% url 'evaluations:anonymous:draft' relation.id %}`)
            .then(response => response.json())
            .then(result => {
                if (result.success && result.data) {
                    const data = result.data;
                    
                    // 恢复总体评价
                    if (data.comment) {
                        document.querySelector('textarea[name="comment"]').value = data.comment;
                    }
                    
                    // 恢复评分项数据
                    for (let [itemId, itemData] of Object.entries(data.items)) {
                        // 恢复数值评分
                        if (itemData.score) {
                            const scoreInput = document.querySelector(`input[name="items[${itemId}][score]"]`);
                            const slider = document.querySelector(`.slider[data-item-id="${itemId}"]`);
                            if (scoreInput) {
                                scoreInput.value = itemData.score;
                                if (slider) {
                                    slider.value = itemData.score;
                                }
                            }
                        }
                        
                        // 恢复等级评分
                        if (itemData.tier) {
                            const radio = document.querySelector(`input[name="items[${itemId}][tier]"][value="${itemData.tier}"]`);
                            if (radio) {
                                radio.checked = true;
                                updateTierScore(radio);
                            }
                        }
                        
                        // 恢复文本评价
                        if (itemData.text) {
                            const textInput = document.querySelector(`textarea[name="items[${itemId}][text]"]`);
                            if (textInput) {
                                textInput.value = itemData.text;
                            }
                        }
                    }
                    
                    updateScoreSummary();
                    showNotification('已恢复草稿数据', 'info');
                    
                } else if (result.message) {
                    // 没有草稿数据，这是正常的
                    console.log('无草稿数据');
                }
            })
            .catch(error => {
                console.error('加载草稿数据失败:', error);
                // 如果服务器草稿加载失败，尝试从localStorage加载作为备用
                loadLocalDraftData();
            });
    }
    
    function loadLocalDraftData() {
        // 备用的本地草稿加载方法
        const draftKey = 'draft_{{ relation.id }}';
        const draftData = localStorage.getItem(draftKey);
        
        if (draftData) {
            try {
                const data = JSON.parse(draftData);
                
                // 恢复表单数据
                for (let [key, value] of Object.entries(data)) {
                    const element = document.querySelector(`[name="${key}"]`);
                    if (element) {
                        if (element.type === 'radio') {
                            const radio = document.querySelector(`[name="${key}"][value="${value}"]`);
                            if (radio) {
                                radio.checked = true;
                                updateTierScore(radio);
                            }
                        } else {
                            element.value = value;
                        }
                    }
                }
                
                updateScoreSummary();
                showNotification('已恢复本地草稿数据', 'info');
                
            } catch (e) {
                console.error('加载本地草稿数据失败:', e);
            }
        }
    }

    // 页面离开前提醒
    window.addEventListener('beforeunload', function(e) {
        const formData = getFormData();
        const hasData = Object.values(formData).some(value => value && value.toString().trim());
        
        if (hasData) {
            e.preventDefault();
            e.returnValue = '您有未保存的评分数据，确定要离开吗？';
        }
    });

    // 定时自动保存草稿
    setInterval(function() {
        const formData = getFormData();
        const hasData = Object.values(formData).some(value => value && value.toString().trim());
        
        if (hasData) {
            localStorage.setItem('draft_{{ relation.id }}', JSON.stringify(formData));
        }
    }, 30000); // 每30秒自动保存一次
</script>
{% endblock %}