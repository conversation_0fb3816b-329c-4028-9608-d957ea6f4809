<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>匿名考评登录 - 企业考评评分系统</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        .login-bg {
            background: linear-gradient(135deg, #4f46e5 0%, #06b6d4 100%);
        }
        
        .floating-animation {
            animation: floating 3s ease-in-out infinite;
        }
        
        @keyframes floating {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }
        
        .glass-effect {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .anonymous-icon {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
    </style>
</head>
<body class="login-bg min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <!-- 消息提示区域 -->
    {% if messages %}
        <div id="messages" class="fixed top-4 right-4 z-50 space-y-2">
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} px-4 py-3 rounded-lg shadow-lg 
                    {% if message.tags == 'success' %}bg-green-100 border border-green-400 text-green-700
                    {% elif message.tags == 'error' %}bg-red-100 border border-red-400 text-red-700
                    {% elif message.tags == 'warning' %}bg-yellow-100 border border-yellow-400 text-yellow-700
                    {% else %}bg-blue-100 border border-blue-400 text-blue-700{% endif %}">
                    <span class="block sm:inline">{{ message }}</span>
                    <button onclick="this.parentElement.remove()" class="float-right ml-4 text-lg leading-none">&times;</button>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <div class="max-w-md w-full space-y-8">
        <!-- Logo和标题 -->
        <div class="text-center floating-animation">
            <div class="mx-auto h-16 w-16 bg-white rounded-full flex items-center justify-center shadow-lg anonymous-icon">
                <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"></path>
                </svg>
            </div>
            <h2 class="mt-6 text-3xl font-extrabold text-white">
                匿名考评系统
            </h2>
            <p class="mt-2 text-sm text-white opacity-90">
                请使用您的匿名编号登录
            </p>
        </div>

        <!-- 登录表单 -->
        <div class="glass-effect rounded-xl shadow-2xl p-8">
            <form class="space-y-6" method="post">
                {% csrf_token %}
                
                <div>
                    <label for="anonymous_code" class="block text-sm font-medium text-gray-700 mb-2">
                        匿名编号
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2v6a2 2 0 01-2 2H9a2 2 0 01-2-2V9a2 2 0 012-2m0 0a2 2 0 012-2m0 0a2 2 0 012 2m-6 9l2 2 4-4"></path>
                            </svg>
                        </div>
                        <input id="anonymous_code" name="anonymous_code" type="text" required 
                               class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200 text-center font-mono text-lg tracking-wider"
                               placeholder="请输入匿名编号"
                               maxlength="64"
                               style="letter-spacing: 1px;">
                    </div>
                    <p class="mt-2 text-sm text-gray-500">
                        请输入管理员为您分配的匿名编号<br>
                        <span class="text-green-600">• 安全编号（SHA256）：64位字符串</span><br>
                        <span class="text-yellow-600">• 传统编号：XX-XX-XXXX 格式</span>
                    </p>
                </div>

                <div>
                    <button type="submit" id="login-btn"
                            class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-200 transform hover:scale-105">
                        <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                            <svg class="h-5 w-5 text-blue-500 group-hover:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                            </svg>
                        </span>
                        <span id="login-text">开始考评</span>
                    </button>
                </div>
            </form>

            <!-- 使用说明 -->
            <div class="mt-6 bg-blue-50 rounded-lg p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-blue-800">使用说明</h3>
                        <div class="mt-2 text-sm text-blue-700">
                            <ul class="list-disc list-inside space-y-1">
                                <li>匿名编号由系统管理员分配</li>
                                <li>评分过程完全匿名，保护您的隐私</li>
                                <li>请客观公正地完成考评任务</li>
                                <li>如有问题请联系系统管理员</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 管理端入口 -->
            <div class="mt-6 text-center">
                <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-gray-300"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-white text-gray-500">管理员</span>
                    </div>
                </div>
                
                <div class="mt-4">
                    <a href="{% url 'organizations:admin:login' %}" 
                       class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 transition duration-200">
                        <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        管理后台入口
                    </a>
                </div>
            </div>
        </div>

        <!-- 版权信息 -->
        <div class="text-center text-white text-sm opacity-75">
            <p>&copy; 2025 企业考评评分系统. 保留所有权利.</p>
            <p class="mt-1">本系统采用匿名考评机制，保护评价者隐私</p>
        </div>
    </div>

    <script>
        // 自动隐藏消息提示
        setTimeout(function() {
            const messages = document.getElementById('messages');
            if (messages) {
                messages.style.display = 'none';
            }
        }, 5000);

        // 匿名编号输入格式化
        document.getElementById('anonymous_code').addEventListener('input', function(e) {
            let value = e.target.value.toUpperCase();
            // 移除非字母数字字符，但保留连字符
            value = value.replace(/[^A-Z0-9-]/g, '');
            e.target.value = value;
        });

        // 表单提交处理
        document.querySelector('form').addEventListener('submit', function(e) {
            const anonymousCode = document.getElementById('anonymous_code').value.trim();
            
            if (!anonymousCode) {
                e.preventDefault();
                alert('请输入匿名编号');
                return;
            }
            
            if (anonymousCode.length < 3) {
                e.preventDefault();
                alert('匿名编号格式不正确');
                return;
            }
            
            const loginBtn = document.getElementById('login-btn');
            const loginText = document.getElementById('login-text');
            
            loginBtn.disabled = true;
            loginText.innerHTML = '<svg class="animate-spin h-5 w-5 mr-2 inline" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>验证中...';
        });

        // Enter键自动提交
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.querySelector('form').submit();
            }
        });

        // 页面加载时聚焦到输入框
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('anonymous_code').focus();
        });
    </script>
</body>
</html>