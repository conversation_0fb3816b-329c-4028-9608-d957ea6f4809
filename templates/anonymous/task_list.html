{% extends "anonymous/base_anonymous.html" %}

{% block page_title %}考评任务{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- 任务概览 -->
    <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-lg text-white p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold">我的考评任务</h1>
                <p class="mt-2 text-blue-100">
                    您当前有 {{ tasks|length }} 项考评任务需要完成
                </p>
            </div>
            <div class="hidden md:block">
                <div class="bg-blue-400 bg-opacity-50 rounded-lg p-4">
                    <div class="grid grid-cols-2 gap-4 text-center">
                        <div>
                            <p class="text-2xl font-bold">{{ tasks|length }}</p>
                            <p class="text-sm text-blue-100">总任务</p>
                        </div>
                        <div>
                            <p class="text-2xl font-bold">
                                {% for task in tasks %}{% if task.status == 'completed' %}{{ forloop.counter0|add:1 }}{% endif %}{% empty %}0{% endfor %}
                            </p>
                            <p class="text-sm text-blue-100">已完成</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                <div class="flex-1 max-w-md">
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i data-lucide="search" class="h-4 w-4 text-gray-400"></i>
                        </div>
                        <input type="text" id="searchInput" 
                               class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500" 
                               placeholder="搜索被评价者姓名或部门...">
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <select id="statusFilter" class="border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">所有状态</option>
                        <option value="pending">待完成</option>
                        <option value="completed">已完成</option>
                    </select>
                    <select id="departmentFilter" class="border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">所有部门</option>
                        {% for task in tasks %}
                            <option value="{{ task.evaluatee.department.name }}">{{ task.evaluatee.department.name }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- 任务列表 -->
    <div class="space-y-4" id="tasksList">
        {% for task in tasks %}
            <div class="bg-white rounded-lg shadow hover:shadow-lg transition-shadow task-card border border-gray-200"
                 data-status="{{ task.status }}" 
                 data-department="{{ task.evaluatee.department.name }}"
                 data-evaluatee="{{ task.evaluatee.name }}">
                
                <div class="px-6 py-6">
                    <div class="flex items-start justify-between">
                        <!-- 任务信息 -->
                        <div class="flex-1">
                            <div class="flex items-center space-x-3 mb-3">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                                        <i data-lucide="user" class="w-6 h-6 text-gray-500"></i>
                                    </div>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900">{{ task.evaluatee.name }}</h3>
                                    <div class="flex items-center space-x-4 text-sm text-gray-600">
                                        <span class="flex items-center">
                                            <i data-lucide="building" class="w-4 h-4 mr-1"></i>
                                            {{ task.evaluatee.department.name }}
                                        </span>
                                        <span class="flex items-center">
                                            <i data-lucide="briefcase" class="w-4 h-4 mr-1"></i>
                                            {{ task.evaluatee.position.name|default:"未设置职位" }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 任务详情 -->
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm mb-4">
                                <div class="flex items-center text-gray-600">
                                    <i data-lucide="calendar" class="w-4 h-4 mr-2"></i>
                                    <span>批次：{{ task.batch.name }}</span>
                                </div>
                                <div class="flex items-center text-gray-600">
                                    <i data-lucide="file-text" class="w-4 h-4 mr-2"></i>
                                    <span>模板：{{ task.template.name }}</span>
                                </div>
                                <div class="flex items-center text-gray-600">
                                    <i data-lucide="clock" class="w-4 h-4 mr-2"></i>
                                    <span>截止：{{ task.batch.end_date|date:"m-d H:i" }}</span>
                                </div>
                                <div class="flex items-center text-gray-600">
                                    <i data-lucide="target" class="w-4 h-4 mr-2"></i>
                                    <span>权重：{{ task.weight_factor }}x</span>
                                </div>
                            </div>
                            
                            <!-- 模板描述 -->
                            {% if task.template.description %}
                                <p class="text-sm text-gray-600 mb-4">{{ task.template.description|truncatechars:100 }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- 状态和操作 -->
                        <div class="flex flex-col items-end space-y-3 ml-6">
                            <!-- 状态标签 -->
                            {% if task.status == 'pending' %}
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                                    <i data-lucide="clock" class="w-4 h-4 mr-1"></i>
                                    待完成
                                </span>
                            {% elif task.status == 'completed' %}
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                    <i data-lucide="check-circle" class="w-4 h-4 mr-1"></i>
                                    已完成
                                </span>
                            {% elif task.status == 'draft' %}
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                    <i data-lucide="edit" class="w-4 h-4 mr-1"></i>
                                    草稿
                                </span>
                            {% endif %}
                            
                            <!-- 操作按钮 -->
                            <div class="flex items-center space-x-2">
                                {% if task.status == 'pending' or task.status == 'draft' %}
                                    <a href="{% url 'evaluations:anonymous:evaluate' task.id %}" 
                                       class="px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 flex items-center space-x-2">
                                        <i data-lucide="edit" class="w-4 h-4"></i>
                                        <span>{% if task.status == 'draft' %}继续编辑{% else %}开始评分{% endif %}</span>
                                    </a>
                                {% elif task.status == 'completed' %}
                                    <button onclick="viewResults({{ task.id }})" 
                                            class="px-4 py-2 border border-gray-300 text-gray-700 text-sm rounded-md hover:bg-gray-50 flex items-center space-x-2">
                                        <i data-lucide="eye" class="w-4 h-4"></i>
                                        <span>查看评分</span>
                                    </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- 进度信息 -->
                    {% if task.status == 'completed' %}
                        <div class="mt-4 pt-4 border-t border-gray-200">
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-600">
                                    <i data-lucide="calendar" class="w-4 h-4 inline mr-1"></i>
                                    完成时间：{{ task.updated_at|date:"Y-m-d H:i" }}
                                </span>
                                {% if task.evaluationrecord %}
                                    <span class="text-blue-600 font-medium">
                                        总分：{{ task.evaluationrecord.total_score }} 分
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                    {% elif task.batch.end_date %}
                        <div class="mt-4 pt-4 border-t border-gray-200">
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-600">
                                    <i data-lucide="clock" class="w-4 h-4 inline mr-1"></i>
                                    剩余时间：<span id="countdown-{{ task.id }}" data-end-time="{{ task.batch.end_date|date:'c' }}"></span>
                                </span>
                                <span class="text-gray-500">
                                    权重系数：{{ task.weight_factor }}x
                                </span>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        {% empty %}
            <!-- 空状态 -->
            <div class="text-center py-12">
                <i data-lucide="clipboard-list" class="mx-auto h-16 w-16 text-gray-400"></i>
                <h3 class="mt-4 text-lg font-medium text-gray-900">暂无考评任务</h3>
                <p class="mt-2 text-sm text-gray-500">当前没有分配给您的考评任务，请等待管理员分配。</p>
                <div class="mt-6">
                    <a href="{% url 'evaluations:anonymous:home' %}" 
                       class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        <i data-lucide="home" class="w-4 h-4 mr-2"></i>
                        返回首页
                    </a>
                </div>
            </div>
        {% endfor %}
    </div>

    <!-- 分页 -->
    {% if is_paginated %}
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    显示第 {{ page_obj.start_index }} - {{ page_obj.end_index }} 项，共 {{ paginator.count }} 项
                </div>
                <nav class="flex items-center space-x-2">
                    {% if page_obj.has_previous %}
                        <a href="?page=1" class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">首页</a>
                        <a href="?page={{ page_obj.previous_page_number }}" class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">上一页</a>
                    {% endif %}
                    
                    <span class="px-3 py-2 text-sm bg-blue-50 border border-blue-200 rounded-md text-blue-600">
                        {{ page_obj.number }}
                    </span>
                    
                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}" class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">下一页</a>
                        <a href="?page={{ paginator.num_pages }}" class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">末页</a>
                    {% endif %}
                </nav>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- 查看评分结果模态框 -->
<div id="resultModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">评分详情</h3>
                <button onclick="closeResultModal()" class="text-gray-400 hover:text-gray-600">
                    <i data-lucide="x" class="w-6 h-6"></i>
                </button>
            </div>
            <div id="resultContent">
                <!-- 评分详情内容将在这里显示 -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 搜索功能
    document.getElementById('searchInput').addEventListener('input', function() {
        filterTasks();
    });

    // 状态筛选
    document.getElementById('statusFilter').addEventListener('change', function() {
        filterTasks();
    });

    // 部门筛选
    document.getElementById('departmentFilter').addEventListener('change', function() {
        filterTasks();
    });

    function filterTasks() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const statusFilter = document.getElementById('statusFilter').value;
        const departmentFilter = document.getElementById('departmentFilter').value;
        const taskCards = document.querySelectorAll('.task-card');

        taskCards.forEach(card => {
            const evaluatee = card.dataset.evaluatee.toLowerCase();
            const department = card.dataset.department;
            const status = card.dataset.status;

            let showCard = true;

            // 文本搜索
            if (searchTerm && !evaluatee.includes(searchTerm) && !department.toLowerCase().includes(searchTerm)) {
                showCard = false;
            }

            // 状态筛选
            if (statusFilter && status !== statusFilter) {
                showCard = false;
            }

            // 部门筛选
            if (departmentFilter && department !== departmentFilter) {
                showCard = false;
            }

            card.style.display = showCard ? 'block' : 'none';
        });
    }

    // 倒计时功能
    function updateCountdowns() {
        document.querySelectorAll('[id^="countdown-"]').forEach(element => {
            const endTime = new Date(element.dataset.endTime);
            const now = new Date();
            const diff = endTime - now;

            if (diff > 0) {
                const days = Math.floor(diff / (1000 * 60 * 60 * 24));
                const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

                if (days > 0) {
                    element.textContent = `${days}天${hours}小时`;
                    element.className = 'text-green-600';
                } else if (hours > 0) {
                    element.textContent = `${hours}小时${minutes}分钟`;
                    element.className = hours > 6 ? 'text-yellow-600' : 'text-red-600';
                } else {
                    element.textContent = `${minutes}分钟`;
                    element.className = 'text-red-600 font-bold';
                }
            } else {
                element.textContent = '已截止';
                element.className = 'text-red-600 font-bold';
            }
        });
    }

    // 查看评分结果
    function viewResults(taskId) {
        fetch(`/evaluations/anonymous/results/?task_id=${taskId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('resultContent').innerHTML = data.html;
                    document.getElementById('resultModal').classList.remove('hidden');
                } else {
                    showNotification('获取评分详情失败', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('获取评分详情失败', 'error');
            });
    }

    // 关闭结果模态框
    function closeResultModal() {
        document.getElementById('resultModal').classList.add('hidden');
    }

    // 点击模态框外部关闭
    document.getElementById('resultModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeResultModal();
        }
    });

    // 页面加载时初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 更新倒计时
        updateCountdowns();
        
        // 每分钟更新一次倒计时
        setInterval(updateCountdowns, 60000);
        
        // 去重部门选项
        const departmentSelect = document.getElementById('departmentFilter');
        const departments = new Set();
        Array.from(departmentSelect.options).forEach(option => {
            if (option.value && departments.has(option.value)) {
                option.remove();
            } else if (option.value) {
                departments.add(option.value);
            }
        });
    });
</script>
{% endblock %}