<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}通用员工评价系统{% endblock %}</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <!-- 自定义样式 -->
    <style>
        /* 全局样式 */
        * {
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #374151;
            background-color: #f9fafb;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        
        /* 导航栏 */
        .navbar {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 50;
        }
        
        .navbar-brand {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
        }
        
        .navbar-nav {
            display: flex;
            align-items: center;
            gap: 2rem;
        }
        
        .nav-link {
            color: #6b7280;
            text-decoration: none;
            transition: color 0.2s;
        }
        
        .nav-link:hover {
            color: #374151;
        }
        
        .nav-link.active {
            color: #3b82f6;
            font-weight: 500;
        }
        
        /* 主内容区域 */
        .main-content {
            min-height: calc(100vh - 80px);
            padding: 2rem 0;
        }
        
        /* 页脚 */
        .footer {
            background: white;
            border-top: 1px solid #e5e7eb;
            padding: 2rem 0;
            margin-top: auto;
            text-align: center;
            color: #6b7280;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="container">
            <div class="flex items-center justify-between">
                <div class="navbar-brand">
                    {% block brand %}通用员工评价系统{% endblock %}
                </div>
                
                <div class="navbar-nav">
                    {% block navigation %}
                    <a href="#" class="nav-link">首页</a>
                    <a href="#" class="nav-link">消息中心</a>
                    <a href="#" class="nav-link">设置</a>
                    {% endblock %}
                    
                    <!-- 用户菜单 -->
                    <div class="relative">
                        <div class="flex items-center space-x-2 text-sm">
                            <span class="text-gray-700">欢迎，用户</span>
                            <a href="#" class="text-blue-600 hover:text-blue-800">退出</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- 主内容区域 -->
    <main class="main-content">
        {% block content %}
        <div class="container">
            <h1>欢迎使用通用员工评价系统</h1>
        </div>
        {% endblock %}
    </main>
    
    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            {% block footer %}
            <p>&copy; 2024 通用员工评价系统. 保留所有权利.</p>
            {% endblock %}
        </div>
    </footer>
    
    <!-- JavaScript -->
    <script>
        // 初始化Lucide图标
        document.addEventListener('DOMContentLoaded', function() {
            if (window.lucide) {
                lucide.createIcons({ nameAttr: 'data-lucide' });
            }
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>