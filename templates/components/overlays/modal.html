<!-- 模态框组件 -->
<!-- 使用方式: {% include 'components/overlays/modal.html' with id='myModal' title='模态框标题' %} -->

{% load static %}

<!-- 模态框遮罩层 -->
<div id="{{ id|default:'modal' }}" 
     class="modal modal-overlay {{ size|default:'md' }} {{ extra_classes|default:'' }} hidden" 
     role="dialog" 
     aria-labelledby="{{ id|default:'modal' }}_title" 
     aria-hidden="true"
     {% if backdrop_static %}data-backdrop="static"{% endif %}>
    
    <!-- 模态框容器 -->
    <div class="modal-container">
        <!-- 模态框内容 -->
        <div class="modal-content">
            
            <!-- 模态框头部 -->
            {% if title or close_button %}
                <div class="modal-header">
                    <div class="modal-title-section">
                        {% if title_icon %}
                            <i data-lucide="{{ title_icon }}" class="modal-title-icon"></i>
                        {% endif %}
                        {% if title %}
                            <h3 id="{{ id|default:'modal' }}_title" class="modal-title">{{ title }}</h3>
                        {% endif %}
                        {% if subtitle %}
                            <p class="modal-subtitle">{{ subtitle }}</p>
                        {% endif %}
                    </div>
                    
                    {% if close_button != False %}
                        <button type="button" 
                                class="modal-close-btn" 
                                onclick="closeModal('{{ id|default:'modal' }}')"
                                aria-label="关闭">
                            <i data-lucide="x" class="w-5 h-5"></i>
                        </button>
                    {% endif %}
                </div>
            {% endif %}
            
            <!-- 模态框主体 -->
            <div class="modal-body">
                {% if loading %}
                    <div class="modal-loading">
                        <svg class="animate-spin h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-2 text-sm text-gray-600">{{ loading_text|default:'加载中...' }}</p>
                    </div>
                {% else %}
                    {% if content %}
                        {{ content|safe }}
                    {% else %}
                        <!-- 这里可以包含嵌套的内容 -->
                        {{ block.super }}
                    {% endif %}
                {% endif %}
            </div>
            
            <!-- 模态框底部 -->
            {% if footer_actions or footer_content %}
                <div class="modal-footer">
                    {% if footer_content %}
                        <div class="modal-footer-content">{{ footer_content|safe }}</div>
                    {% endif %}
                    
                    {% if footer_actions %}
                        <div class="modal-footer-actions">
                            {% for action in footer_actions %}
                                {% include 'components/base/button.html' with type=action.type size=action.size text=action.text icon=action.icon onclick=action.onclick disabled=action.disabled %}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
/* 模态框基础样式 */
.modal-overlay {
    @apply fixed inset-0 z-50 overflow-y-auto;
    backdrop-filter: blur(4px);
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-overlay.hidden {
    @apply invisible opacity-0;
}

.modal-overlay:not(.hidden) {
    @apply visible opacity-100;
    animation: fadeIn 0.3s ease-out;
}

.modal-overlay.closing {
    animation: fadeOut 0.3s ease-in forwards;
}

/* 模态框容器 */
.modal-container {
    @apply flex min-h-full items-center justify-center p-4;
}

.modal-content {
    @apply bg-white rounded-lg shadow-xl max-w-full max-h-full overflow-hidden;
    animation: slideUp 0.3s ease-out;
}

.modal-overlay.closing .modal-content {
    animation: slideDown 0.3s ease-in forwards;
}

/* 模态框尺寸 */
.modal.sm .modal-content {
    @apply w-full max-w-sm;
}

.modal.md .modal-content {
    @apply w-full max-w-md;
}

.modal.lg .modal-content {
    @apply w-full max-w-lg;
}

.modal.xl .modal-content {
    @apply w-full max-w-xl;
}

.modal.2xl .modal-content {
    @apply w-full max-w-2xl;
}

.modal.3xl .modal-content {
    @apply w-full max-w-3xl;
}

.modal.4xl .modal-content {
    @apply w-full max-w-4xl;
}

.modal.5xl .modal-content {
    @apply w-full max-w-5xl;
}

.modal.6xl .modal-content {
    @apply w-full max-w-6xl;
}

.modal.full .modal-content {
    @apply w-full max-w-none h-full max-h-none rounded-none;
}

/* 模态框头部 */
.modal-header {
    @apply px-6 py-4 border-b border-gray-200 flex items-center justify-between;
}

.modal-title-section {
    @apply flex items-center space-x-3 flex-1;
}

.modal-title-icon {
    @apply w-6 h-6 text-gray-500 flex-shrink-0;
}

.modal-title {
    @apply text-lg font-semibold text-gray-900;
}

.modal-subtitle {
    @apply text-sm text-gray-500 mt-1;
}

.modal-close-btn {
    @apply text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md p-1 transition-colors;
}

/* 模态框主体 */
.modal-body {
    @apply px-6 py-4 max-h-96 overflow-y-auto;
}

.modal.sm .modal-body {
    @apply px-4 py-3;
}

.modal.lg .modal-body,
.modal.xl .modal-body,
.modal.2xl .modal-body,
.modal.3xl .modal-body,
.modal.4xl .modal-body,
.modal.5xl .modal-body,
.modal.6xl .modal-body {
    @apply max-h-screen-3/4;
}

.modal.full .modal-body {
    @apply max-h-none;
}

/* 模态框加载状态 */
.modal-loading {
    @apply flex flex-col items-center justify-center py-8 text-center;
}

/* 模态框底部 */
.modal-footer {
    @apply px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-between;
}

.modal-footer-actions {
    @apply flex items-center space-x-3;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(1rem) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes slideDown {
    from {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    to {
        opacity: 0;
        transform: translateY(1rem) scale(0.95);
    }
}

/* 响应式调整 */
@media (max-width: 640px) {
    .modal-container {
        @apply p-2;
    }
    
    .modal-content {
        @apply mx-2;
    }
    
    .modal-header {
        @apply px-4 py-3;
    }
    
    .modal-body {
        @apply px-4 py-3;
    }
    
    .modal-footer {
        @apply px-4 py-3 flex-col space-y-2;
    }
    
    .modal-footer-actions {
        @apply w-full justify-center;
    }
}

/* 特殊变体 */
.modal-danger .modal-header {
    @apply bg-red-50 border-red-200;
}

.modal-danger .modal-title {
    @apply text-red-800;
}

.modal-warning .modal-header {
    @apply bg-yellow-50 border-yellow-200;
}

.modal-warning .modal-title {
    @apply text-yellow-800;
}

.modal-success .modal-header {
    @apply bg-green-50 border-green-200;
}

.modal-success .modal-title {
    @apply text-green-800;
}

.modal-info .modal-header {
    @apply bg-blue-50 border-blue-200;
}

.modal-info .modal-title {
    @apply text-blue-800;
}
</style>

<script>
// 模态框管理类
class ModalManager {
    constructor() {
        this.activeModals = new Set();
        this.setupEventListeners();
    }

    setupEventListeners() {
        // ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeTopModal();
            }
        });

        // 点击遮罩层关闭模态框（如果不是static backdrop）
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay') && 
                !e.target.hasAttribute('data-backdrop')) {
                this.close(e.target.id);
            }
        });
    }

    open(modalId, options = {}) {
        const modal = document.getElementById(modalId);
        if (!modal) {
            console.error(`Modal with id '${modalId}' not found`);
            return;
        }

        // 添加到活动模态框集合
        this.activeModals.add(modalId);

        // 防止页面滚动
        if (this.activeModals.size === 1) {
            document.body.style.overflow = 'hidden';
        }

        // 显示模态框
        modal.classList.remove('hidden');
        modal.setAttribute('aria-hidden', 'false');

        // 设置焦点
        const firstFocusable = modal.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
        if (firstFocusable) {
            setTimeout(() => firstFocusable.focus(), 100);
        }

        // 执行回调
        if (options.onOpen) {
            options.onOpen(modal);
        }

        // 触发自定义事件
        modal.dispatchEvent(new CustomEvent('modal:open', { detail: options }));
    }

    close(modalId) {
        const modal = document.getElementById(modalId);
        if (!modal || !this.activeModals.has(modalId)) {
            return;
        }

        // 添加关闭动画
        modal.classList.add('closing');

        setTimeout(() => {
            // 隐藏模态框
            modal.classList.add('hidden');
            modal.classList.remove('closing');
            modal.setAttribute('aria-hidden', 'true');

            // 从活动模态框集合中移除
            this.activeModals.delete(modalId);

            // 恢复页面滚动
            if (this.activeModals.size === 0) {
                document.body.style.overflow = '';
            }

            // 触发自定义事件
            modal.dispatchEvent(new CustomEvent('modal:close'));
        }, 300);
    }

    closeTopModal() {
        if (this.activeModals.size > 0) {
            const modalIds = Array.from(this.activeModals);
            const topModalId = modalIds[modalIds.length - 1];
            this.close(topModalId);
        }
    }

    closeAll() {
        Array.from(this.activeModals).forEach(modalId => {
            this.close(modalId);
        });
    }

    isOpen(modalId) {
        return this.activeModals.has(modalId);
    }
}

// 创建全局实例
const modalManager = new ModalManager();

// 便捷函数
function openModal(modalId, options) {
    modalManager.open(modalId, options);
}

function closeModal(modalId) {
    modalManager.close(modalId);
}

function toggleModal(modalId, options) {
    if (modalManager.isOpen(modalId)) {
        modalManager.close(modalId);
    } else {
        modalManager.open(modalId, options);
    }
}

// 确认对话框
function showConfirmDialog(title, message, options = {}) {
    const modalId = 'confirmDialog';
    let existingModal = document.getElementById(modalId);
    
    if (existingModal) {
        existingModal.remove();
    }

    const modal = document.createElement('div');
    modal.id = modalId;
    modal.className = 'modal modal-overlay md modal-danger hidden';
    modal.setAttribute('role', 'dialog');
    modal.setAttribute('aria-hidden', 'true');
    modal.setAttribute('data-backdrop', 'static');

    modal.innerHTML = `
        <div class="modal-container">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="modal-title-section">
                        <i data-lucide="alert-triangle" class="modal-title-icon text-red-500"></i>
                        <h3 class="modal-title">${title}</h3>
                    </div>
                </div>
                <div class="modal-body">
                    <p class="text-gray-700">${message}</p>
                </div>
                <div class="modal-footer">
                    <div class="modal-footer-actions">
                        <button type="button" class="btn btn-secondary btn-md" onclick="closeModal('${modalId}')">
                            ${options.cancelText || '取消'}
                        </button>
                        <button type="button" class="btn btn-danger btn-md" onclick="confirmDialogAction('${modalId}')">
                            ${options.confirmText || '确认'}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // 设置确认回调
    window.confirmDialogAction = function(id) {
        if (options.onConfirm) {
            options.onConfirm();
        }
        closeModal(id);
        delete window.confirmDialogAction;
    };

    // 初始化Lucide图标
    if (window.lucide) {
        lucide.createIcons({ nameAttr: 'data-lucide' });
    }

    openModal(modalId);
}

// 信息对话框
function showInfoDialog(title, message, options = {}) {
    const modalId = 'infoDialog';
    let existingModal = document.getElementById(modalId);
    
    if (existingModal) {
        existingModal.remove();
    }

    const modal = document.createElement('div');
    modal.id = modalId;
    modal.className = 'modal modal-overlay md modal-info hidden';
    modal.setAttribute('role', 'dialog');
    modal.setAttribute('aria-hidden', 'true');

    modal.innerHTML = `
        <div class="modal-container">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="modal-title-section">
                        <i data-lucide="info" class="modal-title-icon text-blue-500"></i>
                        <h3 class="modal-title">${title}</h3>
                    </div>
                </div>
                <div class="modal-body">
                    <p class="text-gray-700">${message}</p>
                </div>
                <div class="modal-footer">
                    <div class="modal-footer-actions">
                        <button type="button" class="btn btn-primary btn-md" onclick="closeModal('${modalId}')">
                            ${options.closeText || '确定'}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // 初始化Lucide图标
    if (window.lucide) {
        lucide.createIcons({ nameAttr: 'data-lucide' });
    }

    openModal(modalId, {
        onOpen: options.onOpen
    });
}

// 导出到全局作用域
window.modalManager = modalManager;
window.openModal = openModal;
window.closeModal = closeModal;
window.toggleModal = toggleModal;
window.showConfirmDialog = showConfirmDialog;
window.showInfoDialog = showInfoDialog;
</script>