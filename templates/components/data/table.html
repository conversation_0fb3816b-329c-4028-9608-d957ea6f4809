<!-- 数据表格组件 -->
<!-- 使用方式: {% include 'components/data/table.html' with columns=columns data=data %} -->

{% load static %}

<div class="table-container {{ container_classes|default:'' }}">
    <!-- 表格工具栏 -->
    {% if toolbar or search or filters or actions %}
        <div class="table-toolbar">
            <!-- 左侧：搜索和筛选 -->
            <div class="table-toolbar-left">
                {% if search %}
                    <div class="table-search">
                        {% include 'components/base/input.html' with type='text' name='search' placeholder=search.placeholder prefix_icon='search' input_classes='table-search-input' %}
                    </div>
                {% endif %}
                
                {% if filters %}
                    <div class="table-filters">
                        {% for filter in filters %}
                            {% if filter.type == 'select' %}
                                {% include 'components/base/input.html' with type='select' name=filter.name options=filter.options placeholder=filter.placeholder input_classes='table-filter-select' %}
                            {% elif filter.type == 'date' %}
                                {% include 'components/base/input.html' with type='date' name=filter.name placeholder=filter.placeholder input_classes='table-filter-date' %}
                            {% endif %}
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <!-- 右侧：操作按钮 -->
            {% if actions %}
                <div class="table-toolbar-right">
                    {% for action in actions %}
                        {% include 'components/base/button.html' with type=action.type size='sm' text=action.text icon=action.icon onclick=action.onclick %}
                    {% endfor %}
                </div>
            {% endif %}
        </div>
    {% endif %}
    
    <!-- 表格主体 -->
    <div class="table-wrapper {{ table_classes|default:'' }}">
        {% if loading %}
            <div class="table-loading">
                <svg class="animate-spin h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <p class="mt-2 text-sm text-gray-600">{{ loading_text|default:'加载中...' }}</p>
            </div>
        {% else %}
            <table class="table {{ table_variant|default:'default' }}">
                <!-- 表头 -->
                <thead class="table-header">
                    <tr>
                        {% if selectable %}
                            <th class="table-header-cell table-checkbox-cell">
                                <input type="checkbox" 
                                       class="table-checkbox table-select-all" 
                                       onchange="toggleSelectAll(this)" />
                            </th>
                        {% endif %}
                        
                        {% for column in columns %}
                            <th class="table-header-cell 
                                      {% if column.sortable %}table-sortable{% endif %}
                                      {% if column.width %}{{ column.width }}{% endif %}
                                      {{ column.classes|default:'' }}"
                                {% if column.sortable %}
                                    onclick="sortTable('{{ column.key }}', this)"
                                    data-sort-key="{{ column.key }}"
                                {% endif %}>
                                <div class="table-header-content">
                                    <span>{{ column.title }}</span>
                                    {% if column.sortable %}
                                        <i data-lucide="arrow-up-down" class="table-sort-icon w-4 h-4 text-gray-400"></i>
                                    {% endif %}
                                </div>
                            </th>
                        {% endfor %}
                        
                        {% if row_actions %}
                            <th class="table-header-cell table-actions-cell">操作</th>
                        {% endif %}
                    </tr>
                </thead>
                
                <!-- 表身 -->
                <tbody class="table-body">
                    {% if data %}
                        {% for row in data %}
                            <tr class="table-row {{ row.classes|default:'' }}"
                                {% if row.onclick %}onclick="{{ row.onclick }}"{% endif %}
                                data-row-id="{{ row.id|default:forloop.counter0 }}">
                                
                                {% if selectable %}
                                    <td class="table-cell table-checkbox-cell">
                                        <input type="checkbox" 
                                               class="table-checkbox table-row-select" 
                                               value="{{ row.id|default:forloop.counter0 }}"
                                               onchange="updateSelectAll()" />
                                    </td>
                                {% endif %}
                                
                                {% for column in columns %}
                                    <td class="table-cell {{ column.cell_classes|default:'' }}">
                                        {% if column.type == 'text' %}
                                            {{ row|get_item:column.key|default:'-' }}
                                        {% elif column.type == 'html' %}
                                            {{ row|get_item:column.key|default:'-'|safe }}
                                        {% elif column.type == 'status' %}
                                            <span class="table-status table-status-{{ row|get_item:column.key|lower }}">
                                                {{ row|get_item:column.display_key|default:row|get_item:column.key }}
                                            </span>
                                        {% elif column.type == 'badge' %}
                                            <span class="table-badge table-badge-{{ row|get_item:column.variant_key|default:'default' }}">
                                                {{ row|get_item:column.key }}
                                            </span>
                                        {% elif column.type == 'avatar' %}
                                            <div class="table-avatar">
                                                <img src="{{ row|get_item:column.src_key }}" alt="{{ row|get_item:column.alt_key }}" class="table-avatar-img" />
                                                {% if column.show_name %}
                                                    <span class="table-avatar-name">{{ row|get_item:column.name_key }}</span>
                                                {% endif %}
                                            </div>
                                        {% elif column.type == 'date' %}
                                            <span class="table-date" title="{{ row|get_item:column.key }}">
                                                {{ row|get_item:column.key|date:column.format|default:'Y-m-d H:i' }}
                                            </span>
                                        {% elif column.type == 'link' %}
                                            <a href="{{ row|get_item:column.href_key }}" 
                                               class="table-link"
                                               {% if column.target %}target="{{ column.target }}"{% endif %}>
                                                {{ row|get_item:column.key }}
                                            </a>
                                        {% elif column.type == 'custom' %}
                                            {% if column.template %}
                                                {% include column.template with item=row column=column %}
                                            {% else %}
                                                {{ row|get_item:column.key|default:'-' }}
                                            {% endif %}
                                        {% else %}
                                            {{ row|get_item:column.key|default:'-' }}
                                        {% endif %}
                                    </td>
                                {% endfor %}
                                
                                {% if row_actions %}
                                    <td class="table-cell table-actions-cell">
                                        <div class="table-row-actions">
                                            {% for action in row_actions %}
                                                {% if action.type == 'button' %}
                                                    {% include 'components/base/button.html' with type=action.variant size='sm' text=action.text icon=action.icon onclick=action.onclick %}
                                                {% elif action.type == 'dropdown' %}
                                                    <div class="relative inline-block text-left">
                                                        <button type="button" 
                                                                class="table-action-dropdown" 
                                                                onclick="toggleTableDropdown('{{ forloop.parentloop.counter0 }}_{{ forloop.counter0 }}')">
                                                            <i data-lucide="more-horizontal" class="w-4 h-4"></i>
                                                        </button>
                                                        <div id="dropdown_{{ forloop.parentloop.counter0 }}_{{ forloop.counter0 }}" 
                                                             class="table-dropdown hidden">
                                                            {% for item in action.items %}
                                                                <a href="{{ item.href|default:'#' }}" 
                                                                   class="table-dropdown-item"
                                                                   {% if item.onclick %}onclick="{{ item.onclick }}"{% endif %}>
                                                                    {% if item.icon %}
                                                                        <i data-lucide="{{ item.icon }}" class="w-4 h-4 mr-2"></i>
                                                                    {% endif %}
                                                                    {{ item.text }}
                                                                </a>
                                                            {% endfor %}
                                                        </div>
                                                    </div>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </td>
                                {% endif %}
                            </tr>
                        {% endfor %}
                    {% else %}
                        <tr class="table-empty-row">
                            <td class="table-empty-cell" colspan="{{ columns|length|add:selectable|add:row_actions }}">
                                <div class="table-empty-content">
                                    {% if empty_icon %}
                                        <i data-lucide="{{ empty_icon }}" class="table-empty-icon"></i>
                                    {% endif %}
                                    <p class="table-empty-text">{{ empty_text|default:'暂无数据' }}</p>
                                    {% if empty_action %}
                                        {% include 'components/base/button.html' with type=empty_action.type size='sm' text=empty_action.text icon=empty_action.icon onclick=empty_action.onclick %}
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        {% endif %}
    </div>
    
    <!-- 分页和信息 -->
    {% if pagination or info %}
        <div class="table-footer">
            {% if info %}
                <div class="table-info">
                    <span class="table-info-text">
                        显示第 {{ info.start|default:1 }} - {{ info.end|default:info.total }} 条，共 {{ info.total|default:0 }} 条记录
                    </span>
                    {% if selectable %}
                        <span class="table-selected-info ml-4">
                            已选择 <span class="table-selected-count">0</span> 项
                        </span>
                    {% endif %}
                </div>
            {% endif %}
            
            {% if pagination %}
                <div class="table-pagination">
                    <!-- 页面大小选择 -->
                    {% if pagination.show_size_selector %}
                        <div class="table-page-size">
                            <span class="text-sm text-gray-700 mr-2">每页</span>
                            <select class="table-page-size-select" onchange="changePageSize(this.value)">
                                {% for size in pagination.size_options|default:"10,20,50,100"|split:"," %}
                                    <option value="{{ size }}" {% if size == pagination.current_size %}selected{% endif %}>
                                        {{ size }}
                                    </option>
                                {% endfor %}
                            </select>
                            <span class="text-sm text-gray-700 ml-2">条</span>
                        </div>
                    {% endif %}
                    
                    <!-- 分页按钮 -->
                    <div class="table-page-buttons">
                        {% if pagination.current_page > 1 %}
                            <button type="button" class="table-page-btn" onclick="changePage(1)">
                                <i data-lucide="chevrons-left" class="w-4 h-4"></i>
                            </button>
                            <button type="button" class="table-page-btn" onclick="changePage({{ pagination.current_page|add:-1 }})">
                                <i data-lucide="chevron-left" class="w-4 h-4"></i>
                            </button>
                        {% endif %}
                        
                        {% for page in pagination.pages %}
                            {% if page == '...' %}
                                <span class="table-page-ellipsis">...</span>
                            {% else %}
                                <button type="button" 
                                        class="table-page-btn {% if page == pagination.current_page %}active{% endif %}"
                                        onclick="changePage({{ page }})">
                                    {{ page }}
                                </button>
                            {% endif %}
                        {% endfor %}
                        
                        {% if pagination.current_page < pagination.total_pages %}
                            <button type="button" class="table-page-btn" onclick="changePage({{ pagination.current_page|add:1 }})">
                                <i data-lucide="chevron-right" class="w-4 h-4"></i>
                            </button>
                            <button type="button" class="table-page-btn" onclick="changePage({{ pagination.total_pages }})">
                                <i data-lucide="chevrons-right" class="w-4 h-4"></i>
                            </button>
                        {% endif %}
                    </div>
                </div>
            {% endif %}
        </div>
    {% endif %}
</div>

<style>
/* 表格容器 */
.table-container {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden;
}

/* 工具栏 */
.table-toolbar {
    @apply px-6 py-4 border-b border-gray-200 flex items-center justify-between;
}

.table-toolbar-left {
    @apply flex items-center space-x-4;
}

.table-toolbar-right {
    @apply flex items-center space-x-2;
}

.table-search {
    @apply w-64;
}

.table-filters {
    @apply flex items-center space-x-2;
}

.table-filter-select,
.table-filter-date {
    @apply w-40;
}

/* 表格主体 */
.table-wrapper {
    @apply relative overflow-x-auto;
}

.table {
    @apply min-w-full divide-y divide-gray-200;
}

.table.striped .table-row:nth-child(even) {
    @apply bg-gray-50;
}

.table.bordered {
    @apply border border-gray-200;
}

.table.bordered .table-cell {
    @apply border-r border-gray-200;
}

.table.compact .table-cell {
    @apply px-3 py-2;
}

/* 表头 */
.table-header {
    @apply bg-gray-50;
}

.table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.table-header-cell.table-sortable {
    @apply cursor-pointer hover:bg-gray-100 transition-colors;
}

.table-header-content {
    @apply flex items-center space-x-1;
}

.table-sort-icon {
    @apply opacity-0 group-hover:opacity-100 transition-opacity;
}

.table-header-cell.sorted .table-sort-icon {
    @apply opacity-100;
}

.table-header-cell.sorted.asc .table-sort-icon {
    @apply transform rotate-180;
}

/* 表格行和单元格 */
.table-row {
    @apply hover:bg-gray-50 transition-colors;
}

.table-row.selected {
    @apply bg-blue-50;
}

.table-row.clickable {
    @apply cursor-pointer;
}

.table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

.table-checkbox-cell {
    @apply w-10 px-3;
}

.table-actions-cell {
    @apply w-20 text-right;
}

/* 复选框 */
.table-checkbox {
    @apply h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded;
}

/* 状态标签 */
.table-status {
    @apply inline-flex px-2 py-1 text-xs font-semibold rounded-full;
}

.table-status-active {
    @apply bg-green-100 text-green-800;
}

.table-status-inactive {
    @apply bg-gray-100 text-gray-800;
}

.table-status-pending {
    @apply bg-yellow-100 text-yellow-800;
}

.table-status-error {
    @apply bg-red-100 text-red-800;
}

/* 徽章 */
.table-badge {
    @apply inline-flex px-2 py-1 text-xs font-medium rounded;
}

.table-badge-default {
    @apply bg-gray-100 text-gray-800;
}

.table-badge-primary {
    @apply bg-blue-100 text-blue-800;
}

.table-badge-success {
    @apply bg-green-100 text-green-800;
}

.table-badge-warning {
    @apply bg-yellow-100 text-yellow-800;
}

.table-badge-danger {
    @apply bg-red-100 text-red-800;
}

/* 头像 */
.table-avatar {
    @apply flex items-center space-x-3;
}

.table-avatar-img {
    @apply h-8 w-8 rounded-full;
}

.table-avatar-name {
    @apply text-sm font-medium text-gray-900;
}

/* 链接 */
.table-link {
    @apply text-blue-600 hover:text-blue-800;
}

/* 行操作 */
.table-row-actions {
    @apply flex items-center space-x-2 justify-end;
}

.table-action-dropdown {
    @apply p-1 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100;
}

.table-dropdown {
    @apply absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-10;
}

.table-dropdown-item {
    @apply block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center;
}

/* 加载状态 */
.table-loading {
    @apply flex flex-col items-center justify-center py-12;
}

/* 空状态 */
.table-empty-row {
    @apply bg-gray-50;
}

.table-empty-cell {
    @apply px-6 py-12;
}

.table-empty-content {
    @apply text-center;
}

.table-empty-icon {
    @apply w-12 h-12 text-gray-300 mx-auto mb-4;
}

.table-empty-text {
    @apply text-gray-500 text-base mb-4;
}

/* 表格底部 */
.table-footer {
    @apply px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-between;
}

.table-info {
    @apply flex items-center text-sm text-gray-700;
}

.table-selected-count {
    @apply font-medium text-blue-600;
}

/* 分页 */
.table-pagination {
    @apply flex items-center space-x-4;
}

.table-page-size {
    @apply flex items-center;
}

.table-page-size-select {
    @apply px-2 py-1 border border-gray-300 rounded text-sm;
}

.table-page-buttons {
    @apply flex items-center space-x-1;
}

.table-page-btn {
    @apply px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 transition-colors;
}

.table-page-btn.active {
    @apply bg-blue-600 text-white border-blue-600;
}

.table-page-ellipsis {
    @apply px-3 py-1 text-sm text-gray-500;
}

/* 响应式 */
@media (max-width: 640px) {
    .table-toolbar {
        @apply flex-col items-stretch space-y-4;
    }
    
    .table-toolbar-left,
    .table-toolbar-right {
        @apply justify-center;
    }
    
    .table-footer {
        @apply flex-col items-stretch space-y-4;
    }
    
    .table-pagination {
        @apply justify-center;
    }
    
    .table-cell {
        @apply px-3 py-2;
    }
}
</style>

<script>
// 表格管理类
class DataTable {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId) || document.querySelector(containerId);
        this.options = {
            sortable: true,
            selectable: false,
            searchable: false,
            ...options
        };
        this.sortColumn = null;
        this.sortDirection = 'asc';
        this.selectedRows = new Set();
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.updateSelectAllState();
    }
    
    bindEvents() {
        // 搜索功能
        const searchInput = this.container.querySelector('.table-search-input');
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.search(e.target.value);
                }, 300);
            });
        }
        
        // 筛选功能
        const filters = this.container.querySelectorAll('.table-filter-select, .table-filter-date');
        filters.forEach(filter => {
            filter.addEventListener('change', () => {
                this.applyFilters();
            });
        });
    }
    
    // 排序功能
    sort(column, direction) {
        this.sortColumn = column;
        this.sortDirection = direction;
        
        // 更新排序图标
        const headers = this.container.querySelectorAll('.table-sortable');
        headers.forEach(header => {
            header.classList.remove('sorted', 'asc', 'desc');
            if (header.dataset.sortKey === column) {
                header.classList.add('sorted', direction);
            }
        });
        
        // 触发排序事件
        this.container.dispatchEvent(new CustomEvent('table:sort', {
            detail: { column, direction }
        }));
    }
    
    // 搜索功能
    search(query) {
        this.container.dispatchEvent(new CustomEvent('table:search', {
            detail: { query }
        }));
    }
    
    // 应用筛选
    applyFilters() {
        const filters = {};
        const filterElements = this.container.querySelectorAll('.table-filter-select, .table-filter-date');
        
        filterElements.forEach(filter => {
            if (filter.value) {
                filters[filter.name] = filter.value;
            }
        });
        
        this.container.dispatchEvent(new CustomEvent('table:filter', {
            detail: { filters }
        }));
    }
    
    // 选择功能
    toggleSelectAll() {
        const selectAllCheckbox = this.container.querySelector('.table-select-all');
        const rowCheckboxes = this.container.querySelectorAll('.table-row-select');
        
        if (selectAllCheckbox.checked) {
            rowCheckboxes.forEach(checkbox => {
                checkbox.checked = true;
                this.selectedRows.add(checkbox.value);
            });
        } else {
            rowCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
                this.selectedRows.delete(checkbox.value);
            });
        }
        
        this.updateSelectedInfo();
        this.onSelectionChange();
    }
    
    updateSelectAll() {
        const selectAllCheckbox = this.container.querySelector('.table-select-all');
        const rowCheckboxes = this.container.querySelectorAll('.table-row-select');
        const checkedCount = this.container.querySelectorAll('.table-row-select:checked').length;
        
        if (checkedCount === 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        } else if (checkedCount === rowCheckboxes.length) {
            selectAllCheckbox.checked = true;
            selectAllCheckbox.indeterminate = false;
        } else {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = true;
        }
        
        // 更新选中行集合
        this.selectedRows.clear();
        this.container.querySelectorAll('.table-row-select:checked').forEach(checkbox => {
            this.selectedRows.add(checkbox.value);
        });
        
        this.updateSelectedInfo();
        this.onSelectionChange();
    }
    
    updateSelectedInfo() {
        const selectedCount = this.selectedRows.size;
        const countElement = this.container.querySelector('.table-selected-count');
        if (countElement) {
            countElement.textContent = selectedCount;
        }
    }
    
    onSelectionChange() {
        this.container.dispatchEvent(new CustomEvent('table:selection-change', {
            detail: { selectedRows: Array.from(this.selectedRows) }
        }));
    }
    
    // 获取选中的行
    getSelectedRows() {
        return Array.from(this.selectedRows);
    }
    
    // 清除选择
    clearSelection() {
        this.selectedRows.clear();
        this.container.querySelectorAll('.table-row-select').forEach(checkbox => {
            checkbox.checked = false;
        });
        this.updateSelectAll();
    }
}

// 全局函数
function sortTable(column, element) {
    const currentDirection = element.classList.contains('asc') ? 'desc' : 'asc';
    
    // 获取表格实例
    const container = element.closest('.table-container');
    if (container && container.dataTable) {
        container.dataTable.sort(column, currentDirection);
    }
}

function toggleSelectAll(checkbox) {
    const container = checkbox.closest('.table-container');
    if (container && container.dataTable) {
        container.dataTable.toggleSelectAll();
    }
}

function updateSelectAll() {
    const checkbox = document.querySelector('.table-row-select:checked, .table-row-select:not(:checked)');
    if (checkbox) {
        const container = checkbox.closest('.table-container');
        if (container && container.dataTable) {
            container.dataTable.updateSelectAll();
        }
    }
}

function toggleTableDropdown(id) {
    const dropdown = document.getElementById('dropdown_' + id);
    if (dropdown) {
        dropdown.classList.toggle('hidden');
    }
}

function changePage(page) {
    // 触发分页事件
    document.dispatchEvent(new CustomEvent('table:page-change', {
        detail: { page }
    }));
}

function changePageSize(size) {
    // 触发页面大小变更事件
    document.dispatchEvent(new CustomEvent('table:page-size-change', {
        detail: { size: parseInt(size) }
    }));
}

// 点击外部关闭下拉菜单
document.addEventListener('click', function(event) {
    const dropdowns = document.querySelectorAll('.table-dropdown');
    dropdowns.forEach(function(dropdown) {
        if (!dropdown.contains(event.target) && !event.target.matches('.table-action-dropdown')) {
            dropdown.classList.add('hidden');
        }
    });
});

// 导出到全局作用域
window.DataTable = DataTable;
window.sortTable = sortTable;
window.toggleSelectAll = toggleSelectAll;
window.updateSelectAll = updateSelectAll;
window.toggleTableDropdown = toggleTableDropdown;
window.changePage = changePage;
window.changePageSize = changePageSize;
</script>