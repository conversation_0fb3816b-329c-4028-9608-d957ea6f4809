<!-- Toast通知组件 -->
<!-- 使用方式: 通过JavaScript调用 showToast('success', '操作成功', '数据已保存') -->

<div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2">
    <!-- Toast消息将通过JavaScript动态插入 -->
</div>

<style>
/* Toast基础样式 */
.toast {
    @apply max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden;
    animation: slideInRight 0.3s ease-out;
}

.toast.removing {
    animation: slideOutRight 0.3s ease-in forwards;
}

/* Toast变体 */
.toast-success {
    @apply border-l-4 border-green-400;
}

.toast-error {
    @apply border-l-4 border-red-400;
}

.toast-warning {
    @apply border-l-4 border-yellow-400;
}

.toast-info {
    @apply border-l-4 border-blue-400;
}

/* Toast内容 */
.toast-content {
    @apply p-4;
}

.toast-header {
    @apply flex items-start;
}

.toast-icon {
    @apply flex-shrink-0;
}

.toast-icon-success {
    @apply text-green-400;
}

.toast-icon-error {
    @apply text-red-400;
}

.toast-icon-warning {
    @apply text-yellow-400;
}

.toast-icon-info {
    @apply text-blue-400;
}

.toast-body {
    @apply ml-3 w-0 flex-1 pt-0.5;
}

.toast-title {
    @apply text-sm font-medium text-gray-900;
}

.toast-message {
    @apply mt-1 text-sm text-gray-500;
}

.toast-actions {
    @apply mt-3 flex space-x-2;
}

.toast-close {
    @apply ml-4 flex-shrink-0 flex;
}

.toast-close-btn {
    @apply bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500;
}

/* 动画 */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* 响应式调整 */
@media (max-width: 640px) {
    #toast-container {
        @apply left-4 right-4 top-4;
    }
}
</style>

<script>
// Toast通知系统
class ToastManager {
    constructor() {
        this.container = document.getElementById('toast-container');
        this.toasts = new Map();
        this.counter = 0;
    }

    show(type = 'info', title = '', message = '', options = {}) {
        const id = ++this.counter;
        const toast = this.createToast(id, type, title, message, options);
        
        this.container.appendChild(toast);
        this.toasts.set(id, toast);

        // 自动消失
        const duration = options.duration || this.getDefaultDuration(type);
        if (duration > 0) {
            setTimeout(() => {
                this.hide(id);
            }, duration);
        }

        return id;
    }

    hide(id) {
        const toast = this.toasts.get(id);
        if (toast) {
            toast.classList.add('removing');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
                this.toasts.delete(id);
            }, 300);
        }
    }

    createToast(id, type, title, message, options) {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.setAttribute('data-toast-id', id);

        const iconMap = {
            success: 'check-circle',
            error: 'x-circle',
            warning: 'alert-triangle',
            info: 'info'
        };

        let actionsHtml = '';
        if (options.actions && options.actions.length > 0) {
            actionsHtml = '<div class="toast-actions">';
            options.actions.forEach(action => {
                actionsHtml += `
                    <button type="button" 
                            class="btn btn-${action.type || 'secondary'} btn-sm"
                            onclick="${action.onclick || ''}">
                        ${action.text}
                    </button>
                `;
            });
            actionsHtml += '</div>';
        }

        toast.innerHTML = `
            <div class="toast-content">
                <div class="toast-header">
                    <div class="toast-icon toast-icon-${type}">
                        <i data-lucide="${iconMap[type]}" class="w-6 h-6"></i>
                    </div>
                    <div class="toast-body">
                        ${title ? `<p class="toast-title">${title}</p>` : ''}
                        ${message ? `<p class="toast-message">${message}</p>` : ''}
                        ${actionsHtml}
                    </div>
                    ${options.closable !== false ? `
                        <div class="toast-close">
                            <button type="button" class="toast-close-btn" onclick="toastManager.hide(${id})">
                                <span class="sr-only">关闭</span>
                                <i data-lucide="x" class="w-5 h-5"></i>
                            </button>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;

        // 初始化Lucide图标
        if (window.lucide) {
            lucide.createIcons({ nameAttr: 'data-lucide' });
        }

        return toast;
    }

    getDefaultDuration(type) {
        const durations = {
            success: 3000,
            info: 4000,
            warning: 5000,
            error: 0 // 错误消息不自动消失
        };
        return durations[type] || 4000;
    }

    clear() {
        this.toasts.forEach((toast, id) => {
            this.hide(id);
        });
    }
}

// 创建全局实例
const toastManager = new ToastManager();

// 便捷方法
function showToast(type, title, message, options) {
    return toastManager.show(type, title, message, options);
}

function showSuccess(title, message, options) {
    return showToast('success', title, message, options);
}

function showError(title, message, options) {
    return showToast('error', title, message, options);
}

function showWarning(title, message, options) {
    return showToast('warning', title, message, options);
}

function showInfo(title, message, options) {
    return showToast('info', title, message, options);
}

// API响应处理辅助函数
function handleApiResponse(response, successTitle = '操作成功') {
    if (response.success) {
        showSuccess(successTitle, response.message);
    } else {
        showError('操作失败', response.message || response.error);
    }
}

// 导出到全局作用域
window.toastManager = toastManager;
window.showToast = showToast;
window.showSuccess = showSuccess;
window.showError = showError;
window.showWarning = showWarning;
window.showInfo = showInfo;
window.handleApiResponse = handleApiResponse;
</script>