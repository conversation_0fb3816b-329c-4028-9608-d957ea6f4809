<!-- 页面内提示组件 -->
<!-- 使用方式: {% include 'components/notifications/alert.html' with type='success' title='成功' message='操作完成' %} -->

{% load static %}

<div class="alert alert-{{ type|default:'info' }} {{ extra_classes|default:'' }}" 
     {% if id %}id="{{ id }}"{% endif %}
     {% if dismissible %}data-dismissible="true"{% endif %}>
    
    <div class="alert-content">
        <div class="alert-header">
            <!-- 图标 -->
            <div class="alert-icon">
                {% if type == 'success' %}
                    <i data-lucide="check-circle" class="w-5 h-5"></i>
                {% elif type == 'error' or type == 'danger' %}
                    <i data-lucide="x-circle" class="w-5 h-5"></i>
                {% elif type == 'warning' %}
                    <i data-lucide="alert-triangle" class="w-5 h-5"></i>
                {% else %}
                    <i data-lucide="info" class="w-5 h-5"></i>
                {% endif %}
            </div>
            
            <!-- 内容 -->
            <div class="alert-body">
                {% if title %}
                    <h4 class="alert-title">{{ title }}</h4>
                {% endif %}
                
                {% if message %}
                    <div class="alert-message">
                        {{ message|safe }}
                    </div>
                {% endif %}
                
                {% if content %}
                    <div class="alert-content-custom">
                        {{ content|safe }}
                    </div>
                {% endif %}
                
                {% if actions %}
                    <div class="alert-actions">
                        {% for action in actions %}
                            {% include 'components/base/button.html' with type=action.type size='sm' text=action.text onclick=action.onclick %}
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <!-- 关闭按钮 -->
            {% if dismissible %}
                <div class="alert-close">
                    <button type="button" class="alert-close-btn" onclick="dismissAlert(this)">
                        <span class="sr-only">关闭</span>
                        <i data-lucide="x" class="w-4 h-4"></i>
                    </button>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
/* Alert基础样式 */
.alert {
    @apply relative rounded-md p-4 mb-4;
}

/* Alert变体 */
.alert-success {
    @apply bg-green-50 border border-green-200;
}

.alert-error, .alert-danger {
    @apply bg-red-50 border border-red-200;
}

.alert-warning {
    @apply bg-yellow-50 border border-yellow-200;
}

.alert-info {
    @apply bg-blue-50 border border-blue-200;
}

/* Alert内容布局 */
.alert-header {
    @apply flex items-start;
}

.alert-icon {
    @apply flex-shrink-0;
}

.alert-success .alert-icon {
    @apply text-green-400;
}

.alert-error .alert-icon, .alert-danger .alert-icon {
    @apply text-red-400;
}

.alert-warning .alert-icon {
    @apply text-yellow-400;
}

.alert-info .alert-icon {
    @apply text-blue-400;
}

.alert-body {
    @apply ml-3 flex-1;
}

.alert-title {
    @apply text-sm font-medium mb-1;
}

.alert-success .alert-title {
    @apply text-green-800;
}

.alert-error .alert-title, .alert-danger .alert-title {
    @apply text-red-800;
}

.alert-warning .alert-title {
    @apply text-yellow-800;
}

.alert-info .alert-title {
    @apply text-blue-800;
}

.alert-message {
    @apply text-sm;
}

.alert-success .alert-message {
    @apply text-green-700;
}

.alert-error .alert-message, .alert-danger .alert-message {
    @apply text-red-700;
}

.alert-warning .alert-message {
    @apply text-yellow-700;
}

.alert-info .alert-message {
    @apply text-blue-700;
}

.alert-content-custom {
    @apply mt-2;
}

.alert-actions {
    @apply mt-3 flex space-x-2;
}

.alert-close {
    @apply ml-auto pl-3;
}

.alert-close-btn {
    @apply inline-flex rounded-md p-1.5 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.alert-success .alert-close-btn {
    @apply bg-green-50 text-green-500 hover:bg-green-100 focus:ring-green-600;
}

.alert-error .alert-close-btn, .alert-danger .alert-close-btn {
    @apply bg-red-50 text-red-500 hover:bg-red-100 focus:ring-red-600;
}

.alert-warning .alert-close-btn {
    @apply bg-yellow-50 text-yellow-500 hover:bg-yellow-100 focus:ring-yellow-600;
}

.alert-info .alert-close-btn {
    @apply bg-blue-50 text-blue-500 hover:bg-blue-100 focus:ring-blue-600;
}

/* 特殊变体 */
.alert-solid {
    @apply border-0;
}

.alert-solid.alert-success {
    @apply bg-green-600 text-white;
}

.alert-solid.alert-error, .alert-solid.alert-danger {
    @apply bg-red-600 text-white;
}

.alert-solid.alert-warning {
    @apply bg-yellow-600 text-white;
}

.alert-solid.alert-info {
    @apply bg-blue-600 text-white;
}

.alert-minimal {
    @apply bg-transparent border-0 border-l-4 pl-4 rounded-none;
}

.alert-minimal.alert-success {
    @apply border-green-400;
}

.alert-minimal.alert-error, .alert-minimal.alert-danger {
    @apply border-red-400;
}

.alert-minimal.alert-warning {
    @apply border-yellow-400;
}

.alert-minimal.alert-info {
    @apply border-blue-400;
}

/* 动画效果 */
.alert {
    animation: slideDown 0.3s ease-out;
}

.alert.dismissing {
    animation: slideUp 0.3s ease-in forwards;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 1;
        transform: translateY(0);
        max-height: 200px;
    }
    to {
        opacity: 0;
        transform: translateY(-10px);
        max-height: 0;
        margin: 0;
        padding: 0;
    }
}
</style>

<script>
// Alert相关JavaScript函数
function dismissAlert(button) {
    const alert = button.closest('.alert');
    if (alert) {
        alert.classList.add('dismissing');
        setTimeout(() => {
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        }, 300);
    }
}

// 程序化创建Alert
function createAlert(type, title, message, options = {}) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} ${options.extraClasses || ''}`;
    
    const iconMap = {
        success: 'check-circle',
        error: 'x-circle',
        danger: 'x-circle',
        warning: 'alert-triangle',
        info: 'info'
    };
    
    let actionsHtml = '';
    if (options.actions && options.actions.length > 0) {
        actionsHtml = '<div class="alert-actions">';
        options.actions.forEach(action => {
            actionsHtml += `
                <button type="button" 
                        class="btn btn-${action.type || 'secondary'} btn-sm"
                        onclick="${action.onclick || ''}">
                    ${action.text}
                </button>
            `;
        });
        actionsHtml += '</div>';
    }
    
    alertDiv.innerHTML = `
        <div class="alert-content">
            <div class="alert-header">
                <div class="alert-icon">
                    <i data-lucide="${iconMap[type]}" class="w-5 h-5"></i>
                </div>
                <div class="alert-body">
                    ${title ? `<h4 class="alert-title">${title}</h4>` : ''}
                    ${message ? `<div class="alert-message">${message}</div>` : ''}
                    ${actionsHtml}
                </div>
                ${options.dismissible !== false ? `
                    <div class="alert-close">
                        <button type="button" class="alert-close-btn" onclick="dismissAlert(this)">
                            <span class="sr-only">关闭</span>
                            <i data-lucide="x" class="w-4 h-4"></i>
                        </button>
                    </div>
                ` : ''}
            </div>
        </div>
    `;
    
    // 初始化Lucide图标
    if (window.lucide) {
        lucide.createIcons({ nameAttr: 'data-lucide' });
    }
    
    return alertDiv;
}

// 便捷函数
function showAlert(type, title, message, container, options = {}) {
    const alert = createAlert(type, title, message, options);
    
    if (typeof container === 'string') {
        container = document.getElementById(container) || document.querySelector(container);
    }
    
    if (container) {
        container.appendChild(alert);
        
        // 自动消失
        if (options.autoDismiss !== false && type === 'success') {
            setTimeout(() => {
                dismissAlert(alert.querySelector('.alert-close-btn'));
            }, options.duration || 5000);
        }
    }
    
    return alert;
}

// 导出到全局作用域
window.dismissAlert = dismissAlert;
window.createAlert = createAlert;
window.showAlert = showAlert;
</script>