<!-- 通用按钮组件 -->
<!-- 使用方式: {% include 'components/base/button.html' with type='primary' size='md' text='确认' icon='check' disabled=False %} -->

{% load static %}

<button 
    type="{{ button_type|default:'button' }}"
    class="btn btn-{{ type|default:'primary' }} btn-{{ size|default:'md' }} 
           {% if disabled %}btn-disabled{% endif %} 
           {% if full_width %}w-full{% endif %} 
           {{ extra_classes|default:'' }}"
    {% if disabled %}disabled{% endif %}
    {% if onclick %}onclick="{{ onclick }}"{% endif %}
    {% if id %}id="{{ id }}"{% endif %}
    {% if data_attrs %}
        {% for key, value in data_attrs.items %}data-{{ key }}="{{ value }}"{% endfor %}
    {% endif %}>
    
    {% if loading %}
        <!-- 加载状态 -->
        <div class="inline-flex items-center">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span>{{ loading_text|default:'处理中...' }}</span>
        </div>
    {% else %}
        <!-- 正常状态 -->
        <div class="inline-flex items-center justify-center space-x-2">
            {% if icon %}
                <i data-lucide="{{ icon }}" class="w-4 h-4 {% if icon_only %}{% else %}{% if text %}-ml-1{% endif %}{% endif %}"></i>
            {% endif %}
            
            {% if text and not icon_only %}
                <span>{{ text }}</span>
            {% endif %}
        </div>
    {% endif %}
</button>

<style>
/* 按钮基础样式 */
.btn {
    @apply inline-flex items-center justify-center font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
}

/* 按钮尺寸 */
.btn-sm {
    @apply px-3 py-1.5 text-sm;
}

.btn-md {
    @apply px-4 py-2 text-sm;
}

.btn-lg {
    @apply px-6 py-3 text-base;
}

.btn-xl {
    @apply px-8 py-4 text-lg;
}

/* 按钮类型 */
.btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
    @apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
}

.btn-success {
    @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
}

.btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}

.btn-warning {
    @apply bg-yellow-500 text-white hover:bg-yellow-600 focus:ring-yellow-500;
}

.btn-info {
    @apply bg-cyan-600 text-white hover:bg-cyan-700 focus:ring-cyan-500;
}

.btn-outline-primary {
    @apply border border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white focus:ring-blue-500;
}

.btn-outline-secondary {
    @apply border border-gray-400 text-gray-700 hover:bg-gray-100 focus:ring-gray-500;
}

.btn-link {
    @apply text-blue-600 hover:text-blue-700 hover:underline focus:ring-blue-500;
}

.btn-ghost {
    @apply text-gray-700 hover:bg-gray-100 focus:ring-gray-500;
}

/* 禁用状态 */
.btn-disabled {
    @apply opacity-50 cursor-not-allowed pointer-events-none;
}

/* 图标按钮 */
.btn-icon {
    @apply p-2 rounded-full;
}
</style>