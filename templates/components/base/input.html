<!-- 通用输入框组件 -->
<!-- 使用方式: {% include 'components/base/input.html' with type='text' name='username' label='用户名' required=True %} -->

{% load static %}

<div class="form-group {% if full_width %}w-full{% endif %} {{ wrapper_classes|default:'' }}">
    {% if label %}
        <label for="{{ id|default:name }}" 
               class="form-label block text-sm font-medium text-gray-700 mb-1">
            {{ label }}
            {% if required %}
                <span class="text-red-500 ml-1">*</span>
            {% endif %}
            {% if tooltip %}
                <span class="tooltip ml-1" title="{{ tooltip }}">
                    <i data-lucide="help-circle" class="w-4 h-4 text-gray-400 inline"></i>
                </span>
            {% endif %}
        </label>
    {% endif %}

    <div class="input-wrapper relative">
        {% if prefix_icon %}
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i data-lucide="{{ prefix_icon }}" class="w-5 h-5 text-gray-400"></i>
            </div>
        {% endif %}

        {% if type == 'textarea' %}
            <textarea
                {% if id %}id="{{ id }}"{% else %}id="{{ name }}"{% endif %}
                name="{{ name }}"
                class="form-input form-textarea 
                       {% if prefix_icon %}pl-10{% endif %}
                       {% if suffix_icon or suffix_button %}pr-10{% endif %}
                       {% if error %}border-red-300 focus:border-red-500 focus:ring-red-500{% endif %}
                       {{ input_classes|default:'' }}"
                {% if placeholder %}placeholder="{{ placeholder }}"{% endif %}
                {% if required %}required{% endif %}
                {% if disabled %}disabled{% endif %}
                {% if readonly %}readonly{% endif %}
                {% if rows %}rows="{{ rows }}"{% else %}rows="3"{% endif %}
                {% if maxlength %}maxlength="{{ maxlength }}"{% endif %}
                {% if data_attrs %}
                    {% for key, value in data_attrs.items %}data-{{ key }}="{{ value }}"{% endfor %}
                {% endif %}>{{ value|default:'' }}</textarea>
        {% elif type == 'select' %}
            <select
                {% if id %}id="{{ id }}"{% else %}id="{{ name }}"{% endif %}
                name="{{ name }}"
                class="form-select 
                       {% if prefix_icon %}pl-10{% endif %}
                       {% if error %}border-red-300 focus:border-red-500 focus:ring-red-500{% endif %}
                       {{ input_classes|default:'' }}"
                {% if required %}required{% endif %}
                {% if disabled %}disabled{% endif %}
                {% if multiple %}multiple{% endif %}
                {% if data_attrs %}
                    {% for key, value in data_attrs.items %}data-{{ key }}="{{ value }}"{% endfor %}
                {% endif %}>
                
                {% if placeholder %}
                    <option value="">{{ placeholder }}</option>
                {% endif %}
                
                {% for option in options %}
                    <option value="{{ option.value }}" 
                            {% if option.value == value %}selected{% endif %}>
                        {{ option.label }}
                    </option>
                {% endfor %}
            </select>
        {% else %}
            <input
                type="{{ type|default:'text' }}"
                {% if id %}id="{{ id }}"{% else %}id="{{ name }}"{% endif %}
                name="{{ name }}"
                class="form-input 
                       {% if prefix_icon %}pl-10{% endif %}
                       {% if suffix_icon or suffix_button %}pr-10{% endif %}
                       {% if error %}border-red-300 focus:border-red-500 focus:ring-red-500{% endif %}
                       {{ input_classes|default:'' }}"
                {% if placeholder %}placeholder="{{ placeholder }}"{% endif %}
                {% if value %}value="{{ value }}"{% endif %}
                {% if required %}required{% endif %}
                {% if disabled %}disabled{% endif %}
                {% if readonly %}readonly{% endif %}
                {% if min %}min="{{ min }}"{% endif %}
                {% if max %}max="{{ max }}"{% endif %}
                {% if step %}step="{{ step }}"{% endif %}
                {% if maxlength %}maxlength="{{ maxlength }}"{% endif %}
                {% if pattern %}pattern="{{ pattern }}"{% endif %}
                {% if autocomplete %}autocomplete="{{ autocomplete }}"{% endif %}
                {% if data_attrs %}
                    {% for key, value in data_attrs.items %}data-{{ key }}="{{ value }}"{% endfor %}
                {% endif %} />
        {% endif %}

        {% if suffix_icon %}
            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <i data-lucide="{{ suffix_icon }}" class="w-5 h-5 text-gray-400"></i>
            </div>
        {% endif %}

        {% if suffix_button %}
            <div class="absolute inset-y-0 right-0 flex items-center">
                {% include 'components/base/button.html' with type='ghost' size='sm' icon=suffix_button.icon text=suffix_button.text onclick=suffix_button.onclick %}
            </div>
        {% endif %}

        {% if loading %}
            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <svg class="animate-spin h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            </div>
        {% endif %}
    </div>

    {% if help_text %}
        <p class="form-help mt-1 text-sm text-gray-500">{{ help_text }}</p>
    {% endif %}

    {% if error %}
        <p class="form-error mt-1 text-sm text-red-600 flex items-center">
            <i data-lucide="alert-circle" class="w-4 h-4 mr-1"></i>
            {{ error }}
        </p>
    {% endif %}

    {% if maxlength and show_counter %}
        <div class="form-counter mt-1 text-right text-xs text-gray-500">
            <span id="{{ id|default:name }}_counter">0</span>/{{ maxlength }}
        </div>
    {% endif %}
</div>

<style>
/* 表单基础样式 */
.form-group {
    @apply mb-4;
}

.form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm 
           placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 
           disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed;
}

.form-textarea {
    @apply resize-vertical;
}

.form-select {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm 
           focus:outline-none focus:ring-blue-500 focus:border-blue-500 
           disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed;
}

.form-help {
    @apply mt-1 text-sm text-gray-500;
}

.form-error {
    @apply mt-1 text-sm text-red-600;
}

.form-counter {
    @apply mt-1 text-right text-xs text-gray-500;
}

/* 输入框尺寸变体 */
.form-input-sm {
    @apply px-2 py-1 text-sm;
}

.form-input-lg {
    @apply px-4 py-3 text-base;
}

/* 特殊状态 */
.form-input-success {
    @apply border-green-300 focus:border-green-500 focus:ring-green-500;
}

.form-input-warning {
    @apply border-yellow-300 focus:border-yellow-500 focus:ring-yellow-500;
}
</style>

<script>
// 字符计数器
document.addEventListener('DOMContentLoaded', function() {
    const inputs = document.querySelectorAll('input[maxlength], textarea[maxlength]');
    inputs.forEach(function(input) {
        const counter = document.getElementById(input.id + '_counter');
        if (counter) {
            function updateCounter() {
                counter.textContent = input.value.length;
            }
            input.addEventListener('input', updateCounter);
            updateCounter(); // 初始化
        }
    });
});
</script>