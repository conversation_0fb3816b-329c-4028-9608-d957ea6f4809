<!-- 通用卡片组件 -->
<!-- 使用方式: {% include 'components/base/card.html' with title='卡片标题' %} -->

{% load static %}

<div class="card {{ variant|default:'default' }} {{ size|default:'md' }} {{ extra_classes|default:'' }}">
    {% if title or actions %}
        <div class="card-header">
            {% if title %}
                <div class="card-title-section">
                    {% if title_icon %}
                        <i data-lucide="{{ title_icon }}" class="card-title-icon"></i>
                    {% endif %}
                    <h3 class="card-title">{{ title }}</h3>
                    {% if subtitle %}
                        <p class="card-subtitle">{{ subtitle }}</p>
                    {% endif %}
                </div>
            {% endif %}
            
            {% if actions %}
                <div class="card-actions">
                    {% for action in actions %}
                        {% if action.type == 'button' %}
                            {% include 'components/base/button.html' with type=action.variant size='sm' text=action.text icon=action.icon onclick=action.onclick %}
                        {% elif action.type == 'dropdown' %}
                            <div class="relative inline-block text-left">
                                <button type="button" class="card-action-dropdown" onclick="toggleDropdown('{{ action.id }}')">
                                    <i data-lucide="{{ action.icon|default:'more-horizontal' }}" class="w-4 h-4"></i>
                                </button>
                                <div id="{{ action.id }}" class="card-dropdown hidden">
                                    {% for item in action.items %}
                                        <a href="{{ item.url|default:'#' }}" 
                                           class="card-dropdown-item"
                                           {% if item.onclick %}onclick="{{ item.onclick }}"{% endif %}>
                                            {% if item.icon %}
                                                <i data-lucide="{{ item.icon }}" class="w-4 h-4 mr-2"></i>
                                            {% endif %}
                                            {{ item.text }}
                                        </a>
                                    {% endfor %}
                                </div>
                            </div>
                        {% endif %}
                    {% endfor %}
                </div>
            {% endif %}
        </div>
    {% endif %}

    {% if content or block.super %}
        <div class="card-content">
            {% if content %}
                {{ content|safe }}
            {% else %}
                {{ block.super }}
            {% endif %}
        </div>
    {% endif %}

    {% if footer_content or footer_actions %}
        <div class="card-footer">
            {% if footer_content %}
                <div class="card-footer-content">
                    {{ footer_content|safe }}
                </div>
            {% endif %}
            
            {% if footer_actions %}
                <div class="card-footer-actions">
                    {% for action in footer_actions %}
                        {% include 'components/base/button.html' with type=action.type size=action.size text=action.text icon=action.icon onclick=action.onclick %}
                    {% endfor %}
                </div>
            {% endif %}
        </div>
    {% endif %}

    {% if loading %}
        <div class="card-loading">
            <div class="card-loading-content">
                <svg class="animate-spin h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <p class="mt-2 text-sm text-gray-600">{{ loading_text|default:'加载中...' }}</p>
            </div>
        </div>
    {% endif %}
</div>

<style>
/* 卡片基础样式 */
.card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden;
    position: relative;
}

/* 卡片变体 */
.card.default {
    @apply bg-white border-gray-200;
}

.card.primary {
    @apply border-blue-200 bg-blue-50;
}

.card.success {
    @apply border-green-200 bg-green-50;
}

.card.warning {
    @apply border-yellow-200 bg-yellow-50;
}

.card.danger {
    @apply border-red-200 bg-red-50;
}

.card.flat {
    @apply shadow-none border-0 bg-gray-50;
}

.card.elevated {
    @apply shadow-lg border-0;
}

/* 卡片尺寸 */
.card.sm {
    @apply text-sm;
}

.card.md {
    @apply text-base;
}

.card.lg {
    @apply text-lg;
}

/* 卡片头部 */
.card-header {
    @apply px-6 py-4 border-b border-gray-200 flex items-center justify-between;
}

.card-title-section {
    @apply flex items-center space-x-3;
}

.card-title-icon {
    @apply w-5 h-5 text-gray-500;
}

.card-title {
    @apply text-lg font-semibold text-gray-900;
}

.card-subtitle {
    @apply text-sm text-gray-500 mt-1;
}

.card-actions {
    @apply flex items-center space-x-2;
}

.card-action-dropdown {
    @apply p-1 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100;
}

.card-dropdown {
    @apply absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-10;
}

.card-dropdown-item {
    @apply block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center;
}

/* 卡片内容 */
.card-content {
    @apply px-6 py-4;
}

.card.sm .card-content {
    @apply px-4 py-3;
}

.card.lg .card-content {
    @apply px-8 py-6;
}

/* 卡片底部 */
.card-footer {
    @apply px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-between;
}

.card-footer-actions {
    @apply flex items-center space-x-2;
}

/* 加载状态 */
.card-loading {
    @apply absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center;
}

.card-loading-content {
    @apply text-center;
}

/* 响应式调整 */
@media (max-width: 640px) {
    .card-header {
        @apply px-4 py-3 flex-col items-start space-y-2;
    }
    
    .card-content {
        @apply px-4 py-3;
    }
    
    .card-footer {
        @apply px-4 py-3 flex-col items-stretch space-y-2;
    }
    
    .card-footer-actions {
        @apply justify-center;
    }
}

/* 特殊状态 */
.card-hoverable {
    @apply transition-shadow duration-200 cursor-pointer;
}

.card-hoverable:hover {
    @apply shadow-md;
}

.card-selectable {
    @apply transition-all duration-200 cursor-pointer;
}

.card-selectable:hover {
    @apply ring-2 ring-blue-200;
}

.card-selected {
    @apply ring-2 ring-blue-500 border-blue-500;
}
</style>

<script>
// 下拉菜单控制
function toggleDropdown(id) {
    const dropdown = document.getElementById(id);
    if (dropdown) {
        dropdown.classList.toggle('hidden');
    }
}

// 点击外部关闭下拉菜单
document.addEventListener('click', function(event) {
    const dropdowns = document.querySelectorAll('.card-dropdown');
    dropdowns.forEach(function(dropdown) {
        if (!dropdown.contains(event.target) && !event.target.matches('.card-action-dropdown')) {
            dropdown.classList.add('hidden');
        }
    });
});
</script>