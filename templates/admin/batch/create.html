{% extends "admin/base_admin.html" %}

{% block page_title %}创建考评批次{% endblock %}
{% block page_description %}创建新的考评批次，配置考评参数和时间安排{% endblock %}

{% block header_actions %}
<a href="{% url 'evaluations:admin:batch_list' %}" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center space-x-2 transition-colors duration-200">
    <i data-lucide="arrow-left" class="w-4 h-4"></i>
    <span>返回列表</span>
</a>
{% endblock %}

{% block admin_content %}
<div class="max-w-4xl mx-auto">
    <!-- 进度指示器 -->
    <div class="mb-8">
        <div class="flex items-center justify-center">
            <div class="flex items-center space-x-4">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                        <i data-lucide="calendar" class="w-4 h-4 text-white"></i>
                    </div>
                    <span class="ml-2 text-sm font-medium text-blue-600">基本信息</span>
                </div>
                <div class="w-16 h-0.5 bg-gray-300"></div>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                        <i data-lucide="clock" class="w-4 h-4 text-gray-500"></i>
                    </div>
                    <span class="ml-2 text-sm text-gray-500">时间设置</span>
                </div>
                <div class="w-16 h-0.5 bg-gray-300"></div>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                        <i data-lucide="check" class="w-4 h-4 text-gray-500"></i>
                    </div>
                    <span class="ml-2 text-sm text-gray-500">完成</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 主表单卡片 -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
        <!-- 卡片头部 -->
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                    <i data-lucide="calendar-plus" class="w-5 h-5 text-white"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-900">创建考评批次</h3>
                    <p class="text-sm text-gray-600">配置考评活动的基本信息和时间安排</p>
                </div>
            </div>
        </div>

        <form method="post" class="p-6" id="batchForm">
            {% csrf_token %}

            <!-- 基本信息区域 -->
            <div class="space-y-6">
                <div class="border-b border-gray-200 pb-4">
                    <h4 class="text-md font-semibold text-gray-900 flex items-center">
                        <i data-lucide="info" class="w-4 h-4 mr-2 text-blue-600"></i>
                        基本信息
                    </h4>
                    <p class="mt-1 text-sm text-gray-600">设置考评批次的基础信息</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 批次名称 -->
                    <div class="group">
                        <label for="{{ form.name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            <i data-lucide="tag" class="w-4 h-4 inline mr-1 text-gray-500"></i>
                            批次名称 <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            {{ form.name }}
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <i data-lucide="type" class="w-4 h-4 text-gray-400"></i>
                            </div>
                        </div>
                        {% if form.name.errors %}
                            <p class="mt-2 text-sm text-red-600 flex items-center">
                                <i data-lucide="x-circle" class="w-3 h-3 mr-1"></i>
                                {{ form.name.errors.0 }}
                            </p>
                        {% endif %}
                        <p class="mt-2 text-sm text-gray-500 flex items-center">
                            <i data-lucide="help-circle" class="w-3 h-3 mr-1"></i>
                            {{ form.name.help_text }}
                        </p>
                    </div>

                    <!-- 默认模板 -->
                    <div class="group">
                        <label for="{{ form.default_template.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            <i data-lucide="file-text" class="w-4 h-4 inline mr-1 text-gray-500"></i>
                            默认评价模板 <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            {{ form.default_template }}
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <i data-lucide="template" class="w-4 h-4 text-gray-400"></i>
                            </div>
                        </div>
                        {% if form.default_template.errors %}
                            <p class="mt-2 text-sm text-red-600 flex items-center">
                                <i data-lucide="alert-triangle" class="w-3 h-3 mr-1"></i>
                                {{ form.default_template.errors.0 }}
                            </p>
                        {% endif %}
                        <p class="mt-2 text-sm text-gray-500 flex items-center">
                            <i data-lucide="info" class="w-3 h-3 mr-1"></i>
                            {{ form.default_template.help_text }}
                        </p>
                    </div>
                </div>
            </div>

            <!-- 时间设置区域 -->
            <div class="space-y-6 pt-6">
                <div class="border-b border-gray-200 pb-4">
                    <h4 class="text-md font-semibold text-gray-900 flex items-center">
                        <i data-lucide="clock" class="w-4 h-4 mr-2 text-blue-600"></i>
                        时间设置
                    </h4>
                    <p class="mt-1 text-sm text-gray-600">配置考评活动的开始和结束时间</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 开始时间 -->
                    <div class="group">
                        <label for="{{ form.start_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            <i data-lucide="play" class="w-4 h-4 inline mr-1 text-gray-500"></i>
                            开始时间 <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            {{ form.start_date }}
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <i data-lucide="calendar-plus" class="w-4 h-4 text-gray-400"></i>
                            </div>
                        </div>
                        {% if form.start_date.errors %}
                            <p class="mt-2 text-sm text-red-600 flex items-center">
                                <i data-lucide="calendar-x" class="w-3 h-3 mr-1"></i>
                                {{ form.start_date.errors.0 }}
                            </p>
                        {% endif %}
                        <p class="mt-2 text-sm text-gray-500 flex items-center">
                            <i data-lucide="clock" class="w-3 h-3 mr-1"></i>
                            {{ form.start_date.help_text }}
                        </p>
                    </div>

                    <!-- 结束时间 -->
                    <div class="group">
                        <label for="{{ form.end_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            <i data-lucide="stop-circle" class="w-4 h-4 inline mr-1 text-gray-500"></i>
                            结束时间 <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            {{ form.end_date }}
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <i data-lucide="calendar-clock" class="w-4 h-4 text-gray-400"></i>
                            </div>
                        </div>
                        {% if form.end_date.errors %}
                            <p class="mt-2 text-sm text-red-600 flex items-center">
                                <i data-lucide="clock-x" class="w-3 h-3 mr-1"></i>
                                {{ form.end_date.errors.0 }}
                            </p>
                        {% endif %}
                        <p class="mt-2 text-sm text-gray-500 flex items-center">
                            <i data-lucide="timer" class="w-3 h-3 mr-1"></i>
                            {{ form.end_date.help_text }}
                        </p>
                    </div>
                </div>

                <!-- 时间范围提示 -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-start">
                        <i data-lucide="info" class="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0"></i>
                        <div class="ml-3">
                            <h5 class="text-sm font-medium text-blue-900">时间设置说明</h5>
                            <div class="mt-1 text-sm text-blue-800">
                                <ul class="list-disc list-inside space-y-1">
                                    <li>开始时间不能早于当前时间</li>
                                    <li>结束时间必须晚于开始时间</li>
                                    <li>考评时间间隔至少需要1小时</li>
                                    <li>建议给予充足的时间让员工完成评价</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 详细信息区域 -->
            <div class="space-y-6 pt-6">
                <div class="border-b border-gray-200 pb-4">
                    <h4 class="text-md font-semibold text-gray-900 flex items-center">
                        <i data-lucide="file-text" class="w-4 h-4 mr-2 text-blue-600"></i>
                        详细信息
                    </h4>
                    <p class="mt-1 text-sm text-gray-600">添加批次的详细描述和说明</p>
                </div>

                <!-- 批次描述 -->
                <div class="group">
                    <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        <i data-lucide="align-left" class="w-4 h-4 inline mr-1 text-gray-500"></i>
                        批次描述
                    </label>
                    <div class="relative">
                        {{ form.description }}
                        <div class="absolute bottom-3 right-3 text-xs text-gray-400">
                            <span id="description_count">0</span>/1000
                        </div>
                    </div>
                    {% if form.description.errors %}
                        <p class="mt-2 text-sm text-red-600 flex items-center">
                            <i data-lucide="file-x" class="w-3 h-3 mr-1"></i>
                            {{ form.description.errors.0 }}
                        </p>
                    {% endif %}
                    <p class="mt-2 text-sm text-gray-500 flex items-center">
                        <i data-lucide="message-circle" class="w-3 h-3 mr-1"></i>
                        {{ form.description.help_text }}
                    </p>
                </div>
            </div>

            <!-- 表单错误显示 -->
            {% if form.non_field_errors %}
                <div class="rounded-lg bg-red-50 border border-red-200 p-4 mt-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i data-lucide="alert-circle" class="w-5 h-5 text-red-500"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">请修正以下错误：</h3>
                            <div class="mt-2 text-sm text-red-700">
                                <ul class="list-disc list-inside space-y-1">
                                    {% for error in form.non_field_errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- 提交按钮区域 -->
            <div class="flex items-center justify-between pt-8 border-t border-gray-200">
                <div class="flex items-center text-sm text-gray-500">
                    <i data-lucide="info" class="w-4 h-4 mr-1"></i>
                    <span>创建后可以在批次列表中进行管理和配置</span>
                </div>

                <div class="flex items-center space-x-4">
                    <a href="{% url 'evaluations:admin:batch_list' %}"
                       class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-all duration-200 flex items-center space-x-2">
                        <i data-lucide="x" class="w-4 h-4"></i>
                        <span>取消</span>
                    </a>
                    <button type="submit"
                            id="submitBtn"
                            class="px-8 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-200 flex items-center space-x-2 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                        <i data-lucide="calendar-plus" class="w-4 h-4"></i>
                        <span id="submitText">创建批次</span>
                        <div id="submitSpinner" class="hidden">
                            <svg class="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        </div>
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- 帮助信息卡片 -->
    <div class="mt-8 bg-blue-50 border border-blue-200 rounded-xl p-6">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <i data-lucide="lightbulb" class="w-5 h-5 text-blue-600 mt-0.5"></i>
            </div>
            <div class="ml-3">
                <h4 class="text-sm font-medium text-blue-900">创建考评批次小贴士</h4>
                <div class="mt-2 text-sm text-blue-800">
                    <ul class="list-disc list-inside space-y-1">
                        <li>批次名称应该清晰地反映考评的时间和类型</li>
                        <li>选择合适的评价模板，确保评分标准统一</li>
                        <li>合理安排考评时间，给予员工充足的评价时间</li>
                        <li>详细的批次描述有助于参与者理解考评目的</li>
                        <li>创建后可以在批次管理中配置更多高级选项</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 设置表单字段样式
    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        if (input.type === 'date') {
            input.className = 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500';
        } else if (input.tagName === 'SELECT') {
            input.className = 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500';
        } else if (input.tagName === 'TEXTAREA') {
            input.className = 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500';
            input.rows = 4;
        } else if (input.type === 'text') {
            input.className = 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500';
        }
    });
    
    // 获取表单元素
    const form = document.getElementById('batchForm');
    const submitBtn = document.getElementById('submitBtn');
    const submitText = document.getElementById('submitText');
    const submitSpinner = document.getElementById('submitSpinner');
    const descriptionInput = document.getElementById('id_description');
    const descriptionCount = document.getElementById('description_count');
    const startDateInput = document.getElementById('id_start_date');
    const endDateInput = document.getElementById('id_end_date');

    // 描述字数统计
    if (descriptionInput && descriptionCount) {
        descriptionInput.addEventListener('input', function() {
            const length = this.value.length;
            descriptionCount.textContent = length;

            if (length > 1000) {
                descriptionCount.className = 'text-red-500';
                this.value = this.value.substring(0, 1000);
                descriptionCount.textContent = '1000';
            } else if (length > 800) {
                descriptionCount.className = 'text-orange-500';
            } else {
                descriptionCount.className = 'text-gray-400';
            }
        });

        // 初始化字数统计
        const initialLength = descriptionInput.value.length;
        descriptionCount.textContent = initialLength;
    }

    // 日期验证函数
    function validateDates() {
        if (startDateInput && endDateInput && startDateInput.value && endDateInput.value) {
            const start = new Date(startDateInput.value);
            const end = new Date(endDateInput.value);
            const now = new Date();

            // 清除之前的错误样式
            startDateInput.classList.remove('border-red-500');
            endDateInput.classList.remove('border-red-500');

            // 检查开始时间是否早于当前时间
            if (start < now) {
                startDateInput.classList.add('border-red-500');
                return false;
            }

            // 检查结束时间是否晚于开始时间
            if (end <= start) {
                endDateInput.classList.add('border-red-500');
                return false;
            }

            // 检查时间间隔（至少1小时）
            const timeDiff = end - start;
            if (timeDiff < 3600000) { // 1小时 = 3600000毫秒
                endDateInput.classList.add('border-red-500');
                return false;
            }

            return true;
        }
        return true;
    }

    // 日期字段事件监听
    if (startDateInput) {
        startDateInput.addEventListener('change', validateDates);
    }
    if (endDateInput) {
        endDateInput.addEventListener('change', validateDates);
    }

    // 表单提交处理
    if (form) {
        form.addEventListener('submit', function(e) {
            // 验证必填字段
            const nameField = document.querySelector('#id_name');
            if (!nameField || !nameField.value.trim()) {
                e.preventDefault();
                if (nameField) nameField.focus();
                alert('请输入批次名称');
                return false;
            }

            // 验证日期
            if (!validateDates()) {
                e.preventDefault();
                alert('请检查日期设置');
                return false;
            }

            // 更新提交按钮状态
            if (submitBtn && submitText && submitSpinner) {
                submitBtn.disabled = true;
                submitText.textContent = '创建中...';
                submitSpinner.classList.remove('hidden');
            }
        });
    }

    // 初始化图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
});
</script>
{% endblock %}