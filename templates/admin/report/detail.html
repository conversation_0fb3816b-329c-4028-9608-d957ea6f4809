{% extends "admin/base_admin.html" %}

{% block page_title %}报告详情 - {{ report.title }}{% endblock %}
{% block page_description %}查看考评报告的详细内容和分析数据{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <button onclick="downloadReport()" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center space-x-2">
        <i data-lucide="download" class="w-4 h-4"></i>
        <span>下载报告</span>
    </button>
    <button onclick="shareReport()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
        <i data-lucide="share-2" class="w-4 h-4"></i>
        <span>分享报告</span>
    </button>
    <a href="{% url 'reports:admin:report_generate' %}" class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 flex items-center space-x-2">
        <i data-lucide="file-plus" class="w-4 h-4"></i>
        <span>生成新报告</span>
    </a>
    <a href="{% url 'reports:admin:report_list' %}" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center space-x-2">
        <i data-lucide="arrow-left" class="w-4 h-4"></i>
        <span>返回列表</span>
    </a>
</div>
{% endblock %}

{% block admin_content %}
<div class="max-w-7xl mx-auto space-y-6">
    <!-- 报告头部信息 -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="bg-gradient-to-r from-blue-500 to-blue-600 px-6 py-4">
            <div class="flex items-center justify-between text-white">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <i data-lucide="file-text" class="w-6 h-6"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-xl font-bold">{{ report.title }}</h2>
                        <p class="text-blue-100">{{ report.batch.name }} • {{ report.get_report_type_display }}</p>
                        <div class="flex items-center mt-2 space-x-4">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white bg-opacity-20 text-white">
                                <i data-lucide="calendar" class="w-3 h-3 mr-1"></i>
                                {{ report.created_at|date:"Y-m-d H:i" }}
                            </span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                         {% if report.status == 'completed' %}bg-green-100 text-green-800
                                         {% elif report.status == 'generating' %}bg-yellow-100 text-yellow-800
                                         {% else %}bg-red-100 text-red-800{% endif %}">
                                {% if report.status == 'completed' %}
                                    <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>已完成
                                {% elif report.status == 'generating' %}
                                    <i data-lucide="clock" class="w-3 h-3 mr-1"></i>生成中
                                {% else %}
                                    <i data-lucide="x-circle" class="w-3 h-3 mr-1"></i>生成失败
                                {% endif %}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="text-right">
                    <div class="text-2xl font-bold">{{ report.data.total_evaluations|default:0 }}</div>
                    <div class="text-sm text-blue-100">评价记录</div>
                </div>
            </div>
        </div>
        
        <!-- 基本信息 -->
        <div class="px-6 py-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                        <i data-lucide="info" class="w-5 h-5 mr-2 text-gray-500"></i>
                        基本信息
                    </h3>
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">报告类型</dt>
                            <dd class="text-sm text-gray-900">{{ report.get_report_type_display }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">考评批次</dt>
                            <dd class="text-sm text-gray-900">{{ report.batch.name }}</dd>
                        </div>
                        {% if report.department %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">目标部门</dt>
                            <dd class="text-sm text-gray-900">{{ report.department.name }}</dd>
                        </div>
                        {% endif %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">创建人</dt>
                            <dd class="text-sm text-gray-900">{{ report.created_by.name|default:"系统" }}</dd>
                        </div>
                    </dl>
                </div>
                
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                        <i data-lucide="bar-chart" class="w-5 h-5 mr-2 text-gray-500"></i>
                        统计概览
                    </h3>
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">评价总数</dt>
                            <dd class="text-sm text-gray-900">{{ report.data.total_evaluations|default:0 }} 条</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">平均分</dt>
                            <dd class="text-sm text-gray-900">{{ report.data.average_score|default:0 }} 分</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">参与人数</dt>
                            <dd class="text-sm text-gray-900">{{ report.data.participant_count|default:0 }} 人</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">完成率</dt>
                            <dd class="text-sm text-gray-900">{{ report.data.completion_rate|default:0 }}%</dd>
                        </div>
                    </dl>
                </div>
                
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                        <i data-lucide="settings" class="w-5 h-5 mr-2 text-gray-500"></i>
                        生成配置
                    </h3>
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">报告格式</dt>
                            <dd class="text-sm text-gray-900">{{ report.format|default:"PDF" }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">文件大小</dt>
                            <dd class="text-sm text-gray-900">{{ report.file_size|default:"未知" }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">生成时间</dt>
                            <dd class="text-sm text-gray-900">{{ report.updated_at|date:"Y-m-d H:i:s" }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">有效期</dt>
                            <dd class="text-sm text-gray-900">长期有效</dd>
                        </div>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    {% if report.status == 'completed' %}
    <!-- 数据图表展示 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 分数分布图 -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i data-lucide="pie-chart" class="w-5 h-5 mr-2 text-gray-500"></i>
                    分数分布
                </h3>
            </div>
            <div class="p-6">
                <div id="scoreDistributionChart" style="height: 300px;"></div>
            </div>
        </div>
        
        <!-- 部门对比图 -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i data-lucide="bar-chart-3" class="w-5 h-5 mr-2 text-gray-500"></i>
                    部门对比
                </h3>
            </div>
            <div class="p-6">
                <div id="departmentComparisonChart" style="height: 300px;"></div>
            </div>
        </div>
    </div>

    <!-- 详细评价数据 -->
    {% if evaluation_data %}
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i data-lucide="table" class="w-5 h-5 mr-2 text-gray-500"></i>
                    详细评价数据
                </h3>
                <div class="flex items-center space-x-3">
                    <input type="text" id="searchInput" placeholder="搜索员工或部门..." 
                           class="text-sm border border-gray-300 rounded px-3 py-1 w-48">
                    <select id="departmentFilter" class="text-sm border border-gray-300 rounded px-3 py-1">
                        <option value="">所有部门</option>
                        {% for dept in departments %}
                        <option value="{{ dept.id }}">{{ dept.name }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">被评价者</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属部门</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">评价者</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">总分</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">权重</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">评价时间</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200" id="evaluationTableBody">
                    {% for relation in evaluation_data %}
                    <tr class="evaluation-row" data-department="{{ relation.evaluatee.department.id }}">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <span class="text-sm font-medium text-blue-600">{{ relation.evaluatee.name|first }}</span>
                                </div>
                                <div class="ml-3">
                                    <div class="text-sm font-medium text-gray-900">{{ relation.evaluatee.name }}</div>
                                    <div class="text-sm text-gray-500">{{ relation.evaluatee.employee_no }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ relation.evaluatee.department.name }}</div>
                            <div class="text-sm text-gray-500">{{ relation.evaluatee.position.name|default:"未设置职位" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">匿名评价者</div>
                            <div class="text-sm text-gray-500">{{ relation.evaluator.department.name }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if relation.evaluationrecord_set.first %}
                            <div class="text-lg font-bold text-gray-900">
                                {{ relation.evaluationrecord_set.first.total_score|floatformat:1 }}
                            </div>
                            <div class="text-xs text-gray-500">满分100分</div>
                            {% else %}
                            <span class="text-sm text-gray-400">未评价</span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {{ relation.weight_factor }}×
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {% if relation.evaluationrecord_set.first %}
                                {{ relation.evaluationrecord_set.first.created_at|date:"m-d H:i" }}
                            {% else %}
                                <span class="text-gray-400">-</span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                            <button onclick="viewEvaluationDetail('{{ relation.id }}')" 
                                    class="text-blue-600 hover:text-blue-900 font-medium">
                                查看详情
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% endif %}

    <!-- 报告分析与建议 -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="lightbulb" class="w-5 h-5 mr-2 text-gray-500"></i>
                分析与建议
            </h3>
        </div>
        
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-blue-50 rounded-lg p-4">
                    <h4 class="text-md font-medium text-blue-900 mb-3">数据分析</h4>
                    <div class="text-sm text-blue-800 space-y-2" id="dataAnalysis">
                        <p>• 本次评价共包含 {{ report.data.total_evaluations|default:0 }} 条有效记录</p>
                        <p>• 平均得分为 {{ report.data.average_score|default:0 }} 分，{{ report.data.average_score|add:0|floatformat:0|add:0 >= 80|yesno:"表现优秀,需要改进" }}</p>
                        <p>• 完成率达到 {{ report.data.completion_rate|default:0 }}%，{{ report.data.completion_rate|add:0 >= 90|yesno:"完成度较高,完成度有待提升" }}</p>                        
                    </div>
                </div>
                
                <div class="bg-green-50 rounded-lg p-4">
                    <h4 class="text-md font-medium text-green-900 mb-3">改进建议</h4>
                    <div class="text-sm text-green-800 space-y-2" id="suggestions">
                        <p>• 建议加强低分员工的专业技能培训</p>
                        <p>• 优化考评流程，提高参与积极性</p>
                        <p>• 建立持续反馈机制，促进员工成长</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% elif report.status == 'generating' %}
    <!-- 生成中状态 -->
    <div class="bg-white rounded-lg shadow">
        <div class="p-12 text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">报告生成中...</h3>
            <p class="text-sm text-gray-500">请稍候，系统正在处理您的报告请求</p>
            <button onclick="checkReportStatus()" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                检查状态
            </button>
        </div>
    </div>

    {% elif report.status == 'failed' %}
    <!-- 生成失败状态 -->
    <div class="bg-white rounded-lg shadow">
        <div class="p-12 text-center">
            <i data-lucide="x-circle" class="w-12 h-12 text-red-400 mx-auto mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">报告生成失败</h3>
            <p class="text-sm text-gray-500 mb-4">{{ report.error_message|default:"系统处理报告时发生错误" }}</p>
            <div class="space-x-3">
                <button onclick="retryGeneration()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    重新生成
                </button>
                <a href="{% url 'reports:admin:report_generate' %}" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                    创建新报告
                </a>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- 评价详情模态框 -->
<div id="evaluationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-2/3 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900" id="evaluationModalTitle">评价详情</h3>
                <button onclick="closeEvaluationModal()" class="text-gray-400 hover:text-gray-600">
                    <i data-lucide="x" class="w-5 h-5"></i>
                </button>
            </div>
            
            <div id="evaluationModalContent" class="max-h-96 overflow-y-auto">
                <!-- 动态填充评价详情 -->
            </div>
            
            <div class="flex justify-end mt-6">
                <button onclick="closeEvaluationModal()" 
                        class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                    关闭
                </button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    {% if report.status == 'completed' %}
    initializeCharts();
    bindFilterEvents();
    {% endif %}
});

// 初始化图表
function initializeCharts() {
    // 分数分布图
    const scoreChart = echarts.init(document.getElementById('scoreDistributionChart'));
    const scoreOption = {
        title: {
            text: '评分分布',
            left: 'center',
            textStyle: { fontSize: 14 }
        },
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        series: [{
            name: '分数分布',
            type: 'pie',
            radius: '70%',
            center: ['50%', '60%'],
            data: [
                { value: {{ report.data.score_90_100|default:0 }}, name: '90-100分' },
                { value: {{ report.data.score_80_89|default:0 }}, name: '80-89分' },
                { value: {{ report.data.score_70_79|default:0 }}, name: '70-79分' },
                { value: {{ report.data.score_60_69|default:0 }}, name: '60-69分' },
                { value: {{ report.data.score_below_60|default:0 }}, name: '60分以下' }
            ],
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            }
        }]
    };
    scoreChart.setOption(scoreOption);
    
    // 部门对比图
    const deptChart = echarts.init(document.getElementById('departmentComparisonChart'));
    const deptOption = {
        title: {
            text: '部门平均分对比',
            left: 'center',
            textStyle: { fontSize: 14 }
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: { type: 'shadow' }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: {{ report.data.department_names|default:"[]"|safe }},
            axisLabel: {
                rotate: 45,
                fontSize: 10
            }
        },
        yAxis: {
            type: 'value',
            name: '平均分',
            min: 0,
            max: 100
        },
        series: [{
            name: '平均分',
            data: {{ report.data.department_scores|default:"[]"|safe }},
            type: 'bar',
            itemStyle: {
                color: '#3B82F6'
            }
        }]
    };
    deptChart.setOption(deptOption);
    
    // 响应式
    window.addEventListener('resize', function() {
        scoreChart.resize();
        deptChart.resize();
    });
}

// 绑定筛选事件
function bindFilterEvents() {
    const searchInput = document.getElementById('searchInput');
    const departmentFilter = document.getElementById('departmentFilter');
    
    if (searchInput) {
        searchInput.addEventListener('input', filterEvaluations);
    }
    
    if (departmentFilter) {
        departmentFilter.addEventListener('change', filterEvaluations);
    }
}

// 筛选评价数据
function filterEvaluations() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const departmentId = document.getElementById('departmentFilter').value;
    const rows = document.querySelectorAll('.evaluation-row');
    
    rows.forEach(row => {
        const staffName = row.querySelector('.text-sm.font-medium.text-gray-900').textContent.toLowerCase();
        const deptName = row.querySelector('.text-sm.text-gray-900').textContent.toLowerCase();
        const rowDeptId = row.getAttribute('data-department');
        
        let showRow = true;
        
        // 搜索筛选
        if (searchTerm && !staffName.includes(searchTerm) && !deptName.includes(searchTerm)) {
            showRow = false;
        }
        
        // 部门筛选
        if (departmentId && rowDeptId !== departmentId) {
            showRow = false;
        }
        
        row.style.display = showRow ? '' : 'none';
    });
}

// 查看评价详情
function viewEvaluationDetail(relationId) {
    fetch(`/evaluations/admin/relation/${relationId}/detail/`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const modal = document.getElementById('evaluationModal');
                const title = document.getElementById('evaluationModalTitle');
                const content = document.getElementById('evaluationModalContent');
                
                title.textContent = `${data.evaluatee_name} 的评价详情`;
                
                content.innerHTML = `
                    <div class="space-y-4">
                        <div class="grid grid-cols-2 gap-4">
                            <div class="bg-gray-50 p-4 rounded">
                                <h4 class="font-medium text-gray-900 mb-2">被评价者信息</h4>
                                <p class="text-sm text-gray-600">姓名: ${data.evaluatee_name}</p>
                                <p class="text-sm text-gray-600">部门: ${data.evaluatee_department}</p>
                                <p class="text-sm text-gray-600">职位: ${data.evaluatee_position || '未设置'}</p>
                            </div>
                            <div class="bg-gray-50 p-4 rounded">
                                <h4 class="font-medium text-gray-900 mb-2">评价信息</h4>
                                <p class="text-sm text-gray-600">评价者: 匿名</p>
                                <p class="text-sm text-gray-600">权重: ${data.weight_factor}×</p>
                                <p class="text-sm text-gray-600">评价时间: ${data.evaluation_time || '未完成'}</p>
                            </div>
                        </div>
                        
                        ${data.evaluation_record ? `
                            <div class="bg-blue-50 p-4 rounded">
                                <h4 class="font-medium text-blue-900 mb-2">评分详情</h4>
                                <div class="grid grid-cols-2 gap-4 text-sm text-blue-800">
                                    <div>总分: ${data.evaluation_record.total_score}分</div>
                                    <div>完成时间: ${data.evaluation_record.completed_at}</div>
                                </div>
                                ${data.evaluation_record.comment ? `
                                    <div class="mt-2">
                                        <p class="text-sm font-medium text-blue-900">评价意见:</p>
                                        <p class="text-sm text-blue-800 bg-white p-2 rounded border">${data.evaluation_record.comment}</p>
                                    </div>
                                ` : ''}
                            </div>
                        ` : `
                            <div class="bg-yellow-50 p-4 rounded">
                                <p class="text-sm text-yellow-800">该评价关系尚未完成评分</p>
                            </div>
                        `}
                    </div>
                `;
                
                modal.classList.remove('hidden');
            } else {
                UI.showMessage('获取评价详情失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            UI.showMessage('获取评价详情失败', 'error');
        });
}

// 关闭评价详情模态框
function closeEvaluationModal() {
    document.getElementById('evaluationModal').classList.add('hidden');
}

// 下载报告
function downloadReport() {
    const form = document.createElement('form');
    form.method = 'GET';
    form.action = '{% url "reports:admin:report_download" report.pk %}';
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}

// 分享报告
function shareReport() {
    const url = window.location.href;
    if (navigator.share) {
        navigator.share({
            title: '{{ report.title }}',
            text: '考评报告分享',
            url: url
        });
    } else {
        // 复制链接到剪贴板
        navigator.clipboard.writeText(url).then(function() {
            UI.showMessage('报告链接已复制到剪贴板', 'success');
        });
    }
}

// 检查报告状态
function checkReportStatus() {
    fetch(`/reports/admin/report/{{ report.pk }}/status/`)
        .then(response => response.json())
        .then(data => {
            if (data.status === 'completed') {
                UI.showMessage('报告生成完成，正在刷新页面...', 'success');
                setTimeout(() => location.reload(), 1500);
            } else if (data.status === 'failed') {
                UI.showMessage('报告生成失败: ' + data.error_message, 'error');
                setTimeout(() => location.reload(), 2000);
            } else {
                UI.showMessage('报告仍在生成中，请稍候...', 'info');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            UI.showMessage('检查状态失败', 'error');
        });
}

// 重新生成报告
function retryGeneration() {
    if (confirm('确定要重新生成该报告吗？')) {
        fetch(`/reports/admin/report/{{ report.pk }}/retry/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                UI.showMessage('已开始重新生成报告', 'success');
                setTimeout(() => location.reload(), 1500);
            } else {
                UI.showMessage(data.message || '重新生成失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            UI.showMessage('重新生成失败', 'error');
        });
    }
}
</script>
{% endblock %}