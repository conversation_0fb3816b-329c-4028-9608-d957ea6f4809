{% extends "admin/base_admin.html" %}

{% block page_title %}考评报告管理{% endblock %}
{% block page_description %}生成和管理考评报告，查看统计分析数据{% endblock %}

{% block header_actions %}
<a href="{% url 'reports:admin:report_generate' %}" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
    <i data-lucide="plus" class="w-4 h-4"></i>
    <span>生成报告</span>
</a>
{% endblock %}

{% block admin_content %}
<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
                <i data-lucide="file-text" class="w-6 h-6 text-blue-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">报告总数</p>
                <p class="text-2xl font-bold text-gray-900">{{ reports|length|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
                <i data-lucide="check-circle" class="w-6 h-6 text-green-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">已完成</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.completed_reports|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
                <i data-lucide="clock" class="w-6 h-6 text-yellow-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">生成中</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.generating_reports|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
                <i data-lucide="download" class="w-6 h-6 text-purple-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">总下载数</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.total_downloads|default:0 }}</p>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="bg-white rounded-lg shadow mb-6 p-6">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="relative">
            <input type="text" id="searchInput" placeholder="搜索报告名称或描述..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md">
            <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"></i>
        </div>
        <select id="typeFilter" class="border border-gray-300 rounded-md px-3 py-2">
            <option value="">所有类型</option>
            <option value="individual">个人报告</option>
            <option value="department">部门报告</option>
            <option value="company">公司报告</option>
        </select>
        <select id="statusFilter" class="border border-gray-300 rounded-md px-3 py-2">
            <option value="">所有状态</option>
            <option value="completed">已完成</option>
            <option value="generating">生成中</option>
            <option value="failed">生成失败</option>
        </select>
        <select id="batchFilter" class="border border-gray-300 rounded-md px-3 py-2">
            <option value="">所有批次</option>
            {% for batch in batches %}
            <option value="{{ batch.id }}">{{ batch.name }}</option>
            {% endfor %}
        </select>
    </div>
</div>

<!-- Reports Table -->
<div class="bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">报告列表</h3>
    </div>
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">报告信息</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">考评批次</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">生成时间</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">文件大小</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200" id="reportsTableBody">
                {% if reports %}
                    {% for report in reports %}
                    <tr class="report-row" data-type="{{ report.report_type }}" data-status="{{ report.status }}" data-batch="{{ report.batch.id }}">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="p-2 bg-gray-100 rounded-lg mr-3">
                                    {% if report.report_type == 'individual' %}
                                        <i data-lucide="user" class="w-5 h-5 text-blue-600"></i>
                                    {% elif report.report_type == 'department' %}
                                        <i data-lucide="building" class="w-5 h-5 text-green-600"></i>
                                    {% else %}
                                        <i data-lucide="building-2" class="w-5 h-5 text-purple-600"></i>
                                    {% endif %}
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-gray-900">{{ report.title }}</div>
                                    <div class="text-sm text-gray-500">{{ report.description|default:"暂无描述"|truncatechars:40 }}</div>
                                    {% if report.target_staff %}
                                        <div class="text-xs text-gray-400">目标：{{ report.target_staff.name }}</div>
                                    {% elif report.target_department %}
                                        <div class="text-xs text-gray-400">目标：{{ report.target_department.name }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                {% if report.report_type == 'individual' %}bg-blue-100 text-blue-800
                                {% elif report.report_type == 'department' %}bg-green-100 text-green-800
                                {% else %}bg-purple-100 text-purple-800{% endif %}">
                                {% if report.report_type == 'individual' %}个人报告
                                {% elif report.report_type == 'department' %}部门报告
                                {% else %}公司报告{% endif %}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ report.batch.name }}</div>
                            <div class="text-sm text-gray-500">{{ report.batch.start_date|date:"Y-m-d" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if report.status == 'completed' %}
                                <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                    <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>已完成
                                </span>
                            {% elif report.status == 'generating' %}
                                <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                    <i data-lucide="clock" class="w-3 h-3 mr-1"></i>生成中
                                </span>
                            {% elif report.status == 'failed' %}
                                <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                    <i data-lucide="x-circle" class="w-3 h-3 mr-1"></i>失败
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                    <i data-lucide="clock" class="w-3 h-3 mr-1"></i>待生成
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ report.created_at|date:"Y-m-d H:i" }}</div>
                            {% if report.updated_at != report.created_at %}
                                <div class="text-xs text-gray-500">更新：{{ report.updated_at|date:"m-d H:i" }}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if report.file_size %}
                                <div class="text-sm text-gray-900">{{ report.file_size|filesizeformat }}</div>
                            {% else %}
                                <div class="text-sm text-gray-500">-</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                            <a href="{% url 'reports:admin:report_detail' report.pk %}" class="text-blue-600 hover:text-blue-900">查看</a>
                            {% if report.status == 'completed' and report.file_path %}
                                <a href="{% url 'reports:admin:report_download' report.pk %}" class="text-green-600 hover:text-green-900">下载</a>
                            {% endif %}
                            {% if report.status == 'failed' %}
                                <button onclick="regenerateReport('{{ report.pk }}')" class="text-yellow-600 hover:text-yellow-900">重新生成</button>
                            {% endif %}
                            <button onclick="confirmDelete('{{ report.pk }}', '{{ report.title }}')" class="text-red-600 hover:text-red-900">删除</button>
                        </td>
                    </tr>
                    {% endfor %}
                {% else %}
                    <tr>
                        <td colspan="7" class="px-6 py-12 text-center">
                            <i data-lucide="file-text" class="mx-auto h-12 w-12 text-gray-400"></i>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">暂无报告数据</h3>
                            <p class="mt-1 text-sm text-gray-500">开始生成第一个考评报告吧。</p>
                            <div class="mt-6">
                                <a href="{% url 'reports:admin:report_generate' %}" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                                    生成报告
                                </a>
                            </div>
                        </td>
                    </tr>
                {% endif %}
            </tbody>
        </table>
    </div>
</div>

<!-- Pagination -->
{% if is_paginated %}
<div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-6 rounded-lg shadow">
    <div class="flex-1 flex justify-between sm:hidden">
        {% if page_obj.has_previous %}
            <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">上一页</a>
        {% endif %}
        {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">下一页</a>
        {% endif %}
    </div>
    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
        <div>
            <p class="text-sm text-gray-700">
                显示第 <span class="font-medium">{{ page_obj.start_index }}</span> 到 <span class="font-medium">{{ page_obj.end_index }}</span> 条，
                共 <span class="font-medium">{{ page_obj.paginator.count }}</span> 条记录
            </p>
        </div>
        <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <i data-lucide="chevron-left" class="w-4 h-4"></i>
                    </a>
                {% endif %}
                
                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">{{ num }}</span>
                    {% else %}
                        <a href="?page={{ num }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">{{ num }}</a>
                    {% endif %}
                {% endfor %}
                
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <i data-lucide="chevron-right" class="w-4 h-4"></i>
                    </a>
                {% endif %}
            </nav>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    // 搜索功能
    document.getElementById('searchInput').addEventListener('input', function() {
        filterReports();
    });

    // 类型筛选
    document.getElementById('typeFilter').addEventListener('change', function() {
        filterReports();
    });

    // 状态筛选
    document.getElementById('statusFilter').addEventListener('change', function() {
        filterReports();
    });

    // 批次筛选
    document.getElementById('batchFilter').addEventListener('change', function() {
        filterReports();
    });

    function filterReports() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const typeFilter = document.getElementById('typeFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;
        const batchFilter = document.getElementById('batchFilter').value;
        const reportRows = document.querySelectorAll('.report-row');
        
        reportRows.forEach(row => {
            const title = row.querySelector('.text-sm.font-medium.text-gray-900').textContent.toLowerCase();
            const description = row.querySelector('.text-sm.text-gray-500').textContent.toLowerCase();
            const type = row.getAttribute('data-type');
            const status = row.getAttribute('data-status');
            const batch = row.getAttribute('data-batch');
            
            let showRow = true;
            
            // 搜索筛选
            if (searchTerm && !title.includes(searchTerm) && !description.includes(searchTerm)) {
                showRow = false;
            }
            
            // 类型筛选
            if (typeFilter && type !== typeFilter) {
                showRow = false;
            }
            
            // 状态筛选
            if (statusFilter && status !== statusFilter) {
                showRow = false;
            }
            
            // 批次筛选
            if (batchFilter && batch !== batchFilter) {
                showRow = false;
            }
            
            row.style.display = showRow ? '' : 'none';
        });
    }

    // 重新生成报告
    function regenerateReport(reportId) {
        if (confirm('确定要重新生成此报告吗？')) {
            fetch(`/reports/admin/${reportId}/regenerate/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('报告重新生成已开始', 'success');
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showNotification('重新生成失败，请重试', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('重新生成失败，请重试', 'error');
            });
        }
    }

    // 删除确认
    function confirmDelete(reportId, reportTitle) {
        if (confirm(`确定要删除报告 "${reportTitle}" 吗？此操作不可撤销。`)) {
            window.location.href = `/reports/admin/${reportId}/delete/`;
        }
    }

    // 页面加载时自动聚焦搜索框
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.focus();
        }
        
        // 定时刷新生成中的报告状态
        setInterval(function() {
            const generatingRows = document.querySelectorAll('[data-status="generating"]');
            if (generatingRows.length > 0) {
                location.reload();
            }
        }, 30000); // 每30秒检查一次
    });
</script>
{% endblock %}