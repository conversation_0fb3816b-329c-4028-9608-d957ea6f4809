{% extends "admin/base_admin.html" %}

{% block page_title %}生成考评报告{% endblock %}
{% block page_description %}创建个人、部门或全公司的考评分析报告{% endblock %}

{% block header_actions %}
<a href="{% url 'reports:admin:report_list' %}" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center space-x-2">
    <i data-lucide="arrow-left" class="w-4 h-4"></i>
    <span>返回列表</span>
</a>
{% endblock %}

{% block admin_content %}
<div class="max-w-4xl mx-auto">
    <div class="bg-white rounded-lg shadow">
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                        <i data-lucide="file-text" class="w-6 h-6 text-blue-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-medium text-gray-900">生成考评报告</h3>
                    <p class="mt-1 text-sm text-gray-600">
                        选择报告类型和参数，系统将自动生成详细的考评分析报告
                    </p>
                </div>
            </div>
        </div>

        <form method="post" class="p-6 space-y-6" id="reportForm">
            {% csrf_token %}
            
            <!-- 报告类型选择 -->
            <div class="border-b border-gray-200 pb-6">
                <h4 class="text-md font-medium text-gray-900 mb-4">报告类型</h4>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <label class="relative">
                        <input type="radio" name="report_type" value="individual" checked
                               class="sr-only peer" onchange="updateReportOptions()">
                        <div class="p-4 border border-gray-300 rounded-lg cursor-pointer peer-checked:border-blue-500 peer-checked:bg-blue-50 hover:bg-gray-50">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                    <i data-lucide="user" class="w-5 h-5 text-blue-600"></i>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-gray-900">个人报告</div>
                                    <div class="text-xs text-gray-500">针对单个员工的详细分析</div>
                                </div>
                            </div>
                        </div>
                    </label>

                    <label class="relative">
                        <input type="radio" name="report_type" value="department"
                               class="sr-only peer" onchange="updateReportOptions()">
                        <div class="p-4 border border-gray-300 rounded-lg cursor-pointer peer-checked:border-blue-500 peer-checked:bg-blue-50 hover:bg-gray-50">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                    <i data-lucide="building" class="w-5 h-5 text-green-600"></i>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-gray-900">部门报告</div>
                                    <div class="text-xs text-gray-500">针对整个部门的分析对比</div>
                                </div>
                            </div>
                        </div>
                    </label>

                    <label class="relative">
                        <input type="radio" name="report_type" value="company"
                               class="sr-only peer" onchange="updateReportOptions()">
                        <div class="p-4 border border-gray-300 rounded-lg cursor-pointer peer-checked:border-blue-500 peer-checked:bg-blue-50 hover:bg-gray-50">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                    <i data-lucide="globe" class="w-5 h-5 text-purple-600"></i>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-gray-900">全公司报告</div>
                                    <div class="text-xs text-gray-500">全公司整体评估报告</div>
                                </div>
                            </div>
                        </div>
                    </label>
                </div>
            </div>

            <!-- 报告对象选择 -->
            <div class="border-b border-gray-200 pb-6">
                <h4 class="text-md font-medium text-gray-900 mb-4">报告对象</h4>
                
                <!-- 个人选择 -->
                <div id="individualOptions">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="id_department_filter" class="block text-sm font-medium text-gray-700 mb-2">
                                筛选部门
                            </label>
                            <select id="id_department_filter" 
                                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="">全部部门</option>
                                {% for dept in departments %}
                                <option value="{{ dept.id }}">{{ dept.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div>
                            <label for="id_target_staff" class="block text-sm font-medium text-gray-700 mb-2">
                                选择员工 <span class="text-red-500">*</span>
                            </label>
                            <select name="target_staff" id="id_target_staff" required
                                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="">请选择员工</option>
                                {% for staff in all_staff %}
                                <option value="{{ staff.id }}" data-department="{{ staff.department.id }}">
                                    {{ staff.name }} - {{ staff.department.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 部门选择 -->
                <div id="departmentOptions" class="hidden">
                    <div>
                        <label for="id_target_department" class="block text-sm font-medium text-gray-700 mb-2">
                            选择部门 <span class="text-red-500">*</span>
                        </label>
                        <select name="target_department" id="id_target_department"
                                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">请选择部门</option>
                            {% for dept in departments %}
                            <option value="{{ dept.id }}">{{ dept.name }} ({{ dept.staff_set.count }} 人)</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>

                <!-- 全公司选项 -->
                <div id="companyOptions" class="hidden">
                    <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                        <div class="flex">
                            <i data-lucide="info" class="w-5 h-5 text-blue-600 mr-2"></i>
                            <div class="text-sm text-blue-700">
                                <p class="font-medium">全公司报告说明</p>
                                <p>将生成包含所有部门和员工的综合分析报告，包括整体趋势、部门对比、人才分析等内容。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 报告参数设置 -->
            <div class="border-b border-gray-200 pb-6">
                <h4 class="text-md font-medium text-gray-900 mb-4">报告参数</h4>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 考评批次 -->
                    <div>
                        <label for="id_evaluation_batch" class="block text-sm font-medium text-gray-700 mb-2">
                            考评批次 <span class="text-red-500">*</span>
                        </label>
                        <select name="evaluation_batch" id="id_evaluation_batch" required
                                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">请选择考评批次</option>
                            {% for batch in evaluation_batches %}
                            <option value="{{ batch.id }}" data-status="{{ batch.status }}">
                                {{ batch.name }} ({{ batch.get_status_display }})
                            </option>
                            {% endfor %}
                        </select>
                        <p class="mt-1 text-sm text-gray-500">选择要分析的考评批次</p>
                    </div>

                    <!-- 报告格式 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            报告格式 <span class="text-red-500">*</span>
                        </label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="radio" name="report_format" value="pdf" checked
                                       class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300">
                                <span class="ml-2 text-sm text-gray-900">PDF格式 (推荐)</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="report_format" value="excel"
                                       class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300">
                                <span class="ml-2 text-sm text-gray-900">Excel格式</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="report_format" value="word"
                                       class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300">
                                <span class="ml-2 text-sm text-gray-900">Word格式</span>
                            </label>
                        </div>
                    </div>

                    <!-- 包含内容 -->
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            包含内容
                        </label>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                            <label class="flex items-center">
                                <input type="checkbox" name="include_scores" value="1" checked
                                       class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-900">评分详情</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="include_charts" value="1" checked
                                       class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-900">图表分析</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="include_comparison" value="1" checked
                                       class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-900">对比分析</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="include_suggestions" value="1" checked
                                       class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-900">改进建议</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 高级选项 -->
            <div>
                <div class="flex items-center justify-between mb-4">
                    <h4 class="text-md font-medium text-gray-900">高级选项</h4>
                    <button type="button" onclick="toggleAdvancedOptions()" id="toggleAdvancedBtn"
                            class="text-sm text-blue-600 hover:text-blue-800">
                        展开高级选项
                    </button>
                </div>
                
                <div id="advancedOptions" class="hidden space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- 时间范围 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                对比时间范围
                            </label>
                            <select name="comparison_period" id="id_comparison_period"
                                    class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="">不进行时间对比</option>
                                <option value="3">最近3个月</option>
                                <option value="6">最近6个月</option>
                                <option value="12">最近12个月</option>
                            </select>
                        </div>

                        <!-- 匿名化设置 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                匿名化设置
                            </label>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="checkbox" name="anonymize_evaluators" value="1" checked
                                           class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                                    <span class="ml-2 text-sm text-gray-900">评价者匿名</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="hide_individual_scores" value="1"
                                           class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                                    <span class="ml-2 text-sm text-gray-900">隐藏个人明细分数</span>
                                </label>
                            </div>
                        </div>

                        <!-- 报告名称 -->
                        <div class="md:col-span-2">
                            <label for="id_report_name" class="block text-sm font-medium text-gray-700 mb-2">
                                自定义报告名称
                            </label>
                            <input type="text" name="report_name" id="id_report_name"
                                   class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   placeholder="留空将自动生成报告名称">
                            <p class="mt-1 text-sm text-gray-500">不填写将根据报告类型和时间自动生成</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 表单操作 -->
            <div class="border-t border-gray-200 pt-6">
                <div class="flex justify-end space-x-3">
                    <a href="{% url 'reports:admin:report_list' %}" 
                       class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        取消
                    </a>
                    <button type="button" onclick="previewReport()" id="previewBtn"
                            class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i data-lucide="eye" class="w-4 h-4 inline mr-1"></i>
                        预览报告
                    </button>
                    <button type="submit" id="submitBtn"
                            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i data-lucide="file-plus" class="w-4 h-4 inline mr-1"></i>
                        生成报告
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 部门筛选功能
    const deptFilter = document.getElementById('id_department_filter');
    const staffSelect = document.getElementById('id_target_staff');
    const allStaffOptions = Array.from(staffSelect.options).slice(1);
    
    deptFilter.addEventListener('change', function() {
        const selectedDeptId = this.value;
        
        staffSelect.innerHTML = '<option value="">请选择员工</option>';
        
        if (selectedDeptId) {
            allStaffOptions.forEach(option => {
                if (option.dataset.department === selectedDeptId) {
                    staffSelect.appendChild(option.cloneNode(true));
                }
            });
        } else {
            allStaffOptions.forEach(option => {
                staffSelect.appendChild(option.cloneNode(true));
            });
        }
    });
});

// 更新报告选项显示
function updateReportOptions() {
    const reportType = document.querySelector('input[name="report_type"]:checked').value;
    
    // 隐藏所有选项
    document.getElementById('individualOptions').classList.add('hidden');
    document.getElementById('departmentOptions').classList.add('hidden');
    document.getElementById('companyOptions').classList.add('hidden');
    
    // 清除必填验证
    document.getElementById('id_target_staff').required = false;
    document.getElementById('id_target_department').required = false;
    
    // 显示对应选项
    if (reportType === 'individual') {
        document.getElementById('individualOptions').classList.remove('hidden');
        document.getElementById('id_target_staff').required = true;
    } else if (reportType === 'department') {
        document.getElementById('departmentOptions').classList.remove('hidden');
        document.getElementById('id_target_department').required = true;
    } else if (reportType === 'company') {
        document.getElementById('companyOptions').classList.remove('hidden');
    }
}

// 切换高级选项
function toggleAdvancedOptions() {
    const advancedOptions = document.getElementById('advancedOptions');
    const toggleBtn = document.getElementById('toggleAdvancedBtn');
    
    if (advancedOptions.classList.contains('hidden')) {
        advancedOptions.classList.remove('hidden');
        toggleBtn.textContent = '收起高级选项';
    } else {
        advancedOptions.classList.add('hidden');
        toggleBtn.textContent = '展开高级选项';
    }
}

// 预览报告
function previewReport() {
    const previewBtn = document.getElementById('previewBtn');
    previewBtn.disabled = true;
    previewBtn.innerHTML = '<i data-lucide="loader-2" class="w-4 h-4 inline mr-1 animate-spin"></i>生成预览...';
    
    // 模拟预览过程
    setTimeout(() => {
        previewBtn.disabled = false;
        previewBtn.innerHTML = '<i data-lucide="eye" class="w-4 h-4 inline mr-1"></i>预览报告';
        
        // 显示预览模态框或新窗口
        UI.showMessage('报告预览功能开发中', 'info');
    }, 2000);
}

// 表单提交处理
document.getElementById('reportForm').addEventListener('submit', function(e) {
    const submitBtn = document.getElementById('submitBtn');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i data-lucide="loader-2" class="w-4 h-4 inline mr-1 animate-spin"></i>正在生成...';
    
    // 验证必填字段
    const reportType = document.querySelector('input[name="report_type"]:checked').value;
    const evaluationBatch = document.getElementById('id_evaluation_batch').value;
    
    if (!evaluationBatch) {
        e.preventDefault();
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i data-lucide="file-plus" class="w-4 h-4 inline mr-1"></i>生成报告';
        UI.showMessage('请选择考评批次', 'error');
        return;
    }
    
    if (reportType === 'individual' && !document.getElementById('id_target_staff').value) {
        e.preventDefault();
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i data-lucide="file-plus" class="w-4 h-4 inline mr-1"></i>生成报告';
        UI.showMessage('请选择目标员工', 'error');
        return;
    }
    
    if (reportType === 'department' && !document.getElementById('id_target_department').value) {
        e.preventDefault();
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i data-lucide="file-plus" class="w-4 h-4 inline mr-1"></i>生成报告';
        UI.showMessage('请选择目标部门', 'error');
        return;
    }
});

// 初始化
updateReportOptions();
</script>
{% endblock %}