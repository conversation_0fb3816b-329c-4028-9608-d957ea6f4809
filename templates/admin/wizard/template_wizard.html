{% extends "admin/base_admin.html" %}

{% block page_title %}模板创建向导{% endblock %}
{% block page_description %}通过简单步骤创建考评模板{% endblock %}

{% block header_actions %}
<a href="{% url 'evaluations:admin:template_list' %}" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center space-x-2">
    <i data-lucide="arrow-left" class="w-4 h-4"></i>
    <span>返回模板管理</span>
</a>
{% endblock %}

{% block admin_content %}
<div class="template-wizard-container">
    <!-- 进度指示器 -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="step-indicator active" data-step="1">
                    <div class="step-number">1</div>
                    <div class="step-title">选择类型</div>
                </div>
                <div class="step-line"></div>
                <div class="step-indicator" data-step="2">
                    <div class="step-number">2</div>
                    <div class="step-title">预览模板</div>
                </div>
                <div class="step-line"></div>
                <div class="step-indicator" data-step="3">
                    <div class="step-number">3</div>
                    <div class="step-title">自定义</div>
                </div>
                <div class="step-line"></div>
                <div class="step-indicator" data-step="4">
                    <div class="step-number">4</div>
                    <div class="step-title">完成创建</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 步骤1：选择模板类型 -->
    <div class="wizard-step active" id="step1">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">选择评分模式</h3>
                <p class="mt-1 text-sm text-gray-500">选择最适合您需求的评分模板类型</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- 简单数值评分 -->
                    <div class="preset-card cursor-pointer border-2 border-gray-200 rounded-lg p-6 hover:border-blue-500 transition-colors" data-preset="simple_numeric">
                        <div class="text-center">
                            <div class="text-4xl mb-4">📊</div>
                            <h4 class="text-lg font-medium text-gray-900 mb-2">简单数值评分</h4>
                            <p class="text-sm text-gray-600 mb-4">适合快速评分，每项0-10分制</p>
                            <div class="space-y-2 text-xs">
                                <div class="flex justify-between">
                                    <span>难度:</span>
                                    <span class="px-2 py-1 bg-green-100 text-green-800 rounded">简单</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>时间:</span>
                                    <span class="text-gray-600">2-3分钟</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>评分项:</span>
                                    <span class="text-gray-600">4项</span>
                                </div>
                            </div>
                            <div class="mt-4 text-xs text-gray-500">
                                适合：日常考评、快速反馈
                            </div>
                        </div>
                    </div>

                    <!-- 详细等级评分 -->
                    <div class="preset-card cursor-pointer border-2 border-gray-200 rounded-lg p-6 hover:border-blue-500 transition-colors" data-preset="detailed_tier">
                        <div class="text-center">
                            <div class="text-4xl mb-4">⭐</div>
                            <h4 class="text-lg font-medium text-gray-900 mb-2">详细等级评分</h4>
                            <p class="text-sm text-gray-600 mb-4">使用等级标准进行细致评价</p>
                            <div class="space-y-2 text-xs">
                                <div class="flex justify-between">
                                    <span>难度:</span>
                                    <span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded">中等</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>时间:</span>
                                    <span class="text-gray-600">5-8分钟</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>评分项:</span>
                                    <span class="text-gray-600">3项</span>
                                </div>
                            </div>
                            <div class="mt-4 text-xs text-gray-500">
                                适合：正式考评、晋升评估
                            </div>
                        </div>
                    </div>

                    <!-- 综合评价模式 -->
                    <div class="preset-card cursor-pointer border-2 border-gray-200 rounded-lg p-6 hover:border-blue-500 transition-colors" data-preset="comprehensive_mixed">
                        <div class="text-center">
                            <div class="text-4xl mb-4">🎯</div>
                            <h4 class="text-lg font-medium text-gray-900 mb-2">综合评价模式</h4>
                            <p class="text-sm text-gray-600 mb-4">数值+等级+文本，全面深入评价</p>
                            <div class="space-y-2 text-xs">
                                <div class="flex justify-between">
                                    <span>难度:</span>
                                    <span class="px-2 py-1 bg-red-100 text-red-800 rounded">复杂</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>时间:</span>
                                    <span class="text-gray-600">10-15分钟</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>评分项:</span>
                                    <span class="text-gray-600">3项</span>
                                </div>
                            </div>
                            <div class="mt-4 text-xs text-gray-500">
                                适合：重要岗位、管理层考核
                            </div>
                        </div>
                    </div>

                    <!-- 领导力评估 -->
                    <div class="preset-card cursor-pointer border-2 border-gray-200 rounded-lg p-6 hover:border-blue-500 transition-colors" data-preset="leadership_assessment">
                        <div class="text-center">
                            <div class="text-4xl mb-4">👑</div>
                            <h4 class="text-lg font-medium text-gray-900 mb-2">领导力评估</h4>
                            <p class="text-sm text-gray-600 mb-4">专门针对管理层的领导力评价</p>
                            <div class="space-y-2 text-xs">
                                <div class="flex justify-between">
                                    <span>难度:</span>
                                    <span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded">中等</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>时间:</span>
                                    <span class="text-gray-600">8-10分钟</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>评分项:</span>
                                    <span class="text-gray-600">4项</span>
                                </div>
                            </div>
                            <div class="mt-4 text-xs text-gray-500">
                                适合：管理层考核、领导力发展
                            </div>
                        </div>
                    </div>

                    <!-- 自定义模式 -->
                    <div class="preset-card cursor-pointer border-2 border-gray-200 rounded-lg p-6 hover:border-blue-500 transition-colors" data-preset="custom">
                        <div class="text-center">
                            <div class="text-4xl mb-4">⚙️</div>
                            <h4 class="text-lg font-medium text-gray-900 mb-2">自定义模式</h4>
                            <p class="text-sm text-gray-600 mb-4">完全自定义评分项和模式</p>
                            <div class="space-y-2 text-xs">
                                <div class="flex justify-between">
                                    <span>难度:</span>
                                    <span class="px-2 py-1 bg-purple-100 text-purple-800 rounded">高级</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>时间:</span>
                                    <span class="text-gray-600">可配置</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>评分项:</span>
                                    <span class="text-gray-600">自定义</span>
                                </div>
                            </div>
                            <div class="mt-4 text-xs text-gray-500">
                                适合：特殊需求、高级用户
                            </div>
                        </div>
                    </div>

                    <!-- 推荐模板 -->
                    <div class="preset-card cursor-pointer border-2 border-dashed border-blue-300 rounded-lg p-6 hover:border-blue-500 transition-colors bg-blue-50" data-preset="recommended">
                        <div class="text-center">
                            <div class="text-4xl mb-4">💡</div>
                            <h4 class="text-lg font-medium text-blue-900 mb-2">智能推荐</h4>
                            <p class="text-sm text-blue-700 mb-4">根据您的组织特点推荐最适合的模板</p>
                            <div class="mt-4">
                                <button type="button" class="px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">
                                    获取推荐
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 步骤2：预览模板 -->
    <div class="wizard-step" id="step2" style="display: none;">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">预览模板</h3>
                <p class="mt-1 text-sm text-gray-500">查看所选模板的详细内容</p>
            </div>
            <div class="p-6">
                <div id="templatePreview">
                    <!-- 模板预览内容将通过JavaScript动态加载 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 步骤3：自定义模板 -->
    <div class="wizard-step" id="step3" style="display: none;">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">自定义模板（可选）</h3>
                <p class="mt-1 text-sm text-gray-500">根据需要调整模板内容</p>
            </div>
            <div class="p-6">
                <div class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">模板名称</label>
                        <input type="text" id="templateName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入模板名称">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">模板描述</label>
                        <textarea id="templateDescription" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入模板描述"></textarea>
                    </div>
                    
                    <div id="itemsCustomization">
                        <!-- 评分项自定义内容将动态生成 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 步骤4：完成创建 -->
    <div class="wizard-step" id="step4" style="display: none;">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">完成创建</h3>
                <p class="mt-1 text-sm text-gray-500">确认模板信息并创建</p>
            </div>
            <div class="p-6">
                <div id="finalSummary">
                    <!-- 最终摘要将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 导航按钮 -->
    <div class="flex justify-between mt-8">
        <button type="button" id="prevBtn" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
            <i data-lucide="chevron-left" class="w-4 h-4 mr-2"></i>
            上一步
        </button>
        <button type="button" id="nextBtn" class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed">
            下一步
            <i data-lucide="chevron-right" class="w-4 h-4 ml-2"></i>
        </button>
        <button type="button" id="createBtn" class="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700" style="display: none;">
            <i data-lucide="check" class="w-4 h-4 mr-2"></i>
            创建模板
        </button>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.step-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e5e7eb;
    color: #6b7280;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-bottom: 8px;
    transition: all 0.3s;
}

.step-indicator.active .step-number {
    background-color: #3b82f6;
    color: white;
}

.step-indicator.completed .step-number {
    background-color: #10b981;
    color: white;
}

.step-title {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
}

.step-indicator.active .step-title {
    color: #3b82f6;
}

.step-line {
    width: 60px;
    height: 2px;
    background-color: #e5e7eb;
    margin: 0 16px;
    margin-top: 20px;
}

.preset-card.selected {
    border-color: #3b82f6;
    background-color: #eff6ff;
}

.preset-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
class TemplateWizard {
    constructor() {
        this.currentStep = 1;
        this.selectedPreset = null;
        this.templateData = {};
        
        this.initializeEventListeners();
    }
    
    initializeEventListeners() {
        // 预设选择
        document.querySelectorAll('.preset-card').forEach(card => {
            card.addEventListener('click', () => {
                if (card.dataset.preset === 'recommended') {
                    this.showRecommendation();
                } else {
                    this.selectPreset(card.dataset.preset);
                }
            });
        });
        
        // 导航按钮
        document.getElementById('prevBtn').addEventListener('click', () => this.previousStep());
        document.getElementById('nextBtn').addEventListener('click', () => this.nextStep());
        document.getElementById('createBtn').addEventListener('click', () => this.createTemplate());
    }
    
    selectPreset(presetName) {
        // 移除之前的选择
        document.querySelectorAll('.preset-card').forEach(card => {
            card.classList.remove('selected');
        });
        
        // 选择新预设
        document.querySelector(`[data-preset="${presetName}"]`).classList.add('selected');
        this.selectedPreset = presetName;
        
        // 启用下一步按钮
        document.getElementById('nextBtn').disabled = false;
    }
    
    async showRecommendation() {
        // 显示推荐对话框
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                <h3 class="text-lg font-medium mb-4">获取智能推荐</h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">组织类型</label>
                        <select id="orgType" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                            <option value="startup">初创企业</option>
                            <option value="enterprise">大型企业</option>
                            <option value="government">政府机构</option>
                            <option value="nonprofit">非营利组织</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">评价目的</label>
                        <select id="evalPurpose" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                            <option value="daily">日常评价</option>
                            <option value="annual">年度考核</option>
                            <option value="promotion">晋升评估</option>
                            <option value="development">发展评估</option>
                        </select>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" onclick="this.parentElement.parentElement.parentElement.remove()" class="px-4 py-2 text-gray-600 hover:text-gray-800">取消</button>
                    <button type="button" onclick="templateWizard.getRecommendation()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">获取推荐</button>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }
    
    async getRecommendation() {
        const orgType = document.getElementById('orgType').value;
        const evalPurpose = document.getElementById('evalPurpose').value;
        
        // 关闭模态框
        document.querySelector('.fixed.inset-0').remove();
        
        try {
            const response = await fetch('/evaluations/admin/wizard/template-recommendation/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify({
                    organization_type: orgType,
                    evaluation_purpose: evalPurpose
                })
            });
            
            const data = await response.json();
            
            if (data.success && data.recommendations.length > 0) {
                this.selectPreset(data.recommendations[0]);
                alert(`推荐使用：${data.recommendations[0]} 模板`);
            } else {
                alert('暂无推荐，请手动选择模板');
            }
        } catch (error) {
            alert('获取推荐失败，请手动选择模板');
        }
    }
    
    async nextStep() {
        if (this.currentStep < 4) {
            if (this.currentStep === 1 && !this.selectedPreset) {
                alert('请先选择一个模板类型');
                return;
            }
            
            if (this.currentStep === 1) {
                await this.loadTemplatePreview();
            }
            
            if (this.currentStep === 2) {
                this.loadCustomization();
            }
            
            if (this.currentStep === 3) {
                this.loadFinalSummary();
            }
            
            this.currentStep++;
            this.updateStepDisplay();
        }
    }
    
    previousStep() {
        if (this.currentStep > 1) {
            this.currentStep--;
            this.updateStepDisplay();
        }
    }
    
    updateStepDisplay() {
        // 更新步骤指示器
        document.querySelectorAll('.step-indicator').forEach((indicator, index) => {
            const stepNum = index + 1;
            indicator.classList.remove('active', 'completed');
            
            if (stepNum === this.currentStep) {
                indicator.classList.add('active');
            } else if (stepNum < this.currentStep) {
                indicator.classList.add('completed');
            }
        });
        
        // 显示/隐藏步骤内容
        document.querySelectorAll('.wizard-step').forEach((step, index) => {
            step.style.display = (index + 1 === this.currentStep) ? 'block' : 'none';
        });
        
        // 更新导航按钮
        document.getElementById('prevBtn').disabled = this.currentStep === 1;
        document.getElementById('nextBtn').style.display = this.currentStep === 4 ? 'none' : 'inline-flex';
        document.getElementById('createBtn').style.display = this.currentStep === 4 ? 'inline-flex' : 'none';
    }
    
    async loadTemplatePreview() {
        const previewContainer = document.getElementById('templatePreview');
        previewContainer.innerHTML = '<div class="text-center py-8"><i data-lucide="loader-2" class="w-8 h-8 animate-spin mx-auto text-gray-400"></i><p class="text-gray-500 mt-2">正在加载预览...</p></div>';
        
        try {
            const response = await fetch(`/evaluations/admin/wizard/template-preview/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify({
                    preset_name: this.selectedPreset
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.renderTemplatePreview(data.preview);
            } else {
                previewContainer.innerHTML = `<div class="text-red-600">加载预览失败: ${data.error}</div>`;
            }
        } catch (error) {
            previewContainer.innerHTML = '<div class="text-red-600">网络错误，请重试</div>';
        }
    }
    
    renderTemplatePreview(preview) {
        const container = document.getElementById('templatePreview');
        const preset = preview.preset_info;
        const stats = preview.statistics;
        
        let itemsHtml = '';
        preset.items.forEach((item, index) => {
            itemsHtml += `
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex justify-between items-start mb-2">
                        <h5 class="font-medium text-gray-900">${item.name}</h5>
                        <span class="px-2 py-1 text-xs rounded ${this.getScoringModeClass(item.scoring_mode)}">${this.getScoringModeText(item.scoring_mode)}</span>
                    </div>
                    <p class="text-sm text-gray-600 mb-2">${item.description || ''}</p>
                    <div class="text-xs text-gray-500">
                        权重: ${item.weight || 1.0} | ${item.is_required ? '必填' : '可选'}
                    </div>
                </div>
            `;
        });
        
        container.innerHTML = `
            <div class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="bg-blue-50 p-4 rounded-lg text-center">
                        <div class="text-2xl font-bold text-blue-600">${stats.total_items}</div>
                        <div class="text-sm text-blue-700">评分项数量</div>
                    </div>
                    <div class="bg-green-50 p-4 rounded-lg text-center">
                        <div class="text-2xl font-bold text-green-600">${stats.total_weight.toFixed(1)}</div>
                        <div class="text-sm text-green-700">总权重</div>
                    </div>
                    <div class="bg-purple-50 p-4 rounded-lg text-center">
                        <div class="text-2xl font-bold text-purple-600">${preset.time_estimate}</div>
                        <div class="text-sm text-purple-700">预计用时</div>
                    </div>
                </div>
                
                <div>
                    <h4 class="font-medium text-gray-900 mb-3">评分项详情</h4>
                    <div class="space-y-3">
                        ${itemsHtml}
                    </div>
                </div>
            </div>
        `;
    }
    
    getScoringModeClass(mode) {
        const classes = {
            'score': 'bg-blue-100 text-blue-800',
            'level': 'bg-green-100 text-green-800',
            'text': 'bg-purple-100 text-purple-800'
        };
        return classes[mode] || 'bg-gray-100 text-gray-800';
    }
    
    getScoringModeText(mode) {
        const texts = {
            'score': '数值评分',
            'level': '等级评分',
            'text': '文本评价'
        };
        return texts[mode] || '未知';
    }
    
    loadCustomization() {
        // 加载自定义选项
        document.getElementById('templateName').value = `${this.selectedPreset}_模板_${new Date().getTime()}`;
        document.getElementById('templateDescription').value = '';
        
        // 这里可以添加更多自定义选项
    }
    
    loadFinalSummary() {
        const templateName = document.getElementById('templateName').value;
        const templateDescription = document.getElementById('templateDescription').value;
        
        this.templateData = {
            preset_name: this.selectedPreset,
            template_name: templateName,
            template_description: templateDescription
        };
        
        const container = document.getElementById('finalSummary');
        container.innerHTML = `
            <div class="space-y-4">
                <div>
                    <h4 class="font-medium text-gray-900">模板信息</h4>
                    <div class="mt-2 text-sm text-gray-600">
                        <p><strong>名称:</strong> ${templateName}</p>
                        <p><strong>类型:</strong> ${this.selectedPreset}</p>
                        <p><strong>描述:</strong> ${templateDescription || '无'}</p>
                    </div>
                </div>
                
                <div class="bg-green-50 p-4 rounded-lg">
                    <h4 class="font-medium text-green-900 mb-2">准备创建</h4>
                    <p class="text-sm text-green-800">点击"创建模板"按钮完成模板创建</p>
                </div>
            </div>
        `;
    }
    
    async createTemplate() {
        const createBtn = document.getElementById('createBtn');
        createBtn.disabled = true;
        createBtn.innerHTML = '<i data-lucide="loader-2" class="w-4 h-4 mr-2 animate-spin"></i>创建中...';
        
        try {
            const response = await fetch('/evaluations/admin/wizard/create-template/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify(this.templateData)
            });
            
            const data = await response.json();
            
            if (data.success) {
                alert('模板创建成功！');
                window.location.href = `{% url "evaluations:admin:template_list" %}`;
            } else {
                alert(`创建失败: ${data.error}`);
                createBtn.disabled = false;
                createBtn.innerHTML = '<i data-lucide="check" class="w-4 h-4 mr-2"></i>创建模板';
            }
        } catch (error) {
            alert('网络错误，请重试');
            createBtn.disabled = false;
            createBtn.innerHTML = '<i data-lucide="check" class="w-4 h-4 mr-2"></i>创建模板';
        }
    }
}

// 初始化向导
let templateWizard;
document.addEventListener('DOMContentLoaded', () => {
    templateWizard = new TemplateWizard();
});
</script>
{% endblock %}
