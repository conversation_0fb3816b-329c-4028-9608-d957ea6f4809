{% extends "admin/base_admin.html" %}

{% block page_title %}权重配置向导{% endblock %}
{% block page_description %}通过简单步骤配置考评权重规则{% endblock %}

{% block header_actions %}
<a href="{% url 'evaluations:admin:rule_list' %}" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center space-x-2">
    <i data-lucide="arrow-left" class="w-4 h-4"></i>
    <span>返回规则管理</span>
</a>
{% endblock %}

{% block admin_content %}
<div class="weight-wizard-container">
    <!-- 进度指示器 -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="step-indicator active" data-step="1">
                    <div class="step-number">1</div>
                    <div class="step-title">选择方案</div>
                </div>
                <div class="step-line"></div>
                <div class="step-indicator" data-step="2">
                    <div class="step-number">2</div>
                    <div class="step-title">预览影响</div>
                </div>
                <div class="step-line"></div>
                <div class="step-indicator" data-step="3">
                    <div class="step-number">3</div>
                    <div class="step-title">微调权重</div>
                </div>
                <div class="step-line"></div>
                <div class="step-indicator" data-step="4">
                    <div class="step-number">4</div>
                    <div class="step-title">确认应用</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 步骤1：选择权重方案 -->
    <div class="wizard-step active" id="step1">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">选择权重方案</h3>
                <p class="mt-1 text-sm text-gray-500">选择最适合您组织的权重配置方案</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 平衡模式 -->
                    <div class="scheme-card cursor-pointer border-2 border-gray-200 rounded-lg p-6 hover:border-blue-500 transition-colors" data-scheme="balanced">
                        <div class="flex items-start space-x-4">
                            <div class="text-3xl">⚖️</div>
                            <div class="flex-1">
                                <h4 class="text-lg font-medium text-gray-900">平衡模式</h4>
                                <p class="text-sm text-gray-600 mt-1">所有评价关系权重相等，适合民主化考评环境</p>
                                <div class="mt-3">
                                    <div class="text-xs text-gray-500 mb-2">权重预览：</div>
                                    <div class="flex space-x-2 text-xs">
                                        <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded">下级→上级: 1.0</span>
                                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded">上级→下级: 1.0</span>
                                        <span class="px-2 py-1 bg-purple-100 text-purple-800 rounded">同级: 1.0</span>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <div class="text-xs text-green-600">✓ 公平公正</div>
                                    <div class="text-xs text-green-600">✓ 减少层级偏见</div>
                                    <div class="text-xs text-gray-500">适合：扁平化组织、创新型团队</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 层级模式 -->
                    <div class="scheme-card cursor-pointer border-2 border-gray-200 rounded-lg p-6 hover:border-blue-500 transition-colors" data-scheme="hierarchical">
                        <div class="flex items-start space-x-4">
                            <div class="text-3xl">🏢</div>
                            <div class="flex-1">
                                <h4 class="text-lg font-medium text-gray-900">层级模式</h4>
                                <p class="text-sm text-gray-600 mt-1">体现管理层级权威，适合传统企业管理</p>
                                <div class="mt-3">
                                    <div class="text-xs text-gray-500 mb-2">权重预览：</div>
                                    <div class="flex space-x-2 text-xs">
                                        <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded">下级→上级: 0.8</span>
                                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded">上级→下级: 1.2</span>
                                        <span class="px-2 py-1 bg-purple-100 text-purple-800 rounded">同级: 1.0</span>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <div class="text-xs text-green-600">✓ 体现管理权威</div>
                                    <div class="text-xs text-green-600">✓ 符合传统管理理念</div>
                                    <div class="text-xs text-gray-500">适合：传统企业、政府机构</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 协作模式 -->
                    <div class="scheme-card cursor-pointer border-2 border-gray-200 rounded-lg p-6 hover:border-blue-500 transition-colors" data-scheme="collaborative">
                        <div class="flex items-start space-x-4">
                            <div class="text-3xl">🤝</div>
                            <div class="flex-1">
                                <h4 class="text-lg font-medium text-gray-900">协作模式</h4>
                                <p class="text-sm text-gray-600 mt-1">强调团队协作和跨部门合作</p>
                                <div class="mt-3">
                                    <div class="text-xs text-gray-500 mb-2">权重预览：</div>
                                    <div class="flex space-x-2 text-xs">
                                        <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded">下级→上级: 1.1</span>
                                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded">上级→下级: 1.0</span>
                                        <span class="px-2 py-1 bg-purple-100 text-purple-800 rounded">同级: 1.2</span>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <div class="text-xs text-green-600">✓ 鼓励团队合作</div>
                                    <div class="text-xs text-green-600">✓ 重视同级意见</div>
                                    <div class="text-xs text-gray-500">适合：项目型组织、敏捷团队</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 绩效导向 -->
                    <div class="scheme-card cursor-pointer border-2 border-gray-200 rounded-lg p-6 hover:border-blue-500 transition-colors" data-scheme="performance_driven">
                        <div class="flex items-start space-x-4">
                            <div class="text-3xl">📈</div>
                            <div class="flex-1">
                                <h4 class="text-lg font-medium text-gray-900">绩效导向</h4>
                                <p class="text-sm text-gray-600 mt-1">基于绩效表现调整权重，激励高绩效</p>
                                <div class="mt-3">
                                    <div class="text-xs text-gray-500 mb-2">权重预览：</div>
                                    <div class="flex space-x-2 text-xs">
                                        <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded">下级→上级: 0.9</span>
                                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded">上级→下级: 1.3</span>
                                        <span class="px-2 py-1 bg-purple-100 text-purple-800 rounded">同级: 1.1</span>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <div class="text-xs text-green-600">✓ 激励高绩效</div>
                                    <div class="text-xs text-green-600">✓ 强化结果导向</div>
                                    <div class="text-xs text-gray-500">适合：销售团队、业绩导向型组织</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 步骤2：预览影响 -->
    <div class="wizard-step" id="step2" style="display: none;">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">预览方案影响</h3>
                <p class="mt-1 text-sm text-gray-500">查看所选方案对当前考评批次的影响</p>
            </div>
            <div class="p-6">
                <div id="impactPreview">
                    <!-- 影响预览内容将通过JavaScript动态加载 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 步骤3：微调权重 -->
    <div class="wizard-step" id="step3" style="display: none;">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">微调权重（可选）</h3>
                <p class="mt-1 text-sm text-gray-500">根据需要对权重进行细微调整</p>
            </div>
            <div class="p-6">
                <div class="space-y-6">
                    <div class="weight-adjustment">
                        <label class="block text-sm font-medium text-gray-700 mb-2">下级评上级权重</label>
                        <div class="flex items-center space-x-4">
                            <input type="range" id="subordinateWeight" min="0.1" max="2.0" step="0.1" value="1.0" class="flex-1">
                            <span id="subordinateValue" class="text-sm font-medium text-gray-900 w-12">1.0</span>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">下级员工对上级管理者的评价权重</p>
                    </div>

                    <div class="weight-adjustment">
                        <label class="block text-sm font-medium text-gray-700 mb-2">上级评下级权重</label>
                        <div class="flex items-center space-x-4">
                            <input type="range" id="superiorWeight" min="0.1" max="2.0" step="0.1" value="1.0" class="flex-1">
                            <span id="superiorValue" class="text-sm font-medium text-gray-900 w-12">1.0</span>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">上级管理者对下级员工的评价权重</p>
                    </div>

                    <div class="weight-adjustment">
                        <label class="block text-sm font-medium text-gray-700 mb-2">同级互评权重</label>
                        <div class="flex items-center space-x-4">
                            <input type="range" id="peerWeight" min="0.1" max="2.0" step="0.1" value="1.0" class="flex-1">
                            <span id="peerValue" class="text-sm font-medium text-gray-900 w-12">1.0</span>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">同级同事之间的评价权重</p>
                    </div>

                    <div class="weight-adjustment">
                        <label class="block text-sm font-medium text-gray-700 mb-2">跨部门评价权重</label>
                        <div class="flex items-center space-x-4">
                            <input type="range" id="crossDeptWeight" min="0.1" max="2.0" step="0.1" value="1.0" class="flex-1">
                            <span id="crossDeptValue" class="text-sm font-medium text-gray-900 w-12">1.0</span>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">跨部门人员的评价权重</p>
                    </div>
                </div>

                <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                    <h4 class="text-sm font-medium text-blue-900 mb-2">权重调整建议</h4>
                    <ul class="text-xs text-blue-800 space-y-1">
                        <li>• 权重范围：0.1 - 2.0，默认为1.0</li>
                        <li>• 权重越高，该类评价在最终分数中的影响越大</li>
                        <li>• 建议根据组织文化和管理理念进行调整</li>
                        <li>• 可以随时在规则管理中进一步修改</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- 步骤4：确认应用 -->
    <div class="wizard-step" id="step4" style="display: none;">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">确认配置</h3>
                <p class="mt-1 text-sm text-gray-500">确认权重配置并应用到考评批次</p>
            </div>
            <div class="p-6">
                <div id="configSummary">
                    <!-- 配置摘要将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 导航按钮 -->
    <div class="flex justify-between mt-8">
        <button type="button" id="prevBtn" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
            <i data-lucide="chevron-left" class="w-4 h-4 mr-2"></i>
            上一步
        </button>
        <button type="button" id="nextBtn" class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed">
            下一步
            <i data-lucide="chevron-right" class="w-4 h-4 ml-2"></i>
        </button>
        <button type="button" id="applyBtn" class="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700" style="display: none;">
            <i data-lucide="check" class="w-4 h-4 mr-2"></i>
            应用配置
        </button>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.step-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e5e7eb;
    color: #6b7280;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-bottom: 8px;
    transition: all 0.3s;
}

.step-indicator.active .step-number {
    background-color: #3b82f6;
    color: white;
}

.step-indicator.completed .step-number {
    background-color: #10b981;
    color: white;
}

.step-title {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
}

.step-indicator.active .step-title {
    color: #3b82f6;
}

.step-line {
    width: 60px;
    height: 2px;
    background-color: #e5e7eb;
    margin: 0 16px;
    margin-top: 20px;
}

.scheme-card.selected {
    border-color: #3b82f6;
    background-color: #eff6ff;
}

.weight-adjustment input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
    height: 6px;
    background: #e5e7eb;
    border-radius: 3px;
    outline: none;
}

.weight-adjustment input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    background: #3b82f6;
    border-radius: 50%;
    cursor: pointer;
}

.weight-adjustment input[type="range"]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: #3b82f6;
    border-radius: 50%;
    cursor: pointer;
    border: none;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
class WeightWizard {
    constructor() {
        this.currentStep = 1;
        this.selectedScheme = null;
        this.customWeights = {};
        this.batchId = {{ batch.id }};
        
        this.initializeEventListeners();
    }
    
    initializeEventListeners() {
        // 方案选择
        document.querySelectorAll('.scheme-card').forEach(card => {
            card.addEventListener('click', () => {
                this.selectScheme(card.dataset.scheme);
            });
        });
        
        // 导航按钮
        document.getElementById('prevBtn').addEventListener('click', () => this.previousStep());
        document.getElementById('nextBtn').addEventListener('click', () => this.nextStep());
        document.getElementById('applyBtn').addEventListener('click', () => this.applyConfiguration());
        
        // 权重滑块
        ['subordinate', 'superior', 'peer', 'crossDept'].forEach(type => {
            const slider = document.getElementById(type + 'Weight');
            const value = document.getElementById(type + 'Value');
            
            slider.addEventListener('input', () => {
                value.textContent = slider.value;
                this.customWeights[type] = parseFloat(slider.value);
            });
        });
    }
    
    selectScheme(schemeName) {
        // 移除之前的选择
        document.querySelectorAll('.scheme-card').forEach(card => {
            card.classList.remove('selected');
        });
        
        // 选择新方案
        document.querySelector(`[data-scheme="${schemeName}"]`).classList.add('selected');
        this.selectedScheme = schemeName;
        
        // 启用下一步按钮
        document.getElementById('nextBtn').disabled = false;
    }
    
    async nextStep() {
        if (this.currentStep < 4) {
            if (this.currentStep === 1 && !this.selectedScheme) {
                alert('请先选择一个权重方案');
                return;
            }
            
            if (this.currentStep === 1) {
                await this.loadImpactPreview();
            }
            
            if (this.currentStep === 2) {
                this.loadWeightAdjustment();
            }
            
            if (this.currentStep === 3) {
                this.loadConfigSummary();
            }
            
            this.currentStep++;
            this.updateStepDisplay();
        }
    }
    
    previousStep() {
        if (this.currentStep > 1) {
            this.currentStep--;
            this.updateStepDisplay();
        }
    }
    
    updateStepDisplay() {
        // 更新步骤指示器
        document.querySelectorAll('.step-indicator').forEach((indicator, index) => {
            const stepNum = index + 1;
            indicator.classList.remove('active', 'completed');
            
            if (stepNum === this.currentStep) {
                indicator.classList.add('active');
            } else if (stepNum < this.currentStep) {
                indicator.classList.add('completed');
            }
        });
        
        // 显示/隐藏步骤内容
        document.querySelectorAll('.wizard-step').forEach((step, index) => {
            step.style.display = (index + 1 === this.currentStep) ? 'block' : 'none';
        });
        
        // 更新导航按钮
        document.getElementById('prevBtn').disabled = this.currentStep === 1;
        document.getElementById('nextBtn').style.display = this.currentStep === 4 ? 'none' : 'inline-flex';
        document.getElementById('applyBtn').style.display = this.currentStep === 4 ? 'inline-flex' : 'none';
    }
    
    async loadImpactPreview() {
        const previewContainer = document.getElementById('impactPreview');
        previewContainer.innerHTML = '<div class="text-center py-8"><i data-lucide="loader-2" class="w-8 h-8 animate-spin mx-auto text-gray-400"></i><p class="text-gray-500 mt-2">正在分析影响...</p></div>';
        
        try {
            const response = await fetch(`/evaluations/admin/wizard/weight-preview/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify({
                    batch_id: this.batchId,
                    scheme_name: this.selectedScheme
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.renderImpactPreview(data.preview);
            } else {
                previewContainer.innerHTML = `<div class="text-red-600">加载预览失败: ${data.error}</div>`;
            }
        } catch (error) {
            previewContainer.innerHTML = '<div class="text-red-600">网络错误，请重试</div>';
        }
    }
    
    renderImpactPreview(preview) {
        const container = document.getElementById('impactPreview');
        container.innerHTML = `
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-blue-50 p-4 rounded-lg">
                    <h4 class="font-medium text-blue-900">影响关系数</h4>
                    <p class="text-2xl font-bold text-blue-600">${preview.total_relations || 0}</p>
                    <p class="text-sm text-blue-700">个考评关系将受影响</p>
                </div>
                <div class="bg-green-50 p-4 rounded-lg">
                    <h4 class="font-medium text-green-900">权重变化</h4>
                    <p class="text-2xl font-bold text-green-600">${preview.weight_changes || 0}</p>
                    <p class="text-sm text-green-700">个权重规则将更新</p>
                </div>
                <div class="bg-purple-50 p-4 rounded-lg">
                    <h4 class="font-medium text-purple-900">预期效果</h4>
                    <p class="text-sm text-purple-700">${preview.expected_effect || '权重分布更加合理'}</p>
                </div>
            </div>
        `;
    }
    
    loadWeightAdjustment() {
        // 根据选择的方案设置默认权重值
        const schemes = {
            'balanced': { subordinate: 1.0, superior: 1.0, peer: 1.0, crossDept: 1.0 },
            'hierarchical': { subordinate: 0.8, superior: 1.2, peer: 1.0, crossDept: 0.9 },
            'collaborative': { subordinate: 1.1, superior: 1.0, peer: 1.2, crossDept: 1.1 },
            'performance_driven': { subordinate: 0.9, superior: 1.3, peer: 1.1, crossDept: 1.0 }
        };
        
        const weights = schemes[this.selectedScheme] || schemes['balanced'];
        
        Object.keys(weights).forEach(type => {
            const slider = document.getElementById(type + 'Weight');
            const value = document.getElementById(type + 'Value');
            
            slider.value = weights[type];
            value.textContent = weights[type];
            this.customWeights[type] = weights[type];
        });
    }
    
    loadConfigSummary() {
        const container = document.getElementById('configSummary');
        container.innerHTML = `
            <div class="space-y-6">
                <div>
                    <h4 class="font-medium text-gray-900 mb-2">选择的方案</h4>
                    <p class="text-sm text-gray-600">${this.selectedScheme}</p>
                </div>
                <div>
                    <h4 class="font-medium text-gray-900 mb-2">权重配置</h4>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>下级评上级: <span class="font-medium">${this.customWeights.subordinate || 1.0}</span></div>
                        <div>上级评下级: <span class="font-medium">${this.customWeights.superior || 1.0}</span></div>
                        <div>同级互评: <span class="font-medium">${this.customWeights.peer || 1.0}</span></div>
                        <div>跨部门评价: <span class="font-medium">${this.customWeights.crossDept || 1.0}</span></div>
                    </div>
                </div>
                <div class="bg-yellow-50 p-4 rounded-lg">
                    <h4 class="font-medium text-yellow-900 mb-2">注意事项</h4>
                    <ul class="text-sm text-yellow-800 space-y-1">
                        <li>• 应用配置将覆盖现有的权重规则</li>
                        <li>• 配置应用后立即生效</li>
                        <li>• 可以随时在规则管理中修改</li>
                    </ul>
                </div>
            </div>
        `;
    }
    
    async applyConfiguration() {
        const applyBtn = document.getElementById('applyBtn');
        applyBtn.disabled = true;
        applyBtn.innerHTML = '<i data-lucide="loader-2" class="w-4 h-4 mr-2 animate-spin"></i>应用中...';
        
        try {
            const response = await fetch(`/evaluations/admin/wizard/apply-weight/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify({
                    batch_id: this.batchId,
                    scheme_name: this.selectedScheme,
                    custom_weights: this.customWeights
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                alert('权重配置应用成功！');
                window.location.href = '{% url "evaluations:admin:rule_list" %}';
            } else {
                alert(`应用失败: ${data.error}`);
                applyBtn.disabled = false;
                applyBtn.innerHTML = '<i data-lucide="check" class="w-4 h-4 mr-2"></i>应用配置';
            }
        } catch (error) {
            alert('网络错误，请重试');
            applyBtn.disabled = false;
            applyBtn.innerHTML = '<i data-lucide="check" class="w-4 h-4 mr-2"></i>应用配置';
        }
    }
}

// 初始化向导
document.addEventListener('DOMContentLoaded', () => {
    new WeightWizard();
});
</script>
{% endblock %}
