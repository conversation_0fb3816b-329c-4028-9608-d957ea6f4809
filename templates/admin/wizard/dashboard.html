{% extends "admin/base_admin.html" %}

{% block page_title %}向导中心{% endblock %}
{% block page_description %}通过简化向导快速完成复杂配置{% endblock %}

{% block admin_content %}
<div class="wizard-dashboard">
    <!-- 欢迎区域 -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 text-white mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold mb-2">欢迎使用向导中心</h1>
                <p class="text-blue-100 text-lg">通过简化的向导流程，轻松完成复杂的考评系统配置</p>
            </div>
            <div class="text-6xl opacity-20">🧙‍♂️</div>
        </div>
    </div>

    <!-- 主要向导 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- 权重配置向导 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
            <div class="flex items-center space-x-4 mb-4">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="balance-scale" class="w-6 h-6 text-blue-600"></i>
                </div>
                <div>
                    <h3 class="text-lg font-medium text-gray-900">权重配置向导</h3>
                    <p class="text-sm text-gray-500">简化权重规则配置</p>
                </div>
            </div>
            <p class="text-gray-600 mb-4">通过预设方案快速配置考评权重规则，支持平衡、层级、协作等多种模式。</p>
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-500">
                    <span class="font-medium">{{ weight_schemes_count }}</span> 个预设方案
                </div>
                <a href="#" onclick="showBatchSelector('weight')" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
                    开始配置
                </a>
            </div>
        </div>

        <!-- 模板创建向导 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
            <div class="flex items-center space-x-4 mb-4">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="file-text" class="w-6 h-6 text-green-600"></i>
                </div>
                <div>
                    <h3 class="text-lg font-medium text-gray-900">模板创建向导</h3>
                    <p class="text-sm text-gray-500">快速创建评价模板</p>
                </div>
            </div>
            <p class="text-gray-600 mb-4">从预设模板快速创建考评表单，支持数值、等级、文本等多种评分模式。</p>
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-500">
                    <span class="font-medium">{{ template_presets_count }}</span> 个预设模板
                </div>
                <a href="{% url 'evaluations:admin:template_wizard' %}" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm">
                    创建模板
                </a>
            </div>
        </div>

        <!-- 分配策略向导 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
            <div class="flex items-center space-x-4 mb-4">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <i data-lucide="users" class="w-6 h-6 text-purple-600"></i>
                </div>
                <div>
                    <h3 class="text-lg font-medium text-gray-900">分配策略向导</h3>
                    <p class="text-sm text-gray-500">智能分配考评关系</p>
                </div>
            </div>
            <p class="text-gray-600 mb-4">使用智能算法自动分配考评关系，支持简单、全面、同级互评等多种策略。</p>
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-500">
                    <span class="font-medium">{{ assignment_strategies_count }}</span> 个分配策略
                </div>
                <a href="#" onclick="showBatchSelector('assignment')" class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 text-sm">
                    智能分配
                </a>
            </div>
        </div>
    </div>

    <!-- 快速访问 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 最近的批次 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">最近的考评批次</h3>
            </div>
            <div class="p-6">
                {% if recent_batches %}
                    <div class="space-y-3">
                        {% for batch in recent_batches %}
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div>
                                <h4 class="font-medium text-gray-900">{{ batch.name }}</h4>
                                <p class="text-sm text-gray-500">{{ batch.description|truncatechars:50 }}</p>
                            </div>
                            <div class="flex space-x-2">
                                <a href="{% url 'evaluations:admin:weight_wizard' batch.id %}" class="text-blue-600 hover:text-blue-800 text-sm">权重</a>
                                <a href="{% url 'evaluations:admin:assignment_wizard' batch.id %}" class="text-purple-600 hover:text-purple-800 text-sm">分配</a>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-8">
                        <i data-lucide="calendar" class="w-12 h-12 text-gray-400 mx-auto mb-4"></i>
                        <p class="text-gray-500">暂无考评批次</p>
                        <a href="{% url 'evaluations:admin:batch_create' %}" class="text-blue-600 hover:text-blue-800 text-sm">创建第一个批次</a>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- 最近的模板 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">最近的评价模板</h3>
            </div>
            <div class="p-6">
                {% if recent_templates %}
                    <div class="space-y-3">
                        {% for template in recent_templates %}
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div>
                                <h4 class="font-medium text-gray-900">{{ template.name }}</h4>
                                <p class="text-sm text-gray-500">{{ template.description|truncatechars:50 }}</p>
                            </div>
                            <div class="flex space-x-2">
                                <a href="{% url 'evaluations:admin:template_detail' template.id %}" class="text-green-600 hover:text-green-800 text-sm">查看</a>
                                <a href="{% url 'evaluations:admin:template_update' template.id %}" class="text-blue-600 hover:text-blue-800 text-sm">编辑</a>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-8">
                        <i data-lucide="file-text" class="w-12 h-12 text-gray-400 mx-auto mb-4"></i>
                        <p class="text-gray-500">暂无评价模板</p>
                        <a href="{% url 'evaluations:admin:template_wizard' %}" class="text-green-600 hover:text-green-800 text-sm">创建第一个模板</a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 帮助和资源 -->
    <div class="mt-8 bg-gray-50 rounded-lg p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900">帮助和资源</h3>
            <a href="{% url 'evaluations:admin:help_center' %}" class="text-blue-600 hover:text-blue-800 text-sm">查看全部</a>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-white p-4 rounded-lg">
                <h4 class="font-medium text-gray-900 mb-2">📖 使用指南</h4>
                <p class="text-sm text-gray-600">详细的向导使用说明和最佳实践</p>
            </div>
            <div class="bg-white p-4 rounded-lg">
                <h4 class="font-medium text-gray-900 mb-2">🎥 视频教程</h4>
                <p class="text-sm text-gray-600">观看视频了解如何使用各种向导</p>
            </div>
            <div class="bg-white p-4 rounded-lg">
                <h4 class="font-medium text-gray-900 mb-2">❓ 常见问题</h4>
                <p class="text-sm text-gray-600">查看常见问题和解决方案</p>
            </div>
        </div>
    </div>
</div>

<!-- 批次选择模态框 -->
<div id="batchSelectorModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <h3 class="text-lg font-medium mb-4">选择考评批次</h3>
        <div id="batchList" class="space-y-2 max-h-60 overflow-y-auto">
            {% for batch in recent_batches %}
            <div class="batch-option p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50" data-batch-id="{{ batch.id }}">
                <h4 class="font-medium text-gray-900">{{ batch.name }}</h4>
                <p class="text-sm text-gray-500">{{ batch.description|truncatechars:50 }}</p>
            </div>
            {% endfor %}
        </div>
        <div class="flex justify-end space-x-3 mt-6">
            <button type="button" onclick="closeBatchSelector()" class="px-4 py-2 text-gray-600 hover:text-gray-800">取消</button>
            <button type="button" id="confirmBatchBtn" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50" disabled>确认</button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let selectedWizardType = null;
let selectedBatchId = null;

function showBatchSelector(wizardType) {
    selectedWizardType = wizardType;
    selectedBatchId = null;
    document.getElementById('batchSelectorModal').classList.remove('hidden');
    document.getElementById('batchSelectorModal').classList.add('flex');
    
    // 重置选择状态
    document.querySelectorAll('.batch-option').forEach(option => {
        option.classList.remove('bg-blue-50', 'border-blue-500');
        option.addEventListener('click', function() {
            // 清除其他选择
            document.querySelectorAll('.batch-option').forEach(opt => {
                opt.classList.remove('bg-blue-50', 'border-blue-500');
            });
            
            // 选择当前项
            this.classList.add('bg-blue-50', 'border-blue-500');
            selectedBatchId = this.dataset.batchId;
            document.getElementById('confirmBatchBtn').disabled = false;
        });
    });
    
    // 确认按钮事件
    document.getElementById('confirmBatchBtn').onclick = function() {
        if (selectedBatchId && selectedWizardType) {
            let url = '';
            if (selectedWizardType === 'weight') {
                url = `{% url 'evaluations:admin:weight_wizard' 0 %}`.replace('0', selectedBatchId);
            } else if (selectedWizardType === 'assignment') {
                url = `{% url 'evaluations:admin:assignment_wizard' 0 %}`.replace('0', selectedBatchId);
            }
            
            if (url) {
                window.location.href = url;
            }
        }
    };
}

function closeBatchSelector() {
    document.getElementById('batchSelectorModal').classList.add('hidden');
    document.getElementById('batchSelectorModal').classList.remove('flex');
    selectedWizardType = null;
    selectedBatchId = null;
}

// 点击模态框外部关闭
document.getElementById('batchSelectorModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeBatchSelector();
    }
});
</script>
{% endblock %}
