{% extends "admin/base_admin.html" %}

{% block page_title %}智能分配向导{% endblock %}
{% block page_description %}通过简单步骤配置考评关系分配策略{% endblock %}

{% block header_actions %}
<a href="{% url 'evaluations:admin:batch_detail' batch.id %}" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center space-x-2">
    <i data-lucide="arrow-left" class="w-4 h-4"></i>
    <span>返回批次详情</span>
</a>
{% endblock %}

{% block admin_content %}
<div class="assignment-wizard-container">
    <!-- 批次信息 -->
    <div class="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex items-center space-x-3">
            <i data-lucide="calendar" class="w-5 h-5 text-blue-600"></i>
            <div>
                <h3 class="font-medium text-blue-900">{{ batch.name }}</h3>
                <p class="text-sm text-blue-700">{{ batch.description }}</p>
            </div>
        </div>
    </div>

    <!-- 进度指示器 -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="step-indicator active" data-step="1">
                    <div class="step-number">1</div>
                    <div class="step-title">选择策略</div>
                </div>
                <div class="step-line"></div>
                <div class="step-indicator" data-step="2">
                    <div class="step-number">2</div>
                    <div class="step-title">预览分配</div>
                </div>
                <div class="step-line"></div>
                <div class="step-indicator" data-step="3">
                    <div class="step-number">3</div>
                    <div class="step-title">执行分配</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 步骤1：选择分配策略 -->
    <div class="wizard-step active" id="step1">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">选择分配策略</h3>
                <p class="mt-1 text-sm text-gray-500">选择最适合您组织的考评关系分配策略</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 简单分配 -->
                    <div class="strategy-card cursor-pointer border-2 border-gray-200 rounded-lg p-6 hover:border-blue-500 transition-colors" data-strategy="simple">
                        <div class="flex items-start space-x-4">
                            <div class="text-3xl">🎯</div>
                            <div class="flex-1">
                                <h4 class="text-lg font-medium text-gray-900">简单分配</h4>
                                <p class="text-sm text-gray-600 mt-1">每人评价直接上级和同级同事，适合小团队</p>
                                <div class="mt-3 space-y-2">
                                    <div class="flex justify-between text-xs">
                                        <span class="text-gray-500">复杂度:</span>
                                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded">简单</span>
                                    </div>
                                    <div class="flex justify-between text-xs">
                                        <span class="text-gray-500">平均评价者:</span>
                                        <span class="font-medium">3-5人</span>
                                    </div>
                                    <div class="flex justify-between text-xs">
                                        <span class="text-gray-500">评价负担:</span>
                                        <span class="text-green-600">轻</span>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <div class="text-xs text-green-600">✓ 配置简单</div>
                                    <div class="text-xs text-green-600">✓ 评价负担轻</div>
                                    <div class="text-xs text-gray-500">适合：小团队、扁平化组织</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 全面分配 -->
                    <div class="strategy-card cursor-pointer border-2 border-gray-200 rounded-lg p-6 hover:border-blue-500 transition-colors" data-strategy="comprehensive">
                        <div class="flex items-start space-x-4">
                            <div class="text-3xl">🔄</div>
                            <div class="flex-1">
                                <h4 class="text-lg font-medium text-gray-900">全面分配</h4>
                                <p class="text-sm text-gray-600 mt-1">360度评价，包含上级、同级、下级，适合正式考核</p>
                                <div class="mt-3 space-y-2">
                                    <div class="flex justify-between text-xs">
                                        <span class="text-gray-500">复杂度:</span>
                                        <span class="px-2 py-1 bg-red-100 text-red-800 rounded">复杂</span>
                                    </div>
                                    <div class="flex justify-between text-xs">
                                        <span class="text-gray-500">平均评价者:</span>
                                        <span class="font-medium">8-12人</span>
                                    </div>
                                    <div class="flex justify-between text-xs">
                                        <span class="text-gray-500">评价负担:</span>
                                        <span class="text-red-600">重</span>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <div class="text-xs text-green-600">✓ 评价全面</div>
                                    <div class="text-xs text-green-600">✓ 多角度反馈</div>
                                    <div class="text-xs text-gray-500">适合：正式考核、年度评估</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 同级互评 -->
                    <div class="strategy-card cursor-pointer border-2 border-gray-200 rounded-lg p-6 hover:border-blue-500 transition-colors" data-strategy="peer_focused">
                        <div class="flex items-start space-x-4">
                            <div class="text-3xl">🤝</div>
                            <div class="flex-1">
                                <h4 class="text-lg font-medium text-gray-900">同级互评</h4>
                                <p class="text-sm text-gray-600 mt-1">主要由同级同事进行评价，适合协作型团队</p>
                                <div class="mt-3 space-y-2">
                                    <div class="flex justify-between text-xs">
                                        <span class="text-gray-500">复杂度:</span>
                                        <span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded">中等</span>
                                    </div>
                                    <div class="flex justify-between text-xs">
                                        <span class="text-gray-500">平均评价者:</span>
                                        <span class="font-medium">6-8人</span>
                                    </div>
                                    <div class="flex justify-between text-xs">
                                        <span class="text-gray-500">评价负担:</span>
                                        <span class="text-yellow-600">中</span>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <div class="text-xs text-green-600">✓ 同级视角</div>
                                    <div class="text-xs text-green-600">✓ 减少权力偏见</div>
                                    <div class="text-xs text-gray-500">适合：项目团队、协作型组织</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 管理导向 -->
                    <div class="strategy-card cursor-pointer border-2 border-gray-200 rounded-lg p-6 hover:border-blue-500 transition-colors" data-strategy="management_focused">
                        <div class="flex items-start space-x-4">
                            <div class="text-3xl">👔</div>
                            <div class="flex-1">
                                <h4 class="text-lg font-medium text-gray-900">管理导向</h4>
                                <p class="text-sm text-gray-600 mt-1">重点关注管理层评价，适合层级化组织</p>
                                <div class="mt-3 space-y-2">
                                    <div class="flex justify-between text-xs">
                                        <span class="text-gray-500">复杂度:</span>
                                        <span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded">中等</span>
                                    </div>
                                    <div class="flex justify-between text-xs">
                                        <span class="text-gray-500">平均评价者:</span>
                                        <span class="font-medium">4-6人</span>
                                    </div>
                                    <div class="flex justify-between text-xs">
                                        <span class="text-gray-500">评价负担:</span>
                                        <span class="text-yellow-600">中</span>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <div class="text-xs text-green-600">✓ 突出管理权威</div>
                                    <div class="text-xs text-green-600">✓ 层级清晰</div>
                                    <div class="text-xs text-gray-500">适合：传统企业、层级化组织</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 步骤2：预览分配结果 -->
    <div class="wizard-step" id="step2" style="display: none;">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">预览分配结果</h3>
                <p class="mt-1 text-sm text-gray-500">查看所选策略的分配预览和影响分析</p>
            </div>
            <div class="p-6">
                <div id="assignmentPreview">
                    <!-- 分配预览内容将通过JavaScript动态加载 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 步骤3：执行分配 -->
    <div class="wizard-step" id="step3" style="display: none;">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">执行分配</h3>
                <p class="mt-1 text-sm text-gray-500">确认分配策略并执行智能分配</p>
            </div>
            <div class="p-6">
                <div id="executionResult">
                    <div class="text-center py-8">
                        <div class="text-6xl mb-4">🚀</div>
                        <h4 class="text-lg font-medium text-gray-900 mb-2">准备执行分配</h4>
                        <p class="text-sm text-gray-600 mb-6">点击下方按钮开始执行智能分配算法</p>
                        
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                            <div class="flex items-start space-x-3">
                                <i data-lucide="alert-triangle" class="w-5 h-5 text-yellow-600 mt-0.5"></i>
                                <div class="text-left">
                                    <h5 class="font-medium text-yellow-900">注意事项</h5>
                                    <ul class="text-sm text-yellow-800 mt-1 space-y-1">
                                        <li>• 执行分配将清除现有的考评关系</li>
                                        <li>• 分配过程可能需要几分钟时间</li>
                                        <li>• 分配完成后可以手动调整关系</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 导航按钮 -->
    <div class="flex justify-between mt-8">
        <button type="button" id="prevBtn" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
            <i data-lucide="chevron-left" class="w-4 h-4 mr-2"></i>
            上一步
        </button>
        <button type="button" id="nextBtn" class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed">
            下一步
            <i data-lucide="chevron-right" class="w-4 h-4 ml-2"></i>
        </button>
        <button type="button" id="executeBtn" class="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700" style="display: none;">
            <i data-lucide="play" class="w-4 h-4 mr-2"></i>
            执行分配
        </button>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.step-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e5e7eb;
    color: #6b7280;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-bottom: 8px;
    transition: all 0.3s;
}

.step-indicator.active .step-number {
    background-color: #3b82f6;
    color: white;
}

.step-indicator.completed .step-number {
    background-color: #10b981;
    color: white;
}

.step-title {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
}

.step-indicator.active .step-title {
    color: #3b82f6;
}

.step-line {
    width: 80px;
    height: 2px;
    background-color: #e5e7eb;
    margin: 0 16px;
    margin-top: 20px;
}

.strategy-card.selected {
    border-color: #3b82f6;
    background-color: #eff6ff;
}

.strategy-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
class AssignmentWizard {
    constructor() {
        this.currentStep = 1;
        this.selectedStrategy = null;
        this.batchId = {{ batch.id }};
        this.previewData = null;
        
        this.initializeEventListeners();
    }
    
    initializeEventListeners() {
        // 策略选择
        document.querySelectorAll('.strategy-card').forEach(card => {
            card.addEventListener('click', () => {
                this.selectStrategy(card.dataset.strategy);
            });
        });
        
        // 导航按钮
        document.getElementById('prevBtn').addEventListener('click', () => this.previousStep());
        document.getElementById('nextBtn').addEventListener('click', () => this.nextStep());
        document.getElementById('executeBtn').addEventListener('click', () => this.executeAssignment());
    }
    
    selectStrategy(strategyName) {
        // 移除之前的选择
        document.querySelectorAll('.strategy-card').forEach(card => {
            card.classList.remove('selected');
        });
        
        // 选择新策略
        document.querySelector(`[data-strategy="${strategyName}"]`).classList.add('selected');
        this.selectedStrategy = strategyName;
        
        // 启用下一步按钮
        document.getElementById('nextBtn').disabled = false;
    }
    
    async nextStep() {
        if (this.currentStep < 3) {
            if (this.currentStep === 1 && !this.selectedStrategy) {
                alert('请先选择一个分配策略');
                return;
            }
            
            if (this.currentStep === 1) {
                await this.loadAssignmentPreview();
            }
            
            this.currentStep++;
            this.updateStepDisplay();
        }
    }
    
    previousStep() {
        if (this.currentStep > 1) {
            this.currentStep--;
            this.updateStepDisplay();
        }
    }
    
    updateStepDisplay() {
        // 更新步骤指示器
        document.querySelectorAll('.step-indicator').forEach((indicator, index) => {
            const stepNum = index + 1;
            indicator.classList.remove('active', 'completed');
            
            if (stepNum === this.currentStep) {
                indicator.classList.add('active');
            } else if (stepNum < this.currentStep) {
                indicator.classList.add('completed');
            }
        });
        
        // 显示/隐藏步骤内容
        document.querySelectorAll('.wizard-step').forEach((step, index) => {
            step.style.display = (index + 1 === this.currentStep) ? 'block' : 'none';
        });
        
        // 更新导航按钮
        document.getElementById('prevBtn').disabled = this.currentStep === 1;
        document.getElementById('nextBtn').style.display = this.currentStep === 3 ? 'none' : 'inline-flex';
        document.getElementById('executeBtn').style.display = this.currentStep === 3 ? 'inline-flex' : 'none';
    }
    
    async loadAssignmentPreview() {
        const previewContainer = document.getElementById('assignmentPreview');
        previewContainer.innerHTML = '<div class="text-center py-8"><i data-lucide="loader-2" class="w-8 h-8 animate-spin mx-auto text-gray-400"></i><p class="text-gray-500 mt-2">正在分析分配策略...</p></div>';
        
        try {
            const response = await fetch(`/evaluations/admin/wizard/assignment-preview/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify({
                    batch_id: this.batchId,
                    strategy_name: this.selectedStrategy
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.previewData = data.preview;
                this.renderAssignmentPreview(data.preview);
            } else {
                previewContainer.innerHTML = `<div class="text-red-600">加载预览失败: ${data.error}</div>`;
            }
        } catch (error) {
            previewContainer.innerHTML = '<div class="text-red-600">网络错误，请重试</div>';
        }
    }
    
    renderAssignmentPreview(preview) {
        const container = document.getElementById('assignmentPreview');
        
        // 构建部门分布HTML
        let deptHtml = '';
        Object.entries(preview.department_distribution || {}).forEach(([dept, data]) => {
            deptHtml += `
                <div class="bg-gray-50 p-3 rounded">
                    <div class="font-medium text-gray-900">${dept}</div>
                    <div class="text-sm text-gray-600">
                        ${data.evaluatees} 人被评价，平均 ${data.avg_evaluators} 个评价者
                    </div>
                </div>
            `;
        });
        
        // 构建评价者负担HTML
        let loadHtml = '';
        const sortedLoad = Object.entries(preview.evaluator_load || {})
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5);
        
        sortedLoad.forEach(([name, count]) => {
            const level = count > 8 ? 'high' : count > 5 ? 'medium' : 'low';
            const colorClass = level === 'high' ? 'text-red-600' : level === 'medium' ? 'text-yellow-600' : 'text-green-600';
            
            loadHtml += `
                <div class="flex justify-between items-center py-2">
                    <span class="text-sm text-gray-900">${name}</span>
                    <span class="text-sm font-medium ${colorClass}">${count} 个评价任务</span>
                </div>
            `;
        });
        
        container.innerHTML = `
            <div class="space-y-6">
                <!-- 总体统计 -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div class="bg-blue-50 p-4 rounded-lg text-center">
                        <div class="text-2xl font-bold text-blue-600">${preview.participants_count || 0}</div>
                        <div class="text-sm text-blue-700">参与人数</div>
                    </div>
                    <div class="bg-green-50 p-4 rounded-lg text-center">
                        <div class="text-2xl font-bold text-green-600">${preview.estimated_relations || 0}</div>
                        <div class="text-sm text-green-700">预计关系数</div>
                    </div>
                    <div class="bg-purple-50 p-4 rounded-lg text-center">
                        <div class="text-2xl font-bold text-purple-600">${preview.avg_evaluators_per_person || 0}</div>
                        <div class="text-sm text-purple-700">平均评价者</div>
                    </div>
                    <div class="bg-yellow-50 p-4 rounded-lg text-center">
                        <div class="text-2xl font-bold text-yellow-600">${Object.keys(preview.department_distribution || {}).length}</div>
                        <div class="text-sm text-yellow-700">涉及部门</div>
                    </div>
                </div>
                
                <!-- 部门分布 -->
                <div>
                    <h4 class="font-medium text-gray-900 mb-3">部门分布情况</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                        ${deptHtml}
                    </div>
                </div>
                
                <!-- 评价者负担 -->
                <div>
                    <h4 class="font-medium text-gray-900 mb-3">评价者负担（前5名）</h4>
                    <div class="bg-white border border-gray-200 rounded-lg divide-y">
                        ${loadHtml}
                    </div>
                </div>
                
                <!-- 策略说明 -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 class="font-medium text-blue-900 mb-2">策略说明</h4>
                    <p class="text-sm text-blue-800">${preview.strategy_info?.description || '暂无说明'}</p>
                </div>
            </div>
        `;
    }
    
    async executeAssignment() {
        const executeBtn = document.getElementById('executeBtn');
        const resultContainer = document.getElementById('executionResult');
        
        executeBtn.disabled = true;
        executeBtn.innerHTML = '<i data-lucide="loader-2" class="w-4 h-4 mr-2 animate-spin"></i>执行中...';
        
        resultContainer.innerHTML = `
            <div class="text-center py-8">
                <div class="text-6xl mb-4">⏳</div>
                <h4 class="text-lg font-medium text-gray-900 mb-2">正在执行分配</h4>
                <p class="text-sm text-gray-600 mb-4">请稍候，系统正在为您分配考评关系...</p>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-blue-600 h-2 rounded-full animate-pulse" style="width: 60%"></div>
                </div>
            </div>
        `;
        
        try {
            const response = await fetch(`/evaluations/admin/wizard/execute-assignment/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify({
                    batch_id: this.batchId,
                    strategy_name: this.selectedStrategy
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.renderExecutionSuccess(data.result);
            } else {
                this.renderExecutionError(data.error);
            }
        } catch (error) {
            this.renderExecutionError('网络错误，请重试');
        }
    }
    
    renderExecutionSuccess(result) {
        const container = document.getElementById('executionResult');
        container.innerHTML = `
            <div class="text-center py-8">
                <div class="text-6xl mb-4">✅</div>
                <h4 class="text-lg font-medium text-green-900 mb-2">分配完成！</h4>
                <p class="text-sm text-green-700 mb-6">智能分配已成功执行</p>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div class="bg-green-50 p-4 rounded-lg">
                        <div class="text-2xl font-bold text-green-600">${result.relations_created || 0}</div>
                        <div class="text-sm text-green-700">创建关系数</div>
                    </div>
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <div class="text-2xl font-bold text-blue-600">${result.participants_count || 0}</div>
                        <div class="text-sm text-blue-700">参与人数</div>
                    </div>
                    <div class="bg-purple-50 p-4 rounded-lg">
                        <div class="text-2xl font-bold text-purple-600">${result.errors?.length || 0}</div>
                        <div class="text-sm text-purple-700">错误数量</div>
                    </div>
                </div>
                
                <div class="space-y-3">
                    <a href="{% url 'evaluations:admin:batch_detail' batch.id %}" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        <i data-lucide="eye" class="w-4 h-4 mr-2"></i>
                        查看批次详情
                    </a>
                    <a href="{% url 'evaluations:admin:relation_list' %}?batch=${this.batchId}" class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 ml-3">
                        <i data-lucide="users" class="w-4 h-4 mr-2"></i>
                        管理考评关系
                    </a>
                </div>
            </div>
        `;
    }
    
    renderExecutionError(error) {
        const container = document.getElementById('executionResult');
        const executeBtn = document.getElementById('executeBtn');
        
        container.innerHTML = `
            <div class="text-center py-8">
                <div class="text-6xl mb-4">❌</div>
                <h4 class="text-lg font-medium text-red-900 mb-2">分配失败</h4>
                <p class="text-sm text-red-700 mb-6">${error}</p>
                
                <button type="button" onclick="location.reload()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    <i data-lucide="refresh-cw" class="w-4 h-4 mr-2"></i>
                    重新尝试
                </button>
            </div>
        `;
        
        executeBtn.disabled = false;
        executeBtn.innerHTML = '<i data-lucide="play" class="w-4 h-4 mr-2"></i>执行分配';
    }
}

// 初始化向导
document.addEventListener('DOMContentLoaded', () => {
    new AssignmentWizard();
});
</script>
{% endblock %}
