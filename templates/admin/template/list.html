{% extends "admin/base_admin.html" %}

{% block page_title %}考评模板管理{% endblock %}
{% block page_description %}创建和管理考评表单模板，设置评分标准{% endblock %}

{% block header_actions %}
<a href="{% url 'evaluations:admin:template_create' %}" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
    <i data-lucide="plus" class="w-4 h-4"></i>
    <span>新建模板</span>
</a>
{% endblock %}

{% block admin_content %}
<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
                <i data-lucide="file-text" class="w-6 h-6 text-blue-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">模板总数</p>
                <p class="text-2xl font-bold text-gray-900">{{ templates|length|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
                <i data-lucide="check-circle" class="w-6 h-6 text-green-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">启用模板</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.active_templates|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
                <i data-lucide="star" class="w-6 h-6 text-yellow-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">默认模板</p>
                <p class="text-2xl font-bold text-gray-900">1</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
                <i data-lucide="layers" class="w-6 h-6 text-purple-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">模板类型</p>
                <p class="text-2xl font-bold text-gray-900">3</p>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
    <div class="px-6 py-4">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
            <div class="flex-1 max-w-lg">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i data-lucide="search" class="h-4 w-4 text-gray-400"></i>
                    </div>
                    <input type="text" id="searchInput" 
                           class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500" 
                           placeholder="搜索模板名称或描述...">
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <select id="typeFilter" class="border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">所有类型</option>
                    <option value="structured">结构化评分</option>
                    <option value="open">开放式评分</option>
                    <option value="mixed">混合式评分</option>
                </select>
                <select id="statusFilter" class="border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">所有状态</option>
                    <option value="active">已启用</option>
                    <option value="inactive">已禁用</option>
                </select>
            </div>
        </div>
    </div>
</div>

<!-- Templates Grid -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="templatesGrid">
    {% for template in templates %}
        <div class="bg-white rounded-lg shadow hover:shadow-lg transition-shadow template-card border border-gray-200"
             data-type="{{ template.template_type }}" 
             data-status="{% if template.is_active %}active{% else %}inactive{% endif %}">
            
            <!-- Card Header -->
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold text-gray-900 mb-1">{{ template.name }}</h3>
                        <p class="text-sm text-gray-600">{{ template.description|default:"暂无描述"|truncatechars:50 }}</p>
                    </div>
                    <div class="flex items-center space-x-2 ml-4">
                        {% if template.is_default %}
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                <i data-lucide="star" class="w-3 h-3 mr-1"></i>
                                默认
                            </span>
                        {% endif %}
                        {% if template.is_active %}
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>
                                启用
                            </span>
                        {% else %}
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                <i data-lucide="pause-circle" class="w-3 h-3 mr-1"></i>
                                禁用
                            </span>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Card Content -->
            <div class="px-6 py-4">
                <div class="space-y-3">
                    <!-- Template Type -->
                    <div class="flex items-center text-sm">
                        <i data-lucide="layers" class="w-4 h-4 text-gray-400 mr-2"></i>
                        <span class="text-gray-600">类型：</span>
                        <span class="ml-1 font-medium">
                            {% if template.template_type == 'structured' %}结构化评分
                            {% elif template.template_type == 'open' %}开放式评分
                            {% elif template.template_type == 'mixed' %}混合式评分
                            {% endif %}
                        </span>
                    </div>

                    <!-- Items Count -->
                    <div class="flex items-center text-sm">
                        <i data-lucide="list" class="w-4 h-4 text-gray-400 mr-2"></i>
                        <span class="text-gray-600">评分项：</span>
                        <span class="ml-1 font-medium">{{ template.get_items_count }} 项</span>
                    </div>

                    <!-- Total Score -->
                    <div class="flex items-center text-sm">
                        <i data-lucide="target" class="w-4 h-4 text-gray-400 mr-2"></i>
                        <span class="text-gray-600">总分：</span>
                        <span class="ml-1 font-medium text-blue-600">{{ template.calculate_total_score }} 分</span>
                    </div>

                    <!-- Created Info -->
                    <div class="flex items-center text-sm">
                        <i data-lucide="calendar" class="w-4 h-4 text-gray-400 mr-2"></i>
                        <span class="text-gray-600">创建：</span>
                        <span class="ml-1 text-gray-500">{{ template.created_at|date:"Y-m-d" }}</span>
                    </div>
                </div>
            </div>

            <!-- Card Actions -->
            <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                        <a href="{% url 'evaluations:admin:template_detail' template.pk %}" 
                           class="text-sm text-blue-600 hover:text-blue-800 font-medium">
                            查看详情
                        </a>
                        <span class="text-gray-300">|</span>
                        <a href="{% url 'evaluations:admin:template_update' template.pk %}" 
                           class="text-sm text-green-600 hover:text-green-800 font-medium">
                            编辑
                        </a>
                    </div>
                    <div class="flex items-center space-x-1">
                        <a href="{% url 'evaluations:admin:template_copy' template.pk %}" 
                           class="p-1 text-gray-400 hover:text-blue-600" title="复制模板">
                            <i data-lucide="copy" class="w-4 h-4"></i>
                        </a>
                        <button onclick="deleteTemplate({{ template.pk }}, '{{ template.name }}')" 
                                class="p-1 text-gray-400 hover:text-red-600" title="删除模板">
                            <i data-lucide="trash-2" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    {% empty %}
        <!-- Empty State -->
        <div class="col-span-3 text-center py-12">
            <i data-lucide="file-plus" class="mx-auto h-16 w-16 text-gray-400"></i>
            <h3 class="mt-4 text-lg font-medium text-gray-900">暂无考评模板</h3>
            <p class="mt-2 text-sm text-gray-500">开始创建第一个考评模板，设置评分标准和规则。</p>
            <div class="mt-6">
                <a href="{% url 'evaluations:admin:template_create' %}" 
                   class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                    创建模板
                </a>
            </div>
        </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if is_paginated %}
<div class="mt-8 flex items-center justify-between">
    <div class="text-sm text-gray-700">
        显示第 {{ page_obj.start_index }} - {{ page_obj.end_index }} 项，共 {{ paginator.count }} 项
    </div>
    <nav class="flex items-center space-x-2">
        {% if page_obj.has_previous %}
            <a href="?page=1" class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">首页</a>
            <a href="?page={{ page_obj.previous_page_number }}" class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">上一页</a>
        {% endif %}
        
        <span class="px-3 py-2 text-sm bg-blue-50 border border-blue-200 rounded-md text-blue-600">
            {{ page_obj.number }}
        </span>
        
        {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}" class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">下一页</a>
            <a href="?page={{ paginator.num_pages }}" class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">末页</a>
        {% endif %}
    </nav>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    // 搜索功能
    document.getElementById('searchInput').addEventListener('input', function() {
        filterTemplates();
    });

    // 类型筛选
    document.getElementById('typeFilter').addEventListener('change', function() {
        filterTemplates();
    });

    // 状态筛选
    document.getElementById('statusFilter').addEventListener('change', function() {
        filterTemplates();
    });

    function filterTemplates() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const typeFilter = document.getElementById('typeFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;
        const templateCards = document.querySelectorAll('.template-card');

        templateCards.forEach(card => {
            const title = card.querySelector('h3').textContent.toLowerCase();
            const description = card.querySelector('p').textContent.toLowerCase();
            const type = card.dataset.type;
            const status = card.dataset.status;

            let showCard = true;

            // 文本搜索
            if (searchTerm && !title.includes(searchTerm) && !description.includes(searchTerm)) {
                showCard = false;
            }

            // 类型筛选
            if (typeFilter && type !== typeFilter) {
                showCard = false;
            }

            // 状态筛选
            if (statusFilter && status !== statusFilter) {
                showCard = false;
            }

            card.style.display = showCard ? 'block' : 'none';
        });
    }

    // 删除模板
    function deleteTemplate(templateId, templateName) {
        if (confirm(`确定要删除模板"${templateName}"吗？此操作不可恢复。`)) {
            fetch(`/evaluations/admin/templates/${templateId}/delete/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => {
                if (response.ok) {
                    showNotification('模板删除成功', 'success');
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showNotification('删除失败，请重试', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('删除失败，请重试', 'error');
            });
        }
    }

    // 搜索框自动聚焦
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.focus();
        }
    });
</script>
{% endblock %}