{% extends "admin/base_admin.html" %}
{% load static %}

{% block page_title %}员工管理{% endblock %}
{% block page_description %}管理公司员工信息和权限设置{% endblock %}

{% block header_actions %}
<a href="{% url 'organizations:admin:staff_create' %}" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
    <i data-lucide="plus" class="w-4 h-4"></i>
    <span>新建员工</span>
</a>
{% endblock %}

{% block admin_content %}
<!-- Stats -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
                <i data-lucide="users" class="w-6 h-6 text-blue-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">总员工数</p>
                <p class="text-2xl font-bold text-gray-900">{{ object_list|length|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
                <i data-lucide="user-check" class="w-6 h-6 text-green-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">在职员工</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.active_staff|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
                <i data-lucide="shield-check" class="w-6 h-6 text-yellow-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">管理人员</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.admin_staff|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
                <i data-lucide="building" class="w-6 h-6 text-purple-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">部门数量</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.total_departments|default:0 }}</p>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="bg-white rounded-lg shadow mb-6 p-6">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <div class="relative">
            <input type="text" id="searchInput" placeholder="搜索员工姓名、编号或邮箱..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md">
            <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"></i>
        </div>
        <select id="departmentFilter" class="border border-gray-300 rounded-md px-3 py-2">
            <option value="">所有部门</option>
            {% for dept in departments %}
            <option value="{{ dept.id }}">{{ dept.name }}</option>
            {% endfor %}
        </select>
        <select id="positionFilter" class="border border-gray-300 rounded-md px-3 py-2">
            <option value="">所有职位</option>
            <option value="management">管理人员</option>
            <option value="regular">普通员工</option>
        </select>
    </div>
</div>

<!-- Staff Table -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">员工列表</h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">员工信息</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属部门</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">职位信息</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">匿名编号</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="staffTableBody">
                {% if object_list %}
                    {% for staff in object_list %}
                    <tr data-department="{{ staff.department.id }}" 
                        data-position="{% if staff.position %}{{ staff.position.level }}{% else %}0{% endif %}">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                    <span class="text-sm font-medium">{{ staff.name|first|default:"员" }}</span>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">{{ staff.name }}</div>
                                    <div class="text-sm text-gray-500">{{ staff.employee_no }}</div>
                                    <div class="text-sm text-gray-500">{{ staff.email|default:"未设置邮箱" }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ staff.department.name }}</div>
                            <div class="text-sm text-gray-500">{{ staff.department.dept_code }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if staff.position %}
                                <div class="text-sm text-gray-900">{{ staff.position.name }}</div>
                                <div class="text-sm text-gray-500">等级{{ staff.position.level }}级</div>
                            {% else %}
                                <div class="text-sm text-gray-400">无职位</div>
                                <div class="text-sm text-gray-500">普通员工（1-4级）</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if staff.is_active %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">在职</span>
                            {% else %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">离职</span>
                            {% endif %}
                            {% if staff.is_admin %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 ml-1">管理员</span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">
                                {% if staff.new_anonymous_code %}
                                    <span class="font-mono">{{ staff.new_anonymous_code }}</span>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 ml-2">安全</span>
                                {% elif staff.anonymous_code %}
                                    <span class="font-mono">{{ staff.anonymous_code }}</span>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800 ml-2">待升级</span>
                                {% else %}
                                    <span class="text-gray-400">未生成</span>
                                {% endif %}
                            </div>
                            {% if staff.new_anonymous_code or staff.anonymous_code %}
                                <div class="text-sm text-gray-500">可匿名登录</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                            <a href="{% url 'organizations:admin:staff_detail' staff.pk %}" class="text-blue-600 hover:text-blue-900">查看</a>
                            <a href="{% url 'organizations:admin:staff_update' staff.pk %}" class="text-indigo-600 hover:text-indigo-900">编辑</a>
                            <a href="{% url 'organizations:admin:staff_reset_password' staff.pk %}" class="text-yellow-600 hover:text-yellow-900">重置密码</a>
                            <button onclick="confirmDelete('{{ staff.pk }}', '{{ staff.name }}')" class="text-red-600 hover:text-red-900">删除</button>
                        </td>
                    </tr>
                    {% endfor %}
                {% else %}
                    <tr>
                        <td colspan="6" class="px-6 py-12 text-center">
                            <i data-lucide="users" class="mx-auto h-12 w-12 text-gray-400"></i>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">暂无员工数据</h3>
                            <p class="mt-1 text-sm text-gray-500">开始添加第一个员工吧。</p>
                            <div class="mt-6">
                                <a href="{% url 'organizations:admin:staff_create' %}" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                                    新建员工
                                </a>
                            </div>
                        </td>
                    </tr>
                {% endif %}
                    </tbody>
                </table>
            </div>
        </div>

<!-- Pagination -->
{% if is_paginated %}
<div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-6 rounded-lg shadow">
    <div class="flex-1 flex justify-between sm:hidden">
        {% if page_obj.has_previous %}
            <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">上一页</a>
        {% endif %}
        {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">下一页</a>
        {% endif %}
    </div>
    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
        <div>
            <p class="text-sm text-gray-700">
                显示第 <span class="font-medium">{{ page_obj.start_index }}</span> 到 <span class="font-medium">{{ page_obj.end_index }}</span> 条，
                共 <span class="font-medium">{{ page_obj.paginator.count }}</span> 条记录
            </p>
        </div>
        <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <i data-lucide="chevron-left" class="w-4 h-4"></i>
                    </a>
                {% endif %}
                
                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">{{ num }}</span>
                    {% else %}
                        <a href="?page={{ num }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">{{ num }}</a>
                    {% endif %}
                {% endfor %}
                
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <i data-lucide="chevron-right" class="w-4 h-4"></i>
                    </a>
                {% endif %}
            </nav>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<!-- 视图切换器脚本 -->
<script src="{% static 'js/view-switcher.js' %}"></script>

<script>
// 全局变量
let viewSwitcher = null;

// 页面配置
window.viewSwitcherConfig = {
    containerSelector: '#data-container',
    listViewSelector: '#list-view',
    cardViewSelector: '#card-view',
    listBtnSelector: '#list-view-btn',
    cardBtnSelector: '#card-view-btn',
    searchSelector: '#searchInput',
    
    // 员工页面特定配置
    cardConfig: {
        template: 'staff',
        extractDataFromRows: true
    },
    
    // 事件回调
    onViewChange: function(oldView, newView) {
        console.log(`员工管理视图从 ${oldView} 切换到 ${newView}`);
        updateViewStats(newView);
    },
    
    onSearchFilter: function(term, resultCount) {
        const countElement = document.getElementById('search-result-count');
        if (countElement) {
            countElement.textContent = `共 ${resultCount} 名员工`;
        }
    }
};

// 初始化函数
document.addEventListener('DOMContentLoaded', function() {
    console.log('=== 员工管理页面开始初始化 ===');
    
    try {
        // 检查必要的依赖
        console.log('检查依赖:');
        console.log('- Lucide:', typeof lucide !== 'undefined' ? '✓' : '✗');
        console.log('- ViewSwitcher:', typeof ViewSwitcher !== 'undefined' ? '✓' : '✗');
        
        // 确保Lucide图标加载
        initializeLucideIcons();
        
        // 检查DOM元素
        console.log('检查DOM元素:');
        console.log('- list-view-btn:', document.getElementById('list-view-btn') ? '✓' : '✗');
        console.log('- card-view-btn:', document.getElementById('card-view-btn') ? '✓' : '✗');
        console.log('- data-container:', document.getElementById('data-container') ? '✓' : '✗');
        console.log('- 数据行数量:', document.querySelectorAll('.staff-row').length);
        
        // 测试JSON数据解析
        const firstRow = document.querySelector('.staff-row');
        if (firstRow && firstRow.dataset.item) {
            try {
                const testData = JSON.parse(firstRow.dataset.item);
                console.log('✓ JSON数据解析测试成功:', testData);
            } catch (e) {
                console.error('✗ JSON数据解析测试失败:', e);
                console.log('原始数据:', firstRow.dataset.item);
            }
        }
        
        // 初始化视图切换器
        if (typeof ViewSwitcher !== 'undefined') {
            viewSwitcher = new ViewSwitcher(window.viewSwitcherConfig);
            console.log('✓ ViewSwitcher初始化成功');
        } else {
            throw new Error('ViewSwitcher类未定义');
        }
        
        // 绑定搜索和筛选事件
        setupFilters();
        
        // 自动聚焦搜索框
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.focus();
        }
        
        // 添加手动切换测试
        window.testCardView = function() {
            console.log('手动测试卡片视图...');
            if (viewSwitcher) {
                console.log('ViewSwitcher实例状态:', viewSwitcher);
                console.log('原始数据:', viewSwitcher.originalData);
                console.log('过滤数据:', viewSwitcher.filteredData);
                viewSwitcher.switchView('card');
                
                // 额外调试信息
                setTimeout(() => {
                    const cardView = document.getElementById('card-view');
                    console.log('卡片视图DOM:', cardView);
                    console.log('卡片视图内容:', cardView ? cardView.innerHTML : '未找到');
                    console.log('卡片视图样式:', cardView ? window.getComputedStyle(cardView).display : '未找到');
                }, 1000);
            } else {
                console.error('ViewSwitcher实例不存在');
            }
        };
        
        // 临时修复卡片视图的方法
        window.fixCardView = function() {
            console.log('尝试修复卡片视图...');
            const cardView = document.getElementById('card-view');
            const staffRows = document.querySelectorAll('.staff-row');
            
            if (!cardView || !staffRows.length) {
                console.error('找不到必要的元素');
                return;
            }
            
            let cardsHtml = '';
            staffRows.forEach((row, index) => {
                try {
                    const dataStr = row.dataset.item;
                    if (!dataStr) {
                        console.warn(`第 ${index + 1} 行没有数据`);
                        return;
                    }
                    
                    const data = JSON.parse(dataStr);
                    console.log(`处理第 ${index + 1} 行数据:`, data);
                    
                    // 简单的员工卡片HTML
                    const cardHtml = `
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
                            <div class="flex items-center space-x-4 mb-4">
                                <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                                    <span class="text-xl">👤</span>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <h3 class="text-lg font-semibold text-gray-900 truncate">${data.name}</h3>
                                    <p class="text-sm text-gray-600">${data.employee_no}</p>
                                </div>
                                <div class="flex-shrink-0">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${data.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                                        ${data.is_active ? '在职' : '离职'}
                                    </span>
                                </div>
                            </div>
                            
                            <div class="space-y-3">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-500">部门</span>
                                    <span class="text-sm font-medium text-gray-900">${data.department_name}</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-500">职位</span>
                                    <span class="text-sm font-medium text-gray-900">${data.position_name}</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-500">邮箱</span>
                                    <span class="text-sm font-medium text-gray-600">${data.email}</span>
                                </div>
                                ${data.anonymous_code ? `
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-500">匿名编号</span>
                                    <div class="flex items-center space-x-2">
                                        <span class="text-sm font-mono text-blue-600">${data.anonymous_code}</span>
                                        <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium ${data.anonymous_code_type === 'secure' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">
                                            ${data.anonymous_code_type === 'secure' ? '安全' : '待升级'}
                                        </span>
                                    </div>
                                </div>
                                ` : `
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-500">匿名编号</span>
                                    <span class="text-sm text-gray-400">未生成</span>
                                </div>
                                `}
                            </div>
                            
                            <div class="mt-6 flex space-x-2">
                                <button onclick="viewStaffDetail('${data.id}')" class="flex-1 px-3 py-2 text-xs font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100 transition-colors">
                                    <span class="mr-1">👁</span>查看
                                </button>
                                <button onclick="editStaff('${data.id}')" class="flex-1 px-3 py-2 text-xs font-medium text-gray-600 bg-gray-50 rounded-md hover:bg-gray-100 transition-colors">
                                    <span class="mr-1">✏️</span>编辑
                                </button>
                            </div>
                        </div>
                    `;
                    
                    cardsHtml += cardHtml;
                } catch (e) {
                    console.error(`处理第 ${index + 1} 行数据时出错:`, e);
                }
            });
            
            if (cardsHtml) {
                cardView.innerHTML = cardsHtml;
                cardView.className = 'card-view grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6';
                cardView.style.display = 'grid';
                
                // 隐藏列表视图
                const listView = document.getElementById('list-view');
                if (listView) {
                    listView.style.display = 'none';
                }
                
                console.log('卡片视图修复完成！');
            } else {
                console.error('没有生成任何卡片HTML');
            }
        };
        
        // 添加简单的切换按钮事件（备用方案）
        window.simpleViewSwitch = function() {
            console.log('使用简单切换方案...');
            const listView = document.getElementById('list-view');
            const cardView = document.getElementById('card-view');
            const listBtn = document.getElementById('list-view-btn');
            const cardBtn = document.getElementById('card-view-btn');
            
            if (listView && cardView) {
                // 切换显示
                if (listView.style.display !== 'none') {
                    listView.style.display = 'none';
                    cardView.style.display = 'grid';
                    cardView.innerHTML = '<div class="col-span-full p-8 text-center text-lg font-medium text-blue-600">卡片视图测试成功！数据将在这里显示。</div>';
                    
                    // 更新按钮状态
                    listBtn.classList.remove('bg-blue-500', 'text-white');
                    listBtn.classList.add('bg-gray-100', 'text-gray-700');
                    cardBtn.classList.remove('bg-gray-100', 'text-gray-700');
                    cardBtn.classList.add('bg-blue-500', 'text-white');
                } else {
                    listView.style.display = 'block';
                    cardView.style.display = 'none';
                    
                    // 更新按钮状态
                    cardBtn.classList.remove('bg-blue-500', 'text-white');
                    cardBtn.classList.add('bg-gray-100', 'text-gray-700');
                    listBtn.classList.remove('bg-gray-100', 'text-gray-700');
                    listBtn.classList.add('bg-blue-500', 'text-white');
                }
            }
        };
        
        // 绑定简单切换事件（备用方案）
        const listBtn = document.getElementById('list-view-btn');
        const cardBtn = document.getElementById('card-view-btn');
        
        if (listBtn && cardBtn) {
            listBtn.addEventListener('click', function() {
                console.log('点击列表按钮');
                if (!viewSwitcher) {
                    simpleViewSwitch();
                }
            });
            
            cardBtn.addEventListener('click', function() {
                console.log('点击卡片按钮');
                if (!viewSwitcher) {
                    // 如果ViewSwitcher不存在，使用备用修复方法
                    fixCardView();
                } else {
                    // 使用ViewSwitcher切换，如果失败则使用修复方法
                    try {
                        viewSwitcher.switchView('card');
                        // 检查是否切换成功
                        setTimeout(() => {
                            const cardView = document.getElementById('card-view');
                            if (cardView && cardView.style.display !== 'none' && cardView.innerHTML.trim() === '') {
                                console.warn('ViewSwitcher切换后卡片为空，使用修复方法...');
                                fixCardView();
                            }
                        }, 500);
                    } catch (e) {
                        console.error('ViewSwitcher切换失败，使用修复方法:', e);
                        fixCardView();
                    }
                }
            });
        }
        
        console.log('=== 员工管理页面初始化完成 ===');
        console.log('测试命令: 在控制台输入 testCardView() 来手动切换到卡片视图');
        
    } catch (error) {
        console.error('=== 页面初始化失败 ===');
        console.error('错误详情:', error);
        console.error('错误堆栈:', error.stack);
        // 降级处理 - 使用原始筛选功能
        setupLegacyFilters();
    }
});

// 初始化Lucide图标
function initializeLucideIcons() {
    try {
        // 检查是否存在Lucide
        if (typeof lucide !== 'undefined' && lucide.createIcons) {
            lucide.createIcons();
            console.log('✓ Lucide图标初始化成功');
            return true;
        } 
        
        // 尝试从window对象获取
        if (typeof window.lucide !== 'undefined' && window.lucide.createIcons) {
            window.lucide.createIcons();
            console.log('✓ Window.lucide图标初始化成功');
            return true;
        }
        
        // 延迟重试机制
        console.warn('⚠ Lucide图标库未立即可用，将延迟重试...');
        setTimeout(() => {
            if (typeof lucide !== 'undefined' && lucide.createIcons) {
                lucide.createIcons();
                console.log('✓ Lucide图标延迟初始化成功');
            } else if (typeof window.lucide !== 'undefined' && window.lucide.createIcons) {
                window.lucide.createIcons();
                console.log('✓ Window.lucide图标延迟初始化成功');
            } else {
                console.warn('⚠ Lucide图标库加载失败，使用备用图标');
                // 不影响功能，因为我们已经使用了Emoji图标作为备用
            }
        }, 1000);
        
        return false;
    } catch (error) {
        console.error('✗ 图标初始化失败:', error);
        return false;
    }
}

// 设置筛选器事件监听
function setupFilters() {
    // 部门筛选
    document.getElementById('departmentFilter').addEventListener('change', function() {
        applyFilters();
    });

    // 职位筛选
    document.getElementById('positionFilter').addEventListener('change', function() {
        applyFilters();
    });
}

// 应用筛选器
function applyFilters() {
    const departmentFilter = document.getElementById('departmentFilter').value;
    const positionFilter = document.getElementById('positionFilter').value;
    
    // 获取所有员工行
    const staffRows = document.querySelectorAll('.staff-row');
    let visibleCount = 0;
    
    staffRows.forEach(row => {
        const departmentId = row.getAttribute('data-department');
        const positionLevel = parseInt(row.getAttribute('data-position'));
        let showRow = true;
        
        // 部门筛选
        if (departmentFilter && departmentId !== departmentFilter) {
            showRow = false;
        }
        
        // 职位筛选
        if (positionFilter) {
            if (positionFilter === 'management' && positionLevel < 6) {
                showRow = false;
            } else if (positionFilter === 'regular' && positionLevel >= 6) {
                showRow = false;
            }
        }
        
        // 更新行的可见性
        if (showRow) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
        
        // 更新数据项的可见性（用于卡片视图）
        if (viewSwitcher && viewSwitcher.originalData) {
            const dataItem = viewSwitcher.originalData.find(item => 
                item.element === row
            );
            if (dataItem) {
                dataItem.visible = showRow;
            }
        }
    });
    
    // 刷新卡片视图
    if (viewSwitcher && viewSwitcher.getCurrentView() === 'card') {
        viewSwitcher.renderCards();
    }
    
    // 更新计数
    const countElement = document.getElementById('search-result-count');
    if (countElement) {
        countElement.textContent = `共 ${visibleCount} 名员工`;
    }
}

// 更新视图统计
function updateViewStats(viewType) {
    // 可以在这里添加统计逻辑
    console.log(`当前视图: ${viewType}`);
}

// 降级筛选功能（如果视图切换器初始化失败）
function setupLegacyFilters() {
    console.log('使用降级筛选功能');
    
    // 搜索功能
    document.getElementById('searchInput').addEventListener('input', function() {
        legacyFilterStaff();
    });

    // 部门筛选
    document.getElementById('departmentFilter').addEventListener('change', function() {
        legacyFilterStaff();
    });

    // 职位筛选
    document.getElementById('positionFilter').addEventListener('change', function() {
        legacyFilterStaff();
    });
}

function legacyFilterStaff() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const departmentFilter = document.getElementById('departmentFilter').value;
    const positionFilter = document.getElementById('positionFilter').value;
    const staffRows = document.querySelectorAll('.staff-row');
    let visibleCount = 0;
    
    staffRows.forEach(row => {
        const staffName = row.querySelector('.text-sm.font-medium.text-gray-900').textContent.toLowerCase();
        const staffNo = row.querySelector('.text-sm.text-gray-500').textContent.toLowerCase();
        const departmentId = row.getAttribute('data-department');
        const positionLevel = parseInt(row.getAttribute('data-position'));
        
        let showRow = true;
        
        // 搜索筛选
        if (searchTerm && !staffName.includes(searchTerm) && !staffNo.includes(searchTerm)) {
            showRow = false;
        }
        
        // 部门筛选
        if (departmentFilter && departmentId !== departmentFilter) {
            showRow = false;
        }
        
        // 职位筛选
        if (positionFilter) {
            if (positionFilter === 'management' && positionLevel < 6) {
                showRow = false;
            } else if (positionFilter === 'regular' && positionLevel >= 6) {
                showRow = false;
            }
        }
        
        row.style.display = showRow ? '' : 'none';
        if (showRow) visibleCount++;
    });
    
    // 更新计数
    const countElement = document.getElementById('search-result-count');
    if (countElement) {
        countElement.textContent = `共 ${visibleCount} 名员工`;
    }
}

// 全局函数 - 供模板调用
function viewStaffDetail(staffId) {
    window.location.href = `/organizations/admin/staff/${staffId}/`;
}

function editStaff(staffId) {
    window.location.href = `/organizations/admin/staff/${staffId}/update/`;
}

function resetStaffPassword(staffId) {
    window.location.href = `/organizations/admin/staff/${staffId}/reset-password/`;
}

// 删除确认
function confirmDelete(staffId, staffName) {
    if (confirm(`确定要删除员工 "${staffName}" 吗？此操作不可撤销。`)) {
        // 这里应该发送删除请求
        window.location.href = `/organizations/admin/staff/${staffId}/delete/`;
    }
}

// 导出全局函数供调试使用
window.StaffPageUtils = {
    getViewSwitcher: () => viewSwitcher,
    refreshView: () => viewSwitcher?.refresh(),
    getCurrentView: () => viewSwitcher?.getCurrentView() || 'list',
    applyFilters: applyFilters
};
</script>
{% endblock %}