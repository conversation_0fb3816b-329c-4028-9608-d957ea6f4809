{% extends "admin/base_admin.html" %}
{% load static %}

{% block page_title %}重置密码{% endblock %}
{% block page_description %}重置员工 {{ staff.name }} 的登录密码{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <a href="{% url 'organizations:admin:staff_detail' staff.pk %}" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center space-x-2">
        <i data-lucide="arrow-left" class="w-4 h-4"></i>
        <span>返回详情</span>
    </a>
    <a href="{% url 'organizations:admin:staff_list' %}" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
        <i data-lucide="users" class="w-4 h-4"></i>
        <span>员工列表</span>
    </a>
</div>
{% endblock %}

{% block admin_content %}
<div class="max-w-2xl mx-auto">
    <!-- 员工信息卡片 -->
    <div class="bg-white rounded-lg shadow mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">员工信息</h3>
        </div>
        <div class="px-6 py-4">
            <div class="flex items-center space-x-4">
                <div class="p-3 bg-blue-100 rounded-lg">
                    <i data-lucide="user" class="w-8 h-8 text-blue-600"></i>
                </div>
                <div>
                    <h4 class="text-xl font-semibold text-gray-900">{{ staff.name }}</h4>
                    <p class="text-gray-600">{{ staff.employee_no }} | {{ staff.username }}</p>
                    <p class="text-sm text-gray-500">
                        {{ staff.department.name }} - {{ staff.position.name }}
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- 密码重置表单 -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">重置密码</h3>
            <p class="mt-1 text-sm text-gray-600">为该员工设置新的登录密码</p>
        </div>
        
        <form method="post" class="px-6 py-6">
            {% csrf_token %}
            
            <!-- 安全提示 -->
            <div class="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i data-lucide="alert-triangle" class="w-5 h-5 text-yellow-400"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-yellow-800">安全提示</h3>
                        <div class="mt-2 text-sm text-yellow-700">
                            <ul class="list-disc list-inside space-y-1">
                                <li>密码长度至少6位字符</li>
                                <li>建议包含字母、数字和特殊字符</li>
                                <li>重置后请及时通知员工修改密码</li>
                                <li>此操作将被记录到审计日志中</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 新密码 -->
            <div class="mb-6">
                <label for="new_password" class="block text-sm font-medium text-gray-700 mb-2">
                    新密码 <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                    <input type="password" id="new_password" name="new_password" required
                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                           placeholder="请输入新密码（至少6位）"
                           minlength="6">
                    <button type="button" id="toggle-new-password" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                        <i data-lucide="eye" class="w-5 h-5 text-gray-400 hover:text-gray-600"></i>
                    </button>
                </div>
            </div>

            <!-- 确认密码 -->
            <div class="mb-6">
                <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-2">
                    确认密码 <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                    <input type="password" id="confirm_password" name="confirm_password" required
                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                           placeholder="请再次输入新密码"
                           minlength="6">
                    <button type="button" id="toggle-confirm-password" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                        <i data-lucide="eye" class="w-5 h-5 text-gray-400 hover:text-gray-600"></i>
                    </button>
                </div>
                <div id="password-match-message" class="mt-1 text-sm"></div>
            </div>

            <!-- 密码强度指示器 -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">密码强度</label>
                <div class="flex space-x-1">
                    <div id="strength-bar-1" class="h-2 w-1/4 bg-gray-200 rounded"></div>
                    <div id="strength-bar-2" class="h-2 w-1/4 bg-gray-200 rounded"></div>
                    <div id="strength-bar-3" class="h-2 w-1/4 bg-gray-200 rounded"></div>
                    <div id="strength-bar-4" class="h-2 w-1/4 bg-gray-200 rounded"></div>
                </div>
                <p id="strength-text" class="mt-1 text-sm text-gray-500">请输入密码</p>
            </div>

            <!-- 操作按钮 -->
            <div class="flex items-center justify-end space-x-3">
                <a href="{% url 'organizations:admin:staff_detail' staff.pk %}" 
                   class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    取消
                </a>
                <button type="submit" id="submit-btn"
                        class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed">
                    <i data-lucide="key" class="w-4 h-4 mr-2 inline"></i>
                    重置密码
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const newPasswordInput = document.getElementById('new_password');
    const confirmPasswordInput = document.getElementById('confirm_password');
    const toggleNewPassword = document.getElementById('toggle-new-password');
    const toggleConfirmPassword = document.getElementById('toggle-confirm-password');
    const passwordMatchMessage = document.getElementById('password-match-message');
    const submitBtn = document.getElementById('submit-btn');
    const strengthBars = [
        document.getElementById('strength-bar-1'),
        document.getElementById('strength-bar-2'),
        document.getElementById('strength-bar-3'),
        document.getElementById('strength-bar-4')
    ];
    const strengthText = document.getElementById('strength-text');

    // 密码可见性切换
    toggleNewPassword.addEventListener('click', function() {
        togglePasswordVisibility(newPasswordInput, this);
    });

    toggleConfirmPassword.addEventListener('click', function() {
        togglePasswordVisibility(confirmPasswordInput, this);
    });

    // 密码强度检测
    newPasswordInput.addEventListener('input', function() {
        checkPasswordStrength(this.value);
        checkPasswordMatch();
    });

    // 密码匹配检测
    confirmPasswordInput.addEventListener('input', checkPasswordMatch);

    function togglePasswordVisibility(input, button) {
        const icon = button.querySelector('i');
        if (input.type === 'password') {
            input.type = 'text';
            icon.setAttribute('data-lucide', 'eye-off');
        } else {
            input.type = 'password';
            icon.setAttribute('data-lucide', 'eye');
        }
        // 重新初始化图标
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }

    function checkPasswordStrength(password) {
        let strength = 0;
        let feedback = [];

        if (password.length >= 6) strength++;
        if (password.length >= 8) strength++;
        if (/[a-z]/.test(password) && /[A-Z]/.test(password)) strength++;
        if (/\d/.test(password)) strength++;
        if (/[^a-zA-Z\d]/.test(password)) strength++;

        // 重置所有强度条
        strengthBars.forEach(bar => {
            bar.className = 'h-2 w-1/4 bg-gray-200 rounded';
        });

        // 设置强度条颜色
        const colors = ['bg-red-500', 'bg-orange-500', 'bg-yellow-500', 'bg-green-500'];
        const texts = ['很弱', '较弱', '中等', '强', '很强'];
        
        let level = Math.min(Math.floor(strength), 4);
        
        for (let i = 0; i <= level && i < 4; i++) {
            strengthBars[i].className = `h-2 w-1/4 ${colors[Math.min(i, 3)]} rounded`;
        }

        strengthText.textContent = password.length === 0 ? '请输入密码' : texts[level];
        strengthText.className = `mt-1 text-sm ${level >= 2 ? 'text-green-600' : 'text-red-600'}`;
    }

    function checkPasswordMatch() {
        const newPassword = newPasswordInput.value;
        const confirmPassword = confirmPasswordInput.value;

        if (confirmPassword.length === 0) {
            passwordMatchMessage.textContent = '';
            passwordMatchMessage.className = 'mt-1 text-sm';
            submitBtn.disabled = false;
            return;
        }

        if (newPassword === confirmPassword) {
            passwordMatchMessage.textContent = '✓ 密码匹配';
            passwordMatchMessage.className = 'mt-1 text-sm text-green-600';
            submitBtn.disabled = false;
        } else {
            passwordMatchMessage.textContent = '✗ 密码不匹配';
            passwordMatchMessage.className = 'mt-1 text-sm text-red-600';
            submitBtn.disabled = true;
        }
    }

    // 表单提交确认
    document.querySelector('form').addEventListener('submit', function(e) {
        if (!confirm('确定要重置该员工的密码吗？此操作不可撤销。')) {
            e.preventDefault();
        }
    });

    // 初始化图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
});
</script>
{% endblock %}
