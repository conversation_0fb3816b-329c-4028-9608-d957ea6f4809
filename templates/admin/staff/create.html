{% extends "admin/base_admin.html" %}

{% block page_title %}新建员工{% endblock %}
{% block page_description %}添加新的员工信息，包含基本信息和账号设置{% endblock %}

{% block header_actions %}
<a href="{% url 'organizations:admin:staff_list' %}" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center space-x-2">
    <i data-lucide="arrow-left" class="w-4 h-4"></i>
    <span>返回列表</span>
</a>
{% endblock %}

{% block admin_content %}
<div class="max-w-4xl mx-auto">
    <div class="bg-white rounded-lg shadow">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">新建员工</h3>
            <p class="mt-1 text-sm text-gray-600">
                填写下面的表单信息来创建新的员工账号。员工编号和用户名必须唯一。
            </p>
        </div>

        <form method="post" class="p-6 space-y-6" id="staffForm" data-optimize="true">
            {% csrf_token %}
            
            <!-- 基本信息 -->
            <div class="border-b border-gray-200 pb-6">
                <h4 class="text-md font-medium text-gray-900 mb-4">基本信息</h4>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 员工编号 -->
                    <div>
                        <label for="id_employee_no" class="block text-sm font-medium text-gray-700 mb-2">
                            员工编号 <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="employee_no" id="id_employee_no" required
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="请输入员工编号" data-validate="required">
                        <p class="mt-1 text-sm text-gray-500">唯一的员工标识编号</p>
                        {% if form.employee_no.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.employee_no.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- 员工姓名 -->
                    <div>
                        <label for="id_name" class="block text-sm font-medium text-gray-700 mb-2">
                            员工姓名 <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="name" id="id_name" required data-validate="required"
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="请输入员工姓名">
                        {% if form.name.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.name.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- 所属部门 -->
                    <div>
                        <label for="id_department" class="block text-sm font-medium text-gray-700 mb-2">
                            所属部门 <span class="text-red-500">*</span>
                        </label>
                        <select name="department" id="id_department" required
                                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">请选择部门</option>
                            {% for dept in form.department.field.queryset %}
                            <option value="{{ dept.id }}">{{ dept.name }}</option>
                            {% endfor %}
                        </select>
                        {% if form.department.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.department.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- 职位 -->
                    <div>
                        <label for="id_position" class="block text-sm font-medium text-gray-700 mb-2">
                            职位
                        </label>
                        <select name="position" id="id_position"
                                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">请选择职位</option>
                            {% for pos in form.position.field.queryset %}
                            <option value="{{ pos.id }}" data-department="{{ pos.department.id }}" data-level="{{ pos.level }}">
                                {{ pos.name }} ({{ pos.level }}级)
                            </option>
                            {% endfor %}
                        </select>
                        <p class="mt-1 text-sm text-gray-500">职位将根据选择的部门进行筛选。不选择职位的员工视为普通员工（1-4级）</p>
                        {% if form.position.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.position.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- 邮箱 -->
                    <div>
                        <label for="id_email" class="block text-sm font-medium text-gray-700 mb-2">
                            邮箱地址
                        </label>
                        <input type="email" name="email" id="id_email"
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="请输入邮箱地址">
                        {% if form.email.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.email.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- 手机号 -->
                    <div>
                        <label for="id_phone" class="block text-sm font-medium text-gray-700 mb-2">
                            手机号码
                        </label>
                        <input type="tel" name="phone" id="id_phone"
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="请输入手机号码">
                        {% if form.phone.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.phone.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- 账号设置 -->
            <div class="border-b border-gray-200 pb-6">
                <h4 class="text-md font-medium text-gray-900 mb-4">账号设置</h4>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 登录用户名 -->
                    <div>
                        <label for="id_username" class="block text-sm font-medium text-gray-700 mb-2">
                            登录用户名 <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="username" id="id_username" required
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="请输入登录用户名">
                        <p class="mt-1 text-sm text-gray-500">用于管理端登录的用户名，必须唯一</p>
                        {% if form.username.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.username.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- 登录密码 -->
                    <div>
                        <label for="id_password" class="block text-sm font-medium text-gray-700 mb-2">
                            登录密码 <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <input type="password" name="password" id="id_password" required
                                   class="w-full border border-gray-300 rounded-md px-3 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   placeholder="请输入登录密码">
                            <button type="button" onclick="togglePassword('id_password')" 
                                    class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <i data-lucide="eye" class="w-4 h-4 text-gray-400"></i>
                            </button>
                        </div>
                        <p class="mt-1 text-sm text-gray-500">建议使用8位以上包含字母和数字的密码</p>
                        {% if form.password.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.password.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- 匿名编号 -->
                    <div>
                        <label for="id_anonymous_code" class="block text-sm font-medium text-gray-700 mb-2">
                            安全匿名编号 <span class="text-red-500">*</span>
                        </label>
                        <div class="mb-2 p-3 bg-green-50 border border-green-200 rounded-md">
                            <div class="flex items-center space-x-2">
                                <i data-lucide="shield-check" class="w-4 h-4 text-green-600"></i>
                                <span class="text-sm font-medium text-green-800">将自动生成SHA256安全加密编号</span>
                            </div>
                            <div class="text-xs text-green-600 mt-1">新员工默认使用最新安全算法</div>
                        </div>
                        <div class="flex">
                            <input type="text" name="anonymous_code" id="id_anonymous_code" required readonly
                                   class="flex-1 border border-gray-300 rounded-l-md px-3 py-2 bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono">
                            <button type="button" onclick="generateAnonymousCode()" 
                                    class="px-3 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 flex items-center">
                                <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                            </button>
                        </div>
                        <p class="mt-1 text-sm text-gray-500">用于匿名评分的唯一编号，点击刷新按钮重新生成</p>
                        {% if form.anonymous_code.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.anonymous_code.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- 用户角色 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            用户角色 <span class="text-red-500">*</span>
                        </label>
                        <div class="grid grid-cols-2 gap-3">
                            {% for value, label in form.role.field.choices %}
                            <label class="flex items-center p-3 border border-gray-300 rounded-md cursor-pointer hover:bg-gray-50">
                                <input type="radio" name="role" value="{{ value }}" 
                                       {% if value == 'employee' %}checked{% endif %}
                                       class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300">
                                <span class="ml-2 text-sm text-gray-900">{{ label }}</span>
                            </label>
                            {% endfor %}
                        </div>
                        {% if form.role.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.role.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- 其他设置 -->
            <div>
                <h4 class="text-md font-medium text-gray-900 mb-4">其他设置</h4>
                
                <div class="flex items-center">
                    <input type="checkbox" name="is_active" id="id_is_active" checked
                           class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                    <label for="id_is_active" class="ml-2 text-sm text-gray-900">
                        启用账号
                    </label>
                </div>
                <p class="mt-1 text-sm text-gray-500">禁用后该员工将无法登录系统</p>
            </div>

            <!-- 表单操作 -->
            <div class="border-t border-gray-200 pt-6">
                <div class="flex justify-end space-x-3">
                    <a href="{% url 'organizations:admin:staff_list' %}" 
                       class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        取消
                    </a>
                    <button type="submit" id="submitBtn"
                            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i data-lucide="save" class="w-4 h-4 inline mr-1"></i>
                        创建员工
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 部门联动职位筛选
    const departmentSelect = document.getElementById('id_department');
    const positionSelect = document.getElementById('id_position');
    const allPositions = Array.from(positionSelect.options).slice(1); // 保留第一个空选项
    
    departmentSelect.addEventListener('change', function() {
        const selectedDeptId = this.value;
        
        // 清空并重新添加第一个空选项
        positionSelect.innerHTML = '<option value="">请选择职位</option>';
        
        if (selectedDeptId) {
            // 筛选指定部门的职位
            allPositions.forEach(option => {
                if (option.dataset.department === selectedDeptId) {
                    positionSelect.appendChild(option.cloneNode(true));
                }
            });
        } else {
            // 显示所有职位
            allPositions.forEach(option => {
                positionSelect.appendChild(option.cloneNode(true));
            });
        }
    });
    
    // 自动生成匿名编号
    generateAnonymousCode();
});

// 生成安全匿名编号
function generateAnonymousCode() {
    const anonymousCodeInput = document.getElementById('id_anonymous_code');
    const refreshButton = document.querySelector('button[onclick="generateAnonymousCode()"]');
    const refreshIcon = refreshButton.querySelector('i');
    
    // 禁用按钮并显示加载状态
    refreshButton.disabled = true;
    refreshIcon.classList.add('animate-spin');
    
    // 获取CSRF token
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
    
    // 发送API请求
    fetch('{% url "organizations:admin:generate_anonymous_code" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken,
        },
        body: JSON.stringify({})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            anonymousCodeInput.value = data.anonymous_code;
            showNotification(data.message || '安全编号生成成功', 'success');
        } else {
            showNotification(data.error || '生成编号失败', 'error');
        }
    })
    .catch(error => {
        console.error('生成匿名编号失败:', error);
        showNotification('生成编号失败，请重试', 'error');
    })
    .finally(() => {
        // 恢复按钮状态
        refreshButton.disabled = false;
        refreshIcon.classList.remove('animate-spin');
    });
}

// 注意：由于现在使用安全编号生成API，不再需要在部门或职位改变时自动重新生成
// 用户可以手动点击刷新按钮来重新生成编号

// 切换密码显示
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const icon = input.nextElementSibling.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.setAttribute('data-lucide', 'eye-off');
        lucide.createIcons();
    } else {
        input.type = 'password';
        icon.setAttribute('data-lucide', 'eye');
        lucide.createIcons();
    }
}

// 表单提交处理
document.getElementById('staffForm').addEventListener('submit', function(e) {
    const submitBtn = document.getElementById('submitBtn');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i data-lucide="loader-2" class="w-4 h-4 inline mr-1 animate-spin"></i>正在创建...';
});

// 员工编号输入自动生成用户名
document.getElementById('id_employee_no').addEventListener('input', function() {
    const usernameInput = document.getElementById('id_username');
    if (!usernameInput.value) {
        usernameInput.value = this.value;
    }
});

// 通知功能
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 px-4 py-3 rounded-md text-white z-50 shadow-lg transform transition-all duration-300 ${
        type === 'success' ? 'bg-green-500' : 
        type === 'error' ? 'bg-red-500' : 
        type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
    }`;
    notification.innerHTML = `
        <div class="flex items-center space-x-2">
            <i data-lucide="${type === 'success' ? 'check-circle' : type === 'error' ? 'x-circle' : type === 'warning' ? 'alert-triangle' : 'info'}" class="w-4 h-4"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(notification);
    lucide.createIcons();
    
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}
</script>
{% endblock %}