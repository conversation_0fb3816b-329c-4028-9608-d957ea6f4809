{% extends "admin/base_admin.html" %}
{% load static %}

{% block page_title %}员工管理{% endblock %}
{% block page_description %}管理公司员工信息和权限设置{% endblock %}

{% block header_actions %}
<a href="{% url 'organizations:admin:staff_create' %}" class="btn btn-primary btn-md flex items-center space-x-2">
    <i data-lucide="plus" class="w-4 h-4"></i>
    <span>新建员工</span>
</a>
{% endblock %}

{% block admin_content %}
<!-- Stats -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="card card-body">
        <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
                <i data-lucide="users" class="w-6 h-6 text-blue-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">总员工数</p>
                <p class="text-2xl font-bold text-gray-900">{{ object_list|length|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="card card-body">
        <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
                <i data-lucide="user-check" class="w-6 h-6 text-green-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">在职员工</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.active_staff|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="card card-body">
        <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
                <i data-lucide="shield-check" class="w-6 h-6 text-yellow-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">管理人员</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.admin_staff|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="card card-body">
        <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
                <i data-lucide="building" class="w-6 h-6 text-purple-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">部门数量</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.total_departments|default:0 }}</p>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="card card-body mb-6">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="relative">
            <input type="text" id="searchInput" placeholder="搜索员工姓名、编号或邮箱..." class="form-input pl-10" data-table-search="staffTable">
            <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"></i>
        </div>
        <select id="departmentFilter" class="form-select">
            <option value="">所有部门</option>
            {% for dept in departments %}
            <option value="{{ dept.id }}">{{ dept.name }}</option>
            {% endfor %}
        </select>
        <select id="positionFilter" class="form-select">
            <option value="">所有职位</option>
            <option value="management">管理人员</option>
            <option value="regular">普通员工</option>
        </select>
    </div>
</div>

<!-- Staff Table -->
<div class="card">
    <div class="card-header">
        <h3 class="text-lg font-medium text-gray-900">员工列表</h3>
    </div>
    <div class="overflow-x-auto">
        <table class="table" data-table-optimize="true" id="staffTable">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" data-sortable="name">员工信息</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" data-sortable="department">所属部门</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" data-sortable="position">职位信息</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" data-sortable="is_active">状态</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">匿名编号</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200" id="staffTableBody">
            {% if object_list %}
                {% for staff in object_list %}
                <tr data-department="{{ staff.department.id }}" 
                    data-position="{% if staff.position %}{{ staff.position.level }}{% else %}0{% endif %}">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                <span class="text-sm font-medium">{{ staff.name|first|default:"员" }}</span>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900">{{ staff.name }}</div>
                                <div class="text-sm text-gray-500">{{ staff.employee_no }}</div>
                                <div class="text-sm text-gray-500">{{ staff.email|default:"未设置邮箱" }}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">{{ staff.department.name }}</div>
                        <div class="text-sm text-gray-500">{{ staff.department.dept_code }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        {% if staff.position %}
                            <div class="text-sm text-gray-900">{{ staff.position.name }}</div>
                            <div class="text-sm text-gray-500">等级{{ staff.position.level }}级</div>
                        {% else %}
                            <div class="text-sm text-gray-400">无职位</div>
                            <div class="text-sm text-gray-500">普通员工（1-4级）</div>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        {% if staff.is_active %}
                            <span class="badge badge-success">在职</span>
                        {% else %}
                            <span class="badge badge-error">离职</span>
                        {% endif %}
                        {% if staff.is_admin %}
                            <span class="badge badge-primary ml-1">管理员</span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">
                            {% if staff.new_anonymous_code %}
                                <span class="font-mono">{{ staff.new_anonymous_code }}</span>
                                <span class="badge badge-success ml-2">安全</span>
                            {% elif staff.anonymous_code %}
                                <span class="font-mono">{{ staff.anonymous_code }}</span>
                                <span class="badge badge-warning ml-2">待升级</span>
                            {% else %}
                                <span class="text-gray-400">未生成</span>
                            {% endif %}
                        </div>
                        {% if staff.new_anonymous_code or staff.anonymous_code %}
                            <div class="text-sm text-gray-500">可匿名登录</div>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        <a href="{% url 'organizations:admin:staff_detail' staff.pk %}" class="text-blue-600 hover:text-blue-900">查看</a>
                        <a href="{% url 'organizations:admin:staff_update' staff.pk %}" class="text-indigo-600 hover:text-indigo-900">编辑</a>
                        <a href="{% url 'organizations:admin:staff_reset_password' staff.pk %}" class="text-yellow-600 hover:text-yellow-900">重置密码</a>
                        <button onclick="confirmDelete('{{ staff.pk }}', '{{ staff.name }}')" class="text-red-600 hover:text-red-900">删除</button>
                    </td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="6" class="px-6 py-12 text-center">
                        <i data-lucide="users" class="mx-auto h-12 w-12 text-gray-400"></i>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">暂无员工数据</h3>
                        <p class="mt-1 text-sm text-gray-500">开始添加第一个员工吧。</p>
                        <div class="mt-6">
                            <a href="{% url 'organizations:admin:staff_create' %}" class="btn btn-primary btn-md inline-flex items-center">
                                <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                                新建员工
                            </a>
                        </div>
                    </td>
                </tr>
            {% endif %}
            </tbody>
        </table>
    </div>
</div>

<!-- Pagination -->
{% if is_paginated %}
<div class="card card-body flex items-center justify-between mt-6">
    <div class="flex-1 flex justify-between sm:hidden">
        {% if page_obj.has_previous %}
            <a href="?page={{ page_obj.previous_page_number }}" class="btn btn-secondary btn-sm">上一页</a>
        {% endif %}
        {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}" class="ml-3 btn btn-secondary btn-sm">下一页</a>
        {% endif %}
    </div>
    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
        <div>
            <p class="text-sm text-gray-700">
                显示第 <span class="font-medium">{{ page_obj.start_index }}</span> 到 <span class="font-medium">{{ page_obj.end_index }}</span> 条，
                共 <span class="font-medium">{{ page_obj.paginator.count }}</span> 条记录
            </p>
        </div>
        <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}" class="btn btn-ghost btn-sm rounded-l-md">
                        <i data-lucide="chevron-left" class="w-4 h-4"></i>
                    </a>
                {% endif %}
                
                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">{{ num }}</span>
                    {% else %}
                        <a href="?page={{ num }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">{{ num }}</a>
                    {% endif %}
                {% endfor %}
                
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}" class="btn btn-ghost btn-sm rounded-r-md">
                        <i data-lucide="chevron-right" class="w-4 h-4"></i>
                    </a>
                {% endif %}
            </nav>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 搜索功能
    document.getElementById('searchInput').addEventListener('input', function() {
        filterStaff();
    });

    // 部门筛选
    document.getElementById('departmentFilter').addEventListener('change', function() {
        filterStaff();
    });

    // 职位筛选
    document.getElementById('positionFilter').addEventListener('change', function() {
        filterStaff();
    });

    // 自动聚焦搜索框
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.focus();
    }
});

function filterStaff() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const departmentFilter = document.getElementById('departmentFilter').value;
    const positionFilter = document.getElementById('positionFilter').value;
    const staffRows = document.querySelectorAll('tbody tr');
    let visibleCount = 0;
    
    staffRows.forEach(row => {
        const staffName = row.querySelector('.text-sm.font-medium.text-gray-900')?.textContent.toLowerCase() || '';
        const staffNo = row.querySelector('.text-sm.text-gray-500')?.textContent.toLowerCase() || '';
        const departmentId = row.getAttribute('data-department');
        const positionLevel = parseInt(row.getAttribute('data-position')) || 0;
        
        let showRow = true;
        
        // 搜索筛选
        if (searchTerm && !staffName.includes(searchTerm) && !staffNo.includes(searchTerm)) {
            showRow = false;
        }
        
        // 部门筛选
        if (departmentFilter && departmentId !== departmentFilter) {
            showRow = false;
        }
        
        // 职位筛选
        if (positionFilter) {
            if (positionFilter === 'management' && positionLevel < 6) {
                showRow = false;
            } else if (positionFilter === 'regular' && positionLevel >= 6) {
                showRow = false;
            }
        }
        
        row.style.display = showRow ? '' : 'none';
        if (showRow) visibleCount++;
    });
}

// 删除确认
function confirmDelete(staffId, staffName) {
    if (confirm(`确定要删除员工 "${staffName}" 吗？此操作不可撤销。`)) {
        window.location.href = `/organizations/admin/staff/${staffId}/delete/`;
    }
}
</script>
{% endblock %}