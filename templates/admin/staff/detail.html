{% extends "admin/base_admin.html" %}

{% block page_title %}员工详情 - {{ object.name }}{% endblock %}
{% block page_description %}查看员工的详细信息和相关统计数据{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <a href="{% url 'organizations:admin:staff_update' object.pk %}" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
        <i data-lucide="edit" class="w-4 h-4"></i>
        <span>编辑员工</span>
    </a>
    <a href="{% url 'organizations:admin:staff_reset_password' object.pk %}" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 flex items-center space-x-2">
        <i data-lucide="key" class="w-4 h-4"></i>
        <span>重置密码</span>
    </a>
    <a href="{% url 'organizations:admin:staff_list' %}" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center space-x-2">
        <i data-lucide="arrow-left" class="w-4 h-4"></i>
        <span>返回列表</span>
    </a>
</div>
{% endblock %}

{% block admin_content %}
<div class="max-w-6xl mx-auto space-y-6">
    <!-- 基本信息卡片 -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="bg-gradient-to-r from-blue-500 to-blue-600 px-6 py-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <i data-lucide="user" class="w-8 h-8 text-white"></i>
                    </div>
                </div>
                <div class="ml-4 text-white">
                    <h2 class="text-2xl font-bold">{{ object.name }}</h2>
                    <p class="text-blue-100">{{ object.department.name }} - {{ object.position.name|default:"未设置职位" }}</p>
                    <div class="flex items-center mt-2 space-x-4">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white bg-opacity-20 text-white">
                            <i data-lucide="hash" class="w-3 h-3 mr-1"></i>
                            {{ object.employee_no }}
                        </span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                     {% if object.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                            {% if object.is_active %}
                                <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>
                                激活
                            {% else %}
                                <i data-lucide="x-circle" class="w-3 h-3 mr-1"></i>
                                禁用
                            {% endif %}
                        </span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                            <i data-lucide="shield" class="w-3 h-3 mr-1"></i>
                            {{ object.get_role_display }}
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <div class="px-6 py-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- 基本信息 -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                        <i data-lucide="user" class="w-5 h-5 mr-2 text-gray-500"></i>
                        基本信息
                    </h3>
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">员工编号</dt>
                            <dd class="text-sm text-gray-900">{{ object.employee_no }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">员工姓名</dt>
                            <dd class="text-sm text-gray-900">{{ object.name }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">所属部门</dt>
                            <dd class="text-sm text-gray-900">{{ object.department.name }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">职位信息</dt>
                            <dd class="text-sm text-gray-900">
                                {% if object.position %}
                                    {{ object.position.name }} ({{ object.position.level }}级)
                                {% else %}
                                    <span class="text-gray-400">未设置职位</span>
                                {% endif %}
                            </dd>
                        </div>
                    </dl>
                </div>

                <!-- 联系信息 -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                        <i data-lucide="mail" class="w-5 h-5 mr-2 text-gray-500"></i>
                        联系信息
                    </h3>
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">邮箱地址</dt>
                            <dd class="text-sm text-gray-900">
                                {% if object.email %}
                                    <a href="mailto:{{ object.email }}" class="text-blue-600 hover:text-blue-800">{{ object.email }}</a>
                                {% else %}
                                    <span class="text-gray-400">未设置邮箱</span>
                                {% endif %}
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">手机号码</dt>
                            <dd class="text-sm text-gray-900">
                                {% if object.phone %}
                                    <a href="tel:{{ object.phone }}" class="text-blue-600 hover:text-blue-800">{{ object.phone }}</a>
                                {% else %}
                                    <span class="text-gray-400">未设置手机号</span>
                                {% endif %}
                            </dd>
                        </div>
                    </dl>
                </div>

                <!-- 账号信息 -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                        <i data-lucide="key" class="w-5 h-5 mr-2 text-gray-500"></i>
                        账号信息
                    </h3>
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">登录用户名</dt>
                            <dd class="text-sm text-gray-900 font-mono">{{ object.username }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">匿名编号</dt>
                            <dd class="text-sm text-gray-900">
                                {% if object.new_anonymous_code %}
                                    <div class="flex items-center space-x-2">
                                        <span class="font-mono">{{ object.new_anonymous_code }}</span>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">安全编号</span>
                                    </div>
                                    <div class="text-xs text-gray-500 mt-1">SHA256安全加密</div>
                                {% elif object.anonymous_code %}
                                    <div class="flex items-center space-x-2">
                                        <span class="font-mono">{{ object.anonymous_code }}</span>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">待升级</span>
                                    </div>
                                    <div class="text-xs text-gray-500 mt-1">旧版编号，建议升级</div>
                                {% else %}
                                    <span class="text-gray-400">未生成</span>
                                {% endif %}
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">用户角色</dt>
                            <dd class="text-sm text-gray-900">{{ object.get_role_display }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">最后登录</dt>
                            <dd class="text-sm text-gray-900">
                                {% if object.last_login %}
                                    {{ object.last_login|date:"Y-m-d H:i:s" }}
                                {% else %}
                                    <span class="text-gray-400">从未登录</span>
                                {% endif %}
                            </dd>
                        </div>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计数据卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <i data-lucide="clipboard-list" class="w-6 h-6 text-blue-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">参与考评</p>
                    <p class="text-2xl font-bold text-gray-900">{{ stats.total_evaluations|default:0 }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 rounded-lg">
                    <i data-lucide="check-circle" class="w-6 h-6 text-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">已完成</p>
                    <p class="text-2xl font-bold text-gray-900">{{ stats.completed_evaluations|default:0 }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 rounded-lg">
                    <i data-lucide="star" class="w-6 h-6 text-yellow-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">平均得分</p>
                    <p class="text-2xl font-bold text-gray-900">{{ stats.average_score|default:0|floatformat:1 }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 rounded-lg">
                    <i data-lucide="trending-up" class="w-6 h-6 text-purple-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">完成率</p>
                    <p class="text-2xl font-bold text-gray-900">{{ stats.completion_rate|default:0|floatformat:1 }}%</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 详细数据表格 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 作为评价者的记录 -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i data-lucide="user-check" class="w-5 h-5 mr-2 text-gray-500"></i>
                    评价他人记录
                </h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">被评价者</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">考评批次</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for relation in evaluator_relations %}
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                        <i data-lucide="user" class="w-4 h-4 text-gray-600"></i>
                                    </div>
                                    <div class="ml-3">
                                        <div class="text-sm font-medium text-gray-900">{{ relation.evaluatee.name }}</div>
                                        <div class="text-sm text-gray-500">{{ relation.evaluatee.department.name }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ relation.batch.name }}</div>
                                <div class="text-sm text-gray-500">{{ relation.batch.get_status_display }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if relation.is_completed %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i data-lucide="check" class="w-3 h-3 mr-1"></i>
                                    已完成
                                </span>
                                {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    <i data-lucide="clock" class="w-3 h-3 mr-1"></i>
                                    待完成
                                </span>
                                {% endif %}
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="3" class="px-6 py-12 text-center text-gray-500">
                                <i data-lucide="inbox" class="w-12 h-12 mx-auto mb-4 text-gray-300"></i>
                                <p>暂无评价他人的记录</p>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 被评价记录 -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i data-lucide="star" class="w-5 h-5 mr-2 text-gray-500"></i>
                    被评价记录
                </h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">评价者</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">考评批次</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">得分</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for relation in evaluatee_relations %}
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                        <i data-lucide="user" class="w-4 h-4 text-gray-600"></i>
                                    </div>
                                    <div class="ml-3">
                                        <div class="text-sm font-medium text-gray-900">匿名评价者</div>
                                        <div class="text-sm text-gray-500">{{ relation.evaluator.department.name }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ relation.batch.name }}</div>
                                <div class="text-sm text-gray-500">{{ relation.batch.get_status_display }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if relation.evaluation_record %}
                                <div class="text-sm font-medium text-gray-900">{{ relation.evaluation_record.total_score|floatformat:1 }}分</div>
                                <div class="text-sm text-gray-500">权重: {{ relation.weight_factor }}×</div>
                                {% else %}
                                <span class="text-sm text-gray-400">待评价</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="3" class="px-6 py-12 text-center text-gray-500">
                                <i data-lucide="inbox" class="w-12 h-12 mx-auto mb-4 text-gray-300"></i>
                                <p>暂无被评价记录</p>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 时间戳信息 -->
    <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <i data-lucide="clock" class="w-5 h-5 mr-2 text-gray-500"></i>
            操作记录
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="bg-gray-50 rounded-lg p-4">
                <dt class="text-sm font-medium text-gray-500">创建时间</dt>
                <dd class="text-sm text-gray-900">{{ object.created_at|date:"Y-m-d H:i:s" }}</dd>
            </div>
            <div class="bg-gray-50 rounded-lg p-4">
                <dt class="text-sm font-medium text-gray-500">更新时间</dt>
                <dd class="text-sm text-gray-900">{{ object.updated_at|date:"Y-m-d H:i:s" }}</dd>
            </div>
            <div class="bg-gray-50 rounded-lg p-4">
                <dt class="text-sm font-medium text-gray-500">创建人</dt>
                <dd class="text-sm text-gray-900">{{ object.created_by|default:"系统" }}</dd>
            </div>
            <div class="bg-gray-50 rounded-lg p-4">
                <dt class="text-sm font-medium text-gray-500">更新人</dt>
                <dd class="text-sm text-gray-900">{{ object.updated_by|default:"系统" }}</dd>
            </div>
        </div>
    </div>
</div>
{% endblock %}