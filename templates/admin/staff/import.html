{% extends "admin/base_admin.html" %}

{% block page_title %}员工信息导入{% endblock %}
{% block page_description %}批量导入员工基本信息和账号数据{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <a href="/organizations/admin/excel/template/staff/" 
       class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center space-x-2">
        <i data-lucide="download" class="w-4 h-4"></i>
        <span>下载模板</span>
    </a>
    <a href="{% url 'organizations:admin:import_history' %}" 
       class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
        <i data-lucide="history" class="w-4 h-4"></i>
        <span>导入历史</span>
    </a>
    <a href="{% url 'organizations:admin:staff_list' %}" 
       class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center space-x-2">
        <i data-lucide="arrow-left" class="w-4 h-4"></i>
        <span>返回列表</span>
    </a>
</div>
{% endblock %}

{% block admin_content %}
<div class="max-w-4xl mx-auto space-y-6">
    <!-- 导入步骤指引 -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="list-checks" class="w-5 h-5 mr-2 text-gray-500"></i>
                导入步骤
            </h3>
        </div>
        <div class="p-6">
            <div class="flex items-center space-x-4">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">1</div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-900">下载模板</p>
                        <p class="text-xs text-gray-500">下载Excel导入模板</p>
                    </div>
                </div>
                <i data-lucide="arrow-right" class="w-4 h-4 text-gray-400"></i>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">2</div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-900">填写数据</p>
                        <p class="text-xs text-gray-500">按模板格式填写员工信息</p>
                    </div>
                </div>
                <i data-lucide="arrow-right" class="w-4 h-4 text-gray-400"></i>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">3</div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-900">上传导入</p>
                        <p class="text-xs text-gray-500">上传Excel文件进行导入</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 文件上传区域 -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="upload" class="w-5 h-5 mr-2 text-gray-500"></i>
                文件上传
            </h3>
        </div>
        <div class="p-6">
            <form id="importForm" enctype="multipart/form-data">
                {% csrf_token %}
                
                <!-- 拖拽上传区域 -->
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors" id="dropZone">
                    <div class="flex flex-col items-center">
                        <i data-lucide="file-spreadsheet" class="w-12 h-12 text-gray-400 mb-4"></i>
                        <p class="text-lg font-medium text-gray-900 mb-2">选择或拖拽Excel文件</p>
                        <p class="text-sm text-gray-500 mb-4">支持.xlsx和.xls格式，文件大小不超过10MB</p>
                        
                        <div class="flex space-x-4">
                            <label for="fileInput" class="cursor-pointer px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
                                <i data-lucide="folder-open" class="w-4 h-4"></i>
                                <span>选择文件</span>
                            </label>
                            <input type="file" id="fileInput" name="file" accept=".xlsx,.xls" class="hidden">
                            
                            <a href="/organizations/admin/excel/template/staff/" 
                               class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 flex items-center space-x-2">
                                <i data-lucide="download" class="w-4 h-4"></i>
                                <span>下载模板</span>
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- 选中的文件信息 -->
                <div id="fileInfo" class="hidden mt-4 bg-blue-50 border border-blue-200 rounded-md p-4">
                    <div class="flex items-center">
                        <i data-lucide="file-check" class="w-5 h-5 text-blue-600 mr-2"></i>
                        <div class="flex-1">
                            <p class="text-sm font-medium text-blue-900" id="fileName"></p>
                            <p class="text-xs text-blue-600" id="fileSize"></p>
                        </div>
                        <button type="button" onclick="clearFile()" class="text-blue-600 hover:text-blue-800">
                            <i data-lucide="x" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>
                
                <!-- 导入选项 -->
                <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">导入选项</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" name="update_existing" value="1" checked
                                       class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-900">更新已存在的记录</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="skip_errors" value="1" checked
                                       class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-900">跳过错误记录继续导入</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="send_notification" value="1"
                                       class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-900">导入完成后发送通知</span>
                            </label>
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">数据验证</label>
                        <div class="bg-gray-50 rounded-md p-3 text-sm text-gray-600">
                            <ul class="space-y-1">
                                <li>• 姓名、部门、级别为必填项</li>
                                <li>• 部门名称必须是系统中已存在的部门名称</li>
                                <li>• 级别必须在1-9之间（1-4级员工，5级副主管，6级正主管，7级副经理，8级正经理，9级领导班子）</li>
                                <li>• 职位名称为可选项，如填写则必须与系统中的职位名称完全一致</li>
                                <li>• 工号为可选项，如不填写系统将自动生成</li>
                                <li>• 邮箱格式必须正确（如填写）</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <!-- 提交按钮 -->
                <div class="mt-6 flex justify-end space-x-3">
                    <button type="button" onclick="validateFile()" id="validateBtn"
                            class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 flex items-center space-x-2 disabled:opacity-50" disabled>
                        <i data-lucide="check-circle" class="w-4 h-4"></i>
                        <span>验证数据</span>
                    </button>
                    <button type="submit" id="importBtn"
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2 disabled:opacity-50" disabled>
                        <i data-lucide="upload" class="w-4 h-4"></i>
                        <span>开始导入</span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 导入进度 -->
    <div id="progressSection" class="bg-white rounded-lg shadow hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="activity" class="w-5 h-5 mr-2 text-gray-500"></i>
                导入进度
            </h3>
        </div>
        <div class="p-6">
            <div class="mb-4">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-gray-700">处理进度</span>
                    <span class="text-sm text-gray-600" id="progressText">0%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                         style="width: 0%" id="progressBar"></div>
                </div>
            </div>
            
            <div class="grid grid-cols-3 gap-4 text-center">
                <div class="bg-blue-50 rounded-lg p-3">
                    <div class="text-2xl font-bold text-blue-600" id="totalCount">0</div>
                    <div class="text-sm text-blue-700">总记录数</div>
                </div>
                <div class="bg-green-50 rounded-lg p-3">
                    <div class="text-2xl font-bold text-green-600" id="successCount">0</div>
                    <div class="text-sm text-green-700">成功导入</div>
                </div>
                <div class="bg-red-50 rounded-lg p-3">
                    <div class="text-2xl font-bold text-red-600" id="errorCount">0</div>
                    <div class="text-sm text-red-700">导入失败</div>
                </div>
            </div>
            
            <div id="statusMessage" class="mt-4 text-sm text-gray-600"></div>
        </div>
    </div>

    <!-- 最近导入历史 -->
    {% if recent_imports %}
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i data-lucide="history" class="w-5 h-5 mr-2 text-gray-500"></i>
                    最近导入记录
                </h3>
                <a href="{% url 'organizations:admin:import_history' %}" 
                   class="text-sm text-blue-600 hover:text-blue-800">
                    查看全部
                </a>
            </div>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">文件名</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">导入时间</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">状态</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">成功/总数</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">处理时间</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for record in recent_imports %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">{{ record.filename }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ record.created_at|date:"Y-m-d H:i" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if record.status == 'completed' %}bg-green-100 text-green-800
                                {% elif record.status == 'partial' %}bg-yellow-100 text-yellow-800
                                {% elif record.status == 'failed' %}bg-red-100 text-red-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ record.get_status_display }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ record.success_count }}/{{ record.total_count }}</div>
                            <div class="text-xs text-gray-500">{{ record.success_rate }}%</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">
                                {% if record.processing_time %}{{ record.processing_time|floatformat:2 }}秒{% else %}-{% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% endif %}
</div>

<script>
let selectedFile = null;

document.addEventListener('DOMContentLoaded', function() {
    initializeFileUpload();
    bindFormEvents();
});

function initializeFileUpload() {
    const dropZone = document.getElementById('dropZone');
    const fileInput = document.getElementById('fileInput');
    
    // 拖拽事件
    dropZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        dropZone.classList.add('border-blue-400', 'bg-blue-50');
    });
    
    dropZone.addEventListener('dragleave', function(e) {
        e.preventDefault();
        dropZone.classList.remove('border-blue-400', 'bg-blue-50');
    });
    
    dropZone.addEventListener('drop', function(e) {
        e.preventDefault();
        dropZone.classList.remove('border-blue-400', 'bg-blue-50');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelect(files[0]);
        }
    });
    
    // 文件选择事件
    fileInput.addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
            handleFileSelect(e.target.files[0]);
        }
    });
}

function handleFileSelect(file) {
    // 验证文件类型
    if (!file.name.match(/\.(xlsx|xls)$/i)) {
        UI.showMessage('只支持Excel文件格式(.xlsx, .xls)', 'error');
        return;
    }
    
    // 验证文件大小 (10MB)
    if (file.size > 10 * 1024 * 1024) {
        UI.showMessage('文件大小不能超过10MB', 'error');
        return;
    }
    
    selectedFile = file;
    
    // 显示文件信息
    document.getElementById('fileName').textContent = file.name;
    document.getElementById('fileSize').textContent = formatFileSize(file.size);
    document.getElementById('fileInfo').classList.remove('hidden');
    
    // 启用按钮
    document.getElementById('validateBtn').disabled = false;
    document.getElementById('importBtn').disabled = false;
}

function clearFile() {
    selectedFile = null;
    document.getElementById('fileInput').value = '';
    document.getElementById('fileInfo').classList.add('hidden');
    document.getElementById('validateBtn').disabled = true;
    document.getElementById('importBtn').disabled = true;
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function bindFormEvents() {
    document.getElementById('importForm').addEventListener('submit', function(e) {
        e.preventDefault();
        if (selectedFile) {
            startImport();
        }
    });
}

function validateFile() {
    if (!selectedFile) {
        UI.showMessage('请先选择文件', 'warning');
        return;
    }
    
    UI.showMessage('文件验证功能开发中', 'info');
}

function startImport() {
    if (!selectedFile) {
        UI.showMessage('请先选择文件', 'warning');
        return;
    }
    
    // 显示进度区域
    document.getElementById('progressSection').classList.remove('hidden');
    
    // 禁用表单
    document.getElementById('importBtn').disabled = true;
    document.getElementById('validateBtn').disabled = true;
    
    // 创建FormData
    const formData = new FormData(document.getElementById('importForm'));
    formData.append('file', selectedFile);
    
    // 开始导入
    fetch('{% url "organizations:admin:staff_import" %}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            handleImportSuccess(data);
        } else {
            handleImportError(data);
        }
    })
    .catch(error => {
        console.error('导入失败:', error);
        handleImportError({
            error: '网络错误，请重试',
            message: '导入过程中发生网络错误'
        });
    })
    .finally(() => {
        // 重新启用表单
        document.getElementById('importBtn').disabled = false;
        document.getElementById('validateBtn').disabled = false;
    });
}

function handleImportSuccess(data) {
    // 更新进度显示
    document.getElementById('progressBar').style.width = '100%';
    document.getElementById('progressText').textContent = '100%';
    
    // 更新统计数据
    document.getElementById('totalCount').textContent = data.total_rows || 0;
    document.getElementById('successCount').textContent = data.success_count || 0;
    document.getElementById('errorCount').textContent = data.error_count || 0;
    
    // 显示成功消息
    const message = `导入完成！成功导入${data.success_count}条记录`;
    document.getElementById('statusMessage').textContent = message;
    
    UI.showMessage(message, 'success');
    
    // 如果有错误，显示错误详情
    if (data.errors && data.errors.length > 0) {
        console.log('导入错误详情:', data.errors);
    }
    
    // 3秒后刷新页面
    setTimeout(() => {
        window.location.reload();
    }, 3000);
}

function handleImportError(data) {
    // 更新进度显示为错误状态
    document.getElementById('progressBar').style.width = '100%';
    document.getElementById('progressBar').classList.remove('bg-blue-600');
    document.getElementById('progressBar').classList.add('bg-red-600');
    document.getElementById('progressText').textContent = '失败';
    
    // 显示错误消息
    const errorMessage = data.message || data.error || '导入失败';
    document.getElementById('statusMessage').textContent = errorMessage;
    
    UI.showMessage(errorMessage, 'error');
    
    // 如果有详细错误信息，显示在控制台
    if (data.errors) {
        console.error('导入错误详情:', data.errors);
    }
}
</script>
{% endblock %}