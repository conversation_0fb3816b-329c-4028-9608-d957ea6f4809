{% extends "admin/base_admin.html" %}

{% block page_title %}导入历史记录{% endblock %}
{% block page_description %}查看Excel导入操作的历史记录和详细信息{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <button onclick="clearFilters()" 
            class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center space-x-2">
        <i data-lucide="x-circle" class="w-4 h-4"></i>
        <span>清除筛选</span>
    </button>
</div>
{% endblock %}

{% block admin_content %}
<div class="space-y-6">
    <!-- 筛选器 -->
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center mb-4">
            <i data-lucide="filter" class="w-5 h-5 mr-2 text-gray-500"></i>
            <h3 class="text-lg font-medium text-gray-900">筛选条件</h3>
        </div>
        
        <form method="GET" class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">导入类型</label>
                <select name="type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">全部类型</option>
                    {% for value, label in import_types %}
                    <option value="{{ value }}" {% if selected_type == value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">导入状态</label>
                <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">全部状态</option>
                    {% for value, label in status_choices %}
                    <option value="{{ value }}" {% if selected_status == value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="flex items-end">
                <button type="submit" 
                        class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center justify-center space-x-2">
                    <i data-lucide="search" class="w-4 h-4"></i>
                    <span>筛选</span>
                </button>
            </div>
        </form>
    </div>

    <!-- 导入历史列表 -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i data-lucide="history" class="w-5 h-5 mr-2 text-gray-500"></i>
                    导入历史记录
                </h3>
                <div class="text-sm text-gray-500">
                    共 {{ paginator.count }} 条记录
                </div>
            </div>
        </div>
        
        {% if import_records %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">文件名</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">导入类型</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">导入时间</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">记录数统计</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">处理时间</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for record in import_records %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <i data-lucide="file-spreadsheet" class="w-4 h-4 mr-2 text-green-500"></i>
                                <div class="text-sm font-medium text-gray-900">{{ record.filename }}</div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {{ record.get_import_type_display }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ record.created_at|date:"Y-m-d H:i:s" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if record.status == 'completed' %}bg-green-100 text-green-800
                                {% elif record.status == 'partial' %}bg-yellow-100 text-yellow-800
                                {% elif record.status == 'failed' %}bg-red-100 text-red-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ record.get_status_display }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">
                                <div class="flex items-center space-x-4">
                                    <span class="text-green-600">成功: {{ record.success_count }}</span>
                                    <span class="text-red-600">失败: {{ record.error_count }}</span>
                                    <span class="text-gray-600">总计: {{ record.total_count }}</span>
                                </div>
                                <div class="text-xs text-gray-500 mt-1">
                                    成功率: {{ record.success_rate }}%
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">
                                {% if record.processing_time %}
                                    {{ record.processing_time|floatformat:2 }}秒
                                {% else %}
                                    -
                                {% endif %}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex items-center space-x-2">
                                {% if record.error_details %}
                                <button onclick="showErrorDetails({{ record.id }})" 
                                        class="text-red-600 hover:text-red-900 flex items-center space-x-1">
                                    <i data-lucide="alert-circle" class="w-4 h-4"></i>
                                    <span>错误详情</span>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        {% if is_paginated %}
        <div class="px-6 py-3 bg-gray-50 border-t border-gray-200">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    显示第 {{ page_obj.start_index }} - {{ page_obj.end_index }} 条，共 {{ paginator.count }} 条记录
                </div>
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                    {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}{% if selected_type %}&type={{ selected_type }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}"
                       class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <i data-lucide="chevron-left" class="w-4 h-4"></i>
                    </a>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if num == page_obj.number %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                            {{ num }}
                        </span>
                        {% else %}
                        <a href="?page={{ num }}{% if selected_type %}&type={{ selected_type }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}"
                           class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                            {{ num }}
                        </a>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}{% if selected_type %}&type={{ selected_type }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}"
                       class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <i data-lucide="chevron-right" class="w-4 h-4"></i>
                    </a>
                    {% endif %}
                </nav>
            </div>
        </div>
        {% endif %}
        
        {% else %}
        <div class="px-6 py-12 text-center">
            <i data-lucide="history" class="w-12 h-12 text-gray-400 mx-auto mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">暂无导入历史记录</h3>
            <p class="text-gray-500">还没有进行过Excel导入操作</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- 错误详情模态框 -->
<div id="errorModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i data-lucide="alert-circle" class="w-5 h-5 mr-2 text-red-500"></i>
                    导入错误详情
                </h3>
                <button onclick="closeErrorModal()" class="text-gray-400 hover:text-gray-600">
                    <i data-lucide="x" class="w-5 h-5"></i>
                </button>
            </div>
            <div id="errorContent" class="max-h-96 overflow-y-auto">
                <!-- 错误详情内容将在这里显示 -->
            </div>
            <div class="mt-4 flex justify-end">
                <button onclick="closeErrorModal()" 
                        class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
                    关闭
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function clearFilters() {
    window.location.href = '{% url "organizations:admin:import_history" %}';
}

function showErrorDetails(recordId) {
    // 这里可以通过AJAX获取详细的错误信息
    // 暂时显示一个占位符
    const errorContent = document.getElementById('errorContent');
    errorContent.innerHTML = `
        <div class="bg-red-50 border border-red-200 rounded-md p-4">
            <p class="text-sm text-red-800">正在加载错误详情...</p>
        </div>
    `;
    
    document.getElementById('errorModal').classList.remove('hidden');
    
    // 模拟AJAX请求获取错误详情
    setTimeout(() => {
        errorContent.innerHTML = `
            <div class="space-y-3">
                <div class="bg-red-50 border border-red-200 rounded-md p-3">
                    <div class="text-sm font-medium text-red-800">第3行：员工编号不能为空</div>
                </div>
                <div class="bg-red-50 border border-red-200 rounded-md p-3">
                    <div class="text-sm font-medium text-red-800">第5行：邮箱格式不正确</div>
                </div>
                <div class="bg-red-50 border border-red-200 rounded-md p-3">
                    <div class="text-sm font-medium text-red-800">第7行：部门编号不存在</div>
                </div>
            </div>
        `;
    }, 500);
}

function closeErrorModal() {
    document.getElementById('errorModal').classList.add('hidden');
}

// 点击模态框外部关闭
document.getElementById('errorModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeErrorModal();
    }
});
</script>
{% endblock %}