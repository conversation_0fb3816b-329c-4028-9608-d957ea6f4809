<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 500px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background-color: #007cba;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #005a87;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .debug {
            background-color: #f8f9fa;
            color: #495057;
            border: 1px solid #dee2e6;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>登录测试页面</h2>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" name="username" value="testadmin" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" name="password" value="test123456" required>
            </div>
            
            <button type="submit" id="loginBtn">登录</button>
        </form>
        
        <div id="messages"></div>
        <div id="debug"></div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const loginBtn = document.getElementById('loginBtn');
            const messagesDiv = document.getElementById('messages');
            const debugDiv = document.getElementById('debug');
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            
            // 清除之前的消息
            messagesDiv.innerHTML = '';
            debugDiv.innerHTML = '';
            
            // 显示加载状态
            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';
            
            try {
                console.log('开始登录请求...');
                debugDiv.innerHTML += '开始登录请求...\n';
                
                const response = await fetch('/api/login/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                console.log('响应状态:', response.status);
                debugDiv.innerHTML += `响应状态: ${response.status}\n`;
                
                const responseText = await response.text();
                console.log('响应文本:', responseText);
                debugDiv.innerHTML += `响应文本: ${responseText}\n`;
                
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    console.error('JSON解析错误:', parseError);
                    debugDiv.innerHTML += `JSON解析错误: ${parseError.message}\n`;
                    showMessage('error', '服务器响应格式错误');
                    return;
                }
                
                console.log('解析后的数据:', data);
                debugDiv.innerHTML += `解析后的数据: ${JSON.stringify(data, null, 2)}\n`;
                
                if (response.ok && data.success) {
                    showMessage('success', data.message || '登录成功！');
                    
                    // 保存token
                    if (data.tokens) {
                        localStorage.setItem('access_token', data.tokens.access_token);
                        localStorage.setItem('refresh_token', data.tokens.refresh_token);
                        localStorage.setItem('user_info', JSON.stringify(data.user));
                        debugDiv.innerHTML += 'Token已保存到localStorage\n';
                    }
                    
                    // 延迟跳转
                    setTimeout(() => {
                        window.location.href = '/admin/';
                    }, 2000);
                    
                } else {
                    showMessage('error', data.message || '登录失败');
                }
                
            } catch (error) {
                console.error('请求错误:', error);
                debugDiv.innerHTML += `请求错误: ${error.message}\n`;
                showMessage('error', '网络错误，请稍后重试');
            } finally {
                // 恢复按钮状态
                loginBtn.disabled = false;
                loginBtn.textContent = '登录';
            }
        });
        
        function showMessage(type, text) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = text;
            messagesDiv.appendChild(messageDiv);
        }
    </script>
</body>
</html>
