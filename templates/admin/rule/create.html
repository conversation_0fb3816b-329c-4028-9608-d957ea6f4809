{% extends "admin/base_admin.html" %}

{% block page_title %}创建权重规则{% endblock %}
{% block page_description %}配置智能分配算法的权重计算规则{% endblock %}

{% block header_actions %}
<a href="{% url 'evaluations:admin:rule_list' %}" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center space-x-2">
    <i data-lucide="arrow-left" class="w-4 h-4"></i>
    <span>返回列表</span>
</a>
{% endblock %}

{% block admin_content %}
<form method="post" id="ruleForm" class="space-y-8">
    {% csrf_token %}
    
    <!-- 基础信息 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="info" class="w-5 h-5 mr-2 text-gray-500"></i>
                基础信息
            </h3>
            <p class="mt-1 text-sm text-gray-500">设置权重规则的基本信息</p>
        </div>
        <div class="px-6 py-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 规则名称 -->
                <div>
                    <label for="id_name" class="block text-sm font-medium text-gray-700 mb-2">
                        规则名称 <span class="text-red-500">*</span>
                    </label>
                    <input type="text" name="name" id="id_name" required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="请输入规则名称">
                    <p class="mt-1 text-xs text-gray-500">例如：部门经理权重规则、跨部门评价规则等</p>
                </div>

                <!-- 优先级 -->
                <div>
                    <label for="id_priority" class="block text-sm font-medium text-gray-700 mb-2">
                        优先级 <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <select name="priority" id="id_priority" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 appearance-none">
                            <option value="">请选择优先级</option>
                            <option value="1">1 - 低优先级</option>
                            <option value="2">2 - 中优先级</option>
                            <option value="3">3 - 中优先级</option>
                            <option value="4">4 - 中优先级</option>
                            <option value="5">5 - 高优先级</option>
                            <option value="6">6 - 高优先级</option>
                            <option value="7">7 - 高优先级</option>
                            <option value="8">8 - 高优先级</option>
                            <option value="9">9 - 最高优先级</option>
                            <option value="10">10 - 最高优先级</option>
                        </select>
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i data-lucide="chevron-down" class="w-4 h-4 text-gray-400"></i>
                        </div>
                    </div>
                    <p class="mt-1 text-xs text-gray-500">数字越大优先级越高，优先级高的规则会优先匹配</p>
                </div>

                <!-- 规则描述 -->
                <div class="lg:col-span-2">
                    <label for="id_description" class="block text-sm font-medium text-gray-700 mb-2">
                        规则描述
                    </label>
                    <textarea name="description" id="id_description" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="请输入规则的详细描述和适用场景"></textarea>
                    <p class="mt-1 text-xs text-gray-500">描述规则的用途、适用场景和注意事项</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 规则条件 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="filter" class="w-5 h-5 mr-2 text-gray-500"></i>
                规则条件
            </h3>
            <p class="mt-1 text-sm text-gray-500">设置规则的匹配条件</p>
        </div>
        <div class="px-6 py-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 条件类型 -->
                <div>
                    <label for="id_condition_type" class="block text-sm font-medium text-gray-700 mb-2">
                        条件类型 <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <select name="condition_type" id="id_condition_type" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 appearance-none">
                            <option value="">请选择条件类型</option>
                            <option value="department">部门关系 - 基于评价者所属部门</option>
                            <option value="position_level">职位层级 - 基于评价者职位层级</option>
                            <option value="relation_type">评价关系 - 基于评价者与被评价者关系</option>
                            <option value="default">默认规则 - 无特定条件，作为兜底规则</option>
                        </select>
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i data-lucide="chevron-down" class="w-4 h-4 text-gray-400"></i>
                        </div>
                    </div>
                    <p class="mt-1 text-xs text-gray-500">选择规则匹配的依据</p>
                </div>

                <!-- 条件值 -->
                <div id="conditionValueDiv" style="display: none;">
                    <label for="id_condition_value" class="block text-sm font-medium text-gray-700 mb-2">
                        条件值
                    </label>
                    <div id="departmentCondition" style="display: none;">
                        <select name="condition_value" id="id_department_condition"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">请选择部门</option>
                            {% for dept in departments %}
                                <option value="{{ dept.id }}">{{ dept.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div id="positionCondition" style="display: none;">
                        <select name="condition_value" id="id_position_condition"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">请选择职位层级</option>
                            <option value="1">1级 - 基层员工</option>
                            <option value="2">2级 - 基层员工</option>
                            <option value="3">3级 - 基层员工</option>
                            <option value="4">4级 - 基层员工</option>
                            <option value="5">5级 - 副主管</option>
                            <option value="6">6级 - 正主管</option>
                            <option value="7">7级 - 副经理</option>
                            <option value="8">8级 - 正经理</option>
                            <option value="9">9级 - 领导班子</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 关系类型 -->
            <div class="mt-6" id="relationTypeDiv" style="display: none;">
                <label for="id_relation_type" class="block text-sm font-medium text-gray-700 mb-2">
                    评价关系类型
                </label>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <label class="relative flex cursor-pointer rounded-lg border border-gray-200 p-4 hover:bg-gray-50 focus:outline-none">
                        <input type="radio" name="relation_type" value="subordinate_to_superior"
                               class="sr-only peer">
                        <div class="flex flex-1 flex-col">
                            <div class="flex items-center justify-between">
                                <span class="block text-sm font-medium text-gray-900">下级评上级</span>
                                <i data-lucide="arrow-up" class="w-4 h-4 text-gray-400"></i>
                            </div>
                            <span class="mt-1 block text-xs text-gray-500">基层员工评价管理人员</span>
                        </div>
                        <div class="absolute -inset-px rounded-lg border-2 pointer-events-none peer-checked:border-blue-500"></div>
                    </label>

                    <label class="relative flex cursor-pointer rounded-lg border border-gray-200 p-4 hover:bg-gray-50 focus:outline-none">
                        <input type="radio" name="relation_type" value="superior_to_subordinate"
                               class="sr-only peer">
                        <div class="flex flex-1 flex-col">
                            <div class="flex items-center justify-between">
                                <span class="block text-sm font-medium text-gray-900">上级评下级</span>
                                <i data-lucide="arrow-down" class="w-4 h-4 text-gray-400"></i>
                            </div>
                            <span class="mt-1 block text-xs text-gray-500">管理人员评价下属员工</span>
                        </div>
                        <div class="absolute -inset-px rounded-lg border-2 pointer-events-none peer-checked:border-blue-500"></div>
                    </label>

                    <label class="relative flex cursor-pointer rounded-lg border border-gray-200 p-4 hover:bg-gray-50 focus:outline-none">
                        <input type="radio" name="relation_type" value="peer_to_peer"
                               class="sr-only peer">
                        <div class="flex flex-1 flex-col">
                            <div class="flex items-center justify-between">
                                <span class="block text-sm font-medium text-gray-900">同级互评</span>
                                <i data-lucide="arrow-right" class="w-4 h-4 text-gray-400"></i>
                            </div>
                            <span class="mt-1 block text-xs text-gray-500">同职位层级员工互相评价</span>
                        </div>
                        <div class="absolute -inset-px rounded-lg border-2 pointer-events-none peer-checked:border-blue-500"></div>
                    </label>

                    <label class="relative flex cursor-pointer rounded-lg border border-gray-200 p-4 hover:bg-gray-50 focus:outline-none">
                        <input type="radio" name="relation_type" value="cross_department"
                               class="sr-only peer">
                        <div class="flex flex-1 flex-col">
                            <div class="flex items-center justify-between">
                                <span class="block text-sm font-medium text-gray-900">跨部门评价</span>
                                <i data-lucide="shuffle" class="w-4 h-4 text-gray-400"></i>
                            </div>
                            <span class="mt-1 block text-xs text-gray-500">不同部门员工之间的评价</span>
                        </div>
                        <div class="absolute -inset-px rounded-lg border-2 pointer-events-none peer-checked:border-blue-500"></div>
                    </label>
                </div>
            </div>
        </div>
    </div>

    <!-- 权重设置 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="target" class="w-5 h-5 mr-2 text-gray-500"></i>
                权重设置
            </h3>
            <p class="mt-1 text-sm text-gray-500">设置权重系数和启用状态</p>
        </div>
        <div class="px-6 py-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 权重系数 -->
                <div>
                    <label for="id_weight_factor" class="block text-sm font-medium text-gray-700 mb-2">
                        权重系数 <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <input type="number" name="weight_factor" id="id_weight_factor" 
                               min="0.1" max="5.0" step="0.1" value="1.0" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <span class="text-gray-500 text-sm">x</span>
                        </div>
                    </div>
                    <p class="mt-1 text-xs text-gray-500">权重系数范围：0.1 - 5.0，默认为1.0</p>
                    
                    <!-- 权重滑块 -->
                    <div class="mt-4">
                        <input type="range" id="weightSlider" min="0.1" max="5.0" step="0.1" value="1.0"
                               class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                        <div class="flex justify-between text-xs text-gray-500 mt-1">
                            <span>0.1x</span>
                            <span id="currentWeight">1.0x</span>
                            <span>5.0x</span>
                        </div>
                    </div>
                </div>

                <!-- 启用状态 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-4">规则状态</label>
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <input type="checkbox" name="is_active" id="id_is_active" checked
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="id_is_active" class="ml-2 block text-sm text-gray-700">
                                启用规则
                            </label>
                            <div class="ml-2 group relative">
                                <i data-lucide="help-circle" class="w-4 h-4 text-gray-400 cursor-help"></i>
                                <div class="hidden group-hover:block absolute z-10 w-64 p-2 bg-gray-800 text-white text-xs rounded shadow-lg -top-2 left-6">
                                    启用后的规则会在智能分配时生效
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 权重说明 -->
                    <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                        <h4 class="text-sm font-medium text-blue-900 mb-2">权重系数说明</h4>
                        <ul class="text-xs text-blue-800 space-y-1">
                            <li>• < 1.0：降低该条件下的评分权重</li>
                            <li>• = 1.0：保持默认权重，不做调整</li>
                            <li>• > 1.0：提高该条件下的评分权重</li>
                            <li>• 常用值：下级评上级(0.8)、上级评下级(1.2)、同级互评(1.0)</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 规则预览 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200" id="rulePreview" style="display: none;">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="eye" class="w-5 h-5 mr-2 text-gray-500"></i>
                规则预览
            </h3>
            <p class="mt-1 text-sm text-gray-500">预览规则的匹配条件和效果</p>
        </div>
        <div class="px-6 py-6">
            <div class="bg-gray-50 rounded-lg p-4">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <i data-lucide="zap" class="w-5 h-5 text-blue-600"></i>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-900" id="previewTitle">规则名称</h4>
                        <p class="text-sm text-gray-600 mt-1" id="previewDescription">规则描述</p>
                        <div class="mt-3 flex flex-wrap gap-2" id="previewTags">
                            <!-- 预览标签将在这里显示 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 提交按钮 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-600">
                    <i data-lucide="info" class="w-4 h-4 inline mr-1"></i>
                    创建后可以继续编辑规则配置
                </div>
                <div class="flex items-center space-x-4">
                    <button type="button" id="saveDraftBtn"
                            class="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
                        保存草稿
                    </button>
                    <button type="submit" id="submitBtn"
                            class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
                        <i data-lucide="save" class="w-4 h-4"></i>
                        <span>创建规则</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
    // 权重滑块同步
    const weightInput = document.getElementById('id_weight_factor');
    const weightSlider = document.getElementById('weightSlider');
    const currentWeight = document.getElementById('currentWeight');

    weightInput.addEventListener('input', function() {
        weightSlider.value = this.value;
        currentWeight.textContent = this.value + 'x';
        updatePreview();
    });

    weightSlider.addEventListener('input', function() {
        weightInput.value = this.value;
        currentWeight.textContent = this.value + 'x';
        updatePreview();
    });

    // 条件类型变化
    document.getElementById('id_condition_type').addEventListener('change', function() {
        const conditionType = this.value;
        const conditionValueDiv = document.getElementById('conditionValueDiv');
        const departmentCondition = document.getElementById('departmentCondition');
        const positionCondition = document.getElementById('positionCondition');
        const relationTypeDiv = document.getElementById('relationTypeDiv');

        // 隐藏所有条件值选项
        conditionValueDiv.style.display = 'none';
        departmentCondition.style.display = 'none';
        positionCondition.style.display = 'none';
        relationTypeDiv.style.display = 'none';

        // 清除所有输入值
        document.getElementById('id_department_condition').value = '';
        document.getElementById('id_position_condition').value = '';
        document.querySelectorAll('input[name="relation_type"]').forEach(radio => {
            radio.checked = false;
        });

        if (conditionType === 'department') {
            conditionValueDiv.style.display = 'block';
            departmentCondition.style.display = 'block';
        } else if (conditionType === 'position_level') {
            conditionValueDiv.style.display = 'block';
            positionCondition.style.display = 'block';
        } else if (conditionType === 'relation_type') {
            relationTypeDiv.style.display = 'block';
        }

        updatePreview();
    });

    // 监听所有表单变化
    document.getElementById('ruleForm').addEventListener('input', updatePreview);
    document.getElementById('ruleForm').addEventListener('change', updatePreview);

    function updatePreview() {
        const name = document.getElementById('id_name').value || '未命名规则';
        const description = document.getElementById('id_description').value || '暂无描述';
        const conditionType = document.getElementById('id_condition_type').value;
        const weightFactor = document.getElementById('id_weight_factor').value;
        const priority = document.getElementById('id_priority').value;
        const isActive = document.getElementById('id_is_active').checked;

        document.getElementById('previewTitle').textContent = name;
        document.getElementById('previewDescription').textContent = description;

        const previewTags = document.getElementById('previewTags');
        previewTags.innerHTML = '';

        // 条件类型标签
        if (conditionType) {
            const conditionLabel = {
                'department': '部门关系',
                'position_level': '职位层级',
                'relation_type': '评价关系',
                'default': '默认规则'
            };
            previewTags.innerHTML += `<span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">${conditionLabel[conditionType]}</span>`;
        }

        // 权重标签
        if (weightFactor) {
            const weightColor = weightFactor < 1 ? 'red' : (weightFactor > 1 ? 'green' : 'gray');
            previewTags.innerHTML += `<span class="px-2 py-1 bg-${weightColor}-100 text-${weightColor}-800 text-xs rounded-full">权重: ${weightFactor}x</span>`;
        }

        // 优先级标签
        if (priority) {
            const priorityColor = priority >= 5 ? 'red' : (priority >= 2 ? 'yellow' : 'gray');
            const priorityLabel = priority >= 5 ? '高优先级' : (priority >= 2 ? '中优先级' : '低优先级');
            previewTags.innerHTML += `<span class="px-2 py-1 bg-${priorityColor}-100 text-${priorityLabel === '高优先级' ? 'red' : priorityLabel === '中优先级' ? 'yellow' : 'gray'}-800 text-xs rounded-full">${priorityLabel}</span>`;
        }

        // 状态标签
        const statusColor = isActive ? 'green' : 'red';
        const statusLabel = isActive ? '启用中' : '已禁用';
        previewTags.innerHTML += `<span class="px-2 py-1 bg-${statusColor}-100 text-${statusColor}-800 text-xs rounded-full">${statusLabel}</span>`;

        // 显示预览
        document.getElementById('rulePreview').style.display = 'block';
    }

    // 表单提交处理
    document.getElementById('ruleForm').addEventListener('submit', function(e) {
        const conditionType = document.getElementById('id_condition_type').value;
        
        if (!conditionType) {
            e.preventDefault();
            showNotification('请选择条件类型', 'error');
            return;
        }

        // 显示加载状态
        const submitBtn = document.getElementById('submitBtn');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i data-lucide="loader-2" class="w-4 h-4 mr-2 animate-spin"></i>创建中...';
    });

    // 保存草稿
    document.getElementById('saveDraftBtn').addEventListener('click', function() {
        const form = document.getElementById('ruleForm');
        const formData = new FormData(form);
        formData.append('save_draft', 'true');
        
        fetch(form.action, {
            method: 'POST',
            body: formData,
        })
        .then(response => {
            if (response.ok) {
                showNotification('草稿保存成功', 'success');
            } else {
                showNotification('保存失败，请重试', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('保存失败，请重试', 'error');
        });
    });

    // 页面加载时初始化预览
    document.addEventListener('DOMContentLoaded', function() {
        updatePreview();
    });
</script>
{% endblock %}