{% extends "admin/base_admin.html" %}

{% block page_title %}编辑权重规则{% endblock %}
{% block page_description %}修改智能分配算法的权重计算规则{% endblock %}

{% block header_actions %}
<a href="{% url 'evaluations:admin:rule_list' %}" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center space-x-2">
    <i data-lucide="arrow-left" class="w-4 h-4"></i>
    <span>返回列表</span>
</a>
{% endblock %}

{% block admin_content %}
<form method="post" id="ruleForm" class="space-y-8">
    {% csrf_token %}
    
    <!-- 基础信息 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="info" class="w-5 h-5 mr-2 text-gray-500"></i>
                基础信息
            </h3>
            <p class="mt-1 text-sm text-gray-500">修改权重规则的基本信息</p>
        </div>
        <div class="px-6 py-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 规则名称 -->
                <div>
                    <label for="id_name" class="block text-sm font-medium text-gray-700 mb-2">
                        规则名称 <span class="text-red-500">*</span>
                    </label>
                    <input type="text" name="name" id="id_name" required value="{{ object.name }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="请输入规则名称">
                    <p class="mt-1 text-xs text-gray-500">例如：部门经理权重规则、跨部门评价规则等</p>
                </div>

                <!-- 优先级 -->
                <div>
                    <label for="id_priority" class="block text-sm font-medium text-gray-700 mb-2">
                        优先级 <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <select name="priority" id="id_priority" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 appearance-none">
                            <option value="">请选择优先级</option>
                            <option value="1" {% if object.priority == 1 %}selected{% endif %}>1 - 低优先级</option>
                            <option value="2" {% if object.priority == 2 %}selected{% endif %}>2 - 中优先级</option>
                            <option value="3" {% if object.priority == 3 %}selected{% endif %}>3 - 中优先级</option>
                            <option value="4" {% if object.priority == 4 %}selected{% endif %}>4 - 中优先级</option>
                            <option value="5" {% if object.priority == 5 %}selected{% endif %}>5 - 高优先级</option>
                            <option value="6" {% if object.priority == 6 %}selected{% endif %}>6 - 高优先级</option>
                            <option value="7" {% if object.priority == 7 %}selected{% endif %}>7 - 高优先级</option>
                            <option value="8" {% if object.priority == 8 %}selected{% endif %}>8 - 高优先级</option>
                            <option value="9" {% if object.priority == 9 %}selected{% endif %}>9 - 最高优先级</option>
                            <option value="10" {% if object.priority == 10 %}selected{% endif %}>10 - 最高优先级</option>
                        </select>
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i data-lucide="chevron-down" class="w-4 h-4 text-gray-400"></i>
                        </div>
                    </div>
                    <p class="mt-1 text-xs text-gray-500">数字越大优先级越高，优先级高的规则会优先匹配</p>
                </div>

                <!-- 规则描述 -->
                <div class="lg:col-span-2">
                    <label for="id_description" class="block text-sm font-medium text-gray-700 mb-2">
                        规则描述
                    </label>
                    <textarea name="description" id="id_description" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="请输入规则的详细描述和适用场景">{{ object.description }}</textarea>
                    <p class="mt-1 text-xs text-gray-500">描述规则的用途、适用场景和注意事项</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 规则条件 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="filter" class="w-5 h-5 mr-2 text-gray-500"></i>
                规则条件
            </h3>
            <p class="mt-1 text-sm text-gray-500">修改规则的匹配条件</p>
        </div>
        <div class="px-6 py-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 条件类型 -->
                <div>
                    <label for="id_condition_type" class="block text-sm font-medium text-gray-700 mb-2">
                        条件类型 <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <select name="condition_type" id="id_condition_type" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 appearance-none">
                            <option value="">请选择条件类型</option>
                            <option value="department" {% if object.condition_type == 'department' %}selected{% endif %}>部门关系 - 基于评价者所属部门</option>
                            <option value="position_level" {% if object.condition_type == 'position_level' %}selected{% endif %}>职位层级 - 基于评价者职位层级</option>
                            <option value="relation_type" {% if object.condition_type == 'relation_type' %}selected{% endif %}>评价关系 - 基于评价者与被评价者关系</option>
                            <option value="default" {% if object.condition_type == 'default' %}selected{% endif %}>默认规则 - 无特定条件，作为兜底规则</option>
                        </select>
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i data-lucide="chevron-down" class="w-4 h-4 text-gray-400"></i>
                        </div>
                    </div>
                    <p class="mt-1 text-xs text-gray-500">选择规则匹配的依据</p>
                </div>

                <!-- 条件值 -->
                <div id="conditionValueDiv" {% if object.condition_type not in 'department,position_level' %}style="display: none;"{% endif %}>
                    <label for="id_condition_value" class="block text-sm font-medium text-gray-700 mb-2">
                        条件值
                    </label>
                    <div id="departmentCondition" {% if object.condition_type != 'department' %}style="display: none;"{% endif %}>
                        <select name="condition_value" id="id_department_condition"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">请选择部门</option>
                            {% for dept in departments %}
                                <option value="{{ dept.id }}" {% if object.condition_value == dept.id|stringformat:"s" %}selected{% endif %}>{{ dept.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div id="positionCondition" {% if object.condition_type != 'position_level' %}style="display: none;"{% endif %}>
                        <select name="condition_value" id="id_position_condition"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">请选择职位层级</option>
                            <option value="1" {% if object.condition_value == '1' %}selected{% endif %}>1级 - 基层员工</option>
                            <option value="2" {% if object.condition_value == '2' %}selected{% endif %}>2级 - 基层员工</option>
                            <option value="3" {% if object.condition_value == '3' %}selected{% endif %}>3级 - 基层员工</option>
                            <option value="4" {% if object.condition_value == '4' %}selected{% endif %}>4级 - 基层员工</option>
                            <option value="5" {% if object.condition_value == '5' %}selected{% endif %}>5级 - 副主管</option>
                            <option value="6" {% if object.condition_value == '6' %}selected{% endif %}>6级 - 正主管</option>
                            <option value="7" {% if object.condition_value == '7' %}selected{% endif %}>7级 - 副经理</option>
                            <option value="8" {% if object.condition_value == '8' %}selected{% endif %}>8级 - 正经理</option>
                            <option value="9" {% if object.condition_value == '9' %}selected{% endif %}>9级 - 领导班子</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 关系类型 -->
            <div class="mt-6" id="relationTypeDiv" {% if object.condition_type != 'relation_type' %}style="display: none;"{% endif %}>
                <label for="id_relation_type" class="block text-sm font-medium text-gray-700 mb-2">
                    评价关系类型
                </label>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <label class="relative flex cursor-pointer rounded-lg border border-gray-200 p-4 hover:bg-gray-50 focus:outline-none">
                        <input type="radio" name="relation_type" value="subordinate_to_superior"
                               class="sr-only peer" {% if object.relation_type == 'subordinate_to_superior' %}checked{% endif %}>
                        <div class="flex flex-1 flex-col">
                            <div class="flex items-center justify-between">
                                <span class="block text-sm font-medium text-gray-900">下级评上级</span>
                                <i data-lucide="arrow-up" class="w-4 h-4 text-gray-400"></i>
                            </div>
                            <span class="mt-1 block text-xs text-gray-500">基层员工评价管理人员</span>
                        </div>
                        <div class="absolute -inset-px rounded-lg border-2 pointer-events-none peer-checked:border-blue-500"></div>
                    </label>

                    <label class="relative flex cursor-pointer rounded-lg border border-gray-200 p-4 hover:bg-gray-50 focus:outline-none">
                        <input type="radio" name="relation_type" value="superior_to_subordinate"
                               class="sr-only peer" {% if object.relation_type == 'superior_to_subordinate' %}checked{% endif %}>
                        <div class="flex flex-1 flex-col">
                            <div class="flex items-center justify-between">
                                <span class="block text-sm font-medium text-gray-900">上级评下级</span>
                                <i data-lucide="arrow-down" class="w-4 h-4 text-gray-400"></i>
                            </div>
                            <span class="mt-1 block text-xs text-gray-500">管理人员评价下属员工</span>
                        </div>
                        <div class="absolute -inset-px rounded-lg border-2 pointer-events-none peer-checked:border-blue-500"></div>
                    </label>

                    <label class="relative flex cursor-pointer rounded-lg border border-gray-200 p-4 hover:bg-gray-50 focus:outline-none">
                        <input type="radio" name="relation_type" value="peer_to_peer"
                               class="sr-only peer" {% if object.relation_type == 'peer_to_peer' %}checked{% endif %}>
                        <div class="flex flex-1 flex-col">
                            <div class="flex items-center justify-between">
                                <span class="block text-sm font-medium text-gray-900">同级互评</span>
                                <i data-lucide="arrow-right" class="w-4 h-4 text-gray-400"></i>
                            </div>
                            <span class="mt-1 block text-xs text-gray-500">同职位层级员工互相评价</span>
                        </div>
                        <div class="absolute -inset-px rounded-lg border-2 pointer-events-none peer-checked:border-blue-500"></div>
                    </label>

                    <label class="relative flex cursor-pointer rounded-lg border border-gray-200 p-4 hover:bg-gray-50 focus:outline-none">
                        <input type="radio" name="relation_type" value="cross_department"
                               class="sr-only peer" {% if object.relation_type == 'cross_department' %}checked{% endif %}>
                        <div class="flex flex-1 flex-col">
                            <div class="flex items-center justify-between">
                                <span class="block text-sm font-medium text-gray-900">跨部门评价</span>
                                <i data-lucide="shuffle" class="w-4 h-4 text-gray-400"></i>
                            </div>
                            <span class="mt-1 block text-xs text-gray-500">不同部门员工之间的评价</span>
                        </div>
                        <div class="absolute -inset-px rounded-lg border-2 pointer-events-none peer-checked:border-blue-500"></div>
                    </label>
                </div>
            </div>
        </div>
    </div>

    <!-- 权重设置 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="target" class="w-5 h-5 mr-2 text-gray-500"></i>
                权重设置
            </h3>
            <p class="mt-1 text-sm text-gray-500">修改权重系数和启用状态</p>
        </div>
        <div class="px-6 py-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 权重系数 -->
                <div>
                    <label for="id_weight_factor" class="block text-sm font-medium text-gray-700 mb-2">
                        权重系数 <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <input type="number" name="weight_factor" id="id_weight_factor" 
                               min="0.1" max="5.0" step="0.1" value="{{ object.weight_factor }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <span class="text-gray-500 text-sm">x</span>
                        </div>
                    </div>
                    <p class="mt-1 text-xs text-gray-500">权重系数范围：0.1 - 5.0，默认为1.0</p>
                    
                    <!-- 权重滑块 -->
                    <div class="mt-4">
                        <input type="range" id="weightSlider" min="0.1" max="5.0" step="0.1" value="{{ object.weight_factor }}"
                               class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                        <div class="flex justify-between text-xs text-gray-500 mt-1">
                            <span>0.1x</span>
                            <span id="currentWeight">{{ object.weight_factor }}x</span>
                            <span>5.0x</span>
                        </div>
                    </div>
                </div>

                <!-- 启用状态 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-4">规则状态</label>
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <input type="checkbox" name="is_active" id="id_is_active" {% if object.is_active %}checked{% endif %}
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="id_is_active" class="ml-2 block text-sm text-gray-700">
                                启用规则
                            </label>
                            <div class="ml-2 group relative">
                                <i data-lucide="help-circle" class="w-4 h-4 text-gray-400 cursor-help"></i>
                                <div class="hidden group-hover:block absolute z-10 w-64 p-2 bg-gray-800 text-white text-xs rounded shadow-lg -top-2 left-6">
                                    启用后的规则会在智能分配时生效
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 权重说明 -->
                    <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                        <h4 class="text-sm font-medium text-blue-900 mb-2">权重系数说明</h4>
                        <ul class="text-xs text-blue-800 space-y-1">
                            <li>• < 1.0：降低该条件下的评分权重</li>
                            <li>• = 1.0：保持默认权重，不做调整</li>
                            <li>• > 1.0：提高该条件下的评分权重</li>
                            <li>• 常用值：下级评上级(0.8)、上级评下级(1.2)、同级互评(1.0)</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 修改历史 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="clock" class="w-5 h-5 mr-2 text-gray-500"></i>
                修改历史
            </h3>
        </div>
        <div class="px-6 py-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
                <div>
                    <div class="flex items-center space-x-2 mb-2">
                        <i data-lucide="user-plus" class="w-4 h-4"></i>
                        <span class="font-medium">创建信息</span>
                    </div>
                    <p>创建时间：{{ object.created_at|date:"Y年m月d日 H:i" }}</p>
                    <p>创建人员：{{ object.created_by|default:"未知" }}</p>
                </div>
                <div>
                    <div class="flex items-center space-x-2 mb-2">
                        <i data-lucide="edit" class="w-4 h-4"></i>
                        <span class="font-medium">更新信息</span>
                    </div>
                    <p>更新时间：{{ object.updated_at|date:"Y年m月d日 H:i" }}</p>
                    <p>更新人员：{{ object.updated_by|default:"未知" }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 提交按钮 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-600">
                    <i data-lucide="info" class="w-4 h-4 inline mr-1"></i>
                    修改后的规则将立即生效
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{% url 'evaluations:admin:rule_list' %}" 
                       class="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
                        取消
                    </a>
                    <button type="submit" id="submitBtn"
                            class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
                        <i data-lucide="save" class="w-4 h-4"></i>
                        <span>保存修改</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
    // 权重滑块同步
    const weightInput = document.getElementById('id_weight_factor');
    const weightSlider = document.getElementById('weightSlider');
    const currentWeight = document.getElementById('currentWeight');

    weightInput.addEventListener('input', function() {
        weightSlider.value = this.value;
        currentWeight.textContent = this.value + 'x';
    });

    weightSlider.addEventListener('input', function() {
        weightInput.value = this.value;
        currentWeight.textContent = this.value + 'x';
    });

    // 条件类型变化
    document.getElementById('id_condition_type').addEventListener('change', function() {
        const conditionType = this.value;
        const conditionValueDiv = document.getElementById('conditionValueDiv');
        const departmentCondition = document.getElementById('departmentCondition');
        const positionCondition = document.getElementById('positionCondition');
        const relationTypeDiv = document.getElementById('relationTypeDiv');

        // 隐藏所有条件值选项
        conditionValueDiv.style.display = 'none';
        departmentCondition.style.display = 'none';
        positionCondition.style.display = 'none';
        relationTypeDiv.style.display = 'none';

        // 清除所有输入值
        document.getElementById('id_department_condition').value = '';
        document.getElementById('id_position_condition').value = '';
        document.querySelectorAll('input[name="relation_type"]').forEach(radio => {
            radio.checked = false;
        });

        if (conditionType === 'department') {
            conditionValueDiv.style.display = 'block';
            departmentCondition.style.display = 'block';
        } else if (conditionType === 'position_level') {
            conditionValueDiv.style.display = 'block';
            positionCondition.style.display = 'block';
        } else if (conditionType === 'relation_type') {
            relationTypeDiv.style.display = 'block';
        }
    });

    // 表单提交处理
    document.getElementById('ruleForm').addEventListener('submit', function(e) {
        const conditionType = document.getElementById('id_condition_type').value;
        
        if (!conditionType) {
            e.preventDefault();
            showNotification('请选择条件类型', 'error');
            return;
        }

        // 显示加载状态
        const submitBtn = document.getElementById('submitBtn');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i data-lucide="loader-2" class="w-4 h-4 mr-2 animate-spin"></i>保存中...';
    });
</script>
{% endblock %}