{% extends "admin/base_admin.html" %}

{% block page_title %}人才九宫格矩阵 - {{ batch.name }}{% endblock %}  
{% block page_description %}基于绩效和潜力的人才九宫格分布可视化{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <button onclick="refreshMatrix()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
        <i data-lucide="refresh-cw" class="w-4 h-4"></i>
        <span>刷新数据</span>
    </button>
    <button onclick="exportMatrix()" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center space-x-2">
        <i data-lucide="download" class="w-4 h-4"></i>
        <span>导出矩阵</span>
    </button>
    <a href="{% url 'reports:admin:talent_assess' batch.id %}" class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 flex items-center space-x-2">
        <i data-lucide="edit" class="w-4 h-4"></i>
        <span>编辑评估</span>
    </a>
    <a href="{% url 'reports:admin:talent_list' %}" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center space-x-2">
        <i data-lucide="arrow-left" class="w-4 h-4"></i>
        <span>返回列表</span>
    </a>
</div>
{% endblock %}

{% block admin_content %}
<div class="max-w-7xl mx-auto space-y-6">
    <!-- 批次信息头部 -->
    <div class="bg-white rounded-lg shadow">
        <div class="bg-gradient-to-r from-purple-500 to-purple-600 px-6 py-4">
            <div class="flex items-center justify-between text-white">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <i data-lucide="grid-3x3" class="w-6 h-6"></i>
                    </div>
                    <div class="ml-4">  
                        <h2 class="text-xl font-bold">{{ batch.name }} - 人才九宫格</h2>
                        <p class="text-purple-100">基于绩效与潜力的人才分布矩阵</p>
                    </div>
                </div>
                <div class="text-right">
                    <div class="text-2xl font-bold" id="totalTalentCount">{{ matrix_data.total_count|default:0 }}</div>
                    <div class="text-sm text-purple-100">总人才数</div>
                </div>
            </div>
        </div>
        
        <!-- 矩阵统计概览 -->
        <div class="p-6">
            <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
                <div class="bg-green-50 rounded-lg p-4 text-center">
                    <div class="text-2xl font-bold text-green-600" id="superStarCount">0</div>
                    <div class="text-sm text-green-700">超级明星</div>
                    <div class="text-xs text-green-500">高绩效/高潜力</div>
                </div>
                <div class="bg-cyan-50 rounded-lg p-4 text-center">
                    <div class="text-2xl font-bold text-cyan-600" id="potentialStarCount">0</div>
                    <div class="text-sm text-cyan-700">潜力明星</div>
                    <div class="text-xs text-cyan-500">中绩效/高潜力</div>
                </div>
                <div class="bg-blue-50 rounded-lg p-4 text-center">
                    <div class="text-2xl font-bold text-blue-600" id="coreEmployeeCount">0</div>
                    <div class="text-sm text-blue-700">核心员工</div>
                    <div class="text-xs text-blue-500">中绩效/中潜力</div>
                </div>
                <div class="bg-yellow-50 rounded-lg p-4 text-center">
                    <div class="text-2xl font-bold text-yellow-600" id="performanceStarCount">0</div>
                    <div class="text-sm text-yellow-700">绩效明星</div>
                    <div class="text-xs text-yellow-500">高绩效/低潜力</div>
                </div>
                <div class="bg-red-50 rounded-lg p-4 text-center">
                    <div class="text-2xl font-bold text-red-600" id="problemEmployeeCount">0</div>
                    <div class="text-sm text-red-700">需关注员工</div>
                    <div class="text-xs text-red-500">低绩效/低潜力</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 九宫格矩阵主体 -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i data-lucide="layout-grid" class="w-5 h-5 mr-2 text-gray-500"></i>
                    人才九宫格矩阵
                </h3>
                <div class="flex items-center space-x-3">
                    <div class="text-sm text-gray-500">
                        <span class="mr-4">X轴: 绩效表现</span>
                        <span>Y轴: 发展潜力</span>
                    </div>
                    <select id="matrix-view-select" class="text-sm border border-gray-300 rounded px-2 py-1">
                        <option value="scatter">散点图视图</option>
                        <option value="grid">九宫格视图</option>
                    </select>
                    <button id="refresh-matrix-chart" class="text-sm text-blue-600 hover:text-blue-800 transition-colors">
                        <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                    </button>
                    <button onclick="toggleDetailView()" class="text-sm text-blue-600 hover:text-blue-800">
                        <span id="detailToggleText">显示详情</span>
                    </button>
                </div>
            </div>
        </div>
        
        <div class="p-8">
            <!-- ECharts散点图容器 -->
            <div id="talent-scatter-view" class="hidden">
                <div id="talent-matrix-scatter-chart" style="height: 600px;"></div>
            </div>
            
            <!-- 九宫格视图 -->
            <div id="talent-grid-view">
                <!-- 坐标轴标签 -->
                <div class="relative">
                    <!-- Y轴标签 (潜力) -->
                    <div class="absolute -left-16 top-0 h-full flex flex-col justify-between text-sm text-gray-600">
                        <div class="transform -rotate-90 origin-center">高潜力</div>
                        <div class="transform -rotate-90 origin-center">中潜力</div>
                        <div class="transform -rotate-90 origin-center">低潜力</div>
                    </div>
                    
                    <!-- 九宫格主体 -->
                    <div class="grid grid-cols-3 gap-4 max-w-5xl mx-auto">
                    <!-- 第一行：高潜力 -->
                    <div class="matrix-cell bg-indigo-50 border-2 border-indigo-200 rounded-lg p-6 min-h-48 transition-all hover:shadow-lg" 
                         data-category="rising_star" onclick="openCategoryDetail('rising_star')">
                        <div class="text-center mb-4">
                            <div class="text-lg font-bold text-indigo-700">新星</div>
                            <div class="text-sm text-indigo-600">低绩效 / 高潜力</div>
                            <div class="text-2xl font-bold text-indigo-800 mt-2" id="risingStarCount">0</div>
                        </div>
                        <div class="space-y-2 max-h-32 overflow-y-auto" id="risingStarList">
                            <!-- 动态填充员工卡片 -->
                        </div>
                    </div>
                    
                    <div class="matrix-cell bg-cyan-50 border-2 border-cyan-200 rounded-lg p-6 min-h-48 transition-all hover:shadow-lg" 
                         data-category="potential_star" onclick="openCategoryDetail('potential_star')">
                        <div class="text-center mb-4">
                            <div class="text-lg font-bold text-cyan-700">潜力明星</div>
                            <div class="text-sm text-cyan-600">中绩效 / 高潜力</div>
                            <div class="text-2xl font-bold text-cyan-800 mt-2" id="potentialStarCountMatrix">0</div>
                        </div>
                        <div class="space-y-2 max-h-32 overflow-y-auto" id="potentialStarList">
                            <!-- 动态填充员工卡片 -->
                        </div>
                    </div>
                    
                    <div class="matrix-cell bg-green-50 border-2 border-green-200 rounded-lg p-6 min-h-48 transition-all hover:shadow-lg" 
                         data-category="super_star" onclick="openCategoryDetail('super_star')">
                        <div class="text-center mb-4">
                            <div class="text-lg font-bold text-green-700">超级明星</div>
                            <div class="text-sm text-green-600">高绩效 / 高潜力</div>
                            <div class="text-2xl font-bold text-green-800 mt-2" id="superStarCountMatrix">0</div>
                        </div>
                        <div class="space-y-2 max-h-32 overflow-y-auto" id="superStarList">
                            <!-- 动态填充员工卡片 -->
                        </div>
                    </div>
                    
                    <!-- 第二行：中潜力 -->
                    <div class="matrix-cell bg-gray-50 border-2 border-gray-200 rounded-lg p-6 min-h-48 transition-all hover:shadow-lg" 
                         data-category="potential_employee" onclick="openCategoryDetail('potential_employee')">
                        <div class="text-center mb-4">
                            <div class="text-lg font-bold text-gray-700">潜力员工</div>
                            <div class="text-sm text-gray-600">低绩效 / 中潜力</div>
                            <div class="text-2xl font-bold text-gray-800 mt-2" id="potentialEmployeeCount">0</div>
                        </div>
                        <div class="space-y-2 max-h-32 overflow-y-auto" id="potentialEmployeeList">
                            <!-- 动态填充员工卡片 -->
                        </div>
                    </div>
                    
                    <div class="matrix-cell bg-blue-50 border-2 border-blue-200 rounded-lg p-6 min-h-48 transition-all hover:shadow-lg" 
                         data-category="core_employee" onclick="openCategoryDetail('core_employee')">
                        <div class="text-center mb-4">
                            <div class="text-lg font-bold text-blue-700">核心员工</div>
                            <div class="text-sm text-blue-600">中绩效 / 中潜力</div>
                            <div class="text-2xl font-bold text-blue-800 mt-2" id="coreEmployeeCountMatrix">0</div>
                        </div>
                        <div class="space-y-2 max-h-32 overflow-y-auto" id="coreEmployeeList">
                            <!-- 动态填充员工卡片 -->
                        </div>
                    </div>
                    
                    <div class="matrix-cell bg-yellow-50 border-2 border-yellow-200 rounded-lg p-6 min-h-48 transition-all hover:shadow-lg" 
                         data-category="performance_star" onclick="openCategoryDetail('performance_star')">
                        <div class="text-center mb-4">
                            <div class="text-lg font-bold text-yellow-700">绩效明星</div>
                            <div class="text-sm text-yellow-600">高绩效 / 中潜力</div>
                            <div class="text-2xl font-bold text-yellow-800 mt-2" id="performanceStarCountMatrix">0</div>
                        </div>
                        <div class="space-y-2 max-h-32 overflow-y-auto" id="performanceStarList">
                            <!-- 动态填充员工卡片 -->
                        </div>
                    </div>
                    
                    <!-- 第三行：低潜力 -->
                    <div class="matrix-cell bg-red-50 border-2 border-red-200 rounded-lg p-6 min-h-48 transition-all hover:shadow-lg" 
                         data-category="problem_employee" onclick="openCategoryDetail('problem_employee')">
                        <div class="text-center mb-4">
                            <div class="text-lg font-bold text-red-700">问题员工</div>
                            <div class="text-sm text-red-600">低绩效 / 低潜力</div>
                            <div class="text-2xl font-bold text-red-800 mt-2" id="problemEmployeeCountMatrix">0</div>
                        </div>
                        <div class="space-y-2 max-h-32 overflow-y-auto" id="problemEmployeeList">
                            <!-- 动态填充员工卡片 -->
                        </div>
                    </div>
                    
                    <div class="matrix-cell bg-orange-50 border-2 border-orange-200 rounded-lg p-6 min-h-48 transition-all hover:shadow-lg" 
                         data-category="watch_list" onclick="openCategoryDetail('watch_list')">
                        <div class="text-center mb-4">
                            <div class="text-lg font-bold text-orange-700">待观察</div>
                            <div class="text-sm text-orange-600">中绩效 / 低潜力</div>
                            <div class="text-2xl font-bold text-orange-800 mt-2" id="watchListCount">0</div>
                        </div>
                        <div class="space-y-2 max-h-32 overflow-y-auto" id="watchListList">
                            <!-- 动态填充员工卡片 -->
                        </div>
                    </div>
                    
                    <div class="matrix-cell bg-pink-50 border-2 border-pink-200 rounded-lg p-6 min-h-48 transition-all hover:shadow-lg" 
                         data-category="undefined" onclick="openCategoryDetail('undefined')">
                        <div class="text-center mb-4">
                            <div class="text-lg font-bold text-pink-700">待定义</div>
                            <div class="text-sm text-pink-600">高绩效 / 低潜力</div>
                            <div class="text-2xl font-bold text-pink-800 mt-2" id="undefinedCount">0</div>
                        </div>
                        <div class="space-y-2 max-h-32 overflow-y-auto" id="undefinedList">
                            <!-- 动态填充员工卡片 -->
                        </div>
                    </div>
                </div>
                    
                    <!-- X轴标签 (绩效) -->
                    <div class="flex justify-between mt-4 text-sm text-gray-600">
                        <div class="w-1/3 text-center">低绩效</div>
                        <div class="w-1/3 text-center">中绩效</div>
                        <div class="w-1/3 text-center">高绩效</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 分析洞察 -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="lightbulb" class="w-5 h-5 mr-2 text-gray-500"></i>
                分析洞察与建议
            </h3>
        </div>
        
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- 人才结构分析 -->
                <div class="bg-blue-50 rounded-lg p-4">
                    <h4 class="text-md font-medium text-blue-900 mb-3">人才结构分析</h4>
                    <div id="structureAnalysis" class="text-sm text-blue-800 space-y-2">
                        <!-- 动态生成分析内容 -->
                    </div>
                </div>
                
                <!-- 发展建议 -->
                <div class="bg-green-50 rounded-lg p-4">
                    <h4 class="text-md font-medium text-green-900 mb-3">发展建议</h4>
                    <div id="developmentSuggestions" class="text-sm text-green-800 space-y-2">
                        <!-- 动态生成建议内容 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 分类详情模态框 -->
<div id="categoryModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900" id="modalTitle">分类详情</h3>
                <button onclick="closeCategoryModal()" class="text-gray-400 hover:text-gray-600">
                    <i data-lucide="x" class="w-5 h-5"></i>
                </button>
            </div>
            
            <div id="modalContent" class="max-h-96 overflow-y-auto">
                <!-- 动态填充分类详情 -->
            </div>
            
            <div class="flex justify-end mt-6 space-x-3">
                <button onclick="closeCategoryModal()" 
                        class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                    关闭
                </button>
                <button onclick="exportCategory()" 
                        class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                    导出该分类
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
let matrixData = {};
let currentCategory = '';
let detailViewEnabled = false;
let matrixChart = null;
let currentView = 'scatter'; // 默认散点图视图

// 人才九宫格图表管理类
class TalentMatrixCharts {
    constructor(batchId) {
        this.batchId = batchId;
        this.apiBaseUrl = '/reports/admin/api/charts/';
    }

    async init() {
        await this.loadTalentMatrix();
        this.bindEvents();
    }

    async loadTalentMatrix() {
        const url = this.apiBaseUrl + 'talent-matrix/';
        
        await chartManager.loadDataAndCreateChart(
            url,
            'talent-matrix-scatter-chart',
            (containerId, data) => {
                if (data.talent_data && data.talent_data.length > 0) {
                    // 将人才数据转换为散点图格式
                    const scatterData = data.talent_data.map(talent => ({
                        name: talent.name,
                        value: [talent.performance_score || 0, talent.potential_score || 0],
                        department: talent.department,
                        position: talent.position,
                        category: talent.talent_category
                    }));

                    return chartManager.createScatterChart(containerId, scatterData, {
                        title: '',
                        showGrid: true,
                        symbolSize: 8
                    });
                }
                return null;
            },
            { batch_id: this.batchId }
        );
    }

    bindEvents() {
        // 视图切换
        document.getElementById('matrix-view-select')?.addEventListener('change', (e) => {
            this.switchView(e.target.value);
        });

        // 刷新按钮
        document.getElementById('refresh-matrix-chart')?.addEventListener('click', () => {
            if (currentView === 'scatter') {
                this.loadTalentMatrix();
            } else {
                loadMatrixData(); // 刷新九宫格数据
            }
        });
    }

    switchView(view) {
        currentView = view;
        const scatterView = document.getElementById('talent-scatter-view');
        const gridView = document.getElementById('talent-grid-view');

        if (view === 'scatter') {
            scatterView.classList.remove('hidden');
            gridView.classList.add('hidden');
            // 确保图表尺寸正确
            setTimeout(() => {
                chartManager.refreshAllCharts();
            }, 100);
        } else {
            scatterView.classList.add('hidden');
            gridView.classList.remove('hidden');
        }
    }

    refresh() {
        if (currentView === 'scatter') {
            this.loadTalentMatrix();
        }
    }
}

document.addEventListener('DOMContentLoaded', function() {
    loadMatrixData();
    
    // 初始化图表
    setTimeout(async () => {
        const batchId = {{ batch.id }};
        const talentCharts = new TalentMatrixCharts(batchId);
        await talentCharts.init();
        
        // 保存到全局变量
        window.talentMatrixCharts = talentCharts;
        
        // 默认显示散点图
        talentCharts.switchView('scatter');
    }, 300);
});

// 加载矩阵数据
function loadMatrixData() {
    fetch(`/reports/admin/talent/matrix-data/{{ batch.id }}/`)
        .then(response => response.json())
        .then(data => {
            matrixData = data;
            updateMatrixDisplay();
            generateAnalysis();
        })
        .catch(error => {
            console.error('Error loading matrix data:', error);
            UI.showMessage('加载矩阵数据失败', 'error');
        });
}

// 更新矩阵显示
function updateMatrixDisplay() {
    // 更新统计数据
    updateStatistics();
    
    // 更新九宫格内容
    updateMatrixCells();
}

// 更新统计数据
function updateStatistics() {
    const stats = matrixData.statistics || {};
    
    document.getElementById('superStarCount').textContent = stats.super_star || 0;
    document.getElementById('potentialStarCount').textContent = stats.potential_star || 0;
    document.getElementById('coreEmployeeCount').textContent = stats.core_employee || 0;
    document.getElementById('performanceStarCount').textContent = stats.performance_star || 0;
    document.getElementById('problemEmployeeCount').textContent = stats.problem_employee || 0;
    
    const totalCount = Object.values(stats).reduce((sum, count) => sum + count, 0);
    document.getElementById('totalTalentCount').textContent = totalCount;
}

// 更新矩阵单元格
function updateMatrixCells() {
    const employees = matrixData.employees || {};
    
    // 定义分类映射
    const categories = {
        'super_star': 'superStarList',
        'potential_star': 'potentialStarList', 
        'core_employee': 'coreEmployeeList',
        'performance_star': 'performanceStarList',
        'rising_star': 'risingStarList',
        'potential_employee': 'potentialEmployeeList',
        'problem_employee': 'problemEmployeeList',
        'watch_list': 'watchListList',
        'undefined': 'undefinedList'
    };
    
    // 更新每个分类的员工列表
    Object.keys(categories).forEach(category => {
        const listElement = document.getElementById(categories[category]);
        const countElement = document.getElementById(category + (category.includes('Count') ? '' : 'CountMatrix'));
        
        const categoryEmployees = employees[category] || [];
        
        // 更新计数
        if (countElement) {
            countElement.textContent = categoryEmployees.length;
        }
        
        // 更新员工列表
        if (listElement) {
            listElement.innerHTML = '';
            
            categoryEmployees.slice(0, detailViewEnabled ? 10 : 3).forEach(employee => {
                const employeeCard = createEmployeeCard(employee);
                listElement.appendChild(employeeCard);
            });
            
            // 如果有更多员工，显示省略号
            if (categoryEmployees.length > (detailViewEnabled ? 10 : 3)) {
                const moreElement = document.createElement('div');
                moreElement.className = 'text-xs text-gray-500 text-center py-1';
                moreElement.textContent = `+${categoryEmployees.length - (detailViewEnabled ? 10 : 3)} 更多...`;
                listElement.appendChild(moreElement);
            }
        }
    });
}

// 创建员工卡片
function createEmployeeCard(employee) {
    const card = document.createElement('div');
    card.className = 'bg-white rounded p-2 border border-gray-200 hover:shadow-sm transition-shadow cursor-pointer';
    card.onclick = () => showEmployeeDetail(employee);
    
    card.innerHTML = `
        <div class="flex items-center space-x-2">
            <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                <span class="text-xs font-medium text-blue-600">${employee.name.charAt(0)}</span>
            </div>
            <div class="flex-1 min-w-0">
                <div class="text-xs font-medium text-gray-900 truncate">${employee.name}</div>
                <div class="text-xs text-gray-500 truncate">${employee.department || '未设置部门'}</div>
            </div>
            ${detailViewEnabled ? `
            <div class="text-xs text-gray-400">
                <div>绩效: ${employee.performance_score || '-'}</div>
                <div>潜力: ${employee.potential_score || '-'}</div>
            </div>
            ` : ''}
        </div>
    `;
    
    return card;
}

// 切换详情视图
function toggleDetailView() {
    detailViewEnabled = !detailViewEnabled;
    document.getElementById('detailToggleText').textContent = detailViewEnabled ? '隐藏详情' : '显示详情';
    updateMatrixCells();
}

// 打开分类详情
function openCategoryDetail(category) {
    currentCategory = category;
    const employees = matrixData.employees[category] || [];
    
    // 设置模态框标题
    const categoryNames = {
        'super_star': '超级明星',
        'potential_star': '潜力明星',
        'core_employee': '核心员工', 
        'performance_star': '绩效明星',
        'rising_star': '新星',
        'potential_employee': '潜力员工',
        'problem_employee': '问题员工',
        'watch_list': '待观察',
        'undefined': '待定义'
    };
    
    document.getElementById('modalTitle').textContent = `${categoryNames[category]} (${employees.length}人)`;
    
    // 生成详情内容
    const modalContent = document.getElementById('modalContent');
    modalContent.innerHTML = '';
    
    if (employees.length === 0) {
        modalContent.innerHTML = `
            <div class="text-center py-8 text-gray-500">
                <i data-lucide="users" class="w-12 h-12 mx-auto mb-4 text-gray-300"></i>
                <p>该分类暂无员工</p>
            </div>
        `;
    } else {
        const table = document.createElement('table');
        table.className = 'min-w-full divide-y divide-gray-200';
        table.innerHTML = `
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">员工</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">部门</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">绩效</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">潜力</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">建议</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                ${employees.map(emp => `
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <span class="text-sm font-medium text-blue-600">${emp.name.charAt(0)}</span>
                                </div>
                                <div class="ml-3">
                                    <div class="text-sm font-medium text-gray-900">${emp.name}</div>
                                    <div class="text-sm text-gray-500">${emp.employee_no || ''}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            ${emp.department || '未设置'}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            ${emp.performance_score || '-'}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            ${emp.potential_score || '-'}
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                            ${emp.development_suggestions || '暂无建议'}
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        `;
        modalContent.appendChild(table);
    }
    
    document.getElementById('categoryModal').classList.remove('hidden');
}

// 关闭分类详情模态框
function closeCategoryModal() {
    document.getElementById('categoryModal').classList.add('hidden');
}

// 显示员工详情
function showEmployeeDetail(employee) {
    const details = `
        员工姓名: ${employee.name}
        员工编号: ${employee.employee_no || '未设置'}
        所属部门: ${employee.department || '未设置'}
        职位: ${employee.position || '未设置'}
        绩效得分: ${employee.performance_score || '未评估'}
        潜力得分: ${employee.potential_score || '未评估'}
        人才分类: ${employee.talent_category || '未分类'}
        发展建议: ${employee.development_suggestions || '暂无建议'}
    `.replace(/\n/g, '<br>');
    
    UI.showMessage(details, 'info');
}

// 生成分析内容
function generateAnalysis() {
    const stats = matrixData.statistics || {};
    const total = Object.values(stats).reduce((sum, count) => sum + count, 0);
    
    if (total === 0) {
        document.getElementById('structureAnalysis').innerHTML = '<p>暂无数据进行分析</p>';
        document.getElementById('developmentSuggestions').innerHTML = '<p>暂无建议</p>';
        return;
    }
    
    // 人才结构分析
    const highPotential = (stats.super_star || 0) + (stats.potential_star || 0) + (stats.rising_star || 0);
    const highPerformance = (stats.super_star || 0) + (stats.performance_star || 0);
    const needAttention = (stats.problem_employee || 0) + (stats.watch_list || 0);
    
    const structureAnalysis = [
        `组织中高潜力人才占比为 ${((highPotential / total) * 100).toFixed(1)}%`,
        `高绩效人才占比为 ${((highPerformance / total) * 100).toFixed(1)}%`,
        `需要重点关注的员工占比为 ${((needAttention / total) * 100).toFixed(1)}%`,
        `核心员工(稳定骨干)占比为 ${(((stats.core_employee || 0) / total) * 100).toFixed(1)}%`
    ];
    
    document.getElementById('structureAnalysis').innerHTML = structureAnalysis.map(text => `<p>• ${text}</p>`).join('');
    
    // 发展建议
    const suggestions = [];
    
    if ((stats.super_star || 0) > 0) {
        suggestions.push('为超级明星提供更高挑战性的岗位和领导机会');
    }
    if ((stats.potential_star || 0) > 0) {
        suggestions.push('重点培养潜力明星，提供专业技能培训');
    }
    if ((stats.problem_employee || 0) > 0) {
        suggestions.push('制定问题员工的改进计划，必要时考虑岗位调整');
    }
    if ((stats.core_employee || 0) > total * 0.5) {
        suggestions.push('组织结构相对稳定，可考虑激发更多员工潜力');
    }
    
    if (suggestions.length === 0) {
        suggestions.push('建议完善人才评估，以便制定更精准的发展策略');
    }
    
    document.getElementById('developmentSuggestions').innerHTML = suggestions.map(text => `<p>• ${text}</p>`).join('');
}

// 刷新矩阵数据
function refreshMatrix() {
    UI.showMessage('正在刷新数据...', 'info');
    loadMatrixData();
}

// 导出矩阵
function exportMatrix() {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `/reports/admin/talent/export-matrix/{{ batch.id }}/`;
    
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = 'csrfmiddlewaretoken';
    csrfToken.value = document.querySelector('[name=csrfmiddlewaretoken]').value;
    form.appendChild(csrfToken);
    
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}

// 导出分类
function exportCategory() {
    if (!currentCategory) return;
    
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `/reports/admin/talent/export-category/{{ batch.id }}/`;
    
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = 'csrfmiddlewaretoken';
    csrfToken.value = document.querySelector('[name=csrfmiddlewaretoken]').value;
    form.appendChild(csrfToken);
    
    const categoryInput = document.createElement('input');
    categoryInput.type = 'hidden';
    categoryInput.name = 'category';
    categoryInput.value = currentCategory;
    form.appendChild(categoryInput);
    
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
    
    closeCategoryModal();
}

// 窗口大小改变时重绘图表
window.addEventListener('resize', function() {
    if (currentView === 'scatter') {
        chartManager.refreshAllCharts();
    }
});
</script>
{% endblock %}