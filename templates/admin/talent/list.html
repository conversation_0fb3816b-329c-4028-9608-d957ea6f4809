{% extends "admin/base_admin.html" %}

{% block page_title %}人才盘点管理{% endblock %}
{% block page_description %}管理员工人才盘点数据，分析人才九宫格分布{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-4">
    <a href="{% url 'reports:admin:talent_assess' 1 %}" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center space-x-2">
        <i data-lucide="user-plus" class="w-4 h-4"></i>
        <span>新建盘点</span>
    </a>
    <button onclick="showMatrixView()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
        <i data-lucide="grid-3x3" class="w-4 h-4"></i>
        <span>九宫格视图</span>
    </button>
</div>
{% endblock %}

{% block admin_content %}
<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
                <i data-lucide="users" class="w-6 h-6 text-blue-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">总人才数</p>
                <p class="text-2xl font-bold text-gray-900">{{ assessments|length|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
                <i data-lucide="star" class="w-6 h-6 text-green-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">高潜人才</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.high_potential|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
                <i data-lucide="trending-up" class="w-6 h-6 text-yellow-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">核心人才</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.core_talent|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-red-100 rounded-lg">
                <i data-lucide="alert-triangle" class="w-6 h-6 text-red-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">待提升</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.need_improvement|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
                <i data-lucide="percent" class="w-6 h-6 text-purple-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">完成率</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.completion_rate|default:0 }}%</p>
            </div>
        </div>
    </div>
</div>

<!-- 人才九宫格概览 -->
<div class="bg-white rounded-lg shadow mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900 flex items-center">
            <i data-lucide="grid-3x3" class="w-5 h-5 mr-2 text-gray-500"></i>
            人才九宫格分布概览
        </h3>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-3 gap-4 max-w-2xl mx-auto">
            <!-- 第一行 -->
            <div class="bg-red-50 border-2 border-red-200 rounded-lg p-4 text-center">
                <div class="text-xs font-medium text-red-600 mb-2">问题员工</div>
                <div class="text-2xl font-bold text-red-700">{{ matrix.problem_employee|default:0 }}</div>
                <div class="text-xs text-red-500">低绩效/低潜力</div>
            </div>
            <div class="bg-yellow-50 border-2 border-yellow-200 rounded-lg p-4 text-center">
                <div class="text-xs font-medium text-yellow-600 mb-2">绩效明星</div>
                <div class="text-2xl font-bold text-yellow-700">{{ matrix.performance_star|default:0 }}</div>
                <div class="text-xs text-yellow-500">高绩效/低潜力</div>
            </div>
            <div class="bg-green-50 border-2 border-green-200 rounded-lg p-4 text-center">
                <div class="text-xs font-medium text-green-600 mb-2">超级明星</div>
                <div class="text-2xl font-bold text-green-700">{{ matrix.super_star|default:0 }}</div>
                <div class="text-xs text-green-500">高绩效/高潜力</div>
            </div>
            
            <!-- 第二行 -->
            <div class="bg-orange-50 border-2 border-orange-200 rounded-lg p-4 text-center">
                <div class="text-xs font-medium text-orange-600 mb-2">待观察</div>
                <div class="text-2xl font-bold text-orange-700">{{ matrix.watch_list|default:0 }}</div>
                <div class="text-xs text-orange-500">中等绩效/低潜力</div>
            </div>
            <div class="bg-blue-50 border-2 border-blue-200 rounded-lg p-4 text-center">
                <div class="text-xs font-medium text-blue-600 mb-2">核心员工</div>
                <div class="text-2xl font-bold text-blue-700">{{ matrix.core_employee|default:0 }}</div>
                <div class="text-xs text-blue-500">中等绩效/中等潜力</div>
            </div>
            <div class="bg-cyan-50 border-2 border-cyan-200 rounded-lg p-4 text-center">
                <div class="text-xs font-medium text-cyan-600 mb-2">潜力明星</div>
                <div class="text-2xl font-bold text-cyan-700">{{ matrix.potential_star|default:0 }}</div>
                <div class="text-xs text-cyan-500">中等绩效/高潜力</div>
            </div>
            
            <!-- 第三行 -->
            <div class="bg-gray-50 border-2 border-gray-200 rounded-lg p-4 text-center">
                <div class="text-xs font-medium text-gray-600 mb-2">潜力员工</div>
                <div class="text-2xl font-bold text-gray-700">{{ matrix.potential_employee|default:0 }}</div>
                <div class="text-xs text-gray-500">低绩效/中等潜力</div>
            </div>
            <div class="bg-indigo-50 border-2 border-indigo-200 rounded-lg p-4 text-center">
                <div class="text-xs font-medium text-indigo-600 mb-2">新星</div>
                <div class="text-2xl font-bold text-indigo-700">{{ matrix.rising_star|default:0 }}</div>
                <div class="text-xs text-indigo-500">低绩效/高潜力</div>
            </div>
            <div class="bg-pink-50 border-2 border-pink-200 rounded-lg p-4 text-center">
                <div class="text-xs font-medium text-pink-600 mb-2">待定义</div>
                <div class="text-2xl font-bold text-pink-700">{{ matrix.undefined|default:0 }}</div>
                <div class="text-xs text-pink-500">待评估</div>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="bg-white rounded-lg shadow mb-6 p-6">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="relative">
            <input type="text" id="searchInput" placeholder="搜索员工姓名或编号..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md">
            <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"></i>
        </div>
        <select id="categoryFilter" class="border border-gray-300 rounded-md px-3 py-2">
            <option value="">所有类别</option>
            <option value="super_star">超级明星</option>
            <option value="performance_star">绩效明星</option>
            <option value="potential_star">潜力明星</option>
            <option value="core_employee">核心员工</option>
            <option value="rising_star">新星</option>
            <option value="potential_employee">潜力员工</option>
            <option value="watch_list">待观察</option>
            <option value="problem_employee">问题员工</option>
            <option value="undefined">待定义</option>
        </select>
        <select id="departmentFilter" class="border border-gray-300 rounded-md px-3 py-2">
            <option value="">所有部门</option>
            {% for dept in departments %}
            <option value="{{ dept.id }}">{{ dept.name }}</option>
            {% endfor %}
        </select>
        <select id="batchFilter" class="border border-gray-300 rounded-md px-3 py-2">
            <option value="">所有批次</option>
            {% for batch in batches %}
            <option value="{{ batch.id }}">{{ batch.name }}</option>
            {% endfor %}
        </select>
    </div>
</div>

<!-- Talent Assessment Table -->
<div class="bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">人才盘点列表</h3>
    </div>
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">员工信息</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属部门</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">绩效得分</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">潜力评估</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">人才类别</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发展建议</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200" id="talentTableBody">
                {% if assessments %}
                    {% for assessment in assessments %}
                    <tr class="talent-row" data-category="{{ assessment.talent_category }}" data-department="{{ assessment.staff.department.id }}" data-batch="{{ assessment.batch.id }}">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                    <span class="text-sm font-medium">{{ assessment.staff.name|first|default:"员" }}</span>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">{{ assessment.staff.name }}</div>
                                    <div class="text-sm text-gray-500">{{ assessment.staff.employee_no }}</div>
                                    <div class="text-sm text-gray-500">{{ assessment.staff.position.name }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ assessment.staff.department.name }}</div>
                            <div class="text-sm text-gray-500">{{ assessment.staff.department.dept_code }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="text-sm font-medium text-gray-900">{{ assessment.performance_score|floatformat:1 }}</div>
                                <div class="ml-2">
                                    {% if assessment.performance_score >= 8.0 %}
                                        <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">高</span>
                                    {% elif assessment.performance_score >= 6.0 %}
                                        <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">中</span>
                                    {% else %}
                                        <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">低</span>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="text-sm font-medium text-gray-900">{{ assessment.potential_score|floatformat:1 }}</div>
                                <div class="ml-2">
                                    {% if assessment.potential_score >= 8.0 %}
                                        <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">高</span>
                                    {% elif assessment.potential_score >= 6.0 %}
                                        <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-indigo-100 text-indigo-800">中</span>
                                    {% else %}
                                        <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">低</span>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if assessment.talent_category == 'super_star' %}
                                <span class="inline-flex items-center px-3 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                                    <i data-lucide="star" class="w-3 h-3 mr-1"></i>超级明星
                                </span>
                            {% elif assessment.talent_category == 'performance_star' %}
                                <span class="inline-flex items-center px-3 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">
                                    <i data-lucide="trending-up" class="w-3 h-3 mr-1"></i>绩效明星
                                </span>
                            {% elif assessment.talent_category == 'potential_star' %}
                                <span class="inline-flex items-center px-3 py-1 text-xs font-medium rounded-full bg-cyan-100 text-cyan-800">
                                    <i data-lucide="zap" class="w-3 h-3 mr-1"></i>潜力明星
                                </span>
                            {% elif assessment.talent_category == 'core_employee' %}
                                <span class="inline-flex items-center px-3 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                                    <i data-lucide="heart" class="w-3 h-3 mr-1"></i>核心员工
                                </span>
                            {% elif assessment.talent_category == 'rising_star' %}
                                <span class="inline-flex items-center px-3 py-1 text-xs font-medium rounded-full bg-indigo-100 text-indigo-800">
                                    <i data-lucide="arrow-up" class="w-3 h-3 mr-1"></i>新星
                                </span>
                            {% elif assessment.talent_category == 'problem_employee' %}
                                <span class="inline-flex items-center px-3 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">
                                    <i data-lucide="alert-triangle" class="w-3 h-3 mr-1"></i>问题员工
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-3 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">
                                    <i data-lucide="help-circle" class="w-3 h-3 mr-1"></i>待定义
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-gray-900 max-w-xs truncate">{{ assessment.development_suggestions|default:"暂无建议"|truncatechars:30 }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                            <a href="{% url 'reports:admin:talent_update' assessment.pk %}" class="text-blue-600 hover:text-blue-900">编辑</a>
                            <button onclick="viewDetails('{{ assessment.pk }}')" class="text-green-600 hover:text-green-900">详情</button>
                            <button onclick="confirmDelete('{{ assessment.pk }}', '{{ assessment.staff.name }}')" class="text-red-600 hover:text-red-900">删除</button>
                        </td>
                    </tr>
                    {% endfor %}
                {% else %}
                    <tr>
                        <td colspan="7" class="px-6 py-12 text-center">
                            <i data-lucide="users" class="mx-auto h-12 w-12 text-gray-400"></i>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">暂无人才盘点数据</h3>
                            <p class="mt-1 text-sm text-gray-500">开始进行第一次人才盘点吧。</p>
                            <div class="mt-6">
                                <a href="{% url 'reports:admin:talent_assess' 1 %}" class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                                    <i data-lucide="user-plus" class="w-4 h-4 mr-2"></i>
                                    新建盘点
                                </a>
                            </div>
                        </td>
                    </tr>
                {% endif %}
            </tbody>
        </table>
    </div>
</div>

<!-- Pagination -->
{% if is_paginated %}
<div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-6 rounded-lg shadow">
    <div class="flex-1 flex justify-between sm:hidden">
        {% if page_obj.has_previous %}
            <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">上一页</a>
        {% endif %}
        {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">下一页</a>
        {% endif %}
    </div>
    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
        <div>
            <p class="text-sm text-gray-700">
                显示第 <span class="font-medium">{{ page_obj.start_index }}</span> 到 <span class="font-medium">{{ page_obj.end_index }}</span> 条，
                共 <span class="font-medium">{{ page_obj.paginator.count }}</span> 条记录
            </p>
        </div>
        <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <i data-lucide="chevron-left" class="w-4 h-4"></i>
                    </a>
                {% endif %}
                
                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">{{ num }}</span>
                    {% else %}
                        <a href="?page={{ num }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">{{ num }}</a>
                    {% endif %}
                {% endfor %}
                
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <i data-lucide="chevron-right" class="w-4 h-4"></i>
                    </a>
                {% endif %}
            </nav>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    // 搜索功能
    document.getElementById('searchInput').addEventListener('input', function() {
        filterTalent();
    });

    // 类别筛选
    document.getElementById('categoryFilter').addEventListener('change', function() {
        filterTalent();
    });

    // 部门筛选
    document.getElementById('departmentFilter').addEventListener('change', function() {
        filterTalent();
    });

    // 批次筛选
    document.getElementById('batchFilter').addEventListener('change', function() {
        filterTalent();
    });

    function filterTalent() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const categoryFilter = document.getElementById('categoryFilter').value;
        const departmentFilter = document.getElementById('departmentFilter').value;
        const batchFilter = document.getElementById('batchFilter').value;
        const talentRows = document.querySelectorAll('.talent-row');
        
        talentRows.forEach(row => {
            const staffName = row.querySelector('.text-sm.font-medium.text-gray-900').textContent.toLowerCase();
            const staffNo = row.querySelectorAll('.text-sm.text-gray-500')[0].textContent.toLowerCase();
            const category = row.getAttribute('data-category');
            const department = row.getAttribute('data-department');
            const batch = row.getAttribute('data-batch');
            
            let showRow = true;
            
            // 搜索筛选
            if (searchTerm && !staffName.includes(searchTerm) && !staffNo.includes(searchTerm)) {
                showRow = false;
            }
            
            // 类别筛选
            if (categoryFilter && category !== categoryFilter) {
                showRow = false;
            }
            
            // 部门筛选
            if (departmentFilter && department !== departmentFilter) {
                showRow = false;
            }
            
            // 批次筛选
            if (batchFilter && batch !== batchFilter) {
                showRow = false;
            }
            
            row.style.display = showRow ? '' : 'none';
        });
    }

    // 显示九宫格视图
    function showMatrixView() {
        // 这里可以跳转到九宫格详细视图页面
        window.location.href = '{% url "reports:admin:talent_matrix" 1 %}';
    }

    // 查看详情
    function viewDetails(assessmentId) {
        // 这里可以打开模态框显示详情或跳转到详情页面
        showNotification('查看详情功能开发中', 'info');
    }

    // 删除确认
    function confirmDelete(assessmentId, staffName) {
        if (confirm(`确定要删除员工 "${staffName}" 的人才盘点数据吗？此操作不可撤销。`)) {
            // 发送删除请求
            fetch(`/reports/admin/talent/${assessmentId}/delete/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => {
                if (response.ok) {
                    showNotification('删除成功', 'success');
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showNotification('删除失败，请重试', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('删除失败，请重试', 'error');
            });
        }
    }

    // 页面加载时自动聚焦搜索框
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.focus();
        }
    });
</script>
{% endblock %}