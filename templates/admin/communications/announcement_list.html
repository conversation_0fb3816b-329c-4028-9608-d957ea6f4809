{% extends "admin/base_admin.html" %}

{% block page_title %}公告管理{% endblock %}
{% block page_description %}查看和管理系统公告{% endblock %}

{% block extra_css %}
<style>
    .announcement-card {
        transition: all 0.2s ease;
    }
    .announcement-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 16px rgba(0,0,0,0.1);
    }
    .priority-high { border-left: 4px solid #dc2626; }
    .priority-medium { border-left: 4px solid #f59e0b; }
    .priority-low { border-left: 4px solid #10b981; }
    .pinned-announcement {
        background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        border: 1px solid #f59e0b;
    }
</style>
{% endblock %}

{% block admin_content %}
<div class="space-y-6">
    <!-- 页面头部 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                    <i data-lucide="megaphone" class="w-6 h-6 mr-2 text-blue-600"></i>
                    公告管理
                </h1>
                <p class="text-gray-600 mt-1">发布和管理系统公告通知</p>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'communications:admin:announcement_create' %}" 
                   class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors">
                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                    发布公告
                </a>
                <button type="button" id="refreshBtn" 
                        class="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-200 transition-colors">
                    <i data-lucide="refresh-cw" class="w-4 h-4 mr-2"></i>
                    刷新
                </button>
            </div>
        </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div class="flex flex-col sm:flex-row gap-4">
            <div class="flex-1">
                <input type="text" id="searchInput" placeholder="搜索公告标题或内容..." 
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            </div>
            <div class="flex space-x-2">
                <select id="priorityFilter" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">所有优先级</option>
                    <option value="HIGH">高优先级</option>
                    <option value="MEDIUM">中优先级</option>
                    <option value="LOW">低优先级</option>
                </select>
                <select id="statusFilter" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">所有状态</option>
                    <option value="published">已发布</option>
                    <option value="draft">草稿</option>
                </select>
            </div>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="megaphone" class="w-4 h-4 text-blue-600"></i>
                    </div>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">总公告数</p>
                    <p class="text-lg font-semibold text-gray-900" id="totalCount">{{ total_count }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="check-circle" class="w-4 h-4 text-green-600"></i>
                    </div>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">已发布</p>
                    <p class="text-lg font-semibold text-gray-900" id="publishedCount">{{ published_count }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="pin" class="w-4 h-4 text-yellow-600"></i>
                    </div>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">置顶公告</p>
                    <p class="text-lg font-semibold text-gray-900" id="pinnedCount">{{ pinned_count }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="alert-circle" class="w-4 h-4 text-red-600"></i>
                    </div>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">高优先级</p>
                    <p class="text-lg font-semibold text-gray-900" id="highPriorityCount">{{ high_priority_count }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 公告列表 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">公告列表</h3>
        </div>
        
        <div class="divide-y divide-gray-200" id="announcementList">
            {% for announcement in announcements %}
            <div class="announcement-card p-6 {% if announcement.is_pinned %}pinned-announcement{% endif %} priority-{{ announcement.priority|lower }}"
                 data-priority="{{ announcement.priority }}" data-status="published">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <div class="flex items-center space-x-2 mb-2">
                            {% if announcement.is_pinned %}
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                <i data-lucide="pin" class="w-3 h-3 mr-1"></i>
                                置顶
                            </span>
                            {% endif %}
                            
                            {% if announcement.priority == 'HIGH' %}
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                高优先级
                            </span>
                            {% elif announcement.priority == 'MEDIUM' %}
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                中优先级
                            </span>
                            {% else %}
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                低优先级
                            </span>
                            {% endif %}
                            
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {% if announcement.announcement_type == 'SYSTEM' %}系统公告
                                {% elif announcement.announcement_type == 'NOTICE' %}通知公告
                                {% elif announcement.announcement_type == 'EVENT' %}活动公告
                                {% else %}其他{% endif %}
                            </span>
                        </div>
                        
                        <h4 class="text-lg font-medium text-gray-900 mb-2 announcement-title">
                            {{ announcement.title }}
                        </h4>
                        
                        <p class="text-gray-600 mb-3 announcement-content">
                            {{ announcement.content|truncatewords:30 }}
                        </p>
                        
                        <div class="flex items-center text-sm text-gray-500 space-x-4">
                            <span class="flex items-center">
                                <i data-lucide="user" class="w-4 h-4 mr-1"></i>
                                {{ announcement.created_by }}
                            </span>
                            <span class="flex items-center">
                                <i data-lucide="calendar" class="w-4 h-4 mr-1"></i>
                                {{ announcement.created_at|date:"Y-m-d H:i" }}
                            </span>
                            {% if announcement.valid_until %}
                            <span class="flex items-center">
                                <i data-lucide="clock" class="w-4 h-4 mr-1"></i>
                                有效期至: {{ announcement.valid_until|date:"Y-m-d" }}
                            </span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="flex space-x-2 ml-4">
                        <a href="{% url 'communications:admin:announcement_detail' announcement.pk %}" 
                           class="p-2 text-gray-400 hover:text-blue-600 transition-colors" title="查看详情">
                            <i data-lucide="eye" class="w-4 h-4"></i>
                        </a>
                        <a href="{% url 'communications:admin:announcement_update' announcement.pk %}" 
                           class="p-2 text-gray-400 hover:text-green-600 transition-colors" title="编辑">
                            <i data-lucide="edit" class="w-4 h-4"></i>
                        </a>
                        <button type="button" onclick="deleteAnnouncement({{ announcement.pk }})" 
                                class="p-2 text-gray-400 hover:text-red-600 transition-colors" title="删除">
                            <i data-lucide="trash-2" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="p-12 text-center">
                <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                    <i data-lucide="megaphone" class="w-8 h-8 text-gray-400"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">暂无公告</h3>
                <p class="text-gray-500 mb-4">还没有发布任何公告</p>
                <a href="{% url 'communications:admin:announcement_create' %}" 
                   class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors">
                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                    发布第一个公告
                </a>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="p-6">
            <div class="flex items-center mb-4">
                <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-4">
                    <i data-lucide="alert-triangle" class="w-5 h-5 text-red-600"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900">确认删除</h3>
            </div>
            <p class="text-gray-600 mb-6">确定要删除这个公告吗？此操作不可撤销。</p>
            <div class="flex justify-end space-x-3">
                <button type="button" onclick="closeDeleteModal()" 
                        class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors">
                    取消
                </button>
                <button type="button" id="confirmDelete" 
                        class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors">
                    确认删除
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 搜索功能
document.getElementById('searchInput').addEventListener('input', function() {
    filterAnnouncements();
});

// 筛选功能
document.getElementById('priorityFilter').addEventListener('change', function() {
    filterAnnouncements();
});

document.getElementById('statusFilter').addEventListener('change', function() {
    filterAnnouncements();
});

// 刷新按钮
document.getElementById('refreshBtn').addEventListener('click', function() {
    location.reload();
});

function filterAnnouncements() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const priority = document.getElementById('priorityFilter').value;
    const status = document.getElementById('statusFilter').value;
    
    const announcements = document.querySelectorAll('.announcement-card');
    let visibleCount = 0;
    
    announcements.forEach(function(announcement) {
        const title = announcement.querySelector('.announcement-title').textContent.toLowerCase();
        const content = announcement.querySelector('.announcement-content').textContent.toLowerCase();
        const announcementPriority = announcement.dataset.priority;
        const announcementStatus = announcement.dataset.status;
        
        const matchesSearch = !searchTerm || title.includes(searchTerm) || content.includes(searchTerm);
        const matchesPriority = !priority || announcementPriority === priority;
        const matchesStatus = !status || announcementStatus === status;
        
        if (matchesSearch && matchesPriority && matchesStatus) {
            announcement.style.display = 'block';
            visibleCount++;
        } else {
            announcement.style.display = 'none';
        }
    });
    
    // 显示/隐藏空状态
    const emptyState = document.querySelector('#announcementList .text-center');
    if (emptyState) {
        emptyState.style.display = visibleCount === 0 ? 'block' : 'none';
    }
}

// 删除公告
function deleteAnnouncement(announcementId) {
    const modal = document.getElementById('deleteModal');
    modal.classList.remove('hidden');
    modal.classList.add('flex');
    
    document.getElementById('confirmDelete').onclick = function() {
        // 这里应该发送AJAX请求删除公告
        fetch(`/communications/admin/announcements/${announcementId}/delete/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('删除失败：' + data.error);
            }
        })
        .catch(error => {
            console.error('删除错误:', error);
            alert('删除失败，请稍后重试');
        });
        
        closeDeleteModal();
    };
}

function closeDeleteModal() {
    const modal = document.getElementById('deleteModal');
    modal.classList.add('hidden');
    modal.classList.remove('flex');
}

// 点击模态框外部关闭
document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDeleteModal();
    }
});
</script>
{% endblock %}