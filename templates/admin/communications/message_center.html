{% extends "admin/base_admin.html" %}

{% block page_title %}消息中心{% endblock %}
{% block page_description %}查看和管理您的消息通知{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <a href="{% url 'communications:message_compose' %}" 
       class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center space-x-2">
        <i data-lucide="plus" class="w-4 h-4"></i>
        <span>写消息</span>
    </a>
    <button onclick="markAllAsRead()" 
            class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center space-x-2">
        <i data-lucide="check-circle" class="w-4 h-4"></i>
        <span>全部已读</span>
    </button>
    <button onclick="refreshMessages()" 
            class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 flex items-center space-x-2">
        <i data-lucide="refresh-cw" class="w-4 h-4"></i>
        <span>刷新</span>
    </button>
</div>
{% endblock %}

{% block admin_content %}
<!-- 统计卡片 -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <!-- 总消息数 -->
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
                <i data-lucide="mail" class="w-6 h-6 text-blue-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">总消息数</p>
                <p class="text-2xl font-bold text-gray-900">{{ total_count|default:0 }}</p>
            </div>
        </div>
    </div>

    <!-- 未读消息 -->
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-red-100 rounded-lg">
                <i data-lucide="mail-open" class="w-6 h-6 text-red-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">未读消息</p>
                <p class="text-2xl font-bold text-gray-900" id="unreadCount">{{ unread_count|default:0 }}</p>
            </div>
        </div>
    </div>

    <!-- 系统通知 -->
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
                <i data-lucide="bell" class="w-6 h-6 text-yellow-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">系统通知</p>
                <p class="text-2xl font-bold text-gray-900">{{ type_stats.SYSTEM|default:0 }}</p>
            </div>
        </div>
    </div>

    <!-- 考评相关 -->
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
                <i data-lucide="clipboard" class="w-6 h-6 text-green-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">考评相关</p>
                <p class="text-2xl font-bold text-gray-900">{{ type_stats.EVALUATION|default:0 }}</p>
            </div>
        </div>
    </div>
</div>

<!-- 筛选器和搜索 -->
<div class="bg-white rounded-lg shadow mb-6 p-6">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="relative">
            <input type="text" id="searchInput" placeholder="搜索消息标题或内容..." 
                   class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"></i>
        </div>
        <select id="typeFilter" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">所有类型</option>
            <option value="SYSTEM">系统通知</option>
            <option value="PERSONAL">个人消息</option>
            <option value="EVALUATION">考评相关</option>
            <option value="ANNOUNCEMENT">公告通知</option>
        </select>
        <select id="readFilter" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">全部消息</option>
            <option value="false">未读消息</option>
            <option value="true">已读消息</option>
        </select>
        <select id="priorityFilter" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">所有优先级</option>
            <option value="HIGH">高优先级</option>
            <option value="MEDIUM">中优先级</option>
            <option value="LOW">低优先级</option>
        </select>
    </div>
</div>

<!-- 消息列表 -->
<div class="bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">消息列表</h3>
            <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-500">共 <span id="totalMessages">{{ total_count|default:0 }}</span> 条消息</span>
            </div>
        </div>
    </div>
    
    <div id="messageList" class="divide-y divide-gray-200">
        {% if message_recipients %}
            {% for recipient in message_recipients %}
            <div class="message-item p-6 hover:bg-gray-50 cursor-pointer {% if not recipient.is_read %}bg-blue-50{% endif %}" 
                 data-message-id="{{ recipient.message.id }}"
                 data-message-type="{{ recipient.message.message_type }}"
                 data-priority="{{ recipient.message.priority }}"
                 data-is-read="{{ recipient.is_read|yesno:'true,false' }}">
                
                <div class="flex items-start space-x-4">
                    <!-- 消息图标 -->
                    <div class="flex-shrink-0">
                        {% if not recipient.is_read %}
                            <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                        {% else %}
                            <div class="w-3 h-3 bg-gray-300 rounded-full"></div>
                        {% endif %}
                    </div>
                    
                    <!-- 消息内容 -->
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <!-- 消息类型图标 -->
                                {% if recipient.message.message_type == 'SYSTEM' %}
                                    <i data-lucide="settings" class="w-4 h-4 text-blue-600"></i>
                                {% elif recipient.message.message_type == 'PERSONAL' %}
                                    <i data-lucide="user" class="w-4 h-4 text-green-600"></i>
                                {% elif recipient.message.message_type == 'EVALUATION' %}
                                    <i data-lucide="clipboard" class="w-4 h-4 text-orange-600"></i>
                                {% else %}
                                    <i data-lucide="megaphone" class="w-4 h-4 text-purple-600"></i>
                                {% endif %}
                                
                                <!-- 优先级标识 -->
                                {% if recipient.message.priority == 'HIGH' %}
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        高优先级
                                    </span>
                                {% elif recipient.message.priority == 'LOW' %}
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        低优先级
                                    </span>
                                {% endif %}
                            </div>
                            
                            <!-- 时间 -->
                            <span class="text-sm text-gray-500">{{ recipient.message.created_at|date:"m-d H:i" }}</span>
                        </div>
                        
                        <!-- 标题 -->
                        <h4 class="text-lg font-medium text-gray-900 mt-1 {% if not recipient.is_read %}font-bold{% endif %}">
                            {{ recipient.message.subject }}
                        </h4>
                        
                        <!-- 发送者 -->
                        <p class="text-sm text-gray-600 mt-1">
                            来自: {{ recipient.message.sender.name|default:"系统" }}
                        </p>
                        
                        <!-- 内容预览 -->
                        <p class="text-sm text-gray-700 mt-2 line-clamp-2">
                            {{ recipient.message.content|truncatechars:120 }}
                        </p>
                        
                        <!-- 操作按钮 -->
                        <div class="flex items-center space-x-4 mt-3">
                            <button onclick="viewMessage({{ recipient.message.id }})" 
                                    class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                查看详情
                            </button>
                            {% if not recipient.is_read %}
                                <button onclick="markAsRead({{ recipient.message.id }})" 
                                        class="text-green-600 hover:text-green-800 text-sm font-medium">
                                    标记已读
                                </button>
                            {% endif %}
                            {% if recipient.is_starred %}
                                <span class="text-yellow-500 text-sm">
                                    <i data-lucide="star" class="w-4 h-4 inline"></i> 已收藏
                                </span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="p-12 text-center">
                <i data-lucide="mail" class="mx-auto h-12 w-12 text-gray-400"></i>
                <h3 class="mt-2 text-sm font-medium text-gray-900">暂无消息</h3>
                <p class="mt-1 text-sm text-gray-500">您还没有收到任何消息。</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- 分页 -->
{% if is_paginated %}
<div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-6 rounded-lg shadow">
    <div class="flex-1 flex justify-between sm:hidden">
        {% if page_obj.has_previous %}
            <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">上一页</a>
        {% endif %}
        {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">下一页</a>
        {% endif %}
    </div>
    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
        <div>
            <p class="text-sm text-gray-700">
                显示第 <span class="font-medium">{{ page_obj.start_index }}</span> 到 <span class="font-medium">{{ page_obj.end_index }}</span> 条，
                共 <span class="font-medium">{{ page_obj.paginator.count }}</span> 条记录
            </p>
        </div>
        <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <i data-lucide="chevron-left" class="w-4 h-4"></i>
                    </a>
                {% endif %}
                
                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">{{ num }}</span>
                    {% else %}
                        <a href="?page={{ num }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">{{ num }}</a>
                    {% endif %}
                {% endfor %}
                
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <i data-lucide="chevron-right" class="w-4 h-4"></i>
                    </a>
                {% endif %}
            </nav>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
let currentPage = 1;
let isLoading = false;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化图标
    if (typeof lucide !== 'undefined' && lucide.createIcons) {
        lucide.createIcons();
    }
    
    // 绑定筛选器事件
    bindFilterEvents();
    
    // 自动轮询新消息
    startMessagePolling();
    
    // 绑定消息点击事件
    bindMessageClickEvents();
});

// 绑定筛选器事件
function bindFilterEvents() {
    const searchInput = document.getElementById('searchInput');
    const typeFilter = document.getElementById('typeFilter');
    const readFilter = document.getElementById('readFilter');
    const priorityFilter = document.getElementById('priorityFilter');
    
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(filterMessages, 500);
    });
    
    typeFilter.addEventListener('change', filterMessages);
    readFilter.addEventListener('change', filterMessages);
    priorityFilter.addEventListener('change', filterMessages);
}

// 筛选消息
function filterMessages() {
    if (isLoading) return;
    isLoading = true;
    
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const typeFilter = document.getElementById('typeFilter').value;
    const readFilter = document.getElementById('readFilter').value;
    const priorityFilter = document.getElementById('priorityFilter').value;
    
    const messageItems = document.querySelectorAll('.message-item');
    let visibleCount = 0;
    
    messageItems.forEach(item => {
        const subject = item.querySelector('h4').textContent.toLowerCase();
        const content = item.querySelector('.line-clamp-2').textContent.toLowerCase();
        const messageType = item.getAttribute('data-message-type');
        const priority = item.getAttribute('data-priority');
        const isRead = item.getAttribute('data-is-read');
        
        let shouldShow = true;
        
        // 搜索筛选
        if (searchTerm && !subject.includes(searchTerm) && !content.includes(searchTerm)) {
            shouldShow = false;
        }
        
        // 类型筛选
        if (typeFilter && messageType !== typeFilter) {
            shouldShow = false;
        }
        
        // 读取状态筛选
        if (readFilter && isRead !== readFilter) {
            shouldShow = false;
        }
        
        // 优先级筛选
        if (priorityFilter && priority !== priorityFilter) {
            shouldShow = false;
        }
        
        item.style.display = shouldShow ? '' : 'none';
        if (shouldShow) visibleCount++;
    });
    
    // 更新消息计数
    document.getElementById('totalMessages').textContent = visibleCount;
    
    isLoading = false;
}

// 绑定消息点击事件
function bindMessageClickEvents() {
    const messageItems = document.querySelectorAll('.message-item');
    messageItems.forEach(item => {
        item.addEventListener('click', function(e) {
            // 避免按钮点击事件冒泡
            if (e.target.tagName === 'BUTTON') return;
            
            const messageId = this.getAttribute('data-message-id');
            viewMessage(messageId);
        });
    });
}

// 查看消息详情
function viewMessage(messageId) {
    window.location.href = `/communications/admin/messages/${messageId}/`;
}

// 标记消息为已读
function markAsRead(messageId) {
    fetch(`/communications/api/messages/${messageId}/read/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            showNotification('消息已标记为已读', 'success');
            // 更新界面
            const messageItem = document.querySelector(`[data-message-id="${messageId}"]`);
            if (messageItem) {
                messageItem.classList.remove('bg-blue-50');
                messageItem.setAttribute('data-is-read', 'true');
                
                // 移除未读标识
                const unreadDot = messageItem.querySelector('.bg-blue-500');
                if (unreadDot) {
                    unreadDot.classList.remove('bg-blue-500');
                    unreadDot.classList.add('bg-gray-300');
                }
                
                // 移除标记已读按钮
                const markReadBtn = messageItem.querySelector('button[onclick*="markAsRead"]');
                if (markReadBtn) {
                    markReadBtn.remove();
                }
            }
            
            // 更新未读数量
            updateUnreadCount();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('操作失败', 'error');
    });
}

// 全部标记为已读
function markAllAsRead() {
    const unreadMessages = Array.from(document.querySelectorAll('.message-item[data-is-read="false"]'));
    const messageIds = unreadMessages.map(item => parseInt(item.getAttribute('data-message-id')));
    
    if (messageIds.length === 0) {
        showNotification('没有未读消息', 'info');
        return;
    }
    
    fetch('/communications/api/messages/batch-read/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify({
            message_ids: messageIds
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.updated_count > 0) {
            showNotification(`已标记 ${data.updated_count} 条消息为已读`, 'success');
            
            // 更新界面
            unreadMessages.forEach(item => {
                item.classList.remove('bg-blue-50');
                item.setAttribute('data-is-read', 'true');
                
                // 更新未读标识
                const unreadDot = item.querySelector('.bg-blue-500');
                if (unreadDot) {
                    unreadDot.classList.remove('bg-blue-500');
                    unreadDot.classList.add('bg-gray-300');
                }
                
                // 移除标记已读按钮
                const markReadBtn = item.querySelector('button[onclick*="markAsRead"]');
                if (markReadBtn) {
                    markReadBtn.remove();
                }
            });
            
            // 更新未读数量
            updateUnreadCount();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('操作失败', 'error');
    });
}

// 刷新消息列表
function refreshMessages() {
    showNotification('正在刷新...', 'info');
    window.location.reload();
}

// 更新未读消息数量
function updateUnreadCount() {
    fetch('/communications/api/messages/unread-count/')
    .then(response => response.json())
    .then(data => {
        document.getElementById('unreadCount').textContent = data.total_unread;
    })
    .catch(error => {
        console.error('Error updating unread count:', error);
    });
}

// 开始消息轮询
function startMessagePolling() {
    setInterval(function() {
        fetch('/communications/api/messages/poll/')
        .then(response => response.json())
        .then(data => {
            if (data.new_messages && data.new_messages.length > 0) {
                // 有新消息时刷新页面或动态添加
                showNotification(`收到 ${data.new_messages.length} 条新消息`, 'info');
            }
            
            // 更新未读数量
            if (data.unread_count !== undefined) {
                document.getElementById('unreadCount').textContent = data.unread_count;
            }
        })
        .catch(error => {
            console.error('Polling error:', error);
        });
    }, 30000); // 30秒轮询一次
}

// 获取CSRF token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>
{% endblock %}