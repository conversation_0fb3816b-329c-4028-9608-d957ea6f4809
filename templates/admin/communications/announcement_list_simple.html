{% extends 'admin_base.html' %}
{% load static %}

{% block title %}公告管理 - 管理后台{% endblock %}

{% block breadcrumb %}
<a href="{% url 'organizations:admin:dashboard' %}" class="text-blue-600 hover:text-blue-800">首页</a>
<i data-lucide="chevron-right" class="w-4 h-4 breadcrumb-separator"></i>
<span class="text-gray-900">公告管理</span>
{% endblock %}

{% block breadcrumb_current %}公告管理{% endblock %}

{% block content %}
<div class="p-6">
    <!-- 页面头部 -->
    <div class="flex items-center justify-between mb-8">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">公告管理</h1>
            <p class="text-gray-600 mt-1">发布和管理系统公告</p>
        </div>
        <div class="flex items-center space-x-3">
            <button type="button" class="btn btn-primary btn-md" onclick="createAnnouncement()">
                <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                发布公告
            </button>
        </div>
    </div>

    <!-- 公告统计卡片 -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- 全部公告 -->
        <div class="card p-6">
            <div class="flex items-center space-x-4">
                <div class="flex-shrink-0 p-3 bg-gray-100 text-gray-600 rounded-lg">
                    <i data-lucide="megaphone" class="w-6 h-6"></i>
                </div>
                <div class="flex-1">
                    <div class="text-2xl font-bold text-gray-900">{{ total_count|default:0 }}</div>
                    <div class="text-sm text-gray-600 mt-1">全部公告</div>
                </div>
            </div>
        </div>

        <!-- 已发布 -->
        <div class="card p-6" style="background: linear-gradient(135deg, var(--success-50), var(--success-100));">
            <div class="flex items-center space-x-4">
                <div class="flex-shrink-0 p-3 bg-green-100 text-green-600 rounded-lg">
                    <i data-lucide="check-circle" class="w-6 h-6"></i>
                </div>
                <div class="flex-1">
                    <div class="text-2xl font-bold text-gray-900">{{ published_count|default:0 }}</div>
                    <div class="text-sm text-gray-600 mt-1">已发布</div>
                </div>
            </div>
        </div>

        <!-- 置顶公告 -->
        <div class="card p-6" style="background: linear-gradient(135deg, var(--warning-50), var(--warning-100));">
            <div class="flex items-center space-x-4">
                <div class="flex-shrink-0 p-3 bg-yellow-100 text-yellow-600 rounded-lg">
                    <i data-lucide="pin" class="w-6 h-6"></i>
                </div>
                <div class="flex-1">
                    <div class="text-2xl font-bold text-gray-900">{{ pinned_count|default:0 }}</div>
                    <div class="text-sm text-gray-600 mt-1">置顶公告</div>
                </div>
            </div>
        </div>

        <!-- 高优先级 -->
        <div class="card p-6" style="background: linear-gradient(135deg, var(--error-50), var(--error-100));">
            <div class="flex items-center space-x-4">
                <div class="flex-shrink-0 p-3 bg-red-100 text-red-600 rounded-lg">
                    <i data-lucide="alert-triangle" class="w-6 h-6"></i>
                </div>
                <div class="flex-1">
                    <div class="text-2xl font-bold text-gray-900">{{ high_priority_count|default:0 }}</div>
                    <div class="text-sm text-gray-600 mt-1">高优先级</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 公告列表 -->
    <div class="card">
        <div class="card-header">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="list" class="w-5 h-5 mr-2 text-gray-500"></i>
                公告列表
            </h3>
            <div class="flex items-center space-x-4">
                <input type="text" id="searchInput" placeholder="搜索公告..." 
                       class="form-input w-64" onkeyup="searchAnnouncements()">
                <button type="button" class="btn btn-success btn-sm" onclick="loadAnnouncements()">
                    <i data-lucide="refresh-cw" class="w-4 h-4 mr-1"></i>
                    刷新
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">标题</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">作者</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">目标部门</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发布时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody id="announcementTableBody" class="bg-white divide-y divide-gray-200">
                        {% for announcement in announcements %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    {% if announcement.is_pinned %}
                                        <i data-lucide="pin" class="w-4 h-4 text-yellow-500 mr-2"></i>
                                    {% endif %}
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ announcement.title }}</div>
                                        <div class="text-xs text-gray-500">浏览量: {{ announcement.view_count|default:0 }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="badge badge-primary badge-sm">
                                    {{ announcement.get_announcement_type_display }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center mr-3">
                                        <i data-lucide="user" class="w-4 h-4 text-gray-500"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ announcement.author.name|default:'系统' }}</div>
                                        {% if announcement.author.department %}
                                            <div class="text-xs text-gray-500">{{ announcement.author.department.name }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ announcement.target_department.name|default:'全公司' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center space-x-2">
                                    {% if announcement.is_published %}
                                        <span class="badge badge-success badge-sm">已发布</span>
                                    {% else %}
                                        <span class="badge badge-secondary badge-sm">草稿</span>
                                    {% endif %}
                                    
                                    {% if announcement.is_pinned %}
                                        <span class="badge badge-warning badge-sm">置顶</span>
                                    {% endif %}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ announcement.publish_at|date:"Y-m-d H:i" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="viewAnnouncement({{ announcement.id }})" class="text-blue-600 hover:text-blue-900 mr-3">查看</button>
                                <button onclick="editAnnouncement({{ announcement.id }})" class="text-green-600 hover:text-green-900 mr-3">编辑</button>
                                <button onclick="deleteAnnouncement({{ announcement.id }})" class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" class="px-6 py-8 text-center text-gray-500">
                                <i data-lucide="megaphone" class="w-12 h-12 mx-auto mb-4 text-gray-300"></i>
                                <p>暂无公告</p>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 分页 -->
    {% if is_paginated %}
    <div class="mt-6 flex items-center justify-between">
        <div class="text-sm text-gray-500">
            显示第 {{ page_obj.start_index }}-{{ page_obj.end_index }} 条，共 {{ paginator.count }} 条记录
        </div>
        <div class="flex items-center space-x-2">
            {% if page_obj.has_previous %}
                <a href="?page={{ page_obj.previous_page_number }}" class="btn btn-secondary btn-sm">上一页</a>
            {% endif %}
            
            <span class="text-sm text-gray-500">
                第 {{ page_obj.number }} 页，共 {{ paginator.num_pages }} 页
            </span>
            
            {% if page_obj.has_next %}
                <a href="?page={{ page_obj.next_page_number }}" class="btn btn-secondary btn-sm">下一页</a>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>

<script>
// 全局公告管理器
window.announcementManager = {
    
    async loadAnnouncements() {
        try {
            showSuccess('刷新成功');
            location.reload();
        } catch (error) {
            console.error('加载公告失败:', error);
            showError('加载失败', error.message);
        }
    }
};

// 全局函数
function loadAnnouncements() {
    window.announcementManager.loadAnnouncements();
}

function searchAnnouncements() {
    const query = document.getElementById('searchInput').value;
    console.log('搜索公告:', query);
    // 实现搜索逻辑
}

function createAnnouncement() {
    showInfo('创建公告', '公告创建功能正在开发中...');
}

function viewAnnouncement(announcementId) {
    showInfo('查看公告', `正在查看公告 ${announcementId}`);
}

function editAnnouncement(announcementId) {
    showInfo('编辑公告', `正在编辑公告 ${announcementId}`);
}

function deleteAnnouncement(announcementId) {
    if (confirm('确定要删除这个公告吗？')) {
        showInfo('删除公告', `正在删除公告 ${announcementId}`);
    }
}

// 工具函数
function showSuccess(message) {
    console.log('成功:', message);
}

function showError(title, message) {
    console.error('错误:', title, message);
}

function showInfo(title, message) {
    console.log('信息:', title, message);
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化图标
    if (window.lucide) {
        lucide.createIcons({ nameAttr: 'data-lucide' });
    }
});
</script>
{% endblock %}