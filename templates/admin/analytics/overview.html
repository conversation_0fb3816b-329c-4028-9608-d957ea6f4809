{% extends "admin/base_admin.html" %}

{% block page_title %}数据分析{% endblock %}
{% block page_description %}考评数据统计分析和可视化展示{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-4">
    <select id="timeRangeFilter" class="form-select">
        <option value="7">最近7天</option>
        <option value="30" selected>最近30天</option>
        <option value="90">最近90天</option>
        <option value="365">最近一年</option>
    </select>
    <button onclick="exportAnalyticsReport()" class="btn btn-primary btn-md flex items-center space-x-2">
        <i data-lucide="download" class="w-4 h-4"></i>
        <span>导出报告</span>
    </button>
    <button onclick="refreshData()" class="btn btn-success btn-md flex items-center space-x-2">
        <i data-lucide="refresh-cw" class="w-4 h-4"></i>
        <span>刷新数据</span>
    </button>
</div>
{% endblock %}

{% block admin_content %}
<!-- Key Metrics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <div class="card p-6 text-white" style="background: linear-gradient(135deg, var(--primary-500), var(--primary-600));">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-white/80 text-sm font-medium">考评完成率</p>
                <p class="text-3xl font-bold" id="completionRate">{{ analytics.completion_rate|default:0 }}%</p>
                <p class="text-white/70 text-xs mt-1">{{ analytics.completed_count|default:0 }}/{{ analytics.total_count|default:0 }} 已完成</p>
            </div>
            <div class="p-3 bg-white/20 rounded-full">
                <i data-lucide="check-circle" class="w-8 h-8"></i>
            </div>
        </div>
    </div>

    <div class="card p-6 text-white" style="background: linear-gradient(135deg, var(--success-500), var(--success-600));">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-white/80 text-sm font-medium">平均评分</p>
                <p class="text-3xl font-bold" id="averageScore">{{ analytics.average_score|default:0|floatformat:1 }}</p>
                <p class="text-white/70 text-xs mt-1">总体评价水平</p>
            </div>
            <div class="p-3 bg-white/20 rounded-full">
                <i data-lucide="star" class="w-8 h-8"></i>
            </div>
        </div>
    </div>

    <div class="card p-6 text-white" style="background: linear-gradient(135deg, var(--warning-500), var(--warning-600));">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-white/80 text-sm font-medium">参与人数</p>
                <p class="text-3xl font-bold" id="participantCount">{{ analytics.participant_count|default:0 }}</p>
                <p class="text-white/70 text-xs mt-1">活跃评价者</p>
            </div>
            <div class="p-3 bg-white/20 rounded-full">
                <i data-lucide="users" class="w-8 h-8"></i>
            </div>
        </div>
    </div>

    <div class="card p-6 text-white" style="background: linear-gradient(135deg, var(--info-500), var(--info-600));">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-white/80 text-sm font-medium">活跃批次</p>
                <p class="text-3xl font-bold" id="activeBatches">{{ analytics.active_batches|default:0 }}</p>
                <p class="text-white/70 text-xs mt-1">进行中批次</p>
            </div>
            <div class="p-3 bg-white/20 rounded-full">
                <i data-lucide="calendar" class="w-8 h-8"></i>
            </div>
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- 完成趋势图 -->
    <div class="card">
        <div class="card-header">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="trending-up" class="w-5 h-5 mr-2 text-gray-500"></i>
                考评完成趋势
            </h3>
        </div>
        <div class="card-body">
            <div id="completionTrendChart" style="height: 300px;"></div>
        </div>
    </div>

    <!-- 评分分布图 -->
    <div class="card">
        <div class="card-header">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="bar-chart-3" class="w-5 h-5 mr-2 text-gray-500"></i>
                评分分布
            </h3>
        </div>
        <div class="card-body">
            <div id="scoreDistributionChart" style="height: 300px;"></div>
        </div>
    </div>
</div>

<!-- Department Analysis -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
    <!-- 部门评分对比 -->
    <div class="lg:col-span-2 bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i data-lucide="building" class="w-5 h-5 mr-2 text-gray-500"></i>
                    部门评分对比
                </h3>
                <div class="flex items-center space-x-2">
                    <select id="comparison-metric-select" class="text-sm border border-gray-300 rounded px-2 py-1">
                        <option value="average_score">平均分</option>
                        <option value="participation_rate">参与率</option>
                    </select>
                    <button id="refresh-comparison-chart" class="text-sm text-blue-600 hover:text-blue-800 transition-colors">
                        <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div id="departmentComparisonChart" style="height: 400px;"></div>
        </div>
    </div>

    <!-- 部门排名 -->
    <div class="card">
        <div class="card-header">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="trophy" class="w-5 h-5 mr-2 text-gray-500"></i>
                部门排名
            </h3>
        </div>
        <div class="card-body">
            <div class="space-y-4" id="departmentRanking">
                {% for dept_stat in department_stats %}
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <span class="flex items-center justify-center w-6 h-6 rounded-full bg-blue-500 text-white text-xs font-medium">
                            {{ forloop.counter }}
                        </span>
                        <div>
                            <p class="text-sm font-medium text-gray-900">{{ dept_stat.name }}</p>
                            <p class="text-xs text-gray-500">{{ dept_stat.staff_count }} 人</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-sm font-bold text-gray-900">{{ dept_stat.average_score|floatformat:1 }}</p>
                        <p class="text-xs text-gray-500">平均分</p>
                    </div>
                </div>
                {% empty %}
                <div class="text-center py-8 text-gray-500">
                    <i data-lucide="building" class="w-12 h-12 mx-auto mb-4 text-gray-300"></i>
                    <p>暂无部门数据</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<!-- Position Analysis -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- 职位层级分析 -->
    <div class="card">
        <div class="card-header">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="layers" class="w-5 h-5 mr-2 text-gray-500"></i>
                职位层级分析
            </h3>
        </div>
        <div class="card-body">
            <div id="positionLevelChart" style="height: 350px;"></div>
        </div>
    </div>

    <!-- 考评活跃度 -->
    <div class="card">
        <div class="card-header">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="activity" class="w-5 h-5 mr-2 text-gray-500"></i>
                考评活跃度
            </h3>
        </div>
        <div class="card-body">
            <div id="activityHeatmapChart" style="height: 350px;"></div>
        </div>
    </div>
</div>

<!-- Data Table -->
<div class="bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="table" class="w-5 h-5 mr-2 text-gray-500"></i>
                详细数据表
            </h3>
            <div class="flex items-center space-x-4">
                <select id="dataTableFilter" class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                    <option value="all">全部数据</option>
                    <option value="departments">部门统计</option>
                    <option value="positions">职位统计</option>
                    <option value="staff">员工统计</option>
                </select>
            </div>
        </div>
    </div>
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200" id="dataTable">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">名称</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">参与数</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">完成数</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">完成率</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">平均分</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">趋势</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200" id="dataTableBody">
                <!-- 数据将通过JavaScript动态加载 -->
            </tbody>
        </table>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
<script>
    // 图表实例
    let charts = {};

    // 页面加载时初始化
    document.addEventListener('DOMContentLoaded', function() {
        initializeCharts();
        loadAnalyticsData();
        setupEventListeners();
    });

    function setupEventListeners() {
        // 时间范围筛选
        document.getElementById('timeRangeFilter').addEventListener('change', function() {
            loadAnalyticsData();
        });

        // 数据表筛选
        document.getElementById('dataTableFilter').addEventListener('change', function() {
            loadTableData();
        });
    }

    function initializeCharts() {
        // 完成趋势图
        charts.completionTrend = echarts.init(document.getElementById('completionTrendChart'));
        const trendOption = {
            tooltip: { trigger: 'axis' },
            xAxis: { type: 'category', data: [] },
            yAxis: { type: 'value', name: '完成率(%)' },
            series: [{
                name: '完成率',
                type: 'line',
                smooth: true,
                data: [],
                areaStyle: { color: 'rgba(59, 130, 246, 0.1)' },
                itemStyle: { color: '#3b82f6' }
            }]
        };
        charts.completionTrend.setOption(trendOption);

        // 评分分布图
        charts.scoreDistribution = echarts.init(document.getElementById('scoreDistributionChart'));
        const distributionOption = {
            tooltip: { trigger: 'item' },
            series: [{
                name: '评分分布',
                type: 'pie',
                radius: ['40%', '70%'],
                center: ['50%', '50%'],
                data: [],
                emphasis: { itemStyle: { shadowBlur: 10, shadowOffsetX: 0, shadowColor: 'rgba(0, 0, 0, 0.5)' } }
            }]
        };
        charts.scoreDistribution.setOption(distributionOption);

        // 部门对比图
        charts.departmentComparison = echarts.init(document.getElementById('departmentComparisonChart'));
        const comparisonOption = {
            tooltip: { trigger: 'axis' },
            legend: { data: ['平均分', '完成率'] },
            xAxis: { type: 'category', data: [] },
            yAxis: [
                { type: 'value', name: '评分', position: 'left' },
                { type: 'value', name: '完成率(%)', position: 'right' }
            ],
            series: [
                { name: '平均分', type: 'bar', data: [], itemStyle: { color: '#3b82f6' } },
                { name: '完成率', type: 'line', yAxisIndex: 1, data: [], itemStyle: { color: '#10b981' } }
            ]
        };
        charts.departmentComparison.setOption(comparisonOption);

        // 职位层级分析图
        charts.positionLevel = echarts.init(document.getElementById('positionLevelChart'));
        const positionOption = {
            tooltip: { trigger: 'axis' },
            xAxis: { type: 'category', name: '职位级别', data: [] },
            yAxis: { type: 'value', name: '人数' },
            series: [{
                name: '人数分布',
                type: 'bar',
                data: [],
                itemStyle: { color: '#8b5cf6' }
            }]
        };
        charts.positionLevel.setOption(positionOption);

        // 活跃度热力图
        charts.activityHeatmap = echarts.init(document.getElementById('activityHeatmapChart'));
        const heatmapOption = {
            tooltip: { position: 'top' },
            grid: { height: '50%', top: '10%' },
            xAxis: { type: 'category', data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'] },
            yAxis: { type: 'category', data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'] },
            visualMap: {
                min: 0,
                max: 100,
                calculable: true,
                orient: 'horizontal',
                left: 'center',
                bottom: '5%',
                inRange: { color: ['#50a3ba', '#eac736', '#d94e5d'] }
            },
            series: [{
                name: '活跃度',
                type: 'heatmap',
                data: [],
                emphasis: { itemStyle: { shadowBlur: 10, shadowColor: 'rgba(0, 0, 0, 0.5)' } }
            }]
        };
        charts.activityHeatmap.setOption(heatmapOption);
    }

    async function loadAnalyticsData() {
        const timeRange = document.getElementById('timeRangeFilter').value;
        
        try {
            // 使用优化的fetch请求，自动处理加载状态和缓存
            const data = await window.optimizedFetch(`/reports/admin/api/analytics/?days=${timeRange}`, {
                useCache: true // 启用缓存，5分钟内重复请求直接使用缓存
            });
            
            if (data.success) {
                updateCharts(data.data);
                updateMetrics(data.data);
                loadTableData();
                window.showNotification('数据加载完成', 'success');
            } else {
                throw new Error(data.message || '获取数据失败');
            }
        } catch (error) {
            console.error('Error loading analytics data:', error);
            // 使用模拟数据
            loadMockData();
            window.showNotification('获取数据失败，使用模拟数据', 'warning');
        }
    }

    function loadMockData() {
        const mockData = {
            trend: {
                dates: ['01-01', '01-02', '01-03', '01-04', '01-05', '01-06', '01-07'],
                completion: [65, 72, 78, 85, 89, 92, 95]
            },
            distribution: [
                { value: 335, name: '优秀(90+)' },
                { value: 310, name: '良好(80-89)' },
                { value: 234, name: '中等(70-79)' },
                { value: 135, name: '待改进(60-69)' },
                { value: 48, name: '不合格(<60)' }
            ],
            departments: {
                names: ['技术部', '产品部', '市场部', '人事部', '财务部'],
                scores: [8.5, 8.2, 7.8, 8.0, 8.3],
                completion: [95, 88, 92, 100, 85]
            },
            positions: {
                levels: ['1级', '2级', '3级', '4级', '5级', '6级', '7级', '8级', '9级'],
                counts: [12, 18, 25, 30, 22, 15, 8, 5, 2]
            },
            activity: generateHeatmapData()
        };
        
        updateCharts(mockData);
    }

    function generateHeatmapData() {
        const data = [];
        const hours = ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'];
        const days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
        
        for (let i = 0; i < days.length; i++) {
            for (let j = 0; j < hours.length; j++) {
                data.push([i, j, Math.round(Math.random() * 100)]);
            }
        }
        return data;
    }

    function updateCharts(data) {
        // 更新完成趋势图
        if (data.trend) {
            charts.completionTrend.setOption({
                xAxis: { data: data.trend.dates },
                series: [{ data: data.trend.completion }]
            });
        }

        // 更新评分分布图
        if (data.distribution) {
            charts.scoreDistribution.setOption({
                series: [{ data: data.distribution }]
            });
        }

        // 更新部门对比图
        if (data.departments) {
            charts.departmentComparison.setOption({
                xAxis: { data: data.departments.names },
                series: [
                    { data: data.departments.scores },
                    { data: data.departments.completion }
                ]
            });
        }

        // 更新职位层级图
        if (data.positions) {
            charts.positionLevel.setOption({
                xAxis: { data: data.positions.levels },
                series: [{ data: data.positions.counts }]
            });
        }

        // 更新活跃度热力图
        if (data.activity) {
            charts.activityHeatmap.setOption({
                series: [{ data: data.activity }]
            });
        }
    }

    function updateMetrics(data) {
        if (data.metrics) {
            document.getElementById('completionRate').textContent = data.metrics.completion_rate + '%';
            document.getElementById('averageScore').textContent = data.metrics.average_score;
            document.getElementById('participantCount').textContent = data.metrics.participant_count;
            document.getElementById('activeBatches').textContent = data.metrics.active_batches;
        }
    }

    function loadTableData() {
        const filter = document.getElementById('dataTableFilter').value;
        const tbody = document.getElementById('dataTableBody');
        
        // 模拟表格数据
        const mockTableData = [
            { name: '技术部', type: '部门', participants: 25, completed: 24, rate: 96, score: 8.5, trend: '↗' },
            { name: '产品部', type: '部门', participants: 18, completed: 16, rate: 89, score: 8.2, trend: '↗' },
            { name: '市场部', type: '部门', participants: 22, completed: 20, rate: 91, score: 7.8, trend: '→' },
            { name: '人事部', type: '部门', participants: 8, completed: 8, rate: 100, score: 8.0, trend: '↗' },
            { name: '财务部', type: '部门', participants: 12, completed: 10, rate: 83, score: 8.3, trend: '↘' }
        ];

        tbody.innerHTML = '';
        mockTableData.forEach(row => {
            const tr = document.createElement('tr');
            tr.className = 'hover:bg-gray-50';
            tr.innerHTML = `
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${row.name}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${row.type}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${row.participants}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${row.completed}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${row.rate}%</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${row.score}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${row.trend}</td>
            `;
            tbody.appendChild(tr);
        });
    }

    function exportAnalyticsReport() {
        showNotification('正在生成分析报告...', 'info');
        // 模拟导出
        setTimeout(() => {
            showNotification('报告导出成功', 'success');
        }, 2000);
    }

    function refreshData() {
        loadAnalyticsData();
    }

    // 窗口大小改变时重绘图表
    window.addEventListener('resize', function() {
        Object.values(charts).forEach(chart => {
            chart.resize();
        });
    });
</script>
{% endblock %}