{% extends 'admin/base_admin.html' %}
{% load static %}

{% block title %}个人资料管理{% endblock %}

{% block content %}
<div class="p-6">
    <!-- 页面标题 -->
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900">个人资料管理</h1>
        <p class="mt-1 text-sm text-gray-600">查看和编辑您的个人信息</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- 个人信息卡片 -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">基本信息</h2>
                </div>
                
                <form method="post" class="p-6">
                    {% csrf_token %}
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- 用户名 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">用户名</label>
                            <input type="text" value="{{ staff.username }}" readonly 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500 cursor-not-allowed">
                            <p class="mt-1 text-xs text-gray-500">用户名不可修改</p>
                        </div>
                        
                        <!-- 员工编号 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">员工编号</label>
                            <input type="text" value="{{ staff.employee_no }}" readonly 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500 cursor-not-allowed">
                            <p class="mt-1 text-xs text-gray-500">员工编号不可修改</p>
                        </div>
                        
                        <!-- 姓名 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">姓名 <span class="text-red-500">*</span></label>
                            <input type="text" name="name" value="{{ staff.name }}" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        
                        <!-- 邮箱 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">邮箱</label>
                            <input type="email" name="email" value="{{ staff.email }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        
                        <!-- 手机号 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">手机号</label>
                            <input type="tel" name="phone" value="{{ staff.phone }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        
                        <!-- 角色 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">角色</label>
                            {% if staff.is_super_admin %}
                                <select name="role" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                                    {% for role_code, role_name in role_choices %}
                                        <option value="{{ role_code }}" {% if staff.role == role_code %}selected{% endif %}>{{ role_name }}</option>
                                    {% endfor %}
                                </select>
                                <p class="mt-1 text-xs text-gray-500">超级管理员可以修改自己的角色</p>
                            {% else %}
                                <input type="text" value="{{ staff.get_role_display }}" readonly 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500 cursor-not-allowed">
                                <p class="mt-1 text-xs text-gray-500">请联系超级管理员修改角色</p>
                            {% endif %}
                        </div>
                        
                        <!-- 部门 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">部门</label>
                            <input type="text" value="{{ staff.department.name }}" readonly 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500 cursor-not-allowed">
                            <p class="mt-1 text-xs text-gray-500">请联系HR管理员修改部门</p>
                        </div>
                        
                        <!-- 职位 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">职位</label>
                            <input type="text" value="{% if staff.position %}{{ staff.position.name }}{% else %}无{% endif %}" readonly 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500 cursor-not-allowed">
                            <p class="mt-1 text-xs text-gray-500">请联系HR管理员修改职位</p>
                        </div>
                    </div>
                    
                    <div class="mt-6 flex justify-end space-x-3">
                        <a href="{% url 'organizations:admin:dashboard' %}" 
                           class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors">
                            取消
                        </a>
                        <button type="submit" 
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                            保存修改
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 侧边栏 -->
        <div class="space-y-6">
            <!-- 账户安全 -->
            <div class="bg-white rounded-lg shadow border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">账户安全</h2>
                </div>
                
                <div class="p-6 space-y-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-900">登录密码</p>
                            <p class="text-xs text-gray-500">定期更新密码以保护账户安全</p>
                        </div>
                        <a href="{% url 'organizations:admin:change_password' %}" 
                           class="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                            修改
                        </a>
                    </div>
                    
                    <div class="pt-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-900">最后登录</p>
                                <p class="text-xs text-gray-500">{{ staff.last_login|date:"Y-m-d H:i:s"|default:"从未登录" }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 账户信息 -->
            <div class="bg-white rounded-lg shadow border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">账户信息</h2>
                </div>
                
                <div class="p-6 space-y-3">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">匿名编号</span>
                        <div class="text-right">
                            {% if staff.new_anonymous_code %}
                                <span class="text-sm font-medium text-gray-900 font-mono">{{ staff.new_anonymous_code }}</span>
                                <div class="text-xs text-green-600">安全编号</div>
                            {% elif staff.anonymous_code %}
                                <span class="text-sm font-medium text-gray-900 font-mono">{{ staff.anonymous_code }}</span>
                                <div class="text-xs text-yellow-600">待升级</div>
                            {% else %}
                                <span class="text-sm text-gray-400">未生成</span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">入职日期</span>
                        <span class="text-sm font-medium text-gray-900">{{ staff.hire_date|date:"Y-m-d"|default:"未设置" }}</span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">账户状态</span>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {% if staff.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                            {% if staff.is_active %}正常{% else %}已禁用{% endif %}
                        </span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">创建时间</span>
                        <span class="text-sm font-medium text-gray-900">{{ staff.created_at|date:"Y-m-d H:i" }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}