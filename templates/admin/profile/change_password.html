{% extends 'admin/base_admin.html' %}
{% load static %}

{% block title %}修改密码{% endblock %}

{% block content %}
<div class="p-6">
    <!-- 页面标题 -->
    <div class="mb-6">
        <div class="flex items-center space-x-2">
            <a href="{% url 'organizations:admin:profile_management' %}" 
               class="text-gray-400 hover:text-gray-600 transition-colors">
                <i data-lucide="arrow-left" class="w-5 h-5"></i>
            </a>
            <h1 class="text-2xl font-bold text-gray-900">修改密码</h1>
        </div>
        <p class="mt-1 text-sm text-gray-600">为了账户安全，请定期更换密码</p>
    </div>

    <div class="max-w-lg">
        <div class="bg-white rounded-lg shadow border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-medium text-gray-900">密码修改</h2>
            </div>
            
            <form method="post" class="p-6">
                {% csrf_token %}
                
                <div class="space-y-4">
                    <!-- 当前密码 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">当前密码 <span class="text-red-500">*</span></label>
                        <div class="relative">
                            <input type="password" name="current_password" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 pr-10"
                                   placeholder="请输入当前密码">
                            <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center" onclick="togglePassword(this)">
                                <i data-lucide="eye" class="w-4 h-4 text-gray-400"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 新密码 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">新密码 <span class="text-red-500">*</span></label>
                        <div class="relative">
                            <input type="password" name="new_password" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 pr-10"
                                   placeholder="请输入新密码（至少6位）">
                            <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center" onclick="togglePassword(this)">
                                <i data-lucide="eye" class="w-4 h-4 text-gray-400"></i>
                            </button>
                        </div>
                        <p class="mt-1 text-xs text-gray-500">密码长度至少6位，建议包含字母、数字和特殊字符</p>
                    </div>
                    
                    <!-- 确认新密码 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">确认新密码 <span class="text-red-500">*</span></label>
                        <div class="relative">
                            <input type="password" name="confirm_password" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 pr-10"
                                   placeholder="请再次输入新密码">
                            <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center" onclick="togglePassword(this)">
                                <i data-lucide="eye" class="w-4 h-4 text-gray-400"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="mt-6 flex justify-end space-x-3">
                    <a href="{% url 'organizations:admin:profile_management' %}" 
                       class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors">
                        取消
                    </a>
                    <button type="submit" 
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                        修改密码
                    </button>
                </div>
            </form>
        </div>
        
        <!-- 安全提示 -->
        <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex">
                <i data-lucide="info" class="w-5 h-5 text-blue-400 flex-shrink-0"></i>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800">密码安全建议</h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <ul class="list-disc list-inside space-y-1">
                            <li>使用至少8位长度的密码</li>
                            <li>包含大小写字母、数字和特殊字符</li>
                            <li>避免使用个人信息和常见密码</li>
                            <li>定期更换密码（建议3个月一次）</li>
                            <li>不要在多个网站使用相同密码</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword(button) {
    const input = button.parentElement.querySelector('input');
    const icon = button.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.setAttribute('data-lucide', 'eye-off');
        lucide.createIcons();
    } else {
        input.type = 'password';
        icon.setAttribute('data-lucide', 'eye');
        lucide.createIcons();
    }
}
</script>
{% endblock %}