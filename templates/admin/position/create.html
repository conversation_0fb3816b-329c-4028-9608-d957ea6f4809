{% extends "admin/base_admin.html" %}

{% block page_title %}创建职位{% endblock %}
{% block page_description %}创建新的职位信息，配置职位层级和管理属性{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <a href="{% url 'organizations:admin:position_list' %}" 
       class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center space-x-2">
        <i data-lucide="arrow-left" class="w-4 h-4"></i>
        <span>返回列表</span>
    </a>
</div>
{% endblock %}

{% block admin_content %}
<div class="max-w-4xl mx-auto">
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="plus-circle" class="w-5 h-5 mr-2 text-gray-500"></i>
                创建职位
            </h3>
        </div>
        
        <form method="post" class="p-6 space-y-6">
            {% csrf_token %}
            
            <!-- 基本信息 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="{{ form.department.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        所属部门 <span class="text-red-500">*</span>
                    </label>
                    {{ form.department }}
                    {% if form.department.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.department.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <div>
                    <label for="{{ form.position_code.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        职位编码 <span class="text-red-500">*</span>
                    </label>
                    {{ form.position_code }}
                    {% if form.position_code.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.position_code.errors.0 }}</p>
                    {% endif %}
                    <p class="mt-1 text-sm text-gray-500">如：POS001、MGR001等</p>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="{{ form.name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        职位名称 <span class="text-red-500">*</span>
                    </label>
                    {{ form.name }}
                    {% if form.name.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.name.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <div>
                    <label for="{{ form.level.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        职位层级
                    </label>
                    {{ form.level }}
                    {% if form.level.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.level.errors.0 }}</p>
                    {% endif %}
                    <p class="mt-1 text-sm text-gray-500">1-4级为员工，5-6级为主管，7-8级为经理，9级为领导</p>
                </div>
            </div>
            
            <!-- 管理属性 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div>
                    <label class="flex items-center space-x-2">
                        {{ form.is_management }}
                        <span class="text-sm text-gray-900">管理岗位</span>
                    </label>
                    {% if form.is_management.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.is_management.errors.0 }}</p>
                    {% endif %}
                    <p class="mt-1 text-sm text-gray-500">勾选表示此职位具有管理权限</p>
                </div>

                <div>
                    <label class="flex items-center space-x-2">
                        {{ form.is_department_manager }}
                        <span class="text-sm text-gray-900">部门经理</span>
                    </label>
                    {% if form.is_department_manager.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.is_department_manager.errors.0 }}</p>
                    {% endif %}
                    <p class="mt-1 text-sm text-gray-500">勾选表示此职位为部门经理</p>
                </div>

                <div>
                    <label class="flex items-center space-x-2">
                        {{ form.is_active }}
                        <span class="text-sm text-gray-900">启用状态</span>
                    </label>
                    {% if form.is_active.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.is_active.errors.0 }}</p>
                    {% endif %}
                    <p class="mt-1 text-sm text-gray-500">勾选表示启用此职位</p>
                </div>
                
                <div>
                    <label for="{{ form.sort_order.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        排序
                    </label>
                    {{ form.sort_order }}
                    {% if form.sort_order.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.sort_order.errors.0 }}</p>
                    {% endif %}
                    <p class="mt-1 text-sm text-gray-500">数字越小排序越靠前</p>
                </div>
            </div>
            
            <!-- 职位描述 -->
            <div>
                <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    职位描述
                </label>
                {{ form.description }}
                {% if form.description.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.description.errors.0 }}</p>
                {% endif %}
                <p class="mt-1 text-sm text-gray-500">请简要描述职位的主要职责和要求</p>
            </div>
            
            <!-- 注意事项 -->
            <div class="bg-green-50 border border-green-200 rounded-md p-4">
                <div class="flex">
                    <i data-lucide="info" class="w-5 h-5 text-green-400 mr-2 mt-0.5"></i>
                    <div class="text-sm text-green-700">
                        <p class="font-medium">创建说明</p>
                        <ul class="mt-1 list-disc list-inside space-y-1">
                            <li>职位编码在系统中必须唯一，建议使用部门缩写+序号</li>
                            <li>职位层级影响智能分配算法中的权重计算</li>
                            <li>管理岗位将具有查看下属评价结果的权限</li>
                            <li>创建后可以在职位管理中进行修改和完善</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- 表单按钮 -->
            <div class="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
                <a href="{% url 'organizations:admin:position_list' %}" 
                   class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
                    取消
                </a>
                <button type="submit" 
                        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center space-x-2">
                    <i data-lucide="save" class="w-4 h-4"></i>
                    <span>创建职位</span>
                </button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 设置表单字段样式
    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        if (input.type === 'checkbox') {
            input.className = 'h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded';
        } else if (input.tagName === 'SELECT') {
            input.className = 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500';
        } else if (input.tagName === 'TEXTAREA') {
            input.className = 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500';
            input.rows = 4;
        } else if (input.type === 'text' || input.type === 'number') {
            input.className = 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500';
        }
    });
    
    // 表单验证
    document.querySelector('form').addEventListener('submit', function(e) {
        const requiredFields = ['id_department', 'id_position_code', 'id_name'];
        let hasError = false;
        
        requiredFields.forEach(fieldId => {
            const field = document.querySelector('#' + fieldId);
            if (field && !field.value.trim()) {
                hasError = true;
                field.focus();
                
                const fieldName = {
                    'id_department': '所属部门',
                    'id_position_code': '职位编码', 
                    'id_name': '职位名称'
                }[fieldId];
                
                UI.showMessage(`请填写${fieldName}`, 'warning');
                return;
            }
        });
        
        if (hasError) {
            e.preventDefault();
            return false;
        }
        
        // 验证职位编码格式
        const positionCode = document.querySelector('#id_position_code');
        if (positionCode && positionCode.value.trim()) {
            const code = positionCode.value.trim();
            if (!/^[A-Z0-9]{3,20}$/.test(code)) {
                e.preventDefault();
                positionCode.focus();
                UI.showMessage('职位编码应为3-20位大写字母和数字组合', 'warning');
                return false;
            }
        }
        
        // 验证职位层级
        const level = document.querySelector('#id_level');
        if (level && level.value) {
            const levelValue = parseInt(level.value);
            if (levelValue < 1 || levelValue > 9) {
                e.preventDefault();
                level.focus();
                UI.showMessage('职位层级应在1-9之间', 'warning');
                return false;
            }
        }
        
        return true;
    });
    
    // 职位编码自动转大写
    const positionCodeField = document.querySelector('#id_position_code');
    if (positionCodeField) {
        positionCodeField.addEventListener('input', function(e) {
            e.target.value = e.target.value.toUpperCase();
        });
    }
});
</script>
{% endblock %}