{% extends "admin/base_admin.html" %}
{% load url_tags %}

{% block page_title %}职位管理{% endblock %}
{% block page_description %}管理公司职位层级和权限设置{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <!-- 视图切换按钮 -->
    <div class="flex bg-gray-100 rounded-lg p-1">
        <button id="card-view-btn" class="px-3 py-1 text-sm font-medium rounded-md bg-white text-gray-900 shadow-sm">
            <i data-lucide="grid-3x3" class="w-4 h-4 mr-1 inline"></i>
            卡片视图
        </button>
        <button id="table-view-btn" class="px-3 py-1 text-sm font-medium rounded-md text-gray-600 hover:text-gray-900">
            <i data-lucide="list" class="w-4 h-4 mr-1 inline"></i>
            表格视图
        </button>
    </div>

    <!-- 导入按钮 -->
    <a href="{% url 'organizations:admin:position_import' %}" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 flex items-center space-x-2">
        <i data-lucide="upload" class="w-4 h-4"></i>
        <span>导入</span>
    </a>

    <!-- 导出按钮 -->
    <button onclick="exportPositions()" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 flex items-center space-x-2">
        <i data-lucide="download" class="w-4 h-4"></i>
        <span>导出</span>
    </button>

    <!-- 新建按钮 -->
    <a href="{% url 'organizations:admin:position_create' %}" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
        <i data-lucide="plus" class="w-4 h-4"></i>
        <span>新建职位</span>
    </a>
</div>
{% endblock %}

{% block admin_content %}
<!-- Stats -->
<div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-8">
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
                <i data-lucide="briefcase" class="w-6 h-6 text-blue-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">总职位数</p>
                <p class="text-2xl font-bold text-gray-900">{{ object_list|length|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
                <i data-lucide="crown" class="w-6 h-6 text-purple-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">管理岗位</p>
                <p class="text-2xl font-bold text-gray-900">{{ management_count|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
                <i data-lucide="user-check" class="w-6 h-6 text-yellow-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">部门经理</p>
                <p class="text-2xl font-bold text-gray-900">{{ department_manager_count|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
                <i data-lucide="layers" class="w-6 h-6 text-yellow-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">最高级别</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.max_level|default:0 }}级</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
                <i data-lucide="building" class="w-6 h-6 text-purple-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">部门覆盖</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.departments_covered|default:0 }}</p>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="bg-white rounded-lg shadow mb-6 p-6">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="relative">
            <input type="text" id="searchInput" placeholder="搜索职位名称或编码..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md">
            <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"></i>
        </div>
        <select id="departmentFilter" class="border border-gray-300 rounded-md px-3 py-2">
            <option value="">所有部门</option>
            {% for dept in departments %}
            <option value="{{ dept.id }}">{{ dept.name }}</option>
            {% endfor %}
        </select>
        <select id="levelFilter" class="border border-gray-300 rounded-md px-3 py-2">
            <option value="">所有级别</option>
            <option value="1-4">基层员工(1-4级)</option>
            <option value="5-6">柜组长(5-6级)</option>
            <option value="7-8">中层管理(7-8级)</option>
            <option value="9">高层管理(9级)</option>
        </select>
    </div>
</div>

<!-- Positions Grid -->
<div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6" id="positionsGrid">
    {% if object_list %}
        {% for position in object_list %}
        <div class="bg-white rounded-lg shadow hover:shadow-lg transition-shadow position-card" data-department="{{ position.department.id }}" data-level="{{ position.level }}">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        {% if position.is_management %}
                            <div class="p-2 bg-purple-100 rounded-lg">
                                <i data-lucide="crown" class="w-5 h-5 text-purple-600"></i>
                            </div>
                        {% else %}
                            <div class="p-2 bg-blue-100 rounded-lg">
                                <i data-lucide="briefcase" class="w-5 h-5 text-blue-600"></i>
                            </div>
                        {% endif %}
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">{{ position.name }}</h3>
                            <p class="text-sm text-gray-500">{{ position.position_code }}</p>
                        </div>
                    </div>
                    <div class="relative">
                        <button onclick="toggleDropdown(this)" class="p-1 text-gray-400 hover:text-gray-600">
                            <i data-lucide="more-horizontal" class="w-4 h-4"></i>
                        </button>
                        <div class="dropdown-menu hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border">
                            <a href="{% url 'organizations:admin:position_detail' position.pk %}" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i data-lucide="eye" class="w-4 h-4 mr-2 inline"></i>查看详情
                            </a>
                            <a href="{% url 'organizations:admin:position_update' position.pk %}" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i data-lucide="edit" class="w-4 h-4 mr-2 inline"></i>编辑职位
                            </a>
                            <button onclick="confirmDelete('{{ position.pk }}', '{{ position.name }}')" class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100">
                                <i data-lucide="trash-2" class="w-4 h-4 mr-2 inline"></i>删除职位
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="p-6">
                <!-- Level Badge -->
                <div class="flex items-center justify-between mb-4">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                        {% if position.level >= 9 %}bg-red-100 text-red-800
                        {% elif position.level >= 7 %}bg-purple-100 text-purple-800
                        {% elif position.level >= 5 %}bg-blue-100 text-blue-800
                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                        {{ position.level }}级职位
                    </span>
                    {% if position.is_management %}
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            管理岗
                        </span>
                    {% endif %}
                </div>

                <!-- Department Info -->
                <div class="mb-4">
                    <p class="text-sm text-gray-600">所属部门</p>
                    <p class="text-lg font-medium text-gray-900">{{ position.department.name }}</p>
                </div>

                <!-- Staff Count -->
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <p class="text-sm text-gray-600">在职人数</p>
                        <p class="text-lg font-semibold">{{ position.get_staff_count|default:0 }}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">创建时间</p>
                        <p class="text-lg font-semibold">{{ position.created_at|date:"m-d" }}</p>
                    </div>
                </div>

                <!-- Description -->
                {% if position.description %}
                <div class="mb-4">
                    <p class="text-sm text-gray-600">职位描述</p>
                    <p class="text-sm text-gray-800 mt-1">{{ position.description|truncatechars:60 }}</p>
                </div>
                {% endif %}

                <!-- Actions -->
                <div class="flex space-x-2">
                    <a href="{% url 'organizations:admin:position_detail' position.pk %}" class="flex-1 px-3 py-2 text-sm text-center border border-gray-300 rounded-md hover:bg-gray-50">
                        查看详情
                    </a>
                    <a href="{% url 'organizations:admin:position_update' position.pk %}" class="flex-1 px-3 py-2 text-sm text-center bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        编辑
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="col-span-3 text-center py-12">
            <i data-lucide="briefcase" class="mx-auto h-12 w-12 text-gray-400"></i>
            <h3 class="mt-2 text-sm font-medium text-gray-900">暂无职位数据</h3>
            <p class="mt-1 text-sm text-gray-500">开始创建第一个职位吧。</p>
            <div class="mt-6">
                <a href="{% url 'organizations:admin:position_create' %}" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                    新建职位
                </a>
            </div>
        </div>
    {% endif %}
</div>

<!-- Pagination -->
{% if is_paginated %}
<div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-6 rounded-lg shadow">
    <div class="flex-1 flex justify-between sm:hidden">
        {% if page_obj.has_previous %}
            <a href="{% current_url_with_page page_obj.previous_page_number %}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">上一页</a>
        {% endif %}
        {% if page_obj.has_next %}
            <a href="{% current_url_with_page page_obj.next_page_number %}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">下一页</a>
        {% endif %}
    </div>
    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
        <div>
            <p class="text-sm text-gray-700">
                显示第 <span class="font-medium">{{ page_obj.start_index }}</span> 到 <span class="font-medium">{{ page_obj.end_index }}</span> 条，
                共 <span class="font-medium">{{ page_obj.paginator.count }}</span> 条记录
            </p>
        </div>
        <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                {% if page_obj.has_previous %}
                    <a href="{% current_url_with_page page_obj.previous_page_number %}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <i data-lucide="chevron-left" class="w-4 h-4"></i>
                    </a>
                {% endif %}

                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">{{ num }}</span>
                    {% else %}
                        <a href="{% current_url_with_page num %}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">{{ num }}</a>
                    {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                    <a href="{% current_url_with_page page_obj.next_page_number %}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <i data-lucide="chevron-right" class="w-4 h-4"></i>
                    </a>
                {% endif %}
            </nav>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    // 搜索功能
    document.getElementById('searchInput').addEventListener('input', function() {
        filterPositions();
    });

    // 部门筛选
    document.getElementById('departmentFilter').addEventListener('change', function() {
        filterPositions();
    });

    // 级别筛选
    document.getElementById('levelFilter').addEventListener('change', function() {
        filterPositions();
    });

    function filterPositions() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const departmentFilter = document.getElementById('departmentFilter').value;
        const levelFilter = document.getElementById('levelFilter').value;
        const positionCards = document.querySelectorAll('.position-card');
        
        positionCards.forEach(card => {
            const positionName = card.querySelector('h3').textContent.toLowerCase();
            const positionCode = card.querySelector('.text-sm.text-gray-500').textContent.toLowerCase();
            const departmentId = card.getAttribute('data-department');
            const positionLevel = parseInt(card.getAttribute('data-level'));
            
            let showCard = true;
            
            // 搜索筛选
            if (searchTerm && !positionName.includes(searchTerm) && !positionCode.includes(searchTerm)) {
                showCard = false;
            }
            
            // 部门筛选
            if (departmentFilter && departmentId !== departmentFilter) {
                showCard = false;
            }
            
            // 级别筛选
            if (levelFilter) {
                switch (levelFilter) {
                    case '1-4':
                        if (positionLevel < 1 || positionLevel > 4) showCard = false;
                        break;
                    case '5-6':
                        if (positionLevel < 5 || positionLevel > 6) showCard = false;
                        break;
                    case '7-8':
                        if (positionLevel < 7 || positionLevel > 8) showCard = false;
                        break;
                    case '9':
                        if (positionLevel !== 9) showCard = false;
                        break;
                }
            }
            
            card.style.display = showCard ? '' : 'none';
        });
    }

    // 删除确认
    function confirmDelete(positionId, positionName) {
        if (confirm(`确定要删除职位 "${positionName}" 吗？此操作不可撤销。`)) {
            window.location.href = `/admin/positions/${positionId}/delete/`;
        }
    }

    // 页面加载时自动聚焦搜索框
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.focus();
        }

        // 视图切换功能
        const tableViewBtn = document.getElementById('table-view-btn');
        const cardViewBtn = document.getElementById('card-view-btn');

        tableViewBtn?.addEventListener('click', function() {
            // 跳转到表格视图（默认视图），删除view参数
            const url = new URL(window.location);
            url.searchParams.delete('view');
            window.location.href = url.toString();
        });

        // 初始化图标
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    });

    // 职位导出功能
    function exportPositions() {
        // 获取当前筛选参数
        const params = new URLSearchParams();
        
        // 添加当前筛选条件
        const departmentFilter = document.getElementById('departmentFilter').value;
        const levelFilter = document.getElementById('levelFilter').value;
        const searchTerm = document.getElementById('searchInput').value;
        
        if (departmentFilter) params.append('department', departmentFilter);
        if (levelFilter) params.append('level', levelFilter);
        if (searchTerm) params.append('search', searchTerm);
        
        // 构建导出URL
        const exportUrl = `{% url 'organizations:admin:position_export' %}?${params.toString()}`;
        
        // 创建临时链接并触发下载
        const link = document.createElement('a');
        link.href = exportUrl;
        link.download = '职位信息导出.xlsx';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // 显示导出提示
        showToast('正在导出职位信息，请稍候...', 'info');
    }

    // 显示提示消息
    function showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `fixed top-4 right-4 px-4 py-2 rounded-md shadow-lg text-white z-50 ${
            type === 'success' ? 'bg-green-500' : 
            type === 'error' ? 'bg-red-500' : 
            'bg-blue-500'
        }`;
        toast.textContent = message;
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }
</script>
{% endblock %}