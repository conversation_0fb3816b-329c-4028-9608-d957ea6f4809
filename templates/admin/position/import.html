{% extends "admin/base_admin.html" %}

{% block page_title %}职位信息导入{% endblock %}
{% block page_description %}批量导入职位信息和层级结构{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <a href="{% url 'organizations:admin:excel_template' template_type='position' %}"
       class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center space-x-2">
        <i data-lucide="download" class="w-4 h-4"></i>
        <span>下载模板</span>
    </a>
    <a href="{% url 'organizations:admin:import_history' %}" 
       class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
        <i data-lucide="history" class="w-4 h-4"></i>
        <span>导入历史</span>
    </a>
    <a href="{% url 'organizations:admin:position_list' %}" 
       class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center space-x-2">
        <i data-lucide="arrow-left" class="w-4 h-4"></i>
        <span>返回列表</span>
    </a>
</div>
{% endblock %}

{% block admin_content %}
<div class="max-w-4xl mx-auto space-y-6">
    <!-- 注意事项 -->
    <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
        <div class="flex">
            <i data-lucide="alert-triangle" class="w-5 h-5 text-yellow-400 mr-2"></i>
            <div class="text-sm text-yellow-700">
                <p class="font-medium">导入注意事项</p>
                <ul class="mt-1 list-disc list-inside space-y-1">
                    <li>部门、职位、编码为必填项，编码在同一部门内必须唯一</li>
                    <li>部门名称必须是系统中已存在的部门名称</li>
                    <li>是否管理岗和是否部门主管填写"是"或"否"，不填默认为"否"</li>
                    <li>职位级别为1-9级，1-4级员工，5级副主管，6级正主管，7级副经理，8级正经理，9级领导班子</li>
                    <li>备注字段为可选项，可以填写职位职责或其他说明信息</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 导入步骤指引 -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="list-checks" class="w-5 h-5 mr-2 text-gray-500"></i>
                导入步骤
            </h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <span class="text-blue-600 font-semibold">1</span>
                    </div>
                    <h4 class="font-medium text-gray-900 mb-2">下载模板</h4>
                    <p class="text-sm text-gray-500">点击"下载模板"获取标准格式的Excel文件</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <span class="text-blue-600 font-semibold">2</span>
                    </div>
                    <h4 class="font-medium text-gray-900 mb-2">填写数据</h4>
                    <p class="text-sm text-gray-500">按照模板格式填写职位信息，注意必填项</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <span class="text-blue-600 font-semibold">3</span>
                    </div>
                    <h4 class="font-medium text-gray-900 mb-2">上传导入</h4>
                    <p class="text-sm text-gray-500">选择文件并点击导入，系统会自动处理</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 文件上传区域 -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="upload" class="w-5 h-5 mr-2 text-gray-500"></i>
                文件上传
            </h3>
        </div>
        <div class="p-6">
            <form id="positionImportForm" enctype="multipart/form-data">
                {% csrf_token %}
                
                <!-- 拖拽上传区域 -->
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors" id="dropZone">
                    <div class="flex flex-col items-center">
                        <i data-lucide="briefcase" class="w-12 h-12 text-gray-400 mb-4"></i>
                        <p class="text-lg font-medium text-gray-900 mb-2">选择或拖拽职位Excel文件</p>
                        <p class="text-sm text-gray-500 mb-4">支持.xlsx和.xls格式，文件大小不超过5MB</p>
                        
                        <div class="flex space-x-4">
                            <label for="fileInput" class="cursor-pointer px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center space-x-2">
                                <i data-lucide="folder-open" class="w-4 h-4"></i>
                                <span>选择文件</span>
                            </label>
                            <input type="file" id="fileInput" name="file" accept=".xlsx,.xls" class="hidden">
                            
                            <a href="{% url 'organizations:admin:excel_template' template_type='position' %}"
                               class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 flex items-center space-x-2">
                                <i data-lucide="download" class="w-4 h-4"></i>
                                <span>下载模板</span>
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- 文件信息显示 -->
                <div id="fileInfo" class="mt-4 hidden">
                    <div class="bg-gray-50 rounded-md p-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <i data-lucide="file-text" class="w-5 h-5 text-gray-400 mr-2"></i>
                                <span id="fileName" class="text-sm font-medium text-gray-900"></span>
                                <span id="fileSize" class="text-sm text-gray-500 ml-2"></span>
                            </div>
                            <button type="button" id="removeFile" class="text-red-600 hover:text-red-800">
                                <i data-lucide="x" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 操作按钮 -->
                <div class="mt-6 flex space-x-4">
                    <button type="button" id="previewBtn" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 flex items-center space-x-2" disabled>
                        <i data-lucide="eye" class="w-4 h-4"></i>
                        <span>预览数据</span>
                    </button>
                    <button type="button" id="importBtn" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2" disabled>
                        <i data-lucide="upload" class="w-4 h-4"></i>
                        <span>开始导入</span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 进度显示区域 -->
    <div id="progressSection" class="bg-white rounded-lg shadow hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="activity" class="w-5 h-5 mr-2 text-gray-500"></i>
                导入进度
            </h3>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-700">处理进度</span>
                    <span id="progressText" class="text-sm text-gray-500">准备中...</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div id="progressBar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 结果显示区域 -->
    <div id="resultSection" class="hidden">
        <!-- 成功结果 -->
        <div id="successResult" class="bg-white rounded-lg shadow hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i data-lucide="check-circle" class="w-5 h-5 mr-2 text-green-500"></i>
                    导入成功
                </h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-3 gap-4 mb-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600" id="totalCount">0</div>
                        <div class="text-sm text-gray-500">总记录数</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600" id="successCount">0</div>
                        <div class="text-sm text-gray-500">成功导入</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-red-600" id="errorCount">0</div>
                        <div class="text-sm text-gray-500">导入失败</div>
                    </div>
                </div>
                <div class="flex space-x-4">
                    <a href="{% url 'organizations:admin:position_list' %}" 
                       class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
                        <i data-lucide="list" class="w-4 h-4"></i>
                        <span>查看职位列表</span>
                    </a>
                    <button type="button" id="importAgainBtn" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 flex items-center space-x-2">
                        <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                        <span>重新导入</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 错误结果 -->
        <div id="errorResult" class="bg-white rounded-lg shadow hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i data-lucide="alert-circle" class="w-5 h-5 mr-2 text-red-500"></i>
                    导入错误
                </h3>
            </div>
            <div class="p-6">
                <div id="errorList" class="space-y-2 mb-4">
                    <!-- 错误信息将在这里显示 -->
                </div>
                <button type="button" id="retryBtn" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 flex items-center space-x-2">
                    <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                    <span>重新尝试</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 最近导入历史 -->
    {% if recent_imports %}
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="clock" class="w-5 h-5 mr-2 text-gray-500"></i>
                最近导入记录
            </h3>
        </div>
        <div class="p-6">
            <div class="space-y-3">
                {% for import_record in recent_imports %}
                <div class="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                    <div class="flex items-center space-x-3">
                        <div class="w-2 h-2 rounded-full {% if import_record.status == 'completed' %}bg-green-500{% elif import_record.status == 'partial' %}bg-yellow-500{% else %}bg-red-500{% endif %}"></div>
                        <span class="text-sm font-medium text-gray-900">{{ import_record.filename }}</span>
                        <span class="text-xs text-gray-500">{{ import_record.created_at|date:"Y-m-d H:i" }}</span>
                    </div>
                    <div class="text-sm text-gray-500">
                        成功: {{ import_record.success_count }} / 失败: {{ import_record.error_count }}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- 消息提示区域 -->
<div id="messageContainer" class="fixed top-4 right-4 z-50 space-y-2">
    <!-- 消息将通过JavaScript动态添加 -->
</div>
{% endblock %}

{% block extra_js %}
<script>
let selectedFile = null;

document.addEventListener('DOMContentLoaded', function() {
    initializeFileUpload();
    initializeButtons();
});

function initializeFileUpload() {
    const dropZone = document.getElementById('dropZone');
    const fileInput = document.getElementById('fileInput');

    // 拖拽事件
    dropZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        dropZone.classList.add('border-green-400', 'bg-green-50');
    });

    dropZone.addEventListener('dragleave', function(e) {
        e.preventDefault();
        dropZone.classList.remove('border-green-400', 'bg-green-50');
    });

    dropZone.addEventListener('drop', function(e) {
        e.preventDefault();
        dropZone.classList.remove('border-green-400', 'bg-green-50');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelect(files[0]);
        }
    });

    // 文件选择事件
    fileInput.addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
            handleFileSelect(e.target.files[0]);
        }
    });

    // 移除文件按钮
    document.getElementById('removeFile').addEventListener('click', function() {
        clearFileSelection();
    });
}

function handleFileSelect(file) {
    // 验证文件类型
    if (!file.name.match(/\.(xlsx|xls)$/i)) {
        showMessage('请选择Excel文件（.xlsx或.xls格式）', 'error');
        return;
    }

    // 验证文件大小（5MB限制）
    if (file.size > 5 * 1024 * 1024) {
        showMessage('文件大小不能超过5MB', 'error');
        return;
    }

    selectedFile = file;

    // 显示文件信息
    document.getElementById('fileName').textContent = file.name;
    document.getElementById('fileSize').textContent = formatFileSize(file.size);
    document.getElementById('fileInfo').classList.remove('hidden');

    // 启用按钮
    document.getElementById('previewBtn').disabled = false;
    document.getElementById('importBtn').disabled = false;

    showMessage('文件选择成功', 'success');
}

function clearFileSelection() {
    selectedFile = null;
    document.getElementById('fileInput').value = '';
    document.getElementById('fileInfo').classList.add('hidden');

    // 禁用按钮
    document.getElementById('previewBtn').disabled = true;
    document.getElementById('importBtn').disabled = true;

    // 隐藏结果区域
    document.getElementById('resultSection').classList.add('hidden');
    document.getElementById('progressSection').classList.add('hidden');
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function initializeButtons() {
    // 预览按钮
    document.getElementById('previewBtn').addEventListener('click', function() {
        if (!selectedFile) {
            showMessage('请先选择文件', 'warning');
            return;
        }
        showMessage('预览功能开发中...', 'info');
    });

    // 导入按钮
    document.getElementById('importBtn').addEventListener('click', function() {
        startImport();
    });

    // 重新导入按钮
    document.getElementById('importAgainBtn').addEventListener('click', function() {
        clearFileSelection();
        document.getElementById('resultSection').classList.add('hidden');
    });

    // 重试按钮
    document.getElementById('retryBtn').addEventListener('click', function() {
        startImport();
    });
}

function startImport() {
    if (!selectedFile) {
        showMessage('请先选择文件', 'warning');
        return;
    }

    // 显示进度区域
    document.getElementById('progressSection').classList.remove('hidden');

    // 禁用表单
    document.getElementById('importBtn').disabled = true;
    document.getElementById('previewBtn').disabled = true;

    // 创建FormData
    const formData = new FormData(document.getElementById('positionImportForm'));
    formData.append('file', selectedFile);

    // 开始导入
    fetch('{% url "organizations:admin:position_import" %}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            handleImportSuccess(data);
        } else {
            handleImportError(data);
        }
    })
    .catch(error => {
        console.error('导入失败:', error);
        handleImportError({
            error: '网络错误，请重试',
            message: '导入过程中发生网络错误'
        });
    });
}

function handleImportSuccess(data) {
    // 隐藏进度区域
    document.getElementById('progressSection').classList.add('hidden');

    // 显示结果区域
    document.getElementById('resultSection').classList.remove('hidden');
    document.getElementById('successResult').classList.remove('hidden');
    document.getElementById('errorResult').classList.add('hidden');

    // 更新统计数据
    document.getElementById('totalCount').textContent = data.total_rows || 0;
    document.getElementById('successCount').textContent = data.success_count || 0;
    document.getElementById('errorCount').textContent = data.error_count || 0;

    // 显示错误信息（如果有）
    if (data.errors && data.errors.length > 0) {
        const errorList = document.getElementById('errorList');
        errorList.innerHTML = '';
        data.errors.forEach(error => {
            const errorItem = document.createElement('div');
            errorItem.className = 'text-sm text-red-600 bg-red-50 p-2 rounded';
            errorItem.textContent = error;
            errorList.appendChild(errorItem);
        });
    }

    showMessage(data.message || '导入完成', 'success');

    // 重新启用按钮
    document.getElementById('importBtn').disabled = false;
    document.getElementById('previewBtn').disabled = false;
}

function handleImportError(data) {
    // 隐藏进度区域
    document.getElementById('progressSection').classList.add('hidden');

    // 显示结果区域
    document.getElementById('resultSection').classList.remove('hidden');
    document.getElementById('successResult').classList.add('hidden');
    document.getElementById('errorResult').classList.remove('hidden');

    // 显示错误信息
    const errorList = document.getElementById('errorList');
    errorList.innerHTML = '';

    if (data.errors && Array.isArray(data.errors)) {
        data.errors.forEach(error => {
            const errorItem = document.createElement('div');
            errorItem.className = 'text-sm text-red-600 bg-red-50 p-2 rounded';
            errorItem.textContent = error;
            errorList.appendChild(errorItem);
        });
    } else {
        const errorItem = document.createElement('div');
        errorItem.className = 'text-sm text-red-600 bg-red-50 p-2 rounded';
        errorItem.textContent = data.error || data.message || '导入失败';
        errorList.appendChild(errorItem);
    }

    showMessage(data.message || '导入失败', 'error');

    // 重新启用按钮
    document.getElementById('importBtn').disabled = false;
    document.getElementById('previewBtn').disabled = false;
}

function showMessage(message, type = 'info') {
    const container = document.getElementById('messageContainer');
    const messageDiv = document.createElement('div');

    const typeClasses = {
        'success': 'bg-green-100 border-green-400 text-green-700',
        'error': 'bg-red-100 border-red-400 text-red-700',
        'warning': 'bg-yellow-100 border-yellow-400 text-yellow-700',
        'info': 'bg-blue-100 border-blue-400 text-blue-700'
    };

    messageDiv.className = `border-l-4 p-4 rounded shadow-md ${typeClasses[type] || typeClasses.info}`;
    messageDiv.innerHTML = `
        <div class="flex items-center justify-between">
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-current opacity-70 hover:opacity-100">
                <i data-lucide="x" class="w-4 h-4"></i>
            </button>
        </div>
    `;

    container.appendChild(messageDiv);

    // 自动移除消息
    setTimeout(() => {
        if (messageDiv.parentElement) {
            messageDiv.remove();
        }
    }, 5000);

    // 重新初始化Lucide图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
}
</script>
{% endblock %}
