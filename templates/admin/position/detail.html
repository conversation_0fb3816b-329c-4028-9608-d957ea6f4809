{% extends "admin/base_admin.html" %}
{% load static %}

{% block page_title %}职位详情{% endblock %}
{% block page_description %}{{ object.name }} - {{ object.department.name }}{% endblock %}

{% block header_actions %}
<div class="flex space-x-3">
    <a href="{% url 'organizations:admin:position_update' object.pk %}" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
        <i data-lucide="edit" class="w-4 h-4"></i>
        <span>编辑职位</span>
    </a>
    <a href="{% url 'organizations:admin:position_list' %}" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 flex items-center space-x-2">
        <i data-lucide="arrow-left" class="w-4 h-4"></i>
        <span>返回列表</span>
    </a>
</div>
{% endblock %}

{% block admin_content %}
<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- 基本信息 -->
    <div class="lg:col-span-2">
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">基本信息</h3>
            </div>
            <div class="p-6 space-y-6">
                <!-- 职位名称和编码 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">职位名称</label>
                        <div class="flex items-center space-x-3">
                            {% if object.is_management %}
                                <div class="p-2 bg-purple-100 rounded-lg">
                                    <i data-lucide="crown" class="w-5 h-5 text-purple-600"></i>
                                </div>
                            {% else %}
                                <div class="p-2 bg-blue-100 rounded-lg">
                                    <i data-lucide="briefcase" class="w-5 h-5 text-blue-600"></i>
                                </div>
                            {% endif %}
                            <div>
                                <p class="text-lg font-semibold text-gray-900">{{ object.name }}</p>
                                {% if object.is_management %}
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        管理岗位
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">职位编码</label>
                        <p class="text-lg text-gray-900 font-mono bg-gray-50 px-3 py-2 rounded-md">{{ object.position_code }}</p>
                    </div>
                </div>

                <!-- 所属部门和职位级别 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">所属部门</label>
                        <div class="flex items-center space-x-3">
                            <div class="p-2 bg-green-100 rounded-lg">
                                <i data-lucide="building" class="w-5 h-5 text-green-600"></i>
                            </div>
                            <div>
                                <p class="text-lg font-semibold text-gray-900">{{ object.department.name }}</p>
                                <p class="text-sm text-gray-500">{{ object.department.dept_code }}</p>
                            </div>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">职位级别</label>
                        <div class="flex items-center space-x-3">
                            <span class="inline-flex items-center px-4 py-2 rounded-full text-lg font-medium
                                {% if object.level >= 9 %}bg-red-100 text-red-800
                                {% elif object.level >= 7 %}bg-purple-100 text-purple-800
                                {% elif object.level >= 5 %}bg-blue-100 text-blue-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ object.level }}级
                            </span>
                            <span class="text-sm text-gray-500">{{ object.get_level_display }}</span>
                        </div>
                    </div>
                </div>

                <!-- 职位描述 -->
                {% if object.description %}
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">职位描述</label>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <p class="text-gray-800 leading-relaxed">{{ object.description }}</p>
                    </div>
                </div>
                {% endif %}

                <!-- 状态信息 -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">启用状态</label>
                        <div class="flex items-center space-x-2">
                            {% if object.is_active %}
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                    <i data-lucide="check-circle" class="w-4 h-4 mr-1"></i>
                                    已启用
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                                    <i data-lucide="x-circle" class="w-4 h-4 mr-1"></i>
                                    已禁用
                                </span>
                            {% endif %}
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">排序</label>
                        <p class="text-lg text-gray-900">{{ object.sort_order }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">创建时间</label>
                        <p class="text-sm text-gray-600">{{ object.created_at|date:"Y-m-d H:i:s" }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 在职员工列表 -->
        <div class="bg-white rounded-lg shadow mt-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">在职员工</h3>
                    <span class="text-sm text-gray-500">共 {{ staff_list|length }} 人</span>
                </div>
            </div>
            <div class="p-6">
                {% if staff_list %}
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {% for staff in staff_list %}
                        <div class="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                <span class="text-sm font-medium">{{ staff.name|first }}</span>
                            </div>
                            <div class="flex-1">
                                <p class="font-medium text-gray-900">{{ staff.name }}</p>
                                <p class="text-sm text-gray-500">{{ staff.employee_no }}</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-medium text-gray-900">{{ staff.get_role_display }}</p>
                                <p class="text-xs text-gray-500">{{ staff.hire_date|date:"Y-m-d" }}</p>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-8">
                        <i data-lucide="users" class="mx-auto h-12 w-12 text-gray-400"></i>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">暂无在职员工</h3>
                        <p class="mt-1 text-sm text-gray-500">该职位目前没有分配员工。</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 右侧统计信息 -->
    <div class="space-y-6">
        <!-- 统计卡片 -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">统计信息</h3>
            </div>
            <div class="p-6 space-y-4">
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">在职人数</span>
                    <span class="text-2xl font-bold text-blue-600">{{ staff_list|length }}</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">职位级别</span>
                    <span class="text-lg font-semibold text-gray-900">{{ object.level }}级</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">管理岗位</span>
                    <span class="text-sm font-medium {% if object.is_management %}text-green-600{% else %}text-gray-400{% endif %}">
                        {% if object.is_management %}是{% else %}否{% endif %}
                    </span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">部门经理</span>
                    <span class="text-sm font-medium {% if object.is_department_manager %}text-green-600{% else %}text-gray-400{% endif %}">
                        {% if object.is_department_manager %}是{% else %}否{% endif %}
                    </span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">启用状态</span>
                    <span class="text-sm font-medium {% if object.is_active %}text-green-600{% else %}text-red-600{% endif %}">
                        {% if object.is_active %}启用{% else %}禁用{% endif %}
                    </span>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">快速操作</h3>
            </div>
            <div class="p-6 space-y-3">
                <a href="{% url 'organizations:admin:position_update' object.pk %}" class="w-full flex items-center px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
                    <i data-lucide="edit" class="w-4 h-4 mr-2 text-gray-500"></i>
                    <span>编辑职位信息</span>
                </a>
                <button onclick="showNotification('功能开发中', 'info')" class="w-full flex items-center px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
                    <i data-lucide="users" class="w-4 h-4 mr-2 text-gray-500"></i>
                    <span>管理员工分配</span>
                </button>
                <button onclick="showNotification('功能开发中', 'info')" class="w-full flex items-center px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
                    <i data-lucide="file-text" class="w-4 h-4 mr-2 text-gray-500"></i>
                    <span>导出职位报告</span>
                </button>
                <button onclick="confirmDelete()" class="w-full flex items-center px-4 py-2 border border-red-300 text-red-600 rounded-md hover:bg-red-50">
                    <i data-lucide="trash-2" class="w-4 h-4 mr-2"></i>
                    <span>删除职位</span>
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete() {
    if (confirm('确定要删除职位 "{{ object.name }}" 吗？此操作不可撤销。')) {
        window.location.href = '{% url "organizations:admin:position_delete" object.pk %}';
    }
}

// 页面加载完成后初始化图标
document.addEventListener('DOMContentLoaded', function() {
    if (typeof lucide !== 'undefined' && lucide.createIcons) {
        lucide.createIcons();
    }
});
</script>
{% endblock %}