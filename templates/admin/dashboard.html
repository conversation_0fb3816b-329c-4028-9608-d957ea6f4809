{% extends "admin/base_admin.html" %}

{% block page_title %}仪表板{% endblock %}
{% block page_description %}欢迎回来，查看最新的考评数据{% endblock %}

{% block admin_content %}
<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="p-3 bg-blue-100 rounded-lg">
                <i data-lucide="users" class="w-6 h-6 text-blue-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">总员工数</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.total_staff|default:0 }}</p>
                <p class="text-xs text-green-600 mt-1">↑ {{ stats.today_new_staff|default:0 }} 今日新增</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="p-3 bg-green-100 rounded-lg">
                <i data-lucide="shield-check" class="w-6 h-6 text-green-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">安全编号</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.secure_codes|default:'-' }}</p>
                <p class="text-xs text-green-600 mt-1">{{ stats.secure_percentage|default:'-' }}% 已升级</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="p-3 bg-yellow-100 rounded-lg">
                <i data-lucide="star" class="w-6 h-6 text-yellow-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">平均评分</p>
                <p class="text-2xl font-bold text-gray-900">4.2</p>
                <p class="text-xs text-green-600 mt-1">↑ 0.3 较上月</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
            <div class="p-3 bg-purple-100 rounded-lg">
                <i data-lucide="calendar" class="w-6 h-6 text-purple-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">活跃批次</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.active_batches|default:0 }}</p>
                <p class="text-xs text-blue-600 mt-1">{{ stats.total_departments|default:0 }}个部门</p>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Analytics -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- Department Distribution Pie Chart -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i data-lucide="pie-chart" class="w-5 h-5 mr-2 text-gray-500"></i>
                    部门人员分布
                </h3>
                <button id="refresh-dept-chart" class="text-sm text-blue-600 hover:text-blue-800 transition-colors">
                    <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                </button>
            </div>
        </div>
        <div class="p-6">
            <div id="department-distribution-chart" style="height: 300px;"></div>
        </div>
    </div>

    <!-- Activity Trend Line Chart -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i data-lucide="trending-up" class="w-5 h-5 mr-2 text-gray-500"></i>
                    30天活动趋势
                </h3>
                <div class="flex items-center space-x-2">
                    <select id="trend-metric-select" class="text-sm border border-gray-300 rounded px-2 py-1">
                        <option value="evaluation_records">评价记录</option>
                        <option value="login_activity">登录活动</option>
                    </select>
                    <button id="refresh-trend-chart" class="text-sm text-blue-600 hover:text-blue-800 transition-colors">
                        <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                    </button>
                </div>
            </div>
        </div>
        <div class="p-6">
            <div id="activity-trend-chart" style="height: 300px;"></div>
        </div>
    </div>
</div>

<!-- Evaluation Progress Bar Chart -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="bar-chart-3" class="w-5 h-5 mr-2 text-gray-500"></i>
                考评批次进度
            </h3>
            <div class="flex items-center space-x-2">
                <a href="{% url 'evaluations:admin:progress_list' %}" class="text-sm text-blue-600 hover:text-blue-800">查看详情</a>
                <button id="refresh-progress-chart" class="text-sm text-blue-600 hover:text-blue-800 transition-colors">
                    <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                </button>
            </div>
        </div>
    </div>
    <div class="p-6">
        <div id="evaluation-progress-chart" style="height: 400px;"></div>
    </div>
</div>

<!-- Quick Actions -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900 flex items-center">
            <i data-lucide="zap" class="w-5 h-5 mr-2 text-gray-500"></i>
            快速操作
        </h3>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4">
            <!-- 考评管理 -->
            <button onclick="window.location.href='{% url 'evaluations:admin:template_create' %}'" class="flex flex-col items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-400 hover:bg-blue-50 transition-all duration-200 group">
                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mb-2 group-hover:bg-blue-200">
                    <i data-lucide="file-text" class="w-5 h-5 text-blue-600"></i>
                </div>
                <p class="text-xs font-medium text-gray-900">创建模板</p>
                <p class="text-xs text-gray-500 mt-1">新建考评模板</p>
            </button>

            <button onclick="window.location.href='{% url 'evaluations:admin:batch_create' %}'" class="flex flex-col items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-green-400 hover:bg-green-50 transition-all duration-200 group">
                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mb-2 group-hover:bg-green-200">
                    <i data-lucide="calendar-plus" class="w-5 h-5 text-green-600"></i>
                </div>
                <p class="text-xs font-medium text-gray-900">创建批次</p>
                <p class="text-xs text-gray-500 mt-1">开始新考评</p>
            </button>

            <button onclick="window.location.href='{% url 'evaluations:admin:progress_list' %}'" class="flex flex-col items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-purple-400 hover:bg-purple-50 transition-all duration-200 group">
                <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mb-2 group-hover:bg-purple-200">
                    <i data-lucide="activity" class="w-5 h-5 text-purple-600"></i>
                </div>
                <p class="text-xs font-medium text-gray-900">进度监控</p>
                <p class="text-xs text-gray-500 mt-1">查看考评进度</p>
            </button>

            <!-- 组织管理 -->
            <button onclick="window.location.href='{% url 'organizations:admin:staff_create' %}'" class="flex flex-col items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-yellow-400 hover:bg-yellow-50 transition-all duration-200 group">
                <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center mb-2 group-hover:bg-yellow-200">
                    <i data-lucide="user-plus" class="w-5 h-5 text-yellow-600"></i>
                </div>
                <p class="text-xs font-medium text-gray-900">添加员工</p>
                <p class="text-xs text-gray-500 mt-1">录入新员工</p>
            </button>

            <button onclick="window.location.href='{% url 'organizations:admin:department_create' %}'" class="flex flex-col items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-indigo-400 hover:bg-indigo-50 transition-all duration-200 group">
                <div class="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center mb-2 group-hover:bg-indigo-200">
                    <i data-lucide="building-2" class="w-5 h-5 text-indigo-600"></i>
                </div>
                <p class="text-xs font-medium text-gray-900">添加部门</p>
                <p class="text-xs text-gray-500 mt-1">新建部门</p>
            </button>

            <!-- 安全管理快捷操作 -->
            <button onclick="window.location.href='{% url 'organizations:admin:permissions_manage' %}'" class="flex flex-col items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-purple-400 hover:bg-purple-50 transition-all duration-200 group">
                <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mb-2 group-hover:bg-purple-200">
                    <i data-lucide="shield-check" class="w-5 h-5 text-purple-600"></i>
                </div>
                <p class="text-xs font-medium text-gray-900">权限管理</p>
                <p class="text-xs text-gray-500 mt-1">配置权限</p>
            </button>

            <button onclick="window.location.href='{% url 'organizations:admin:anonymous_codes_manage' %}'" class="flex flex-col items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-emerald-400 hover:bg-emerald-50 transition-all duration-200 group">
                <div class="w-10 h-10 bg-emerald-100 rounded-lg flex items-center justify-center mb-2 group-hover:bg-emerald-200">
                    <i data-lucide="key" class="w-5 h-5 text-emerald-600"></i>
                </div>
                <p class="text-xs font-medium text-gray-900">匿名编号</p>
                <p class="text-xs text-gray-500 mt-1">管理编号</p>
            </button>

            <!-- 系统设置 -->
            <button onclick="window.location.href='{% url 'evaluations:admin:rule_list' %}'" class="flex flex-col items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-red-400 hover:bg-red-50 transition-all duration-200 group">
                <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mb-2 group-hover:bg-red-200">
                    <i data-lucide="settings" class="w-5 h-5 text-red-600"></i>
                </div>
                <p class="text-xs font-medium text-gray-900">权重规则</p>
                <p class="text-xs text-gray-500 mt-1">配置权重</p>
            </button>

            <!-- 第二行 -->
            <button onclick="window.location.href='{% url 'reports:admin:report_list' %}'" class="flex flex-col items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-cyan-400 hover:bg-cyan-50 transition-all duration-200 group">
                <div class="w-10 h-10 bg-cyan-100 rounded-lg flex items-center justify-center mb-2 group-hover:bg-cyan-200">
                    <i data-lucide="bar-chart-3" class="w-5 h-5 text-cyan-600"></i>
                </div>
                <p class="text-xs font-medium text-gray-900">考评报告</p>
                <p class="text-xs text-gray-500 mt-1">生成报告</p>
            </button>

            <button onclick="window.location.href='{% url 'reports:admin:talent_assessment_list' %}'" class="flex flex-col items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-pink-400 hover:bg-pink-50 transition-all duration-200 group">
                <div class="w-10 h-10 bg-pink-100 rounded-lg flex items-center justify-center mb-2 group-hover:bg-pink-200">
                    <i data-lucide="target" class="w-5 h-5 text-pink-600"></i>
                </div>
                <p class="text-xs font-medium text-gray-900">人才盘点</p>
                <p class="text-xs text-gray-500 mt-1">人才分析</p>
            </button>

            <button onclick="window.location.href='{% url 'reports:admin:analytics' %}'" class="flex flex-col items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-teal-400 hover:bg-teal-50 transition-all duration-200 group">
                <div class="w-10 h-10 bg-teal-100 rounded-lg flex items-center justify-center mb-2 group-hover:bg-teal-200">
                    <i data-lucide="trending-up" class="w-5 h-5 text-teal-600"></i>
                </div>
                <p class="text-xs font-medium text-gray-900">数据分析</p>
                <p class="text-xs text-gray-500 mt-1">统计分析</p>
            </button>

            <button onclick="window.location.href='{% url 'evaluations:admin:batch_list' %}'" class="flex flex-col items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-orange-400 hover:bg-orange-50 transition-all duration-200 group">
                <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mb-2 group-hover:bg-orange-200">
                    <i data-lucide="calendar" class="w-5 h-5 text-orange-600"></i>
                </div>
                <p class="text-xs font-medium text-gray-900">批次管理</p>
                <p class="text-xs text-gray-500 mt-1">管理批次</p>
            </button>

            <button onclick="window.location.href='{% url 'organizations:admin:staff_list' %}'" class="flex flex-col items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-lime-400 hover:bg-lime-50 transition-all duration-200 group">
                <div class="w-10 h-10 bg-lime-100 rounded-lg flex items-center justify-center mb-2 group-hover:bg-lime-200">
                    <i data-lucide="users" class="w-5 h-5 text-lime-600"></i>
                </div>
                <p class="text-xs font-medium text-gray-900">员工管理</p>
                <p class="text-xs text-gray-500 mt-1">管理员工</p>
            </button>

            <button onclick="window.location.href='{% url 'evaluations:anonymous:home' %}'" class="flex flex-col items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-violet-400 hover:bg-violet-50 transition-all duration-200 group">
                <div class="w-10 h-10 bg-violet-100 rounded-lg flex items-center justify-center mb-2 group-hover:bg-violet-200">
                    <i data-lucide="shield" class="w-5 h-5 text-violet-600"></i>
                </div>
                <p class="text-xs font-medium text-gray-900">匿名考评</p>
                <p class="text-xs text-gray-500 mt-1">进行评价</p>
            </button>
        </div>
    </div>
</div>

<!-- Security Status Overview -->
<div class="mt-8 bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900 flex items-center">
            <i data-lucide="shield" class="w-5 h-5 mr-2 text-gray-500"></i>
            安全状态概览
        </h3>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="flex items-center justify-between p-4 bg-green-50 rounded-lg">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <i data-lucide="check" class="w-4 h-4 text-green-600"></i>
                    </div>
                    <div>
                        <p class="font-medium text-gray-900">JWT认证系统</p>
                        <p class="text-sm text-gray-500">已启用并正常运行</p>
                    </div>
                </div>
                <span class="text-sm text-green-600 font-medium">正常</span>
            </div>
            
            <div class="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <i data-lucide="shield-check" class="w-4 h-4 text-blue-600"></i>
                    </div>
                    <div>
                        <p class="font-medium text-gray-900">RBAC权限控制</p>
                        <p class="text-sm text-gray-500">7种角色，50+权限</p>
                    </div>
                </div>
                <span class="text-sm text-blue-600 font-medium">活跃</span>
            </div>
            
            <div class="flex items-center justify-between p-4 bg-yellow-50 rounded-lg">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                        <i data-lucide="key" class="w-4 h-4 text-yellow-600"></i>
                    </div>
                    <div>
                        <p class="font-medium text-gray-900">匿名编号加密</p>
                        <p class="text-sm text-gray-500">SHA256多层加密</p>
                    </div>
                </div>
                <span class="text-sm text-yellow-600 font-medium">迁移中</span>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="mt-8 bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900 flex items-center">
            <i data-lucide="activity" class="w-5 h-5 mr-2 text-gray-500"></i>
            最近活动
        </h3>
    </div>
    <div class="p-6">
        {% if recent_activities %}
            <div class="space-y-4">
                {% for activity in recent_activities %}
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                            <i data-lucide="user-plus" class="w-4 h-4 text-blue-600"></i>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm text-gray-900">
                                <span class="font-medium">{{ activity.user|default:"系统" }}</span> {{ activity.description|truncatechars:100 }}
                            </p>
                            <p class="text-xs text-gray-500 mt-1">{{ activity.created_at|timesince }}前</p>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="space-y-4">
                <div class="flex items-start space-x-3">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                        <i data-lucide="user-plus" class="w-4 h-4 text-blue-600"></i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-900">
                            系统管理员 成功登录系统
                        </p>
                        <p class="text-xs text-gray-500 mt-1">刚刚</p>
                    </div>
                </div>

                <div class="flex items-start space-x-3">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                        <i data-lucide="calendar" class="w-4 h-4 text-green-600"></i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-900">
                            系统初始化完成，数据库连接正常
                        </p>
                        <p class="text-xs text-gray-500 mt-1">5分钟前</p>
                    </div>
                </div>

                <div class="flex items-start space-x-3">
                    <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center flex-shrink-0">
                        <i data-lucide="building" class="w-4 h-4 text-yellow-600"></i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-900">
                            考评系统启动，等待创建第一个考评批次
                        </p>
                        <p class="text-xs text-gray-500 mt-1">10分钟前</p>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 图表数据和功能
    class DashboardCharts {
        constructor() {
            this.charts = {};
            this.apiBaseUrl = '/reports/admin/api/charts/';
        }

        async init() {
            await this.loadDepartmentDistribution();
            await this.loadActivityTrend();
            await this.loadEvaluationProgress();
            this.bindEvents();
        }

        async loadDepartmentDistribution() {
            const url = this.apiBaseUrl + 'dashboard-overview/';
            
            await chartManager.loadDataAndCreateChart(
                url,
                'department-distribution-chart',
                (containerId, data) => {
                    if (data.departments && data.departments.length > 0) {
                        return chartManager.createPieChart(containerId, data.departments, {
                            title: '',
                            seriesName: '人员分布',
                            radius: '70%',
                            center: ['50%', '50%'],
                            labelFormatter: '{b}: {c}人'
                        });
                    }
                    return null;
                }
            );
        }

        async loadActivityTrend(metric = 'evaluation_records') {
            const url = this.apiBaseUrl + 'activity-trend/';
            
            await chartManager.loadDataAndCreateChart(
                url,
                'activity-trend-chart',
                (containerId, data) => {
                    if (data.data && data.data.length > 0) {
                        const categories = data.data.map(item => item.label);
                        const values = data.data.map(item => item.value);
                        
                        return chartManager.createLineChart(containerId, categories, [{
                            name: metric === 'evaluation_records' ? '评价记录' : '登录用户',
                            data: values,
                            color: '#3b82f6'
                        }], {
                            title: '',
                            showArea: true
                        });
                    }
                    return null;
                },
                { metric: metric, days: 30 }
            );
        }

        async loadEvaluationProgress() {
            const url = this.apiBaseUrl + 'evaluation-progress/';
            
            await chartManager.loadDataAndCreateChart(
                url,
                'evaluation-progress-chart',
                (containerId, data) => {
                    if (data.batches && data.batches.length > 0) {
                        const categories = data.batches.map(batch => batch.name);
                        const completedData = data.batches.map(batch => batch.completed);
                        const totalData = data.batches.map(batch => batch.total - batch.completed);
                        
                        return chartManager.createBarChart(containerId, categories, [
                            {
                                name: '已完成',
                                data: completedData,
                                color: '#10b981'
                            },
                            {
                                name: '待完成',
                                data: totalData,
                                color: '#f59e0b'
                            }
                        ], {
                            title: '',
                            legendTop: '5%'
                        });
                    }
                    return null;
                }
            );
        }

        bindEvents() {
            // 刷新按钮事件
            document.getElementById('refresh-dept-chart')?.addEventListener('click', () => {
                this.loadDepartmentDistribution();
            });

            document.getElementById('refresh-trend-chart')?.addEventListener('click', () => {
                const metric = document.getElementById('trend-metric-select').value;
                this.loadActivityTrend(metric);
            });

            document.getElementById('refresh-progress-chart')?.addEventListener('click', () => {
                this.loadEvaluationProgress();
            });

            // 趋势图指标切换
            document.getElementById('trend-metric-select')?.addEventListener('change', (e) => {
                this.loadActivityTrend(e.target.value);
            });
        }

        refresh() {
            this.loadDepartmentDistribution();
            this.loadActivityTrend();
            this.loadEvaluationProgress();
        }
    }

    // 页面加载完成后初始化图表
    document.addEventListener('DOMContentLoaded', function() {
        // 等待一小段时间确保所有资源加载完成
        setTimeout(async () => {
            const dashboard = new DashboardCharts();
            await dashboard.init();
            
            // 每5分钟自动刷新图表数据
            setInterval(() => {
                dashboard.refresh();
            }, 300000);
            
            // 保存到全局变量供调试使用
            window.dashboardCharts = dashboard;
        }, 500);

        // 确保图标正确渲染
        lucide.createIcons();
    });

    // 窗口大小改变时重新调整图表
    window.addEventListener('resize', () => {
        chartManager.refreshAllCharts();
    });
</script>
{% endblock %}