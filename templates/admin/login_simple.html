<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - 企业考评评分系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 400px;
        }
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo h1 {
            color: #1f2937;
            font-size: 24px;
            font-weight: 700;
            margin: 0;
        }
        .logo p {
            color: #6b7280;
            font-size: 14px;
            margin: 8px 0 0 0;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #374151;
            font-weight: 500;
            font-size: 14px;
        }
        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .form-group input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        .login-btn {
            width: 100%;
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .login-btn:hover {
            background: #2563eb;
        }
        .login-btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        .message {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        .message.error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        .message.success {
            background: #f0fdf4;
            color: #16a34a;
            border: 1px solid #bbf7d0;
        }
        .alt-login {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
        }
        .alt-login a {
            color: #3b82f6;
            text-decoration: none;
            font-size: 14px;
        }
        .alt-login a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- 消息提示 -->
        {% if messages %}
            {% for message in messages %}
                <div class="message {{ message.tags }}">{{ message }}</div>
            {% endfor %}
        {% endif %}

        <!-- Logo和标题 -->
        <div class="logo">
            <h1>企业考评评分系统</h1>
            <p>管理员登录</p>
        </div>

        <!-- 登录表单 -->
        <form id="login-form">
            <div class="form-group">
                <label for="username">用户名 / 员工编号</label>
                <input type="text" id="username" name="username" required
                       placeholder="请输入用户名或员工编号" value="testadmin">
            </div>

            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" required
                       placeholder="请输入密码" value="test123456">
            </div>

            <button type="submit" class="login-btn" id="login-btn">
                <span id="login-text">立即登录</span>
            </button>

            <button type="button" class="login-btn" id="clear-btn" style="background-color: #dc3545; margin-top: 10px;">
                清除缓存并重新登录
            </button>
        </form>

        <!-- 匿名端入口 -->
        <div class="alt-login">
            <a href="{% url 'organizations:anonymous:anonymous_login' %}">
                匿名考评入口
            </a>
        </div>
    </div>

    <script>
        // 清除缓存功能
        document.getElementById('clear-btn').addEventListener('click', function() {
            // 清除所有localStorage数据
            localStorage.clear();

            // 清除所有sessionStorage数据
            sessionStorage.clear();

            // 显示成功消息
            showMessage('success', '缓存已清除，请重新登录');

            // 清空表单
            document.getElementById('username').value = 'testadmin';
            document.getElementById('password').value = 'test123456';
        });

        // AJAX登录处理
        document.getElementById('login-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const loginBtn = document.getElementById('login-btn');
            const loginText = document.getElementById('login-text');
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            
            // 显示加载状态
            loginBtn.disabled = true;
            loginText.textContent = '登录中...';
            
            // 清除之前的错误消息
            const existingMessages = document.querySelectorAll('.message');
            existingMessages.forEach(msg => msg.remove());
            
            try {
                // 调用登录API
                const response = await fetch('/api/login/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                const data = await response.json();

                console.log('登录响应:', response.status, data);

                if (response.ok && data.success) {
                    // 保存token到localStorage
                    localStorage.setItem('access_token', data.tokens.access_token);
                    localStorage.setItem('refresh_token', data.tokens.refresh_token);
                    localStorage.setItem('user_info', JSON.stringify(data.user));

                    // 同时设置Cookie（用于服务器端认证）
                    setCookie('access_token', data.tokens.access_token, data.tokens.expires_in || 28800);
                    setCookie('refresh_token', data.tokens.refresh_token, 7 * 24 * 3600); // 7天

                    // 显示成功消息
                    showMessage('success', data.message || '登录成功，正在跳转...');

                    // 延迟跳转，让用户看到成功消息
                    setTimeout(() => {
                        window.location.href = '/admin/';
                    }, 500);
                } else {
                    // 显示错误消息
                    console.error('登录失败:', data);
                    showMessage('error', data.message || '登录失败，请重试');

                    // 恢复按钮状态
                    loginBtn.disabled = false;
                    loginText.textContent = '立即登录';
                }
                
            } catch (error) {
                console.error('登录请求失败:', error);
                showMessage('error', '网络错误，请稍后重试');
                
                // 恢复按钮状态
                loginBtn.disabled = false;
                loginText.textContent = '立即登录';
            }
        });

        // 设置Cookie函数
        function setCookie(name, value, maxAge) {
            const secure = window.location.protocol === 'https:' ? '; Secure' : '';
            document.cookie = `${name}=${value}; Max-Age=${maxAge}; Path=/; SameSite=Lax${secure}`;
        }

        // 显示消息函数
        function showMessage(type, text) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = text;
            
            const container = document.querySelector('.login-container');
            const form = document.getElementById('login-form');
            container.insertBefore(messageDiv, form);
        }
    </script>
</body>
</html>