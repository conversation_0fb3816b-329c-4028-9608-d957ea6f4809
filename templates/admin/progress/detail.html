{% extends "admin/base_admin.html" %}

{% block page_title %}{{ object.name }} - 进度详情{% endblock %}
{% block page_description %}查看考评批次的详细进度和参与人员完成情况{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-4">
    <a href="{% url 'evaluations:admin:progress_list' %}" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center space-x-2">
        <i data-lucide="arrow-left" class="w-4 h-4"></i>
        <span>返回列表</span>
    </a>
    <button onclick="refreshProgress()" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center space-x-2">
        <i data-lucide="refresh-cw" class="w-4 h-4"></i>
        <span>刷新数据</span>
    </button>
    <button onclick="exportDetailReport()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
        <i data-lucide="download" class="w-4 h-4"></i>
        <span>导出详细报告</span>
    </button>
</div>
{% endblock %}

{% block admin_content %}
<!-- 批次基本信息 -->
<div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-lg text-white p-6 mb-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold mb-2">{{ object.name }}</h1>
            <p class="text-blue-100 mb-4">{{ object.description|default:"暂无描述" }}</p>
            <div class="flex items-center space-x-6 text-sm">
                <span class="flex items-center">
                    <i data-lucide="calendar" class="w-4 h-4 mr-2"></i>
                    {{ object.start_date|date:"Y年m月d日" }} ~ {{ object.end_date|date:"Y年m月d日" }}
                </span>
                <span class="flex items-center">
                    <i data-lucide="file-text" class="w-4 h-4 mr-2"></i>
                    {{ object.default_template.name }}
                </span>
                <span class="flex items-center">
                    <i data-lucide="clock" class="w-4 h-4 mr-2"></i>
                    剩余：{{ object.end_date|timeuntil }}
                </span>
            </div>
        </div>
        <div class="text-right">
            {% if object.status == 'active' %}
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                    <i data-lucide="play-circle" class="w-4 h-4 mr-1"></i>
                    进行中
                </span>
            {% elif object.status == 'completed' %}
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                    <i data-lucide="check-circle" class="w-4 h-4 mr-1"></i>
                    已完成
                </span>
            {% elif object.status == 'cancelled' %}
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                    <i data-lucide="x-circle" class="w-4 h-4 mr-1"></i>
                    已取消
                </span>
            {% endif %}
        </div>
    </div>
</div>

<!-- 整体进度统计 -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
                <i data-lucide="clipboard-list" class="w-6 h-6 text-blue-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">总任务数</p>
                <p class="text-2xl font-bold text-gray-900" id="totalTasks">{{ progress.total_relations }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
                <i data-lucide="check-circle" class="w-6 h-6 text-green-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">已完成</p>
                <p class="text-2xl font-bold text-gray-900" id="completedTasks">{{ progress.completed_relations }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
                <i data-lucide="clock" class="w-6 h-6 text-yellow-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">待完成</p>
                <p class="text-2xl font-bold text-gray-900" id="pendingTasks">{{ progress.remaining_relations }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
                <i data-lucide="trending-up" class="w-6 h-6 text-purple-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">完成率</p>
                <p class="text-2xl font-bold text-gray-900" id="completionRate">{{ progress.completion_rate|floatformat:1 }}%</p>
            </div>
        </div>
    </div>
</div>

<!-- 进度可视化 -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900 flex items-center">
            <i data-lucide="bar-chart-3" class="w-5 h-5 mr-2 text-gray-500"></i>
            完成进度可视化
        </h3>
    </div>
    <div class="px-6 py-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 总体进度条 -->
            <div>
                <h4 class="text-sm font-medium text-gray-900 mb-4">总体完成进度</h4>
                <div class="mb-2">
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600">完成进度</span>
                        <span class="font-medium">{{ progress.completion_rate|floatformat:1 }}%</span>
                    </div>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-4">
                    <div class="bg-gradient-to-r from-blue-400 to-blue-600 h-4 rounded-full transition-all duration-300 flex items-center justify-end pr-2" 
                         style="width: {{ progress.completion_rate }}%">
                        <span class="text-xs text-white font-medium">{{ progress.completion_rate|floatformat:0 }}%</span>
                    </div>
                </div>
                
                <!-- 分类进度 -->
                <div class="mt-6 space-y-3">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">已完成任务</span>
                        <span class="text-sm font-medium text-green-600">{{ progress.completed_relations }}</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-green-500 h-2 rounded-full" 
                             style="width: {{ progress.completion_rate }}%"></div>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">草稿状态</span>
                        <span class="text-sm font-medium text-yellow-600">{{ draft_count|default:0 }}</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-yellow-500 h-2 rounded-full draft-progress" 
                             data-count="{{ draft_count|default:0 }}" data-total="{{ progress.total_relations }}"></div>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">未开始任务</span>
                        <span class="text-sm font-medium text-gray-600">{{ progress.remaining_relations }}</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-gray-400 h-2 rounded-full remaining-progress" 
                             data-count="{{ progress.remaining_relations }}" data-total="{{ progress.total_relations }}"></div>
                    </div>
                </div>
            </div>
            
            <!-- 部门完成情况 -->
            <div>
                <h4 class="text-sm font-medium text-gray-900 mb-4">各部门完成情况</h4>
                <div class="space-y-4" id="departmentProgress">
                    {% for dept_stat in department_stats %}
                        <div>
                            <div class="flex items-center justify-between mb-1">
                                <span class="text-sm text-gray-700">{{ dept_stat.department }}</span>
                                <span class="text-sm font-medium">{{ dept_stat.completion_rate|floatformat:0 }}%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="h-2 rounded-full transition-all duration-300
                                            {% if dept_stat.completion_rate >= 80 %}bg-green-500
                                            {% elif dept_stat.completion_rate >= 50 %}bg-yellow-500
                                            {% else %}bg-red-500{% endif %}" 
                                     style="width: {{ dept_stat.completion_rate }}%"></div>
                            </div>
                            <div class="flex items-center justify-between text-xs text-gray-500 mt-1">
                                <span>{{ dept_stat.completed }}/{{ dept_stat.total }}</span>
                                <span>{{ dept_stat.participants }} 人参与</span>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- ECharts 图表分析 -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- 部门进度对比柱状图 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i data-lucide="bar-chart-3" class="w-5 h-5 mr-2 text-gray-500"></i>
                    部门进度对比
                </h3>
                <button id="refresh-dept-progress-chart" class="text-sm text-blue-600 hover:text-blue-800 transition-colors">
                    <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                </button>
            </div>
        </div>
        <div class="p-6">
            <div id="department-comparison-chart" style="height: 350px;"></div>
        </div>
    </div>

    <!-- 评价类型进度饼图 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i data-lucide="pie-chart" class="w-5 h-5 mr-2 text-gray-500"></i>
                    评价类型分布
                </h3>
                <button id="refresh-type-progress-chart" class="text-sm text-blue-600 hover:text-blue-800 transition-colors">
                    <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                </button>
            </div>
        </div>
        <div class="p-6">
            <div id="evaluation-type-chart" style="height: 350px;"></div>
        </div>
    </div>
</div>

<!-- 参与人员详细情况 -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="users" class="w-5 h-5 mr-2 text-gray-500"></i>
                参与人员完成情况
            </h3>
            <div class="flex items-center space-x-4">
                <select id="participantFilter" class="border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">所有人员</option>
                    <option value="completed">已完成</option>
                    <option value="in_progress">进行中</option>
                    <option value="not_started">未开始</option>
                </select>
                <select id="deptFilter" class="border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">所有部门</option>
                    {% for dept in departments %}
                        <option value="{{ dept.name }}">{{ dept.name }}</option>
                    {% endfor %}
                </select>
            </div>
        </div>
    </div>
    <div class="px-6 py-6">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200" id="participantsTable">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            评价者
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            部门
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            总任务
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            已完成
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            完成率
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            最后活动
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            状态
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            操作
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for participant in participants %}
                        <tr class="participant-row" 
                            data-status="{% if participant.completion_rate == 100 %}completed{% elif participant.completion_rate > 0 %}in_progress{% else %}not_started{% endif %}"
                            data-department="{{ participant.department }}">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-8 w-8">
                                        <div class="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                                            <i data-lucide="user" class="h-4 w-4 text-gray-500"></i>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">{{ participant.name }}</div>
                                        <div class="text-sm text-gray-500">{{ participant.position }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ participant.department }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ participant.total_tasks }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ participant.completed_tasks }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="h-2 rounded-full {% if participant.completion_rate == 100 %}bg-green-500{% elif participant.completion_rate >= 50 %}bg-yellow-500{% else %}bg-red-500{% endif %}" 
                                             style="width: {{ participant.completion_rate }}%"></div>
                                    </div>
                                    <span class="text-sm font-medium">{{ participant.completion_rate|floatformat:0 }}%</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ participant.last_activity|default:"无活动" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if participant.completion_rate == 100 %}
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        已完成
                                    </span>
                                {% elif participant.completion_rate > 0 %}
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        进行中
                                    </span>
                                {% else %}
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        未开始
                                    </span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center space-x-2">
                                    <button onclick="viewParticipantTasks({{ participant.id }})" 
                                            class="text-blue-600 hover:text-blue-900">
                                        查看任务
                                    </button>
                                    {% if participant.completion_rate < 100 %}
                                        <button onclick="sendIndividualReminder({{ participant.id }})" 
                                                class="text-green-600 hover:text-green-900">
                                            发送提醒
                                        </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 最近活动记录 -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900 flex items-center">
            <i data-lucide="activity" class="w-5 h-5 mr-2 text-gray-500"></i>
            最近活动记录
        </h3>
    </div>
    <div class="px-6 py-6">
        <div class="flow-root">
            <ul class="-mb-8" id="activityFeed">
                {% for activity in recent_activities %}
                    <li>
                        <div class="relative pb-8">
                            {% if not forloop.last %}
                                <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                            {% endif %}
                            <div class="relative flex space-x-3">
                                <div>
                                    <span class="h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white
                                                {% if activity.type == 'completed' %}bg-green-500
                                                {% elif activity.type == 'started' %}bg-blue-500
                                                {% elif activity.type == 'draft' %}bg-yellow-500
                                                {% else %}bg-gray-500{% endif %}">
                                        {% if activity.type == 'completed' %}
                                            <i data-lucide="check" class="w-4 h-4 text-white"></i>
                                        {% elif activity.type == 'started' %}
                                            <i data-lucide="play" class="w-4 h-4 text-white"></i>
                                        {% elif activity.type == 'draft' %}
                                            <i data-lucide="edit" class="w-4 h-4 text-white"></i>
                                        {% else %}
                                            <i data-lucide="activity" class="w-4 h-4 text-white"></i>
                                        {% endif %}
                                    </span>
                                </div>
                                <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                    <div>
                                        <p class="text-sm text-gray-500">
                                            <span class="font-medium text-gray-900">{{ activity.user }}</span>
                                            {{ activity.description }}
                                        </p>
                                    </div>
                                    <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                        {{ activity.created_at|timesince }}前
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                {% empty %}
                    <li class="text-center py-8 text-gray-500">
                        <i data-lucide="inbox" class="mx-auto h-8 w-8 text-gray-400 mb-2"></i>
                        <p>暂无活动记录</p>
                    </li>
                {% endfor %}
            </ul>
        </div>
    </div>
</div>

<!-- 参与人员任务详情模态框 -->
<div id="participantModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900" id="modalTitle">参与人员任务详情</h3>
                <button onclick="closeParticipantModal()" class="text-gray-400 hover:text-gray-600">
                    <i data-lucide="x" class="w-6 h-6"></i>
                </button>
            </div>
            <div id="modalContent">
                <!-- 模态框内容将在这里动态加载 -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 页面加载时初始化
    document.addEventListener('DOMContentLoaded', function() {
        setupEventListeners();
        loadActivityFeed();
        calculateProgressBars();
        initProgressCharts();
    });

    // 图表初始化和管理
    class ProgressCharts {
        constructor(batchId) {
            this.batchId = batchId;
            this.apiBaseUrl = '/reports/admin/api/charts/';
        }

        async init() {
            await this.loadDepartmentComparison();
            await this.loadEvaluationTypes();
            this.bindChartEvents();
        }

        async loadDepartmentComparison() {
            const url = this.apiBaseUrl + 'evaluation-progress/';
            
            await chartManager.loadDataAndCreateChart(
                url,
                'department-comparison-chart',
                (containerId, data) => {
                    if (data.batch_info && data.department_progress && data.department_progress.length > 0) {
                        const categories = data.department_progress.map(dept => dept.department);
                        const completedData = data.department_progress.map(dept => dept.completed);
                        const totalData = data.department_progress.map(dept => dept.total);
                        const progressData = data.department_progress.map(dept => dept.progress_rate);
                        
                        return chartManager.createBarChart(containerId, categories, [
                            {
                                name: '已完成',
                                data: completedData,
                                color: '#10b981'
                            },
                            {
                                name: '总任务',
                                data: totalData,
                                color: '#e5e7eb'
                            }
                        ], {
                            title: '',
                            legendTop: '5%',
                            xAxisRotate: -45
                        });
                    }
                    return null;
                },
                { batch_id: this.batchId }
            );
        }

        async loadEvaluationTypes() {
            const url = this.apiBaseUrl + 'evaluation-progress/';
            
            await chartManager.loadDataAndCreateChart(
                url,
                'evaluation-type-chart',
                (containerId, data) => {
                    if (data.batch_info && data.type_progress && data.type_progress.length > 0) {
                        const pieData = data.type_progress.map(type => ({
                            name: type.type,
                            value: type.completed
                        }));
                        
                        return chartManager.createPieChart(containerId, pieData, {
                            title: '',
                            seriesName: '评价类型',
                            radius: '60%',
                            center: ['50%', '50%'],
                            labelFormatter: '{b}: {c}项 ({d}%)'
                        });
                    }
                    return null;
                },
                { batch_id: this.batchId }
            );
        }

        bindChartEvents() {
            // 部门进度图表刷新
            document.getElementById('refresh-dept-progress-chart')?.addEventListener('click', () => {
                this.loadDepartmentComparison();
            });

            // 评价类型图表刷新
            document.getElementById('refresh-type-progress-chart')?.addEventListener('click', () => {
                this.loadEvaluationTypes();
            });
        }

        refresh() {
            this.loadDepartmentComparison();
            this.loadEvaluationTypes();
        }
    }

    function initProgressCharts() {
        // 延迟初始化确保页面元素已加载
        setTimeout(async () => {
            const batchId = {{ object.id }};
            const progressCharts = new ProgressCharts(batchId);
            await progressCharts.init();
            
            // 保存到全局变量
            window.progressCharts = progressCharts;
        }, 300);
    }

    function setupEventListeners() {
        // 参与人员筛选
        document.getElementById('participantFilter').addEventListener('change', filterParticipants);
        document.getElementById('deptFilter').addEventListener('change', filterParticipants);
    }

    function calculateProgressBars() {
        // 计算草稿进度条
        const draftBar = document.querySelector('.draft-progress');
        if (draftBar) {
            const count = parseInt(draftBar.dataset.count) || 0;
            const total = parseInt(draftBar.dataset.total) || 1;
            const percentage = total > 0 ? (count / total * 100) : 0;
            draftBar.style.width = percentage + '%';
        }

        // 计算剩余进度条
        const remainingBar = document.querySelector('.remaining-progress');
        if (remainingBar) {
            const count = parseInt(remainingBar.dataset.count) || 0;
            const total = parseInt(remainingBar.dataset.total) || 1;
            const percentage = total > 0 ? (count / total * 100) : 0;
            remainingBar.style.width = percentage + '%';
        }
    }

    function filterParticipants() {
        const statusFilter = document.getElementById('participantFilter').value;
        const deptFilter = document.getElementById('deptFilter').value;
        const rows = document.querySelectorAll('.participant-row');

        rows.forEach(row => {
            const status = row.dataset.status;
            const department = row.dataset.department;

            let showRow = true;

            // 状态筛选
            if (statusFilter && status !== statusFilter) {
                showRow = false;
            }

            // 部门筛选
            if (deptFilter && department !== deptFilter) {
                showRow = false;
            }

            row.style.display = showRow ? 'table-row' : 'none';
        });
    }

    function refreshProgress() {
        showNotification('正在刷新进度数据...', 'info');
        setTimeout(() => {
            location.reload();
        }, 1000);
    }

    function exportDetailReport() {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{% url "evaluations:admin:export_batch_detail" object.id %}';
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrfmiddlewaretoken';
        csrfToken.value = document.querySelector('[name=csrfmiddlewaretoken]').value;
        form.appendChild(csrfToken);
        
        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
        
        showNotification('正在导出详细报告...', 'info');
    }

    function viewParticipantTasks(participantId) {
        fetch(`{% url 'evaluations:admin:participant_tasks' object.id %}?participant_id=${participantId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('modalTitle').textContent = `${data.participant_name} - 任务详情`;
                    document.getElementById('modalContent').innerHTML = data.html;
                    document.getElementById('participantModal').classList.remove('hidden');
                } else {
                    showNotification('获取任务详情失败', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('获取任务详情失败', 'error');
            });
    }

    function sendIndividualReminder(participantId) {
        if (confirm('确定要向此人员发送提醒通知吗？')) {
            fetch(`{% url 'evaluations:admin:send_individual_reminder' object.id %}`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ participant_id: participantId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('提醒发送成功', 'success');
                } else {
                    showNotification('发送提醒失败，请重试', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('发送提醒失败，请重试', 'error');
            });
        }
    }

    function closeParticipantModal() {
        document.getElementById('participantModal').classList.add('hidden');
    }

    function loadActivityFeed() {
        // 这里可以实现活动记录的动态加载
        // 暂时使用静态内容
    }

    // 点击模态框外部关闭
    document.getElementById('participantModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeParticipantModal();
        }
    });

    // 定时刷新统计数据
    setInterval(function() {
        // 异步更新统计数据，避免整页刷新
        fetch(window.location.href, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.progress) {
                document.getElementById('totalTasks').textContent = data.progress.total_relations;
                document.getElementById('completedTasks').textContent = data.progress.completed_relations;
                document.getElementById('pendingTasks').textContent = data.progress.remaining_relations;
                document.getElementById('completionRate').textContent = data.progress.completion_rate.toFixed(1) + '%';
            }
        })
        .catch(error => {
            console.log('自动刷新失败:', error);
        });
    }, 30000); // 每30秒刷新一次
</script>

<!-- 自定义样式 -->
<style>
    /* 进度条动画 */
    .participant-row .bg-green-500,
    .participant-row .bg-yellow-500,
    .participant-row .bg-red-500 {
        transition: width 0.3s ease;
    }
    
    /* 表格行悬停效果 */
    .participant-row:hover {
        background-color: #f9fafb;
    }
    
    /* 模态框动画 */
    #participantModal {
        transition: opacity 0.3s ease;
    }
    
    /* 响应式调整 */
    @media (max-width: 768px) {
        .grid-cols-1.md\\:grid-cols-4 {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .overflow-x-auto {
            font-size: 0.875rem;
        }
    }
</style>
{% endblock %}