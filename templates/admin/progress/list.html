{% extends "admin/base_admin.html" %}

{% block page_title %}考评进度监控{% endblock %}
{% block page_description %}实时监控考评批次的完成进度和参与统计{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-4">
    <button onclick="refreshProgress()" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center space-x-2">
        <i data-lucide="refresh-cw" class="w-4 h-4"></i>
        <span>刷新数据</span>
    </button>
    <button onclick="exportProgress()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
        <i data-lucide="download" class="w-4 h-4"></i>
        <span>导出报告</span>
    </button>
</div>
{% endblock %}

{% block admin_content %}
<!-- 总体统计卡片 -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
                <i data-lucide="calendar" class="w-6 h-6 text-blue-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">活跃批次</p>
                <p class="text-2xl font-bold text-gray-900" id="activeBatchesCount">0</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
                <i data-lucide="users" class="w-6 h-6 text-green-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">参与人员</p>
                <p class="text-2xl font-bold text-gray-900" id="totalParticipants">0</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
                <i data-lucide="clipboard-list" class="w-6 h-6 text-yellow-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">总任务数</p>
                <p class="text-2xl font-bold text-gray-900" id="totalTasks">0</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
                <i data-lucide="trending-up" class="w-6 h-6 text-purple-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">平均完成率</p>
                <p class="text-2xl font-bold text-gray-900" id="averageCompletion">0%</p>
            </div>
        </div>
    </div>
</div>

<!-- 筛选和搜索 -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
    <div class="px-6 py-4">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
            <div class="flex-1 max-w-lg">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i data-lucide="search" class="h-4 w-4 text-gray-400"></i>
                    </div>
                    <input type="text" id="searchInput" 
                           class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500" 
                           placeholder="搜索批次名称...">
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <select id="statusFilter" class="border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">所有状态</option>
                    <option value="active">进行中</option>
                    <option value="completed">已完成</option>
                    <option value="cancelled">已取消</option>
                </select>
                <select id="progressFilter" class="border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">所有进度</option>
                    <option value="high">高完成度 (≥80%)</option>
                    <option value="medium">中完成度 (50-79%)</option>
                    <option value="low">低完成度 (<50%)</option>
                </select>
                <select id="sortBy" class="border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    <option value="created_at">创建时间</option>
                    <option value="end_date">截止时间</option>
                    <option value="completion_rate">完成率</option>
                    <option value="participants">参与人数</option>
                </select>
            </div>
        </div>
    </div>
</div>

<!-- 进度监控列表 -->
<div class="space-y-6" id="progressList">
    {% for progress in progress_list %}
        <div class="bg-white rounded-lg shadow hover:shadow-lg transition-shadow progress-card border border-gray-200"
             data-status="{{ progress.batch.status }}" 
             data-completion="{{ progress.completion_rate|floatformat:0 }}"
             data-participants="{{ progress.batch.get_participants_count.total }}">
            
            <div class="px-6 py-6">
                <!-- 批次头部信息 -->
                <div class="flex items-start justify-between mb-6">
                    <div class="flex-1">
                        <div class="flex items-center space-x-3 mb-2">
                            <h3 class="text-xl font-semibold text-gray-900">{{ progress.batch.name }}</h3>
                            
                            <!-- 状态标签 -->
                            {% if progress.batch.status == 'active' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i data-lucide="play-circle" class="w-3 h-3 mr-1"></i>
                                    进行中
                                </span>
                            {% elif progress.batch.status == 'completed' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>
                                    已完成
                                </span>
                            {% elif progress.batch.status == 'cancelled' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <i data-lucide="x-circle" class="w-3 h-3 mr-1"></i>
                                    已取消
                                </span>
                            {% endif %}
                            
                            <!-- 紧急程度标签 -->
                            {% if progress.batch.end_date|timeuntil|slice:":1" == "0" %}
                                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-red-100 text-red-800">
                                    <i data-lucide="alert-circle" class="w-3 h-3 mr-1"></i>
                                    即将截止
                                </span>
                            {% elif progress.batch.end_date|timeuntil|slice:":1" == "1" %}
                                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                                    <i data-lucide="clock" class="w-3 h-3 mr-1"></i>
                                    剩余{{ progress.batch.end_date|timeuntil }}
                                </span>
                            {% endif %}
                        </div>
                        
                        <p class="text-gray-600 mb-4">{{ progress.batch.description|default:"暂无描述"|truncatechars:80 }}</p>
                        
                        <!-- 基础信息 -->
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                            <div class="flex items-center">
                                <i data-lucide="calendar" class="w-4 h-4 mr-2"></i>
                                <span>{{ progress.batch.start_date|date:"m-d" }} ~ {{ progress.batch.end_date|date:"m-d" }}</span>
                            </div>
                            <div class="flex items-center">
                                <i data-lucide="file-text" class="w-4 h-4 mr-2"></i>
                                <span>{{ progress.batch.default_template.name }}</span>
                            </div>
                            <div class="flex items-center">
                                <i data-lucide="users" class="w-4 h-4 mr-2"></i>
                                <span>{{ progress.batch.get_participants_count.total }} 人参与</span>
                            </div>
                            <div class="flex items-center">
                                <i data-lucide="clock" class="w-4 h-4 mr-2"></i>
                                <span>更新：{{ progress.updated_at|date:"m-d H:i" }}</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 完成率圆环 -->
                    <div class="flex-shrink-0 ml-6">
                        <div class="relative w-20 h-20">
                            <svg class="w-20 h-20 transform -rotate-90" viewBox="0 0 32 32">
                                <circle cx="16" cy="16" r="14" stroke="currentColor" stroke-width="2" fill="none" class="text-gray-200" />
                                <circle cx="16" cy="16" r="14" stroke="currentColor" stroke-width="2" fill="none" 
                                        class="{% if progress.completion_rate >= 80 %}text-green-500{% elif progress.completion_rate >= 50 %}text-yellow-500{% else %}text-red-500{% endif %} progress-circle"
                                        data-rate="{{ progress.completion_rate|floatformat:0 }}"
                                        stroke-linecap="round" />
                            </svg>
                            <div class="absolute inset-0 flex items-center justify-center">
                                <span class="text-sm font-semibold text-gray-900">{{ progress.completion_rate|floatformat:0 }}%</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 详细进度信息 -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <!-- 完成统计 -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-900 mb-3 flex items-center">
                            <i data-lucide="bar-chart-3" class="w-4 h-4 mr-2"></i>
                            完成统计
                        </h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">已完成：</span>
                                <span class="font-medium text-green-600">{{ progress.completed_relations }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">总任务：</span>
                                <span class="font-medium">{{ progress.total_relations }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">剩余：</span>
                                <span class="font-medium text-orange-600">{{ progress.remaining_relations }}</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 参与统计 -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-900 mb-3 flex items-center">
                            <i data-lucide="users" class="w-4 h-4 mr-2"></i>
                            参与统计
                        </h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">评价者：</span>
                                <span class="font-medium">{{ progress.batch.get_participants_count.evaluators }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">被评价者：</span>
                                <span class="font-medium">{{ progress.batch.get_participants_count.evaluatees }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">活跃度：</span>
                                <span class="font-medium text-blue-600">{{ progress.active_rate }}%</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 时间统计 -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-900 mb-3 flex items-center">
                            <i data-lucide="clock" class="w-4 h-4 mr-2"></i>
                            时间统计
                        </h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">开始：</span>
                                <span class="font-medium">{{ progress.batch.start_date|date:"m-d" }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">截止：</span>
                                <span class="font-medium">{{ progress.batch.end_date|date:"m-d" }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">剩余：</span>
                                <span class="font-medium {% if progress.batch.end_date|timeuntil|slice:':1' == '0' %}text-red-600{% else %}text-gray-900{% endif %}">
                                    {{ progress.batch.end_date|timeuntil }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 进度条 -->
                <div class="mb-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-gray-700">完成进度</span>
                        <span class="text-sm text-gray-600">{{ progress.completion_rate|floatformat:1 }}%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-3">
                        <div class="h-3 rounded-full transition-all duration-300 flex items-center justify-end pr-2
                                    {% if progress.completion_rate >= 80 %}bg-gradient-to-r from-green-400 to-green-600
                                    {% elif progress.completion_rate >= 50 %}bg-gradient-to-r from-yellow-400 to-yellow-600
                                    {% else %}bg-gradient-to-r from-red-400 to-red-600{% endif %}" 
                             style="width: {{ progress.completion_rate }}%">
                            {% if progress.completion_rate > 10 %}
                                <span class="text-xs text-white font-medium">{{ progress.completion_rate|floatformat:0 }}%</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- 操作按钮 -->
                <div class="flex items-center justify-between pt-4 border-t border-gray-200">
                    <div class="flex items-center space-x-4 text-sm text-gray-600">
                        <span class="flex items-center">
                            <i data-lucide="activity" class="w-4 h-4 mr-1"></i>
                            最后活动：{{ progress.last_activity|default:"暂无活动" }}
                        </span>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                        <a href="{% url 'evaluations:admin:progress_detail' progress.batch.id %}" 
                           class="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 flex items-center space-x-1">
                            <i data-lucide="eye" class="w-4 h-4"></i>
                            <span>查看详情</span>
                        </a>
                        
                        <a href="{% url 'evaluations:admin:batch_relations' progress.batch.id %}" 
                           class="px-3 py-1 border border-gray-300 text-gray-700 text-sm rounded hover:bg-gray-50 flex items-center space-x-1">
                            <i data-lucide="users" class="w-4 h-4"></i>
                            <span>管理关系</span>
                        </a>
                        
                        <div class="relative">
                            <button type="button" onclick="toggleDropdown({{ progress.batch.id }})" 
                                    class="p-1 text-gray-400 hover:text-gray-600">
                                <i data-lucide="more-vertical" class="w-4 h-4"></i>
                            </button>
                            <div id="dropdown-{{ progress.batch.id }}" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                                <div class="py-1">
                                    <button onclick="sendReminder({{ progress.batch.id }})" 
                                            class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i data-lucide="bell" class="w-4 h-4 mr-2"></i>
                                        发送提醒
                                    </button>
                                    <button onclick="exportBatchReport({{ progress.batch.id }})" 
                                            class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i data-lucide="download" class="w-4 h-4 mr-2"></i>
                                        导出报告
                                    </button>
                                    <a href="{% url 'evaluations:admin:batch_detail' progress.batch.id %}" 
                                       class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i data-lucide="info" class="w-4 h-4 mr-2"></i>
                                        批次详情
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% empty %}
        <!-- 空状态 -->
        <div class="text-center py-12">
            <i data-lucide="activity" class="mx-auto h-16 w-16 text-gray-400"></i>
            <h3 class="mt-4 text-lg font-medium text-gray-900">暂无进度数据</h3>
            <p class="mt-2 text-sm text-gray-500">当前没有活跃的考评批次需要监控。</p>
            <div class="mt-6">
                <a href="{% url 'evaluations:admin:batch_create' %}" 
                   class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                    创建批次
                </a>
            </div>
        </div>
    {% endfor %}
</div>

<!-- 分页 -->
{% if is_paginated %}
<div class="mt-8 flex items-center justify-between">
    <div class="text-sm text-gray-700">
        显示第 {{ page_obj.start_index }} - {{ page_obj.end_index }} 项，共 {{ paginator.count }} 项
    </div>
    <nav class="flex items-center space-x-2">
        {% if page_obj.has_previous %}
            <a href="?page=1" class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">首页</a>
            <a href="?page={{ page_obj.previous_page_number }}" class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">上一页</a>
        {% endif %}
        
        <span class="px-3 py-2 text-sm bg-blue-50 border border-blue-200 rounded-md text-blue-600">
            {{ page_obj.number }}
        </span>
        
        {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}" class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">下一页</a>
            <a href="?page={{ paginator.num_pages }}" class="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">末页</a>
        {% endif %}
    </nav>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    // 页面加载时更新统计数据
    document.addEventListener('DOMContentLoaded', function() {
        updateStatistics();
        setupEventListeners();
        initializeProgressCircles();
        
        // 定时刷新数据
        setInterval(updateStatistics, 60000); // 每分钟刷新一次
    });

    function setupEventListeners() {
        // 搜索功能
        document.getElementById('searchInput').addEventListener('input', filterProgress);
        document.getElementById('statusFilter').addEventListener('change', filterProgress);
        document.getElementById('progressFilter').addEventListener('change', filterProgress);
        document.getElementById('sortBy').addEventListener('change', sortProgress);
    }

    function initializeProgressCircles() {
        // 初始化进度圆圈的stroke-dasharray属性
        const progressCircles = document.querySelectorAll('.progress-circle');
        progressCircles.forEach(circle => {
            const rate = parseFloat(circle.dataset.rate) || 0;
            const dashValue = rate * 0.88; // 计算圆弧长度
            circle.setAttribute('stroke-dasharray', `${dashValue} 88`);
        });
    }

    function updateStatistics() {
        const progressCards = document.querySelectorAll('.progress-card');
        let activeBatches = 0;
        let totalParticipants = 0;
        let totalTasks = 0;
        let totalCompletion = 0;

        progressCards.forEach(card => {
            const status = card.dataset.status;
            const participants = parseInt(card.dataset.participants) || 0;
            const completion = parseFloat(card.dataset.completion) || 0;

            if (status === 'active') activeBatches++;
            totalParticipants += participants;
            totalTasks++;
            totalCompletion += completion;
        });

        document.getElementById('activeBatchesCount').textContent = activeBatches;
        document.getElementById('totalParticipants').textContent = totalParticipants;
        document.getElementById('totalTasks').textContent = totalTasks;
        document.getElementById('averageCompletion').textContent = 
            totalTasks > 0 ? Math.round(totalCompletion / totalTasks) + '%' : '0%';
    }

    function filterProgress() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const statusFilter = document.getElementById('statusFilter').value;
        const progressFilter = document.getElementById('progressFilter').value;
        const progressCards = document.querySelectorAll('.progress-card');

        progressCards.forEach(card => {
            const title = card.querySelector('h3').textContent.toLowerCase();
            const status = card.dataset.status;
            const completion = parseFloat(card.dataset.completion);

            let showCard = true;

            // 文本搜索
            if (searchTerm && !title.includes(searchTerm)) {
                showCard = false;
            }

            // 状态筛选
            if (statusFilter && status !== statusFilter) {
                showCard = false;
            }

            // 进度筛选
            if (progressFilter) {
                if (progressFilter === 'high' && completion < 80) showCard = false;
                else if (progressFilter === 'medium' && (completion < 50 || completion >= 80)) showCard = false;
                else if (progressFilter === 'low' && completion >= 50) showCard = false;
            }

            card.style.display = showCard ? 'block' : 'none';
        });
    }

    function sortProgress() {
        const sortBy = document.getElementById('sortBy').value;
        const progressList = document.getElementById('progressList');
        const cards = Array.from(progressList.querySelectorAll('.progress-card'));

        cards.sort((a, b) => {
            let aValue, bValue;

            switch (sortBy) {
                case 'completion_rate':
                    aValue = parseFloat(a.dataset.completion);
                    bValue = parseFloat(b.dataset.completion);
                    return bValue - aValue; // 降序
                case 'participants':
                    aValue = parseInt(a.dataset.participants);
                    bValue = parseInt(b.dataset.participants);
                    return bValue - aValue; // 降序
                default:
                    return 0;
            }
        });

        // 重新排列卡片
        cards.forEach(card => progressList.appendChild(card));
    }

    function refreshProgress() {
        showNotification('正在刷新进度数据...', 'info');
        setTimeout(() => {
            location.reload();
        }, 1000);
    }

    function exportProgress() {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{% url "evaluations:admin:export_progress" %}';
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrfmiddlewaretoken';
        csrfToken.value = document.querySelector('[name=csrfmiddlewaretoken]').value;
        form.appendChild(csrfToken);
        
        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
        
        showNotification('正在导出进度报告...', 'info');
    }

    function sendReminder(batchId) {
        if (confirm('确定要向所有未完成的评价者发送提醒通知吗？')) {
            fetch(`/evaluations/admin/batches/${batchId}/remind/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(`已向 ${data.count} 人发送提醒`, 'success');
                } else {
                    showNotification('发送提醒失败，请重试', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('发送提醒失败，请重试', 'error');
            });
        }
    }

    function exportBatchReport(batchId) {
        window.open(`/evaluations/admin/batches/${batchId}/export/`, '_blank');
        showNotification('正在导出批次报告...', 'info');
    }

    function toggleDropdown(batchId) {
        const dropdown = document.getElementById(`dropdown-${batchId}`);
        const allDropdowns = document.querySelectorAll('[id^="dropdown-"]');
        
        // 关闭其他下拉菜单
        allDropdowns.forEach(d => {
            if (d !== dropdown) {
                d.classList.add('hidden');
            }
        });
        
        // 切换当前下拉菜单
        dropdown.classList.toggle('hidden');
    }

    // 点击外部关闭下拉菜单
    document.addEventListener('click', function(event) {
        if (!event.target.closest('[onclick*="toggleDropdown"]') && !event.target.closest('[id^="dropdown-"]')) {
            const allDropdowns = document.querySelectorAll('[id^="dropdown-"]');
            allDropdowns.forEach(d => d.classList.add('hidden'));
        }
    });
</script>

<!-- CSS样式 -->
<style>
    /* 自定义进度条动画 */
    .progress-card {
        transition: all 0.3s ease;
    }
    
    .progress-card:hover {
        transform: translateY(-2px);
    }
    
    /* 圆环进度条样式 */
    .progress-card svg circle {
        transition: stroke-dasharray 0.3s ease;
    }
    
    /* 响应式调整 */
    @media (max-width: 768px) {
        .progress-card .grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}