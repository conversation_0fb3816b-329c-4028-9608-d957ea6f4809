{% extends "admin/base_admin.html" %}

{% block page_title %}编辑部门 - {{ object.name }}{% endblock %}
{% block page_description %}修改部门的基本信息和组织架构设置{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <a href="{% url 'organizations:admin:department_detail' object.pk %}" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center space-x-2">
        <i data-lucide="eye" class="w-4 h-4"></i>
        <span>查看详情</span>
    </a>
    <a href="{% url 'organizations:admin:department_list' %}" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center space-x-2">
        <i data-lucide="arrow-left" class="w-4 h-4"></i>
        <span>返回列表</span>
    </a>
</div>
{% endblock %}

{% block admin_content %}
<div class="max-w-4xl mx-auto">
    <div class="bg-white rounded-lg shadow">
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                        <i data-lucide="building" class="w-6 h-6 text-green-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-medium text-gray-900">编辑部门信息</h3>
                    <p class="mt-1 text-sm text-gray-600">
                        修改 {{ object.name }} 的基本信息、组织架构和联系方式
                    </p>
                </div>
            </div>
        </div>

        <form method="post" class="p-6 space-y-6" id="departmentForm">
            {% csrf_token %}
            
            <!-- 基本信息 -->
            <div class="border-b border-gray-200 pb-6">
                <h4 class="text-md font-medium text-gray-900 mb-4">基本信息</h4>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 部门编号 -->
                    <div>
                        <label for="id_dept_code" class="block text-sm font-medium text-gray-700 mb-2">
                            部门编号 <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="dept_code" id="id_dept_code" required
                               value="{{ object.dept_code }}"
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="请输入部门编号">
                        <p class="mt-1 text-sm text-gray-500">唯一的部门标识编号</p>
                        {% if form.dept_code.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.dept_code.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- 部门名称 -->
                    <div>
                        <label for="id_name" class="block text-sm font-medium text-gray-700 mb-2">
                            部门名称 <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="name" id="id_name" required
                               value="{{ object.name }}"
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="请输入部门名称">
                        {% if form.name.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.name.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- 上级部门 -->
                    <div>
                        <label for="id_parent_department" class="block text-sm font-medium text-gray-700 mb-2">
                            上级部门
                        </label>
                        <select name="parent_department" id="id_parent_department"
                                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">无上级部门</option>
                            {% for dept in form.parent_department.field.queryset %}
                            {% if dept.id != object.id %}
                            <option value="{{ dept.id }}" 
                                    {% if object.parent_department and dept.id == object.parent_department.id %}selected{% endif %}>
                                {{ dept.name }}
                            </option>
                            {% endif %}
                            {% endfor %}
                        </select>
                        <p class="mt-1 text-sm text-gray-500">不能选择自己或下级部门作为上级</p>
                        {% if form.parent_department.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.parent_department.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- 部门经理 -->
                    <div>
                        <label for="id_manager" class="block text-sm font-medium text-gray-700 mb-2">
                            部门经理
                        </label>
                        <select name="manager" id="id_manager"
                                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">请选择部门经理</option>
                            {% for staff in department_staff %}
                            <option value="{{ staff.id }}" 
                                    {% if object.manager and staff.id == object.manager.id %}selected{% endif %}>
                                {{ staff.name }} ({{ staff.employee_no }})
                            </option>
                            {% endfor %}
                        </select>
                        <p class="mt-1 text-sm text-gray-500">只能选择本部门的员工作为经理</p>
                        {% if form.manager.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.manager.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- 联系信息 -->
            <div class="border-b border-gray-200 pb-6">
                <h4 class="text-md font-medium text-gray-900 mb-4">联系信息</h4>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 办公地址 -->
                    <div>
                        <label for="id_location" class="block text-sm font-medium text-gray-700 mb-2">
                            办公地址
                        </label>
                        <input type="text" name="location" id="id_location"
                               value="{{ object.location }}"
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="请输入办公地址">
                        {% if form.location.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.location.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- 联系电话 -->
                    <div>
                        <label for="id_phone" class="block text-sm font-medium text-gray-700 mb-2">
                            联系电话
                        </label>
                        <input type="tel" name="phone" id="id_phone"
                               value="{{ object.phone }}"
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="请输入联系电话">
                        {% if form.phone.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.phone.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- 邮箱地址 -->
                    <div class="md:col-span-2">
                        <label for="id_email" class="block text-sm font-medium text-gray-700 mb-2">
                            邮箱地址
                        </label>
                        <input type="email" name="email" id="id_email"
                               value="{{ object.email }}"
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="请输入邮箱地址">
                        {% if form.email.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.email.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- 部门描述 -->
            <div class="border-b border-gray-200 pb-6">
                <h4 class="text-md font-medium text-gray-900 mb-4">部门描述</h4>
                
                <div>
                    <label for="id_description" class="block text-sm font-medium text-gray-700 mb-2">
                        详细描述
                    </label>
                    <textarea name="description" id="id_description" rows="4"
                              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              placeholder="请输入部门描述、职责范围等信息">{{ object.description }}</textarea>
                    <p class="mt-1 text-sm text-gray-500">描述部门的主要职责、业务范围等</p>
                    {% if form.description.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.description.errors.0 }}</p>
                    {% endif %}
                </div>
            </div>

            <!-- 统计信息预览 -->
            <div>
                <h4 class="text-md font-medium text-gray-900 mb-4">部门概况</h4>
                
                <div class="bg-gray-50 rounded-lg p-6">
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-600">{{ staff_count }}</div>
                            <div class="text-sm text-gray-500">员工总数</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-600">{{ positions_count }}</div>
                            <div class="text-sm text-gray-500">职位数量</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-yellow-600">{{ sub_departments_count }}</div>
                            <div class="text-sm text-gray-500">下级部门</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-purple-600">{{ object.created_at|date:"Y-m-d" }}</div>
                            <div class="text-sm text-gray-500">创建日期</div>
                        </div>
                    </div>
                    
                    <!-- 员工列表预览 -->
                    {% if staff_list %}
                    <div class="mt-6">
                        <h5 class="text-sm font-medium text-gray-700 mb-3">部门员工预览</h5>
                        <div class="flex flex-wrap gap-2">
                            {% for staff in staff_list|slice:":8" %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                <i data-lucide="user" class="w-3 h-3 mr-1"></i>
                                {{ staff.name }}
                            </span>
                            {% endfor %}
                            {% if staff_list|length > 8 %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                +{{ staff_list|length|add:"-8" }} 人
                            </span>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- 表单操作 -->
            <div class="border-t border-gray-200 pt-6">
                <div class="flex justify-end space-x-3">
                    <a href="{% url 'organizations:admin:department_detail' object.pk %}" 
                       class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        取消
                    </a>
                    <button type="submit" id="submitBtn"
                            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i data-lucide="save" class="w-4 h-4 inline mr-1"></i>
                        保存修改
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- 风险提示 -->
    {% if staff_count > 0 or sub_departments_count > 0 %}
    <div class="mt-6 bg-yellow-50 border border-yellow-200 rounded-md p-4">
        <div class="flex">
            <i data-lucide="alert-triangle" class="w-5 h-5 text-yellow-400 mr-2"></i>
            <div class="text-sm text-yellow-700">
                <p class="font-medium">修改提醒</p>
                <p>该部门包含 {{ staff_count }} 名员工{% if sub_departments_count > 0 %}和 {{ sub_departments_count }} 个下级部门{% endif %}。修改部门信息可能会影响相关的考评关系和组织架构。</p>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 上级部门选择限制
    const parentDeptSelect = document.getElementById('id_parent_department');
    const currentDeptId = {{ object.id }};
    
    // 移除可能导致循环引用的选项
    function filterParentOptions() {
        const options = parentDeptSelect.options;
        for (let i = options.length - 1; i >= 0; i--) {
            const option = options[i];
            if (option.value && parseInt(option.value) === currentDeptId) {
                option.remove();
            }
        }
    }
    
    filterParentOptions();
    
    // 部门编号输入验证
    document.getElementById('id_dept_code').addEventListener('input', function() {
        const value = this.value.toUpperCase();
        this.value = value;
        
        // 简单的编号格式验证
        if (value && !/^[A-Z0-9]{2,10}$/.test(value)) {
            this.setCustomValidity('部门编号应为2-10位字母或数字组合');
        } else {
            this.setCustomValidity('');
        }
    });
    
    // 表单提交处理
    document.getElementById('departmentForm').addEventListener('submit', function(e) {
        const submitBtn = document.getElementById('submitBtn');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i data-lucide="loader-2" class="w-4 h-4 inline mr-1 animate-spin"></i>正在保存...';
        
        // 验证上级部门不能是自己
        const parentDept = parentDeptSelect.value;
        if (parentDept && parseInt(parentDept) === currentDeptId) {
            e.preventDefault();
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i data-lucide="save" class="w-4 h-4 inline mr-1"></i>保存修改';
            UI.showMessage('不能选择自己作为上级部门', 'error');
        }
    });
});

// 自动生成部门编号建议
function suggestDeptCode() {
    const nameInput = document.getElementById('id_name');
    const codeInput = document.getElementById('id_dept_code');
    
    if (nameInput.value && !codeInput.value) {
        // 提取中文首字母或英文前缀
        let suggestion = '';
        const name = nameInput.value;
        
        if (/[\u4e00-\u9fa5]/.test(name)) {
            // 中文名称，取前2-3个字的拼音首字母
            suggestion = name.substring(0, 2).toUpperCase();
        } else {
            // 英文名称，取前3-4个字母
            suggestion = name.substring(0, 4).toUpperCase();
        }
        
        codeInput.value = suggestion;
    }
}

// 绑定名称输入事件
document.getElementById('id_name').addEventListener('blur', suggestDeptCode);
</script>
{% endblock %}