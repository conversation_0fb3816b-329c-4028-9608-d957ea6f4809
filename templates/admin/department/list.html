{% extends "admin/base_admin.html" %}
{% load static %}

{% block page_title %}部门管理{% endblock %}
{% block page_description %}管理公司组织架构和部门信息{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <!-- 视图切换按钮 -->
    <div class="flex bg-gray-100 rounded-lg p-1">
        <button id="card-view-btn" class="px-3 py-1 text-sm font-medium rounded-md bg-white text-gray-900 shadow-sm">
            <i data-lucide="grid-3x3" class="w-4 h-4 mr-1 inline"></i>
            卡片视图
        </button>
        <button id="table-view-btn" class="px-3 py-1 text-sm font-medium rounded-md text-gray-600 hover:text-gray-900">
            <i data-lucide="list" class="w-4 h-4 mr-1 inline"></i>
            表格视图
        </button>
    </div>

    <a href="{% url 'organizations:admin:department_import' %}" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center space-x-2">
        <i data-lucide="upload" class="w-4 h-4"></i>
        <span>导入部门</span>
    </a>
    <a href="{% url 'organizations:admin:department_export' %}" class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 flex items-center space-x-2">
        <i data-lucide="download" class="w-4 h-4"></i>
        <span>导出部门</span>
    </a>
    <a href="{% url 'organizations:admin:department_create' %}" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
        <i data-lucide="plus" class="w-4 h-4"></i>
        <span>新建部门</span>
    </a>
</div>
{% endblock %}

{% block admin_content %}
<!-- Stats -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
                <i data-lucide="building" class="w-6 h-6 text-blue-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">总部门数</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.total_departments|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
                <i data-lucide="check-circle" class="w-6 h-6 text-green-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">启用部门</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.active_departments|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
                <i data-lucide="users" class="w-6 h-6 text-purple-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">总员工数</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.total_staff|default:0 }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-orange-100 rounded-lg">
                <i data-lucide="building-2" class="w-6 h-6 text-orange-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">有员工部门</p>
                <p class="text-2xl font-bold text-gray-900">{{ stats.departments_with_staff|default:0 }}</p>
            </div>
        </div>
    </div>
</div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
                <i data-lucide="trending-up" class="w-6 h-6 text-yellow-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">平均评分</p>
                <p class="text-2xl font-bold text-gray-900">4.2</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
                <i data-lucide="user" class="w-6 h-6 text-purple-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">部门经理</p>
                <p class="text-2xl font-bold text-gray-900">{{ departments|length|default:0 }}</p>
            </div>
        </div>
    </div>
</div>

<!-- Search -->
<div class="bg-white rounded-lg shadow mb-6 p-6">
    <div class="relative">
        <input type="text" id="searchInput" placeholder="搜索部门名称、编号或描述..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md">
        <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"></i>
    </div>
</div>

<!-- Departments Grid -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6" id="departmentsGrid">
    {% if departments %}
        {% for department in departments %}
        <!-- Department Card -->
        <div class="bg-white rounded-lg shadow hover:shadow-lg transition-shadow department-card">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-xl font-semibold text-gray-900">{{ department.name }}</h3>
                        <p class="text-gray-600 mt-1">{{ department.description|default:"暂无描述"|truncatechars:50 }}</p>
                    </div>
                    <div class="relative">
                        <button onclick="toggleDropdown(this)" class="p-1 text-gray-400 hover:text-gray-600">
                            <i data-lucide="more-horizontal" class="w-4 h-4"></i>
                        </button>
                        <div class="dropdown-menu hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border">
                            <button onclick="showNotification('功能开发中', 'info')" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i data-lucide="eye" class="w-4 h-4 mr-2 inline"></i>查看详情
                            </button>
                            <button onclick="showNotification('功能开发中', 'info')" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i data-lucide="edit" class="w-4 h-4 mr-2 inline"></i>编辑部门
                            </button>
                            <button onclick="showNotification('确认删除部门？', 'warning')" class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100">
                                <i data-lucide="trash-2" class="w-4 h-4 mr-2 inline"></i>删除部门
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="p-6">
                <!-- Manager Info -->
                <div class="flex items-center space-x-3 mb-4">
                    <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                        <span class="text-sm font-medium">{{ department.manager.name|first|default:"未" }}</span>
                    </div>
                    <div>
                        <p class="font-medium text-gray-900">{{ department.manager.name|default:"未指定" }}</p>
                        <p class="text-sm text-gray-500">部门经理</p>
                    </div>
                </div>

                <!-- Stats -->
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <p class="text-sm text-gray-600">员工人数</p>
                        <p class="text-lg font-semibold">{{ department.get_staff_count|default:0 }}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">平均评分</p>
                        <p class="text-lg font-semibold">4.3/5.0</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">部门编号</p>
                        <p class="text-lg font-semibold">{{ department.dept_code }}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">创建时间</p>
                        <p class="text-lg font-semibold">{{ department.created_at|date:"Y-m-d" }}</p>
                    </div>
                </div>

                <!-- Evaluation Progress -->
                <div class="space-y-2 mb-4">
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600">考评完成度</span>
                        <span class="font-medium">{{ department.get_staff_count|default:0 }}/{{ department.get_staff_count|default:0 }}</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-600 h-2 rounded-full" style="width: 84%"></div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="flex space-x-2">
                    <button onclick="showNotification('功能开发中', 'info')" class="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">查看员工</button>
                    <button onclick="showNotification('功能开发中', 'info')" class="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">考评报告</button>
                </div>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <!-- Empty State -->
        <div class="col-span-2 text-center py-12">
            <i data-lucide="building" class="mx-auto h-12 w-12 text-gray-400"></i>
            <h3 class="mt-2 text-sm font-medium text-gray-900">暂无部门数据</h3>
            <p class="mt-1 text-sm text-gray-500">开始创建第一个部门吧。</p>
            <div class="mt-6">
                <a href="{% url 'organizations:admin:department_create' %}" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
                    <i data-lucide="plus" class="w-4 h-4"></i>
                    <span>新建部门</span>
                </a>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 搜索功能
    document.getElementById('searchInput').addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const departmentCards = document.querySelectorAll('.department-card');

        departmentCards.forEach(card => {
            const title = card.querySelector('h3').textContent.toLowerCase();
            const description = card.querySelector('p').textContent.toLowerCase();

            if (title.includes(searchTerm) || description.includes(searchTerm)) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    });

    // 视图切换功能
    const tableViewBtn = document.getElementById('table-view-btn');
    const cardViewBtn = document.getElementById('card-view-btn');

    tableViewBtn?.addEventListener('click', function() {
        // 跳转到表格视图（默认视图），删除view参数
        const url = new URL(window.location);
        url.searchParams.delete('view');
        window.location.href = url.toString();
    });

    // 搜索框自动聚焦
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.focus();
    }

    // 初始化图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
});
</script>
{% endblock %}