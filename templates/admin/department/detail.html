{% extends "admin/base_admin.html" %}

{% block page_title %}部门详情 - {{ object.name }}{% endblock %}
{% block page_description %}查看部门的详细信息、员工构成和统计数据{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <a href="{% url 'organizations:admin:department_update' object.pk %}" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
        <i data-lucide="edit" class="w-4 h-4"></i>
        <span>编辑部门</span>
    </a>
    <a href="{% url 'organizations:admin:department_list' %}" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center space-x-2">
        <i data-lucide="arrow-left" class="w-4 h-4"></i>
        <span>返回列表</span>
    </a>
</div>
{% endblock %}

{% block admin_content %}
<div class="max-w-6xl mx-auto space-y-6">
    <!-- 部门基本信息卡片 -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="bg-gradient-to-r from-green-500 to-green-600 px-6 py-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <i data-lucide="building" class="w-8 h-8 text-white"></i>
                    </div>
                </div>
                <div class="ml-4 text-white">
                    <h2 class="text-2xl font-bold">{{ object.name }}</h2>
                    <p class="text-green-100">{{ object.get_department_type_display|default:"业务部门" }}</p>
                    <div class="flex items-center mt-2 space-x-4">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white bg-opacity-20 text-white">
                            <i data-lucide="hash" class="w-3 h-3 mr-1"></i>
                            {{ object.dept_code }}
                        </span>
                        {% if object.parent_department %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            <i data-lucide="arrow-up" class="w-3 h-3 mr-1"></i>
                            {{ object.parent_department.name }}
                        </span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <div class="px-6 py-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- 基本信息 -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                        <i data-lucide="info" class="w-5 h-5 mr-2 text-gray-500"></i>
                        基本信息
                    </h3>
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">部门编号</dt>
                            <dd class="text-sm text-gray-900 font-mono">{{ object.dept_code }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">部门名称</dt>
                            <dd class="text-sm text-gray-900">{{ object.name }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">上级部门</dt>
                            <dd class="text-sm text-gray-900">
                                {% if object.parent_department %}
                                    <a href="{% url 'organizations:admin:department_detail' object.parent_department.pk %}" 
                                       class="text-blue-600 hover:text-blue-800">{{ object.parent_department.name }}</a>
                                {% else %}
                                    <span class="text-gray-400">无上级部门</span>
                                {% endif %}
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">部门经理</dt>
                            <dd class="text-sm text-gray-900">
                                {% if object.manager %}
                                    <a href="{% url 'organizations:admin:staff_detail' object.manager.pk %}" 
                                       class="text-blue-600 hover:text-blue-800">{{ object.manager.name }}</a>
                                {% else %}
                                    <span class="text-gray-400">未设置经理</span>
                                {% endif %}
                            </dd>
                        </div>
                    </dl>
                </div>

                <!-- 描述信息 -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                        <i data-lucide="file-text" class="w-5 h-5 mr-2 text-gray-500"></i>
                        部门描述
                    </h3>
                    <div class="text-sm text-gray-900">
                        {% if object.description %}
                            <p class="bg-gray-50 rounded-lg p-4 leading-relaxed">{{ object.description }}</p>
                        {% else %}
                            <p class="text-gray-400 italic">暂无部门描述</p>
                        {% endif %}
                    </div>
                </div>

                <!-- 联系信息 -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                        <i data-lucide="map-pin" class="w-5 h-5 mr-2 text-gray-500"></i>
                        联系信息
                    </h3>
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">办公地址</dt>
                            <dd class="text-sm text-gray-900">
                                {% if object.location %}
                                    {{ object.location }}
                                {% else %}
                                    <span class="text-gray-400">未设置地址</span>
                                {% endif %}
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">联系电话</dt>
                            <dd class="text-sm text-gray-900">
                                {% if object.phone %}
                                    <a href="tel:{{ object.phone }}" class="text-blue-600 hover:text-blue-800">{{ object.phone }}</a>
                                {% else %}
                                    <span class="text-gray-400">未设置电话</span>
                                {% endif %}
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">邮箱地址</dt>
                            <dd class="text-sm text-gray-900">
                                {% if object.email %}
                                    <a href="mailto:{{ object.email }}" class="text-blue-600 hover:text-blue-800">{{ object.email }}</a>
                                {% else %}
                                    <span class="text-gray-400">未设置邮箱</span>
                                {% endif %}
                            </dd>
                        </div>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计数据卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <i data-lucide="users" class="w-6 h-6 text-blue-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">员工总数</p>
                    <p class="text-2xl font-bold text-gray-900">{{ staff_list|length }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 rounded-lg">
                    <i data-lucide="briefcase" class="w-6 h-6 text-green-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">职位数量</p>
                    <p class="text-2xl font-bold text-gray-900">{{ positions_count|default:0 }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 rounded-lg">
                    <i data-lucide="building-2" class="w-6 h-6 text-yellow-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">下级部门</p>
                    <p class="text-2xl font-bold text-gray-900">{{ sub_departments|length }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 rounded-lg">
                    <i data-lucide="star" class="w-6 h-6 text-purple-600"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">平均评分</p>
                    <p class="text-2xl font-bold text-gray-900">{{ stats.average_score|default:0|floatformat:1 }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 详细数据 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 部门员工列表 -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i data-lucide="users" class="w-5 h-5 mr-2 text-gray-500"></i>
                    部门员工 ({{ staff_list|length }})
                </h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">职位</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">角色</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for staff in staff_list %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                        <i data-lucide="user" class="w-4 h-4 text-gray-600"></i>
                                    </div>
                                    <div class="ml-3">
                                        <div class="text-sm font-medium text-gray-900">
                                            <a href="{% url 'organizations:admin:staff_detail' staff.pk %}" 
                                               class="text-blue-600 hover:text-blue-800">{{ staff.name }}</a>
                                        </div>
                                        <div class="text-sm text-gray-500">{{ staff.employee_no }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ staff.position.name|default:"未设置" }}</div>
                                <div class="text-sm text-gray-500">{{ staff.position.level|default:"" }}级</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                             {% if staff.role == 'dept_manager' %}bg-red-100 text-red-800{% elif staff.role == 'admin' %}bg-blue-100 text-blue-800{% else %}bg-gray-100 text-gray-800{% endif %}">
                                    {{ staff.get_role_display }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if staff.is_active %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i data-lucide="check" class="w-3 h-3 mr-1"></i>
                                    激活
                                </span>
                                {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    <i data-lucide="x" class="w-3 h-3 mr-1"></i>
                                    禁用
                                </span>
                                {% endif %}
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="4" class="px-6 py-12 text-center text-gray-500">
                                <i data-lucide="user-x" class="w-12 h-12 mx-auto mb-4 text-gray-300"></i>
                                <p>该部门暂无员工</p>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 下级部门列表 -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i data-lucide="git-branch" class="w-5 h-5 mr-2 text-gray-500"></i>
                    下级部门 ({{ sub_departments|length }})
                </h3>
            </div>
            <div class="p-6">
                {% if sub_departments %}
                <div class="space-y-4">
                    {% for sub_dept in sub_departments %}
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                <i data-lucide="building" class="w-5 h-5 text-green-600"></i>
                            </div>
                            <div class="ml-3">
                                <div class="text-sm font-medium text-gray-900">
                                    <a href="{% url 'organizations:admin:department_detail' sub_dept.pk %}" 
                                       class="text-blue-600 hover:text-blue-800">{{ sub_dept.name }}</a>
                                </div>
                                <div class="text-sm text-gray-500">{{ sub_dept.dept_code }}</div>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-medium text-gray-900">{{ sub_dept.staff_set.count }} 人</div>
                            <div class="text-sm text-gray-500">员工数</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-8">
                    <i data-lucide="git-branch" class="w-12 h-12 mx-auto mb-4 text-gray-300"></i>
                    <p class="text-gray-500">该部门暂无下级部门</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 组织架构图 -->
    {% if sub_departments %}
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="network" class="w-5 h-5 mr-2 text-gray-500"></i>
                组织架构图
            </h3>
        </div>
        <div class="p-6">
            <div class="flex flex-col items-center space-y-4">
                <!-- 当前部门 -->
                <div class="bg-blue-100 border-2 border-blue-300 rounded-lg p-4 text-center">
                    <div class="text-lg font-semibold text-blue-900">{{ object.name }}</div>
                    <div class="text-sm text-blue-600">{{ staff_list|length }} 人</div>
                </div>
                
                <!-- 连接线 -->
                <div class="w-px h-8 bg-gray-300"></div>
                
                <!-- 下级部门 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {% for sub_dept in sub_departments %}
                    <div class="bg-green-50 border border-green-200 rounded-lg p-3 text-center">
                        <div class="text-sm font-medium text-green-900">{{ sub_dept.name }}</div>
                        <div class="text-xs text-green-600">{{ sub_dept.staff_set.count }} 人</div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 时间戳信息 -->
    <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <i data-lucide="clock" class="w-5 h-5 mr-2 text-gray-500"></i>
            操作记录
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="bg-gray-50 rounded-lg p-4">
                <dt class="text-sm font-medium text-gray-500">创建时间</dt>
                <dd class="text-sm text-gray-900">{{ object.created_at|date:"Y-m-d H:i:s" }}</dd>
            </div>
            <div class="bg-gray-50 rounded-lg p-4">
                <dt class="text-sm font-medium text-gray-500">更新时间</dt>
                <dd class="text-sm text-gray-900">{{ object.updated_at|date:"Y-m-d H:i:s" }}</dd>
            </div>
            <div class="bg-gray-50 rounded-lg p-4">
                <dt class="text-sm font-medium text-gray-500">创建人</dt>
                <dd class="text-sm text-gray-900">{{ object.created_by|default:"系统" }}</dd>
            </div>
            <div class="bg-gray-50 rounded-lg p-4">
                <dt class="text-sm font-medium text-gray-500">更新人</dt>
                <dd class="text-sm text-gray-900">{{ object.updated_by|default:"系统" }}</dd>
            </div>
        </div>
    </div>
</div>
{% endblock %}