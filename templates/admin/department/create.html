{% extends "admin/base_admin.html" %}

{% block page_title %}新建部门{% endblock %}
{% block page_description %}创建新的企业部门信息，完善组织架构{% endblock %}

{% block header_actions %}
<a href="{% url 'organizations:admin:department_list' %}" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center space-x-2 transition-colors duration-200">
    <i data-lucide="arrow-left" class="w-4 h-4"></i>
    <span>返回列表</span>
</a>
{% endblock %}

{% block admin_content %}
<div class="max-w-4xl mx-auto">
    <!-- 进度指示器 -->
    <div class="mb-8">
        <div class="flex items-center justify-center">
            <div class="flex items-center space-x-4">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                        <i data-lucide="building-2" class="w-4 h-4 text-white"></i>
                    </div>
                    <span class="ml-2 text-sm font-medium text-blue-600">基本信息</span>
                </div>
                <div class="w-16 h-0.5 bg-gray-300"></div>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                        <i data-lucide="users" class="w-4 h-4 text-gray-500"></i>
                    </div>
                    <span class="ml-2 text-sm text-gray-500">组织架构</span>
                </div>
                <div class="w-16 h-0.5 bg-gray-300"></div>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                        <i data-lucide="check" class="w-4 h-4 text-gray-500"></i>
                    </div>
                    <span class="ml-2 text-sm text-gray-500">完成</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 主表单卡片 -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
        <!-- 卡片头部 -->
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                    <i data-lucide="building-2" class="w-5 h-5 text-white"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-900">新建部门</h3>
                    <p class="text-sm text-gray-600">填写部门基本信息，建立组织架构</p>
                </div>
            </div>
        </div>

        <form method="post" class="p-6" id="departmentForm">
            {% csrf_token %}

            <!-- 基本信息区域 -->
            <div class="space-y-6">
                <div class="border-b border-gray-200 pb-4">
                    <h4 class="text-md font-semibold text-gray-900 flex items-center">
                        <i data-lucide="info" class="w-4 h-4 mr-2 text-blue-600"></i>
                        基本信息
                    </h4>
                    <p class="mt-1 text-sm text-gray-600">设置部门的基础标识信息</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 部门编号 -->
                    <div class="group">
                        <label for="id_dept_code" class="block text-sm font-medium text-gray-700 mb-2">
                            <i data-lucide="hash" class="w-4 h-4 inline mr-1 text-gray-500"></i>
                            部门编号 <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <input type="text"
                                   name="dept_code"
                                   id="id_dept_code"
                                   class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 group-hover:border-gray-400"
                                   placeholder="如：GM、IT、HR、FIN"
                                   required
                                   maxlength="20"
                                   pattern="[A-Z0-9]+"
                                   title="请输入大写字母和数字组合">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <i data-lucide="building" class="w-4 h-4 text-gray-400"></i>
                            </div>
                        </div>
                        <p class="mt-2 text-sm text-gray-500 flex items-center">
                            <i data-lucide="lightbulb" class="w-3 h-3 mr-1"></i>
                            部门的唯一编号，建议使用大写字母，如：GM（总经理室）、IT（信息技术部）
                        </p>
                        <div id="dept_code_feedback" class="mt-1 text-sm hidden"></div>
                    </div>

                    <!-- 部门名称 -->
                    <div class="group">
                        <label for="id_name" class="block text-sm font-medium text-gray-700 mb-2">
                            <i data-lucide="tag" class="w-4 h-4 inline mr-1 text-gray-500"></i>
                            部门名称 <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <input type="text"
                                   name="name"
                                   id="id_name"
                                   class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 group-hover:border-gray-400"
                                   placeholder="如：总经理室、信息技术部、人力资源部"
                                   required
                                   maxlength="100">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <i data-lucide="users" class="w-4 h-4 text-gray-400"></i>
                            </div>
                        </div>
                        <p class="mt-2 text-sm text-gray-500 flex items-center">
                            <i data-lucide="lightbulb" class="w-3 h-3 mr-1"></i>
                            部门的完整名称，将显示在系统各处
                        </p>
                        <div id="name_feedback" class="mt-1 text-sm hidden"></div>
                    </div>
                </div>
            </div>

            <!-- 组织架构区域 -->
            <div class="space-y-6 pt-6">
                <div class="border-b border-gray-200 pb-4">
                    <h4 class="text-md font-semibold text-gray-900 flex items-center">
                        <i data-lucide="sitemap" class="w-4 h-4 mr-2 text-blue-600"></i>
                        组织架构
                    </h4>
                    <p class="mt-1 text-sm text-gray-600">设置部门在组织架构中的位置和负责人</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 上级部门 -->
                    <div class="group">
                        <label for="id_parent_department" class="block text-sm font-medium text-gray-700 mb-2">
                            <i data-lucide="arrow-up" class="w-4 h-4 inline mr-1 text-gray-500"></i>
                            上级部门
                        </label>
                        <div class="relative">
                            <select name="parent_department" id="id_parent_department"
                                    class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 group-hover:border-gray-400 appearance-none bg-white">
                                <option value="">请选择上级部门（可选）</option>
                                {% for dept in parent_departments %}
                                    <option value="{{ dept.id }}">{{ dept.dept_code }} - {{ dept.name }}</option>
                                {% endfor %}
                            </select>
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <i data-lucide="chevron-down" class="w-4 h-4 text-gray-400"></i>
                            </div>
                        </div>
                        <p class="mt-2 text-sm text-gray-500 flex items-center">
                            <i data-lucide="lightbulb" class="w-3 h-3 mr-1"></i>
                            选择所属的上级部门，总经理室通常为顶级部门
                        </p>
                    </div>

                    <!-- 部门经理 -->
                    <div class="group">
                        <label for="id_manager" class="block text-sm font-medium text-gray-700 mb-2">
                            <i data-lucide="crown" class="w-4 h-4 inline mr-1 text-gray-500"></i>
                            部门经理
                        </label>
                        <div class="relative">
                            <select name="manager" id="id_manager"
                                    class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 group-hover:border-gray-400 appearance-none bg-white">
                                <option value="">请选择部门经理（可选）</option>
                                {% for staff in managers %}
                                    <option value="{{ staff.id }}">{{ staff.name }} ({{ staff.department.name }})</option>
                                {% endfor %}
                            </select>
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <i data-lucide="chevron-down" class="w-4 h-4 text-gray-400"></i>
                            </div>
                        </div>
                        <p class="mt-2 text-sm text-gray-500 flex items-center">
                            <i data-lucide="lightbulb" class="w-3 h-3 mr-1"></i>
                            选择部门负责人，可以后续修改
                        </p>
                    </div>
                </div>
            </div>

            <!-- 详细信息区域 -->
            <div class="space-y-6 pt-6">
                <div class="border-b border-gray-200 pb-4">
                    <h4 class="text-md font-semibold text-gray-900 flex items-center">
                        <i data-lucide="file-text" class="w-4 h-4 mr-2 text-blue-600"></i>
                        详细信息
                    </h4>
                    <p class="mt-1 text-sm text-gray-600">添加部门的详细描述和配置选项</p>
                </div>

                <!-- 部门描述 -->
                <div class="group">
                    <label for="id_description" class="block text-sm font-medium text-gray-700 mb-2">
                        <i data-lucide="align-left" class="w-4 h-4 inline mr-1 text-gray-500"></i>
                        部门描述
                    </label>
                    <div class="relative">
                        <textarea name="description"
                                  id="id_description"
                                  rows="4"
                                  class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 group-hover:border-gray-400 resize-none"
                                  placeholder="请描述部门的主要职责、业务范围和工作内容...&#10;&#10;例如：&#10;• 负责公司信息系统的建设和维护&#10;• 提供技术支持和培训服务&#10;• 制定信息安全政策和标准"
                                  maxlength="500"></textarea>
                        <div class="absolute bottom-3 right-3 text-xs text-gray-400">
                            <span id="description_count">0</span>/500
                        </div>
                    </div>
                    <p class="mt-2 text-sm text-gray-500 flex items-center">
                        <i data-lucide="lightbulb" class="w-3 h-3 mr-1"></i>
                        详细描述部门的职责范围，有助于员工了解部门定位
                    </p>
                </div>

                <!-- 配置选项 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 是否启用 -->
                    <div class="group">
                        <label class="block text-sm font-medium text-gray-700 mb-3">
                            <i data-lucide="power" class="w-4 h-4 inline mr-1 text-gray-500"></i>
                            部门状态
                        </label>
                        <div class="flex items-center p-4 bg-gray-50 rounded-lg border border-gray-200 group-hover:bg-gray-100 transition-colors duration-200">
                            <input type="checkbox"
                                   name="is_active"
                                   id="id_is_active"
                                   class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                                   checked>
                            <div class="ml-3">
                                <span class="text-sm font-medium text-gray-900">启用部门</span>
                                <p class="text-xs text-gray-500">启用后部门将在系统中可见和使用</p>
                            </div>
                        </div>
                    </div>

                    <!-- 排序 -->
                    <div class="group">
                        <label for="id_sort_order" class="block text-sm font-medium text-gray-700 mb-2">
                            <i data-lucide="arrow-up-down" class="w-4 h-4 inline mr-1 text-gray-500"></i>
                            显示排序
                        </label>
                        <div class="relative">
                            <input type="number"
                                   name="sort_order"
                                   id="id_sort_order"
                                   class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 group-hover:border-gray-400"
                                   placeholder="0"
                                   min="0"
                                   max="9999"
                                   value="0">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <i data-lucide="hash" class="w-4 h-4 text-gray-400"></i>
                            </div>
                        </div>
                        <p class="mt-2 text-sm text-gray-500 flex items-center">
                            <i data-lucide="lightbulb" class="w-3 h-3 mr-1"></i>
                            数字越小排序越靠前，用于控制部门在列表中的显示顺序
                        </p>
                    </div>
                </div>
            </div>

            <!-- 表单错误显示 -->
            {% if form.errors %}
                <div class="rounded-lg bg-red-50 border border-red-200 p-4 mt-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i data-lucide="alert-circle" class="w-5 h-5 text-red-500"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">请修正以下错误：</h3>
                            <div class="mt-2 text-sm text-red-700">
                                <ul class="list-disc list-inside space-y-1">
                                    {% for field, errors in form.errors.items %}
                                        {% for error in errors %}
                                            <li>{{ field }}: {{ error }}</li>
                                        {% endfor %}
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- 提交按钮区域 -->
            <div class="flex items-center justify-between pt-8 border-t border-gray-200">
                <div class="flex items-center text-sm text-gray-500">
                    <i data-lucide="info" class="w-4 h-4 mr-1"></i>
                    <span>创建后可以在部门列表中进行编辑和管理</span>
                </div>

                <div class="flex items-center space-x-4">
                    <a href="{% url 'organizations:admin:department_list' %}"
                       class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-all duration-200 flex items-center space-x-2">
                        <i data-lucide="x" class="w-4 h-4"></i>
                        <span>取消</span>
                    </a>
                    <button type="submit"
                            id="submitBtn"
                            class="px-8 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-200 flex items-center space-x-2 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                        <i data-lucide="plus" class="w-4 h-4"></i>
                        <span id="submitText">创建部门</span>
                        <div id="submitSpinner" class="hidden">
                            <svg class="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        </div>
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- 帮助信息卡片 -->
    <div class="mt-8 bg-blue-50 border border-blue-200 rounded-xl p-6">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <i data-lucide="help-circle" class="w-5 h-5 text-blue-600 mt-0.5"></i>
            </div>
            <div class="ml-3">
                <h4 class="text-sm font-medium text-blue-900">创建部门小贴士</h4>
                <div class="mt-2 text-sm text-blue-800">
                    <ul class="list-disc list-inside space-y-1">
                        <li>部门编号建议使用简短的大写字母组合，便于识别和记忆</li>
                        <li>部门名称应该准确反映部门的职能和定位</li>
                        <li>合理设置上级部门有助于建立清晰的组织架构</li>
                        <li>部门描述可以帮助员工更好地了解部门职责</li>
                        <li>排序数字可以控制部门在各种列表中的显示顺序</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('departmentForm');
    const submitBtn = document.getElementById('submitBtn');
    const submitText = document.getElementById('submitText');
    const submitSpinner = document.getElementById('submitSpinner');
    const deptCodeInput = document.getElementById('id_dept_code');
    const nameInput = document.getElementById('id_name');
    const descriptionInput = document.getElementById('id_description');
    const descriptionCount = document.getElementById('description_count');

    // 部门编号实时验证
    deptCodeInput.addEventListener('input', function() {
        const value = this.value.toUpperCase();
        this.value = value;

        const feedback = document.getElementById('dept_code_feedback');
        if (value.length > 0) {
            if (/^[A-Z0-9]+$/.test(value)) {
                feedback.className = 'mt-1 text-sm text-green-600 flex items-center';
                feedback.innerHTML = '<i data-lucide="check" class="w-3 h-3 mr-1"></i>编号格式正确';
                feedback.classList.remove('hidden');
            } else {
                feedback.className = 'mt-1 text-sm text-red-600 flex items-center';
                feedback.innerHTML = '<i data-lucide="x" class="w-3 h-3 mr-1"></i>请使用大写字母和数字';
                feedback.classList.remove('hidden');
            }
        } else {
            feedback.classList.add('hidden');
        }
    });

    // 部门名称实时验证
    nameInput.addEventListener('input', function() {
        const value = this.value.trim();
        const feedback = document.getElementById('name_feedback');

        if (value.length > 0) {
            if (value.length >= 2) {
                feedback.className = 'mt-1 text-sm text-green-600 flex items-center';
                feedback.innerHTML = '<i data-lucide="check" class="w-3 h-3 mr-1"></i>名称长度合适';
                feedback.classList.remove('hidden');
            } else {
                feedback.className = 'mt-1 text-sm text-orange-600 flex items-center';
                feedback.innerHTML = '<i data-lucide="alert-triangle" class="w-3 h-3 mr-1"></i>名称至少需要2个字符';
                feedback.classList.remove('hidden');
            }
        } else {
            feedback.classList.add('hidden');
        }
    });

    // 描述字数统计
    descriptionInput.addEventListener('input', function() {
        const length = this.value.length;
        descriptionCount.textContent = length;

        if (length > 500) {
            descriptionCount.className = 'text-red-500';
            this.value = this.value.substring(0, 500);
            descriptionCount.textContent = '500';
        } else if (length > 400) {
            descriptionCount.className = 'text-orange-500';
        } else {
            descriptionCount.className = 'text-gray-400';
        }
    });

    // 表单提交处理
    form.addEventListener('submit', function(e) {
        submitBtn.disabled = true;
        submitText.textContent = '创建中...';
        submitSpinner.classList.remove('hidden');

        // 简单的客户端验证
        const deptCode = deptCodeInput.value.trim();
        const name = nameInput.value.trim();

        if (!deptCode || !name) {
            e.preventDefault();
            submitBtn.disabled = false;
            submitText.textContent = '创建部门';
            submitSpinner.classList.add('hidden');

            alert('请填写必填字段：部门编号和部门名称');
            return;
        }

        if (!/^[A-Z0-9]+$/.test(deptCode)) {
            e.preventDefault();
            submitBtn.disabled = false;
            submitText.textContent = '创建部门';
            submitSpinner.classList.add('hidden');

            alert('部门编号只能包含大写字母和数字');
            deptCodeInput.focus();
            return;
        }
    });

    // 初始化图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
});
</script>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 表单验证
        const form = document.querySelector('form');
        const deptCodeInput = document.getElementById('id_dept_code');
        const nameInput = document.getElementById('id_name');
        
        // 部门编号格式验证
        deptCodeInput.addEventListener('input', function() {
            const value = this.value.toUpperCase();
            this.value = value.replace(/[^A-Z0-9]/g, '');
            
            if (this.value.length > 10) {
                this.value = this.value.slice(0, 10);
            }
        });
        
        // 表单提交前验证
        form.addEventListener('submit', function(e) {
            let isValid = true;
            
            // 检查必填项
            if (!deptCodeInput.value.trim()) {
                showFieldError(deptCodeInput, '部门编号不能为空');
                isValid = false;
            }
            
            if (!nameInput.value.trim()) {
                showFieldError(nameInput, '部门名称不能为空');
                isValid = false;
            }
            
            if (!isValid) {
                e.preventDefault();
            }
        });
        
        // 显示字段错误
        function showFieldError(field, message) {
            // 移除之前的错误信息
            const existingError = field.parentNode.querySelector('.field-error');
            if (existingError) {
                existingError.remove();
            }
            
            // 添加错误样式
            field.classList.add('border-red-500');
            
            // 显示错误信息
            const errorDiv = document.createElement('div');
            errorDiv.className = 'field-error mt-1 text-sm text-red-600';
            errorDiv.textContent = message;
            field.parentNode.appendChild(errorDiv);
            
            // 聚焦到错误字段
            field.focus();
        }
        
        // 输入时清除错误状态
        [deptCodeInput, nameInput].forEach(input => {
            input.addEventListener('input', function() {
                this.classList.remove('border-red-500');
                const error = this.parentNode.querySelector('.field-error');
                if (error) {
                    error.remove();
                }
            });
        });
    });
</script>
{% endblock %}