<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - 企业考评评分系统</title>
    
    <!-- 设计系统 -->
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/design-system.css' %}">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        .login-bg {
            background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-700) 100%);
        }
        
        .floating-animation {
            animation: floating 3s var(--transition-slow) infinite;
        }
        
        @keyframes floating {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }
        
        .glass-effect {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body class="login-bg min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <!-- 消息提示区域 -->
    {% if messages %}
        <div id="messages" class="fixed top-4 right-4 z-50 space-y-2">
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} px-4 py-3 rounded-lg shadow-lg 
                    {% if message.tags == 'success' %}bg-green-100 border border-green-400 text-green-700
                    {% elif message.tags == 'error' %}bg-red-100 border border-red-400 text-red-700
                    {% elif message.tags == 'warning' %}bg-yellow-100 border border-yellow-400 text-yellow-700
                    {% else %}bg-blue-100 border border-blue-400 text-blue-700{% endif %}">
                    <span class="block sm:inline">{{ message }}</span>
                    <button onclick="this.parentElement.remove()" class="float-right ml-4 text-lg leading-none">&times;</button>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <div class="max-w-md w-full space-y-8">
        <!-- Logo和标题 -->
        <div class="text-center floating-animation">
            <div class="mx-auto h-16 w-16 flex items-center justify-center">
                <img src="{% static 'images/brand/logo.svg' %}" alt="系统Logo" class="w-16 h-16 drop-shadow-lg">
            </div>
            <h2 class="mt-6 text-3xl font-extrabold text-white">
                企业考评评分系统
            </h2>
            <p class="mt-2 text-sm text-white opacity-90">
                管理员登录
            </p>
        </div>

        <!-- 登录表单 -->
        <div class="card card-body glass-effect shadow-2xl">
            <form class="space-y-6" method="post">
                {% csrf_token %}
                
                <div>
                    <label for="username" class="form-label">
                        用户名 / 员工编号
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                        <input id="username" name="username" type="text" required 
                               class="form-input pl-10"
                               placeholder="请输入用户名或员工编号">
                    </div>
                </div>

                <div>
                    <label for="password" class="form-label">
                        密码
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                        </div>
                        <input id="password" name="password" type="password" required 
                               class="form-input pl-10 pr-10"
                               placeholder="请输入密码">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <button type="button" id="toggle-password" class="text-gray-400 hover:text-gray-600">
                                <svg id="eye-icon" class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input id="remember-me" name="remember-me" type="checkbox" 
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="remember-me" class="ml-2 block text-sm text-gray-700">
                            记住我
                        </label>
                    </div>

                    <div class="text-sm">
                        <a href="#" class="font-medium text-blue-600 hover:text-blue-500 transition duration-200">
                            忘记密码？
                        </a>
                    </div>
                </div>

                <div>
                    <button type="submit" id="login-btn"
                            class="btn btn-primary btn-lg w-full group relative transform hover:scale-105">
                        <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                            <svg class="h-5 w-5 text-blue-500 group-hover:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                            </svg>
                        </span>
                        <span id="login-text">立即登录</span>
                    </button>
                </div>
            </form>

            <!-- 匿名端入口 -->
            <div class="mt-6 text-center">
                <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-gray-300"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-white text-gray-500">或者</span>
                    </div>
                </div>
                
                <div class="mt-4">
                    <a href="{% url 'organizations:anonymous:anonymous_login' %}" 
                       class="btn btn-outline-primary btn-md w-full inline-flex justify-center">
                        <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"></path>
                        </svg>
                        匿名考评入口
                    </a>
                </div>
            </div>
        </div>

        <!-- 版权信息 -->
        <div class="text-center text-white text-sm opacity-75">
            <p>&copy; 2025 企业考评评分系统. 保留所有权利.</p>
        </div>
    </div>

    <script>
        // 自动隐藏消息提示
        setTimeout(function() {
            const messages = document.getElementById('messages');
            if (messages) {
                messages.style.display = 'none';
            }
        }, 5000);

        // 密码显示/隐藏切换
        document.getElementById('toggle-password').addEventListener('click', function() {
            const passwordField = document.getElementById('password');
            const eyeIcon = document.getElementById('eye-icon');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                eyeIcon.innerHTML = `
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                `;
            } else {
                passwordField.type = 'password';
                eyeIcon.innerHTML = `
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                `;
            }
        });

        // 表单提交处理
        document.querySelector('form').addEventListener('submit', function(e) {
            const loginBtn = document.getElementById('login-btn');
            const loginText = document.getElementById('login-text');
            
            loginBtn.disabled = true;
            loginText.innerHTML = '<svg class="animate-spin h-5 w-5 mr-2 inline" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>登录中...';
        });

        // Enter键自动提交
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.querySelector('form').submit();
            }
        });
    </script>
</body>
</html>