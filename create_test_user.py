#!/usr/bin/env python
"""
创建JWT测试用户脚本
"""
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
django.setup()

from organizations.models import Staff, Department

def create_test_user():
    """创建测试用户"""
    try:
        # 1. 创建测试部门
        dept, created = Department.objects.get_or_create(
            dept_code='TEST001',
            defaults={
                'name': '测试部门',
                'is_active': True,
                'sort_order': 1
            }
        )
        
        if created:
            print(f"✅ 创建部门成功: {dept.name}")
        else:
            print(f"ℹ️  部门已存在: {dept.name}")
        
        # 2. 检查是否已有测试用户
        existing_user = Staff.objects.filter(username='testadmin').first()
        if existing_user:
            print(f"⚠️  用户已存在: {existing_user.username}")
            print(f"   员工编号: {existing_user.employee_no}")
            print(f"   姓名: {existing_user.name}")
            print(f"   角色: {existing_user.role}")
            print(f"   部门: {existing_user.department.name}")
            print(f"   是否激活: {existing_user.is_active}")
            
            # 重置密码
            existing_user.set_password('test123456')
            existing_user.save()
            print("🔄 密码已重置为: test123456")
            return existing_user
        
        # 3. 创建新的测试用户
        staff = Staff.objects.create(
            username='testadmin',
            employee_no='EMP001',
            name='测试管理员',
            department=dept,
            role='super_admin',
            is_active=True,
            email='<EMAIL>'
        )
        
        # 4. 设置密码
        staff.set_password('test123456')
        staff.save()
        
        print("✅ 测试用户创建成功!")
        print(f"   用户名: {staff.username}")
        print(f"   密码: test123456")
        print(f"   员工编号: {staff.employee_no}")
        print(f"   姓名: {staff.name}")
        print(f"   角色: {staff.role}")
        print(f"   部门: {staff.department.name}")
        
        return staff
        
    except Exception as e:
        print(f"❌ 创建用户失败: {e}")
        return None

def verify_user():
    """验证用户能否正常登录"""
    try:
        staff = Staff.objects.get(username='testadmin')
        
        # 检查用户基本信息
        print("\n🔍 用户验证信息:")
        print(f"   用户名: {staff.username}")
        print(f"   员工编号: {staff.employee_no}")
        print(f"   姓名: {staff.name}")
        print(f"   角色: {staff.role}")
        print(f"   是否激活: {staff.is_active}")
        print(f"   是否管理员: {staff.is_manager}")
        print(f"   部门: {staff.department.name if staff.department else '无'}")
        
        # 测试密码验证
        from django.contrib.auth.hashers import check_password
        is_valid = check_password('test123456', staff.password)
        print(f"   密码验证: {'✅ 正确' if is_valid else '❌ 错误'}")
        
        # 检查账户锁定状态
        is_locked = staff.is_account_locked() if hasattr(staff, 'is_account_locked') else False
        print(f"   账户锁定: {'🔒 已锁定' if is_locked else '🔓 未锁定'}")
        
        return staff
        
    except Staff.DoesNotExist:
        print("❌ 用户不存在")
        return None
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return None

if __name__ == '__main__':
    print("🚀 开始创建JWT测试用户...")
    print("=" * 50)
    
    # 创建用户
    user = create_test_user()
    
    if user:
        print("\n" + "=" * 50)
        # 验证用户
        verify_user()
        
        print("\n" + "=" * 50)
        print("🎉 测试用户准备完成!")
        print("\n📝 登录信息:")
        print("   URL: http://127.0.0.1:8000/admin/login/")
        print("   用户名: testadmin")
        print("   密码: test123456")
        print("\n现在可以进行JWT功能测试了！")
    else:
        print("\n❌ 用户创建失败，请检查数据库连接和配置")