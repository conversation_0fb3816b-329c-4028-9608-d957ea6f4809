#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查URL字符串占位符问题
"""

import os
import re
import sys

# 处理Windows编码问题
if sys.platform.startswith('win'):
    import io
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

def check_placeholder_issues():
    """检查字符串占位符问题"""
    print("=== 检查URL字符串占位符问题 ===")
    
    # 定义有问题的模式
    problematic_patterns = [
        r'{% url ["\'][^"\']*["\'] ["\'][A-Z_]+["\'] %}\.replace\(["\'][A-Z_]+["\']',
        r'{% url "[^"]*" "[A-Z_]+".*replace\(',
        r"{% url '[^']*' '[A-Z_]+'.*replace\(",
    ]
    
    issues_found = []
    
    # 检查模板文件
    template_dirs = ['templates']
    
    for template_dir in template_dirs:
        dir_path = os.path.join(os.path.dirname(__file__), template_dir)
        if os.path.exists(dir_path):
            for root, dirs, files in os.walk(dir_path):
                for file in files:
                    if file.endswith('.html'):
                        file_path = os.path.join(root, file)
                        
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                                
                                # 检查每个问题模式
                                for pattern in problematic_patterns:
                                    matches = re.findall(pattern, content)
                                    for match in matches:
                                        rel_path = os.path.relpath(file_path, os.path.dirname(__file__))
                                        issues_found.append({
                                            'file': rel_path,
                                            'pattern': match[:100] + '...' if len(match) > 100 else match
                                        })
                                        
                        except Exception as e:
                            print(f"⚠️  无法读取文件 {file_path}: {str(e)}")
    
    if issues_found:
        print(f"❌ 发现 {len(issues_found)} 个占位符问题:")
        for issue in issues_found:
            print(f"   📁 {issue['file']}: {issue['pattern']}")
        return False
    else:
        print("✅ 没有发现URL字符串占位符问题")
        return True

def check_direct_urls():
    """检查是否使用了正确的直接URL路径"""
    print("\n=== 检查直接URL路径 ===")
    
    expected_paths = [
        ('/admin/api/permissions/staff/', 'staff权限API'),
        ('/admin/api/permissions/role/', 'role详情API'),
        ('/admin/anonymous-codes/staff/', '匿名编号历史API'),
    ]
    
    template_files = [
        'templates/admin/permissions/manage.html',
        'templates/admin/anonymous/manage.html'
    ]
    
    all_correct = True
    
    for template_file in template_files:
        file_path = os.path.join(os.path.dirname(__file__), template_file)
        
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                    print(f"\n🔍 检查 {template_file}:")
                    
                    for expected_path, description in expected_paths:
                        if expected_path in content:
                            print(f"   ✅ {description}: 找到正确的直接路径")
                        else:
                            # print(f"   ⚠️  {description}: 未找到直接路径 (可能正常)")
                            pass
                            
            except Exception as e:
                print(f"   ❌ 读取失败: {str(e)}")
                all_correct = False
        else:
            print(f"   ❌ 文件不存在: {template_file}")
            all_correct = False
    
    return all_correct

def main():
    """主函数"""
    print("URL字符串占位符问题检查")
    print("=" * 50)
    
    # 检查占位符问题
    no_placeholder_issues = check_placeholder_issues()
    
    # 检查直接URL路径
    direct_urls_ok = check_direct_urls()
    
    # 总结
    print("\n" + "=" * 50)
    print("检查结果总结")
    print("=" * 50)
    
    if no_placeholder_issues and direct_urls_ok:
        print("🎉 所有URL占位符问题已修复！")
        print("\n💡 现在可以测试:")
        print("   1. 重启Django开发服务器")
        print("   2. 访问权限管理页面: /admin/permissions/")
        print("   3. 访问匿名编号管理页面: /admin/anonymous-codes/")
        print("   4. 测试JavaScript功能按钮")
        return True
    else:
        print("⚠️  仍有问题需要解决:")
        if not no_placeholder_issues:
            print("   - 仍有URL占位符问题")
        if not direct_urls_ok:
            print("   - 直接URL路径检查失败")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)