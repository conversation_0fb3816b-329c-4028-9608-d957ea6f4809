# Django 导入错误解决方案

## 🚨 原始错误

```
ImportError: cannot import name 'wizard_views' from 'evaluations.views'
```

## 🔍 问题分析

### 根本原因
在添加向导系统时，我们创建了新的 `evaluations/views/wizard_views.py` 文件，但是原有的 `evaluations/views.py` 文件仍然存在，导致了模块结构冲突：

1. **原始结构**：`evaluations/views.py` (单文件)
2. **新增结构**：`evaluations/views/` (包目录)
3. **冲突**：Python 无法确定应该导入哪个 views

### 具体问题
1. `evaluations/urls.py` 中导入 `from .views import wizard_views`
2. 但 `evaluations/views.py` 是单文件，不包含 `wizard_views` 模块
3. 新创建的 `evaluations/views/wizard_views.py` 无法被正确识别

---

## 🛠️ 解决步骤

### 步骤 1：重构 views 为包结构
```bash
# 将原始 views.py 重命名为 views_backup.py
move evaluations\views.py evaluations\views_backup.py
```

### 步骤 2：创建 views 包
```python
# 创建 evaluations/views/__init__.py
from evaluations.views_backup import *
from . import wizard_views
```

### 步骤 3：重构 services 为包结构
```bash
# 将原始 services.py 重命名为 services_original.py  
move evaluations\services.py evaluations\services_original.py
```

### 步骤 4：创建 services 包
```python
# 创建 evaluations/services/__init__.py
from .simplified_weighting import SimplifiedWeightingService, WeightingWizardService
from .template_wizard import TemplateWizardService, TemplateRecommendationService
from .assignment_strategy import AssignmentStrategyService
```

### 步骤 5：修复导入路径
```python
# 修改 evaluations/views_backup.py 中的导入
from .services_original import EvaluationSubmissionService, EvaluationProgressService
```

### 步骤 6：修复其他导入错误
```python
# 修复 communications/services.py 中缺少的导入
from django.db.models import QuerySet

# 修复 common/views.py 中的语法错误
@require_auth
def get_token_status(request):
    """获取令牌状态"""
    return JsonResponse({
        'status': 'active',
        'user': request.user.username if hasattr(request, 'user') else None
    })
```

---

## 📁 最终文件结构

```
evaluations/
├── __init__.py
├── models.py
├── admin.py
├── forms.py
├── urls.py
├── views_backup.py          # 原始视图文件
├── services_original.py     # 原始服务文件
├── views/                   # 新的视图包
│   ├── __init__.py         # 导入所有视图
│   └── wizard_views.py     # 向导视图
└── services/               # 新的服务包
    ├── __init__.py         # 导入所有服务
    ├── simplified_weighting.py
    ├── template_wizard.py
    └── assignment_strategy.py
```

---

## ✅ 验证结果

### 系统检查通过
```bash
$ python manage.py check
System check identified no issues (0 silenced).
```

### 开发服务器启动成功
```bash
$ python manage.py runserver
Django version 5.2.3, using settings 'UniversalStaffEvaluation3.settings'
Starting development server at http://127.0.0.1:8000/
```

---

## 🎯 关键解决要点

### 1. **包结构转换**
- 将单文件模块转换为包结构
- 使用 `__init__.py` 维护向后兼容性

### 2. **导入路径管理**
- 避免循环导入
- 使用相对导入和绝对导入的正确组合

### 3. **向后兼容性**
- 保留原始文件作为备份
- 通过 `__init__.py` 重新导出所有原有功能

### 4. **错误修复策略**
- 逐步解决每个导入错误
- 先解决结构性问题，再解决语法问题

---

## 🚀 现在可以使用的功能

### 向导系统 URL
```python
# 向导仪表板
/evaluations/admin/wizard/

# 权重配置向导
/evaluations/admin/wizard/weight/{batch_id}/

# 模板创建向导  
/evaluations/admin/wizard/template/

# 分配策略向导
/evaluations/admin/wizard/assignment/{batch_id}/
```

### 新增服务类
```python
from evaluations.services import (
    SimplifiedWeightingService,
    TemplateWizardService,
    AssignmentStrategyService
)
```

---

## 📝 经验总结

### 避免类似问题的建议
1. **规划模块结构**：在添加新功能前规划好模块结构
2. **渐进式重构**：逐步将单文件模块转换为包结构
3. **测试导入**：每次修改后立即测试导入是否正常
4. **保持向后兼容**：确保现有功能不受影响

### 最佳实践
1. **使用包结构**：对于复杂的应用，使用包结构更易维护
2. **清晰的 `__init__.py`**：明确导出需要的类和函数
3. **避免循环导入**：合理设计模块依赖关系
4. **一致的命名**：保持文件和模块命名的一致性

---

## 🎉 结论

通过系统性的重构和错误修复，我们成功解决了导入错误问题，并建立了更好的模块结构。现在向导系统可以正常工作，为用户提供简化的业务逻辑配置体验。
