#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试浏览器登录流程（模拟真实浏览器行为）
"""

import requests
import json
from http.cookies import Simple<PERSON><PERSON><PERSON>

def test_browser_login():
    """测试完整的浏览器登录流程"""
    base_url = "http://127.0.0.1:8000"
    session = requests.Session()  # 使用session来保持cookie
    
    print("=== 测试浏览器登录流程 ===")
    
    # 1. 访问仪表板（未登录）
    print("\n1. 访问仪表板（未登录）...")
    response = session.get(f"{base_url}/admin/", allow_redirects=False)
    print(f"   状态码: {response.status_code}")
    
    if response.status_code == 302:
        redirect_url = response.headers.get('Location', '')
        print(f"   重定向到: {redirect_url}")
        print("   ✅ 正确重定向到登录页面")
    else:
        print("   ❌ 未正确重定向")
        return
    
    # 2. 访问登录页面
    print("\n2. 访问登录页面...")
    login_page_response = session.get(f"{base_url}/admin/login/")
    print(f"   状态码: {login_page_response.status_code}")
    
    if login_page_response.status_code == 200:
        print("   ✅ 登录页面加载成功")
    else:
        print("   ❌ 登录页面加载失败")
        return
    
    # 3. 执行登录
    print("\n3. 执行登录...")
    login_data = {
        "username": "admin",
        "password": "123456"
    }
    
    login_response = session.post(
        f"{base_url}/api/login/",
        json=login_data,
        headers={"Content-Type": "application/json"}
    )
    
    print(f"   登录API状态码: {login_response.status_code}")
    
    if login_response.status_code == 200:
        data = login_response.json()
        if data.get('success'):
            print("   ✅ 登录成功")
            access_token = data.get('tokens', {}).get('access_token')
            refresh_token = data.get('tokens', {}).get('refresh_token')
            
            # 手动设置cookie（模拟前端JavaScript行为）
            session.cookies.set('access_token', access_token, path='/')
            session.cookies.set('refresh_token', refresh_token, path='/')
            
            print(f"   用户信息: {data.get('user', {}).get('name')}")
            print(f"   设置Cookie: access_token, refresh_token")
        else:
            print(f"   ❌ 登录失败: {data.get('message')}")
            return
    else:
        print(f"   ❌ 登录API失败: {login_response.status_code}")
        return
    
    # 4. 登录后访问仪表板
    print("\n4. 登录后访问仪表板...")
    dashboard_response = session.get(f"{base_url}/admin/", allow_redirects=False)
    print(f"   状态码: {dashboard_response.status_code}")
    
    if dashboard_response.status_code == 200:
        print("   ✅ 成功访问仪表板")
        
        # 检查页面内容
        content = dashboard_response.text
        if "仪表板" in content or "dashboard" in content.lower():
            print("   ✅ 仪表板内容正确")
        else:
            print("   ⚠️ 仪表板内容可能有问题")
            
        # 检查是否包含用户信息
        if "系统管理员" in content or "admin" in content:
            print("   ✅ 页面包含用户信息")
        else:
            print("   ⚠️ 页面可能缺少用户信息")
            
    elif dashboard_response.status_code == 302:
        redirect_url = dashboard_response.headers.get('Location', '')
        print(f"   ❌ 仍然被重定向到: {redirect_url}")
        print("   这表明登录后的认证可能有问题")
    else:
        print(f"   ❌ 访问失败: {dashboard_response.status_code}")
    
    # 5. 测试其他受保护页面
    print("\n5. 测试其他受保护页面...")
    
    test_pages = [
        ("/admin/departments/", "部门管理"),
        ("/admin/staff/", "员工管理"),
    ]
    
    for url, name in test_pages:
        response = session.get(f"{base_url}{url}", allow_redirects=False)
        print(f"   {name} ({url}): {response.status_code}")
        
        if response.status_code == 200:
            print(f"     ✅ 可以访问{name}")
        elif response.status_code == 302:
            print(f"     ❌ 被重定向，可能认证失败")
        else:
            print(f"     ⚠️ 状态码: {response.status_code}")
    
    # 6. 检查Cookie
    print("\n6. 检查Cookie...")
    cookies = session.cookies
    print(f"   当前Cookie: {dict(cookies)}")
    
    if 'access_token' in cookies:
        print("   ✅ access_token Cookie存在")
    else:
        print("   ❌ access_token Cookie缺失")
        
    if 'refresh_token' in cookies:
        print("   ✅ refresh_token Cookie存在")
    else:
        print("   ❌ refresh_token Cookie缺失")

if __name__ == '__main__':
    test_browser_login()
    print("\n=== 测试完成 ===")
