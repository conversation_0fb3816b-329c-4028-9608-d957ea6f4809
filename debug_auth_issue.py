#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试认证问题
检查用户认证状态和角色权限
"""

import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
django.setup()

from organizations.models import Staff

def debug_auth_issue():
    """调试认证问题"""
    
    print("🔍 调试认证问题")
    print("=" * 50)
    
    # 1. 检查所有用户的角色
    print("\n1. 检查所有用户的角色和权限:")
    print("-" * 30)
    
    staffs = Staff.objects.filter(is_active=True, deleted_at__isnull=True)
    for staff in staffs:
        print(f"用户: {staff.username} ({staff.name})")
        print(f"  角色: {staff.role} ({staff.get_role_display()})")
        print(f"  is_manager: {staff.is_manager}")
        print(f"  is_admin: {staff.is_admin}")
        print(f"  is_super_admin: {staff.is_super_admin}")
        print()
    
    # 2. 检查测试管理员用户
    print("\n2. 重点检查admin用户:")
    print("-" * 30)
    
    try:
        admin_user = Staff.objects.get(username='admin')
        print(f"用户: {admin_user.username}")
        print(f"角色: {admin_user.role}")
        print(f"is_manager: {admin_user.is_manager}")
        print(f"部门: {admin_user.department}")
        print(f"is_active: {admin_user.is_active}")
        print(f"deleted_at: {admin_user.deleted_at}")
        
        # 模拟session检查
        print(f"\n模拟session检查结果:")
        print(f"  用户角色在管理员列表中: {admin_user.role in ['super_admin', 'system_admin', 'hr_admin', 'eval_admin', 'dept_manager', 'admin']}")
        
    except Staff.DoesNotExist:
        print("❌ admin用户不存在")
    
    # 3. 检查所有角色的权限定义
    print("\n3. 检查角色权限定义:")
    print("-" * 30)
    
    ROLE_CHOICES = [
        ('super_admin', '超级管理员'),
        ('system_admin', '系统管理员'),
        ('hr_admin', 'HR管理员'),
        ('eval_admin', '考评管理员'),
        ('dept_manager', '部门经理'),
        ('admin', '普通管理员'),
        ('employee', '普通员工'),
    ]
    
    manager_roles = ['super_admin', 'system_admin', 'hr_admin', 'eval_admin', 'dept_manager', 'admin']
    
    for role_code, role_name in ROLE_CHOICES:
        is_manager = role_code in manager_roles
        print(f"角色: {role_code} ({role_name}) - 管理员权限: {is_manager}")

if __name__ == '__main__':
    debug_auth_issue()