#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
匿名编号安全升级测试脚本
用于验证安全编号生成、兼容性登录等功能
"""

import os
import sys
import json
import requests
from datetime import datetime

# 添加项目路径到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def print_test_header(title):
    """打印测试标题"""
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"{'='*60}")

def print_test_result(test_name, success, message=""):
    """打印测试结果"""
    status = "✅ 通过" if success else "❌ 失败"
    print(f"{test_name}: {status}")
    if message:
        print(f"   → {message}")
    print()

def test_anonymous_code_generation():
    """测试1: 安全编号生成API功能"""
    print_test_header("测试1: 安全编号生成API功能")
    
    try:
        # 导入Django模块
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
        import django
        django.setup()
        
        from common.security.anonymous import SecureAnonymousCodeGenerator
        from organizations.models import Staff
        
        # 测试1.1: 基本生成功能
        generator = SecureAnonymousCodeGenerator()
        code1 = generator.generate_secure_code(1, 1)  # 使用员工ID=1, 部门ID=1
        code2 = generator.generate_secure_code(2, 1)  # 使用员工ID=2, 部门ID=1
        
        print_test_result("基本生成功能", 
                         len(code1) == 14 and len(code2) == 14 and code1 != code2,
                         f"生成编号长度: {len(code1)}, 唯一性: {'是' if code1 != code2 else '否'}")
        
        # 测试1.2: 编号格式验证 - 使用正确的格式（XXXX-XXXX-XXXX）
        is_valid_format = generator.validate_code_format(code1)
        
        print_test_result("编号格式验证",
                         is_valid_format,
                         f"编号: {code1}")
        
        # 测试1.3: 批量生成唯一性
        codes = [generator.generate_secure_code(i+10, 1) for i in range(10)]
        all_unique = len(codes) == len(set(codes))
        
        print_test_result("批量生成唯一性",
                         all_unique,
                         f"生成10个编号，唯一性: {'通过' if all_unique else '失败'}")
        
        return True
        
    except Exception as e:
        print_test_result("安全编号生成API", False, f"错误: {str(e)}")
        return False

def test_login_compatibility():
    """测试2: 新旧编号兼容性登录"""
    print_test_header("测试2: 新旧编号兼容性登录")
    
    try:
        from organizations.models import Staff
        from organizations.views import AnonymousLoginView
        from django.test import RequestFactory
        from django.contrib.sessions.middleware import SessionMiddleware
        from django.contrib.messages.middleware import MessageMiddleware
        
        # 创建测试员工数据
        test_staff = Staff.objects.filter(deleted_at__isnull=True).first()
        if not test_staff:
            print_test_result("测试数据准备", False, "没有找到测试员工数据")
            return False
        
        # 测试2.1: 检查员工编号状态
        has_old_code = bool(test_staff.anonymous_code)
        has_new_code = bool(test_staff.new_anonymous_code)
        
        print_test_result("员工编号状态检查",
                         has_old_code or has_new_code,
                         f"旧编号: {'有' if has_old_code else '无'}, 新编号: {'有' if has_new_code else '无'}")
        
        # 测试2.2: 编号验证函数
        from common.security.anonymous import AnonymousCodeValidator
        validator = AnonymousCodeValidator()
        
        if has_new_code:
            # 测试新编号验证
            is_valid_new, error_msg, staff_obj = validator.validate_login_code(test_staff.new_anonymous_code)
            print_test_result("新编号验证",
                             is_valid_new,
                             f"新编号: {test_staff.new_anonymous_code}")
        
        if has_old_code:
            # 测试旧编号验证
            is_valid_old, error_msg, staff_obj = validator.validate_login_code(test_staff.anonymous_code)
            print_test_result("旧编号验证",
                             is_valid_old,
                             f"旧编号: {test_staff.anonymous_code}")
        
        return True
        
    except Exception as e:
        print_test_result("兼容性登录测试", False, f"错误: {str(e)}")
        return False

def test_ui_display():
    """测试3: 前端界面显示更新"""
    print_test_header("测试3: 前端界面显示更新")
    
    try:
        from organizations.models import Staff
        
        # 获取员工数据统计
        total_staff = Staff.objects.filter(deleted_at__isnull=True).count()
        staff_with_new_codes = Staff.objects.filter(
            deleted_at__isnull=True,
            new_anonymous_code__isnull=False
        ).count()
        staff_with_old_codes_only = Staff.objects.filter(
            deleted_at__isnull=True,
            new_anonymous_code__isnull=True,
            anonymous_code__isnull=False
        ).count()
        
        upgrade_percentage = (staff_with_new_codes / total_staff * 100) if total_staff > 0 else 0
        
        print_test_result("数据统计获取", True,
                         f"总员工: {total_staff}, 安全编号: {staff_with_new_codes}, "
                         f"待升级: {staff_with_old_codes_only}, 升级率: {upgrade_percentage:.1f}%")
        
        # 测试员工列表显示数据
        sample_staff = Staff.objects.filter(deleted_at__isnull=True).first()
        if sample_staff:
            display_info = {
                'name': sample_staff.name,
                'has_new_code': bool(sample_staff.new_anonymous_code),
                'has_old_code': bool(sample_staff.anonymous_code),
                'display_code': sample_staff.new_anonymous_code or sample_staff.anonymous_code,
                'status': '安全' if sample_staff.new_anonymous_code else '待升级'
            }
            
            print_test_result("员工显示数据", True,
                             f"员工: {display_info['name']}, 状态: {display_info['status']}")
        
        return True
        
    except Exception as e:
        print_test_result("前端显示测试", False, f"错误: {str(e)}")
        return False

def test_batch_migration():
    """测试4: 批量迁移功能"""
    print_test_header("测试4: 批量迁移功能")
    
    try:
        from common.security.anonymous import AnonymousCodeMigrator
        from organizations.models import Staff
        
        # 测试4.1: 迁移器初始化
        migrator = AnonymousCodeMigrator()
        
        # 获取需要迁移的员工
        need_migration = Staff.objects.filter(
            deleted_at__isnull=True,
            new_anonymous_code__isnull=True,
            anonymous_code__isnull=False
        ).count()
        
        print_test_result("迁移数据统计", True,
                         f"需要迁移的员工数量: {need_migration}")
        
        # 测试4.2: 模拟小批量迁移
        if need_migration > 0:
            # 只迁移前3个员工进行测试
            test_staff_list = Staff.objects.filter(
                deleted_at__isnull=True,
                new_anonymous_code__isnull=True,
                anonymous_code__isnull=False
            )[:3]
            
            # 测试迁移状态获取
            migration_status = migrator.get_migration_status()
            print_test_result("迁移状态获取",
                             'total_staff' in migration_status,
                             f"迁移进度: {migration_status.get('migration_progress', 0):.1f}%")
            
            print_test_result("小批量迁移测试",
                             migration_status.get('is_complete', False),
                             f"已完成迁移，无需测试")
        else:
            print_test_result("批量迁移测试", True, "所有员工已使用安全编号，无需迁移")
        
        return True
        
    except Exception as e:
        print_test_result("批量迁移测试", False, f"错误: {str(e)}")
        return False

def test_unique_constraints():
    """测试5: 安全编号唯一性约束"""
    print_test_header("测试5: 安全编号唯一性约束")
    
    try:
        from organizations.models import Staff
        from django.db import models
        
        # 测试5.1: 数据库唯一性检查
        all_new_codes = Staff.objects.filter(
            deleted_at__isnull=True,
            new_anonymous_code__isnull=False
        ).values_list('new_anonymous_code', flat=True)
        
        unique_codes = set(all_new_codes)
        is_unique = len(all_new_codes) == len(unique_codes)
        
        print_test_result("数据库唯一性检查",
                         is_unique,
                         f"总编号: {len(all_new_codes)}, 唯一编号: {len(unique_codes)}")
        
        # 测试5.2: 新旧编号交叉检查
        all_old_codes = Staff.objects.filter(
            deleted_at__isnull=True,
            anonymous_code__isnull=False
        ).values_list('anonymous_code', flat=True)
        
        cross_check = set(all_new_codes) & set(all_old_codes)
        no_cross_duplicate = len(cross_check) == 0
        
        print_test_result("新旧编号交叉唯一性",
                         no_cross_duplicate,
                         f"重复编号数量: {len(cross_check)}")
        
        return True
        
    except Exception as e:
        print_test_result("唯一性约束测试", False, f"错误: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("匿名编号安全升级测试开始")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    test_results = []
    
    # 执行各项测试
    test_results.append(("安全编号生成API", test_anonymous_code_generation()))
    test_results.append(("新旧编号兼容性登录", test_login_compatibility()))
    test_results.append(("前端界面显示更新", test_ui_display()))
    test_results.append(("批量迁移功能", test_batch_migration()))
    test_results.append(("安全编号唯一性约束", test_unique_constraints()))
    
    # 汇总测试结果
    print_test_header("测试结果汇总")
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试均通过！匿名编号安全升级功能正常工作。")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查和修复。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)