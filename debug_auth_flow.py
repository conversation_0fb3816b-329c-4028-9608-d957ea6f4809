#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试认证流程
"""

import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
django.setup()

from organizations.models import Staff
from common.security.jwt_auth import JWTAuthentication

def debug_auth_flow():
    """调试认证流程"""
    
    print("🔍 调试认证流程")
    print("=" * 50)
    
    # 1. 检查admin用户
    try:
        admin_user = Staff.objects.get(username='admin')
        print(f"✅ 找到admin用户: {admin_user.name}")
        print(f"   角色: {admin_user.role}")
        print(f"   is_manager: {admin_user.is_manager}")
        print(f"   is_active: {admin_user.is_active}")
        print(f"   部门: {admin_user.department}")
        
        # 2. 测试JWT token生成
        print(f"\n🔑 测试JWT token生成:")
        try:
            tokens = JWTAuthentication.generate_tokens(admin_user)
            print(f"✅ JWT token生成成功")
            print(f"   access_token长度: {len(tokens['access_token'])}")
            print(f"   refresh_token长度: {len(tokens['refresh_token'])}")
            print(f"   expires_in: {tokens['expires_in']}")
            
            # 3. 测试token验证
            print(f"\n🔐 测试token验证:")
            payload = JWTAuthentication.verify_token(tokens['access_token'], 'access')
            if payload:
                print(f"✅ Token验证成功")
                print(f"   staff_id: {payload['staff_id']}")
                print(f"   username: {payload['username']}")
            else:
                print(f"❌ Token验证失败")
                
        except Exception as e:
            print(f"❌ JWT操作失败: {e}")
            
    except Staff.DoesNotExist:
        print("❌ admin用户不存在")
        return
    
    # 4. 检查中间件设置
    print(f"\n⚙️ 检查中间件设置:")
    from django.conf import settings
    middleware = settings.MIDDLEWARE
    
    jwt_auth_index = -1
    auth_required_index = -1
    
    for i, mw in enumerate(middleware):
        if 'JWTAuthenticationMiddleware' in mw:
            jwt_auth_index = i
            print(f"✅ JWT认证中间件位置: {i}")
        elif 'AuthRequiredMiddleware' in mw:
            auth_required_index = i
            print(f"✅ 认证要求中间件位置: {i}")
    
    if jwt_auth_index < auth_required_index:
        print(f"✅ 中间件顺序正确")
    else:
        print(f"❌ 中间件顺序错误，JWT认证中间件应该在认证要求中间件之前")
    
    # 5. 检查URL路径匹配
    print(f"\n🌐 检查URL路径:")
    test_paths = [
        '/admin/departments/',
        '/admin/login/',
        '/admin/',
    ]
    
    from common.security.middleware import JWTAuthenticationMiddleware, AuthRequiredMiddleware
    
    jwt_middleware = JWTAuthenticationMiddleware()
    auth_middleware = AuthRequiredMiddleware()
    
    for path in test_paths:
        class MockRequest:
            def __init__(self, path):
                self.path = path
                self.method = 'GET'
        
        request = MockRequest(path)
        should_skip_jwt = jwt_middleware._should_skip_auth(request)
        requires_auth = auth_middleware._requires_auth(request)
        
        print(f"   路径: {path}")
        print(f"     跳过JWT认证: {should_skip_jwt}")
        print(f"     需要认证: {requires_auth}")

if __name__ == '__main__':
    debug_auth_flow()