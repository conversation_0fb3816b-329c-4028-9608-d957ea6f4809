# -*- coding: utf-8 -*-
"""
自定义认证中间件
支持管理端和匿名端双重登录认证
"""

from django.http import HttpResponseRedirect
from django.urls import reverse
from django.utils.deprecation import MiddlewareMixin
from django.contrib import messages
from django.utils import timezone
from .models import Staff, StaffLoginLog
import logging

logger = logging.getLogger(__name__)


class CustomAuthMiddleware(MiddlewareMixin):
    """
    自定义认证中间件
    处理管理端和匿名端的认证逻辑
    """
    
    # 无需认证的URL路径
    EXEMPT_URLS = [
        '/admin/login/',           # 管理端登录页
        '/anonymous/login/',       # 匿名端登录页
        '/admin/logout/',          # 管理端登出
        '/anonymous/logout/',      # 匿名端登出
        '/api/login/',             # API登录端点
        '/api/logout/',            # API登出端点
        '/api/token/refresh/',     # Token刷新端点
        '/static/',               # 静态文件
        '/media/',                # 媒体文件
    ]
    
    # 需要管理员权限的URL路径
    ADMIN_REQUIRED_URLS = [
        '/admin/',
        '/evaluations/admin/',
        '/reports/admin/',
        '/communications/admin/',  # 站内通信管理端
    ]
    
    def process_request(self, request):
        """
        处理请求前的认证检查
        """
        path = request.path
        
        # 检查是否为免认证路径
        if self._is_exempt_url(path):
            return None
            
        # 获取当前用户信息
        current_staff = self._get_current_staff(request)
        
        # 检查匿名端访问
        if path.startswith('/anonymous/'):
            return self._handle_anonymous_access(request, current_staff)
            
        # 检查管理端访问
        if self._is_admin_url(path):
            return self._handle_admin_access(request, current_staff)
            
        return None
        
    def _is_exempt_url(self, path):
        """检查是否为免认证URL"""
        return any(path.startswith(url) for url in self.EXEMPT_URLS)
        
    def _is_admin_url(self, path):
        """检查是否为管理端URL"""
        return any(path.startswith(url) for url in self.ADMIN_REQUIRED_URLS)
        
    def _get_current_staff(self, request):
        """获取当前登录的员工信息"""
        staff_id = request.session.get('staff_id')
        if not staff_id:
            return None
            
        try:
            return Staff.objects.get(id=staff_id, deleted_at__isnull=True)
        except Staff.DoesNotExist:
            # 清除无效session
            request.session.flush()
            return None
            
    def _handle_anonymous_access(self, request, current_staff):
        """处理匿名端访问认证"""
        if not current_staff:
            # 未登录，重定向到匿名登录页
            return HttpResponseRedirect(reverse('organizations:anonymous:anonymous_login'))
            
        # 检查匿名登录状态
        if not request.session.get('anonymous_login'):
            return HttpResponseRedirect(reverse('organizations:anonymous:anonymous_login'))
            
        # 记录用户信息到request
        request.current_staff = current_staff
        request.is_anonymous_login = True
        return None
        
    def _handle_admin_access(self, request, current_staff):
        """处理管理端访问认证"""
        if not current_staff:
            # 未登录，重定向到管理登录页
            return HttpResponseRedirect(reverse('organizations:admin:login'))
            
        # 检查管理员权限
        if not current_staff.is_manager:
            messages.error(request, '您没有访问管理后台的权限')
            return HttpResponseRedirect(reverse('organizations:anonymous:anonymous_login'))
            
        # 检查管理端登录状态
        if not request.session.get('admin_login'):
            return HttpResponseRedirect(reverse('organizations:admin:login'))
            
        # 记录用户信息到request
        request.current_staff = current_staff
        request.is_admin_login = True
        return None


class LoginLogMiddleware(MiddlewareMixin):
    """
    登录日志记录中间件
    自动记录用户登录和活动日志
    """
    
    def process_request(self, request):
        """记录用户活动日志"""
        # 只记录已认证用户的活动
        if hasattr(request, 'current_staff') and request.current_staff:
            staff = request.current_staff
            login_type = 'anonymous' if getattr(request, 'is_anonymous_login', False) else 'admin'
            
            # 更新用户最后登录时间
            try:
                staff.update_last_login()
            except Exception as e:
                logger.error(f"更新用户活动时间失败: {e}")
                
        return None


def require_admin_permission(view_func):
    """
    管理员权限装饰器
    确保只有管理员能访问特定视图
    """
    def wrapper(request, *args, **kwargs):
        if not hasattr(request, 'current_staff') or not request.current_staff:
            return HttpResponseRedirect(reverse('organizations:admin:login'))
            
        staff = request.current_staff
        if not staff.is_manager and not staff.is_admin:
            messages.error(request, '您没有执行此操作的权限')
            return HttpResponseRedirect(reverse('organizations:admin:dashboard'))
            
        return view_func(request, *args, **kwargs)
    return wrapper


def require_anonymous_login(view_func):
    """
    匿名登录装饰器
    确保匿名用户已正确登录
    """
    def wrapper(request, *args, **kwargs):
        if not hasattr(request, 'current_staff') or not request.current_staff:
            return HttpResponseRedirect(reverse('organizations:anonymous:anonymous_login'))
            
        if not getattr(request, 'is_anonymous_login', False):
            return HttpResponseRedirect(reverse('organizations:anonymous:anonymous_login'))
            
        return view_func(request, *args, **kwargs)
    return wrapper