# -*- coding: utf-8 -*-
"""
权限管理视图
实现RBAC权限系统的管理界面

功能包括：
- 角色管理：查看、编辑用户角色
- 权限查看：查看角色权限详情
- 权限测试：测试用户权限
- 批量角色分配：批量修改用户角色
"""

from django.shortcuts import render, get_object_or_404, redirect
from django.http import JsonResponse, HttpResponseForbidden
from django.contrib import messages
from django.core.paginator import Paginator
from django.db import transaction
from django.db.models import Q, Count
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views import View
from django.views.generic import ListView, DetailView
from datetime import datetime
import json
import logging

from .models import Staff, Department
from common.security.permissions import (
    Permission, Role, RolePermissionMapping,
    require_permission, require_role,
    permission_manager, get_role_display_name,
    get_all_permissions, get_role_permissions,
    log_role_change
)

logger = logging.getLogger(__name__)


class PermissionManagementView(ListView):
    """权限管理主页面"""
    model = Staff
    template_name = 'admin/permissions/manage.html'
    context_object_name = 'staff_list'
    paginate_by = 20
    
    @method_decorator(require_permission(Permission.SYS_MANAGE_PERMISSIONS))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)
    
    def get_queryset(self):
        """获取员工查询集"""
        queryset = Staff.objects.filter(deleted_at__isnull=True).select_related('department')
        
        # 搜索功能
        search = self.request.GET.get('search', '').strip()
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(username__icontains=search) |
                Q(employee_no__icontains=search)
            )
        
        # 角色筛选
        role_filter = self.request.GET.get('role', '').strip()
        if role_filter:
            queryset = queryset.filter(role=role_filter)
        
        # 部门筛选
        department_filter = self.request.GET.get('department', '').strip()
        if department_filter:
            try:
                department_id = int(department_filter)
                queryset = queryset.filter(department_id=department_id)
            except (ValueError, TypeError):
                pass
        
        return queryset.order_by('department__name', 'employee_no')
    
    def get_context_data(self, **kwargs):
        """获取模板上下文数据"""
        context = super().get_context_data(**kwargs)
        
        # 角色选项
        context['role_choices'] = Role.CHOICES
        
        # 部门选项
        context['departments'] = Department.objects.filter(
            deleted_at__isnull=True
        ).order_by('dept_code')
        
        # 权限统计
        all_permissions = get_all_permissions()
        context['total_permissions'] = len(all_permissions)
        context['admin_count'] = Staff.objects.filter(
            role__in=['super_admin', 'system_admin', 'hr_admin', 'eval_admin', 'admin'],
            deleted_at__isnull=True
        ).count()
        context['active_departments'] = Department.objects.filter(
            deleted_at__isnull=True, is_active=True
        ).count()
        
        # 角色权限映射
        context['role_mappings'] = self._get_role_mappings()
        
        # 搜索参数
        context['search'] = self.request.GET.get('search', '')
        context['role_filter'] = self.request.GET.get('role', '')
        context['department_filter'] = self.request.GET.get('department', '')
        
        return context
    
    def _get_role_mappings(self):
        """获取角色权限映射数据"""
        role_mappings = []
        
        # 角色图标和颜色映射
        role_styles = {
            'super_admin': {'icon': 'crown', 'color_class': 'bg-red-100', 'text_color': 'text-red-600'},
            'system_admin': {'icon': 'settings', 'color_class': 'bg-orange-100', 'text_color': 'text-orange-600'},
            'hr_admin': {'icon': 'users', 'color_class': 'bg-purple-100', 'text_color': 'text-purple-600'},
            'eval_admin': {'icon': 'clipboard-check', 'color_class': 'bg-blue-100', 'text_color': 'text-blue-600'},
            'dept_manager': {'icon': 'briefcase', 'color_class': 'bg-green-100', 'text_color': 'text-green-600'},
            'admin': {'icon': 'user-check', 'color_class': 'bg-yellow-100', 'text_color': 'text-yellow-600'},
            'employee': {'icon': 'user', 'color_class': 'bg-gray-100', 'text_color': 'text-gray-600'},
        }
        
        for role_code, role_name in Role.CHOICES:
            permissions = get_role_permissions(role_code)
            user_count = Staff.objects.filter(role=role_code, deleted_at__isnull=True).count()
            style = role_styles.get(role_code, role_styles['employee'])
            
            # 主要权限（显示前3个）- 将集合转换为列表以支持切片操作
            main_permissions = []
            permissions_list = list(permissions)  # 转换为列表
            for perm in permissions_list[:3]:
                perm_desc = next((desc for code, desc in get_all_permissions() if code == perm), perm)
                main_permissions.append(perm_desc.split('：')[0] if '：' in perm_desc else perm_desc)
            
            role_mappings.append({
                'code': role_code,
                'display_name': role_name,
                'permission_count': len(permissions),
                'main_permissions': main_permissions,
                'user_count': user_count,
                'icon': style['icon'],
                'color_class': style['color_class'],
                'text_color': style['text_color']
            })
        
        return role_mappings


class StaffRoleEditView(View):
    """员工角色编辑视图"""
    
    @method_decorator(require_permission(Permission.SYS_MANAGE_PERMISSIONS))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)
    
    def get(self, request, staff_id):
        """获取员工角色编辑页面"""
        staff = get_object_or_404(Staff, id=staff_id, deleted_at__isnull=True)
        
        context = {
            'staff': staff,
            'role_choices': Role.CHOICES,
            'current_permissions': permission_manager.get_user_permissions(staff),
            'all_permissions': get_all_permissions(),
        }
        
        return render(request, 'admin/permissions/role_edit.html', context)
    
    def post(self, request, staff_id):
        """更新员工角色"""
        staff = get_object_or_404(Staff, id=staff_id, deleted_at__isnull=True)
        
        new_role = request.POST.get('role', '').strip()
        if not new_role or new_role not in dict(Role.CHOICES):
            messages.error(request, '无效的角色选择')
            return redirect('organizations:admin:staff_role_edit', staff_id=staff_id)
        
        # 检查是否尝试修改超级管理员角色
        if staff.role == Role.SUPER_ADMIN and new_role != Role.SUPER_ADMIN:
            if not request.staff.role == Role.SUPER_ADMIN:
                messages.error(request, '只有超级管理员可以修改超级管理员的角色')
                return redirect('organizations:admin:staff_role_edit', staff_id=staff_id)
        
        # 检查是否尝试将他人设为超级管理员
        if new_role == Role.SUPER_ADMIN and request.staff.role != Role.SUPER_ADMIN:
            messages.error(request, '只有超级管理员可以设置超级管理员角色')
            return redirect('organizations:admin:staff_role_edit', staff_id=staff_id)
        
        # 记录角色变更
        old_role = staff.role
        
        try:
            with transaction.atomic():
                staff.role = new_role
                staff.save(update_fields=['role', 'updated_at'])
                
                # 记录审计日志
                log_role_change(staff, old_role, new_role, request.staff)
                
                messages.success(request, f'成功将 {staff.name} 的角色从 {get_role_display_name(old_role)} 更改为 {get_role_display_name(new_role)}')
                
        except Exception as e:
            logger.error(f"角色变更失败: {str(e)}")
            messages.error(request, '角色变更失败，请稍后重试')
        
        return redirect('organizations:admin:permissions_manage')


class RolePermissionsDetailView(DetailView):
    """角色权限详情视图"""
    template_name = 'admin/permissions/role_detail.html'
    context_object_name = 'role_info'
    
    @method_decorator(require_permission(Permission.SYS_MANAGE_PERMISSIONS))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)
    
    def get_object(self):
        """获取角色信息"""
        role = self.kwargs.get('role')
        if role not in dict(Role.CHOICES):
            return None
        
        return {
            'role': role,
            'display_name': get_role_display_name(role),
            'permissions': get_role_permissions(role),
            'staff_count': Staff.objects.filter(role=role, deleted_at__isnull=True).count(),
        }
    
    def get_context_data(self, **kwargs):
        """获取模板上下文数据"""
        context = super().get_context_data(**kwargs)
        
        if not context['role_info']:
            context['error'] = '角色不存在'
            return context
        
        role_info = context['role_info']
        permissions = role_info['permissions']
        
        # 按类别分组权限
        permission_groups = {
            '组织管理': [p for p in permissions if p.startswith('org.')],
            '考评管理': [p for p in permissions if p.startswith('eval.')],
            '报告权限': [p for p in permissions if p.startswith('report.')],
            '系统管理': [p for p in permissions if p.startswith('sys.')],
            '数据访问': [p for p in permissions if p.startswith('data.')],
            '特殊权限': [p for p in permissions if p.startswith('special.')],
        }
        
        context['permission_groups'] = permission_groups
        
        # 该角色的员工列表
        context['staff_list'] = Staff.objects.filter(
            role=role_info['role'],
            deleted_at__isnull=True
        ).select_related('department').order_by('department__name', 'employee_no')[:20]
        
        return context


class BatchRoleAssignView(View):
    """批量角色分配视图"""
    
    @method_decorator(require_permission(Permission.SYS_MANAGE_PERMISSIONS))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)
    
    def get(self, request):
        """获取批量角色分配页面"""
        context = {
            'departments': Department.objects.filter(deleted_at__isnull=True).order_by('dept_code'),
            'role_choices': Role.CHOICES,
        }
        return render(request, 'admin/permissions/batch_assign.html', context)
    
    @method_decorator(csrf_exempt)
    def post(self, request):
        """执行批量角色分配"""
        try:
            data = json.loads(request.body)
            staff_ids = data.get('staff_ids', [])
            new_role = data.get('role', '').strip()
            
            if not staff_ids or not new_role:
                return JsonResponse({
                    'success': False,
                    'error': '参数不完整'
                })
            
            if new_role not in dict(Role.CHOICES):
                return JsonResponse({
                    'success': False,
                    'error': '无效的角色选择'
                })
            
            # 检查权限
            if new_role == Role.SUPER_ADMIN and request.staff.role != Role.SUPER_ADMIN:
                return JsonResponse({
                    'success': False,
                    'error': '只有超级管理员可以设置超级管理员角色'
                })
            
            success_count = 0
            failed_items = []
            
            with transaction.atomic():
                staff_list = Staff.objects.filter(
                    id__in=staff_ids,
                    deleted_at__isnull=True
                )
                
                for staff in staff_list:
                    try:
                        # 检查是否尝试修改超级管理员
                        if (staff.role == Role.SUPER_ADMIN and 
                            new_role != Role.SUPER_ADMIN and 
                            request.staff.role != Role.SUPER_ADMIN):
                            failed_items.append({
                                'name': staff.name,
                                'reason': '只有超级管理员可以修改超级管理员的角色'
                            })
                            continue
                        
                        old_role = staff.role
                        staff.role = new_role
                        staff.save(update_fields=['role', 'updated_at'])
                        
                        # 记录审计日志
                        log_role_change(staff, old_role, new_role, request.staff)
                        
                        success_count += 1
                        
                    except Exception as e:
                        failed_items.append({
                            'name': staff.name,
                            'reason': str(e)
                        })
            
            return JsonResponse({
                'success': True,
                'success_count': success_count,
                'failed_count': len(failed_items),
                'failed_items': failed_items[:10],  # 只返回前10个失败项
                'message': f'成功修改 {success_count} 个员工的角色'
            })
            
        except Exception as e:
            logger.error(f"批量角色分配失败: {str(e)}")
            return JsonResponse({
                'success': False,
                'error': '操作失败，请稍后重试'
            })


class PermissionTestView(View):
    """权限测试视图"""
    
    @method_decorator(require_permission(Permission.SYS_MANAGE_PERMISSIONS))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)
    
    def get(self, request):
        """获取权限测试页面"""
        context = {
            'staff_list': Staff.objects.filter(deleted_at__isnull=True).select_related('department').order_by('name'),
            'all_permissions': get_all_permissions(),
        }
        return render(request, 'admin/permissions/test.html', context)
    
    def post(self, request):
        """执行权限测试"""
        staff_id = request.POST.get('staff_id')
        permissions_to_test = request.POST.getlist('permissions')
        
        if not staff_id or not permissions_to_test:
            return JsonResponse({
                'success': False,
                'error': '参数不完整'
            })
        
        try:
            staff = Staff.objects.get(id=staff_id, deleted_at__isnull=True)
        except Staff.DoesNotExist:
            return JsonResponse({
                'success': False,
                'error': '员工不存在'
            })
        
        test_results = []
        for permission in permissions_to_test:
            has_perm = permission_manager.has_permission(staff, permission)
            test_results.append({
                'permission': permission,
                'has_permission': has_perm,
                'result_text': '✅ 有权限' if has_perm else '❌ 无权限'
            })
        
        return JsonResponse({
            'success': True,
            'staff_name': staff.name,
            'staff_role': get_role_display_name(staff.role),
            'test_results': test_results
        })


# ==================== Ajax API视图 ====================

class StaffListApiView(View):
    """员工列表API（用于批量操作）"""
    
    @method_decorator(require_permission(Permission.SYS_MANAGE_PERMISSIONS))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)
    
    def get(self, request):
        """获取员工列表数据"""
        department_id = request.GET.get('department_id')
        role_filter = request.GET.get('role')
        search = request.GET.get('search', '').strip()
        
        queryset = Staff.objects.filter(deleted_at__isnull=True).select_related('department')
        
        if department_id:
            try:
                queryset = queryset.filter(department_id=int(department_id))
            except (ValueError, TypeError):
                pass
        
        if role_filter:
            queryset = queryset.filter(role=role_filter)
        
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(username__icontains=search) |
                Q(employee_no__icontains=search)
            )
        
        staff_data = []
        for staff in queryset.order_by('department__name', 'employee_no')[:100]:
            staff_data.append({
                'id': staff.id,
                'name': staff.name,
                'employee_no': staff.employee_no,
                'department_name': staff.department.name if staff.department else '未分配',
                'role': staff.role,
                'role_display': get_role_display_name(staff.role),
                'is_active': staff.is_active,
            })
        
        return JsonResponse({
            'success': True,
            'data': staff_data
        })


class RolePermissionsApiView(View):
    """角色权限API"""
    
    @method_decorator(require_permission(Permission.SYS_MANAGE_PERMISSIONS))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)
    
    def get(self, request, role):
        """获取指定角色的权限列表"""
        if role not in dict(Role.CHOICES):
            return JsonResponse({
                'success': False,
                'error': '角色不存在'
            })
        
        permissions = get_role_permissions(role)
        all_permissions = get_all_permissions()
        
        permission_data = []
        for perm_code, perm_desc in all_permissions:
            permission_data.append({
                'code': perm_code,
                'description': perm_desc,
                'has_permission': perm_code in permissions
            })
        
        return JsonResponse({
            'success': True,
            'role': role,
            'role_display': get_role_display_name(role),
            'permissions': permission_data
        })


# ==================== 前端界面支持视图 ====================

@require_permission(Permission.SYS_MANAGE_PERMISSIONS)
def edit_staff_role_ajax(request):
    """AJAX方式编辑员工角色"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': '只支持POST请求'})
    
    try:
        data = json.loads(request.body)
        staff_id = data.get('staff_id')
        new_role = data.get('new_role')
        reason = data.get('reason', '').strip()
        
        if not all([staff_id, new_role, reason]):
            return JsonResponse({'success': False, 'message': '参数不完整'})
        
        staff = get_object_or_404(Staff, id=staff_id, deleted_at__isnull=True)
        
        if new_role not in dict(Role.CHOICES):
            return JsonResponse({'success': False, 'message': '无效的角色选择'})
        
        # 权限检查
        if staff.role == Role.SUPER_ADMIN and new_role != Role.SUPER_ADMIN:
            if request.staff.role != Role.SUPER_ADMIN:
                return JsonResponse({'success': False, 'message': '只有超级管理员可以修改超级管理员的角色'})
        
        if new_role == Role.SUPER_ADMIN and request.staff.role != Role.SUPER_ADMIN:
            return JsonResponse({'success': False, 'message': '只有超级管理员可以设置超级管理员角色'})
        
        # 执行角色变更
        old_role = staff.role
        with transaction.atomic():
            staff.role = new_role
            staff.save(update_fields=['role', 'updated_at'])
            
            # 记录审计日志
            log_role_change(staff, old_role, new_role, request.staff)
        
        return JsonResponse({
            'success': True,
            'message': f'成功将 {staff.name} 的角色从 {get_role_display_name(old_role)} 更改为 {get_role_display_name(new_role)}'
        })
        
    except Exception as e:
        logger.error(f"AJAX角色变更失败: {str(e)}")
        return JsonResponse({'success': False, 'message': '操作失败，请稍后重试'})


@require_permission(Permission.SYS_MANAGE_PERMISSIONS) 
def batch_role_assignment_ajax(request):
    """AJAX方式批量角色分配"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': '只支持POST请求'})
    
    try:
        data = json.loads(request.body)
        staff_ids = data.get('staff_ids', [])
        new_role = data.get('new_role')
        reason = data.get('reason', '').strip()
        
        if not all([staff_ids, new_role, reason]):
            return JsonResponse({'success': False, 'message': '参数不完整'})
        
        if new_role not in dict(Role.CHOICES):
            return JsonResponse({'success': False, 'message': '无效的角色选择'})
        
        # 权限检查
        if new_role == Role.SUPER_ADMIN and request.staff.role != Role.SUPER_ADMIN:
            return JsonResponse({'success': False, 'message': '只有超级管理员可以设置超级管理员角色'})
        
        success_count = 0
        failed_items = []
        
        with transaction.atomic():
            staff_list = Staff.objects.filter(id__in=staff_ids, deleted_at__isnull=True)
            
            for staff in staff_list:
                try:
                    # 权限检查
                    if (staff.role == Role.SUPER_ADMIN and 
                        new_role != Role.SUPER_ADMIN and 
                        request.staff.role != Role.SUPER_ADMIN):
                        failed_items.append({
                            'name': staff.name,
                            'reason': '只有超级管理员可以修改超级管理员的角色'
                        })
                        continue
                    
                    old_role = staff.role
                    staff.role = new_role
                    staff.save(update_fields=['role', 'updated_at'])
                    
                    # 记录审计日志
                    log_role_change(staff, old_role, new_role, request.staff)
                    
                    success_count += 1
                    
                except Exception as e:
                    failed_items.append({
                        'name': staff.name,
                        'reason': str(e)
                    })
        
        return JsonResponse({
            'success': True,
            'updated_count': success_count,
            'failed_count': len(failed_items),
            'failed_items': failed_items[:10],
            'message': f'成功更新 {success_count} 个员工的角色'
        })
        
    except Exception as e:
        logger.error(f"批量角色分配失败: {str(e)}")
        return JsonResponse({'success': False, 'message': '操作失败，请稍后重试'})


@require_permission(Permission.SYS_MANAGE_PERMISSIONS)
def test_permissions_system(request):
    """运行权限系统测试"""
    try:
        import subprocess
        import os
        
        # 获取项目根目录
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        test_script = os.path.join(project_root, 'test_permissions_system.py')
        
        # 运行测试脚本
        result = subprocess.run(
            ['python', test_script],
            cwd=project_root,
            capture_output=True,
            text=True,
            timeout=300  # 5分钟超时
        )
        
        return JsonResponse({
            'success': result.returncode == 0,
            'output': result.stdout,
            'error': result.stderr if result.returncode != 0 else None
        })
        
    except subprocess.TimeoutExpired:
        return JsonResponse({
            'success': False,
            'error': '测试脚本执行超时（5分钟）'
        })
    except Exception as e:
        logger.error(f"权限系统测试失败: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': f'测试失败: {str(e)}'
        })


@require_permission(Permission.SYS_MANAGE_PERMISSIONS)
def role_details(request, role_code):
    """查看角色详情"""
    if role_code not in dict(Role.CHOICES):
        return JsonResponse({'success': False, 'message': '角色不存在'})
    
    permissions = get_role_permissions(role_code)
    staff_list = Staff.objects.filter(role=role_code, deleted_at__isnull=True).select_related('department')
    
    # 按类别分组权限
    all_permissions = get_all_permissions()
    permission_groups = {
        '组织管理': [p for p in permissions if p.startswith('org.')],
        '考评管理': [p for p in permissions if p.startswith('eval.')],
        '报告权限': [p for p in permissions if p.startswith('report.')],
        '系统管理': [p for p in permissions if p.startswith('sys.')],
        '数据访问': [p for p in permissions if p.startswith('data.')],
        '特殊权限': [p for p in permissions if p.startswith('special.')],
    }
    
    context = {
        'role_code': role_code,
        'role_name': get_role_display_name(role_code),
        'permission_count': len(permissions),
        'permission_groups': permission_groups,
        'staff_count': staff_list.count(),
        'staff_list': [
            {
                'name': staff.name,
                'employee_no': staff.employee_no,
                'department': staff.department.name if staff.department else '未分配',
                'last_login': staff.last_login.strftime('%Y-%m-%d %H:%M') if staff.last_login else '从未登录'
            }
            for staff in staff_list[:20]
        ]
    }
    
    return render(request, 'admin/permissions/role_details.html', context)


@require_permission(Permission.SYS_MANAGE_PERMISSIONS)
def staff_permissions(request, staff_id):
    """查看员工权限详情""" 
    staff = get_object_or_404(Staff, id=staff_id, deleted_at__isnull=True)
    
    user_permissions = permission_manager.get_user_permissions(staff)
    all_permissions = get_all_permissions()
    
    # 权限详情
    permission_details = []
    for perm_code, perm_desc in all_permissions:
        permission_details.append({
            'code': perm_code,
            'description': perm_desc,
            'has_permission': perm_code in user_permissions,
            'category': perm_code.split('.')[0]
        })
    
    # 按类别分组
    permission_groups = {}
    for perm in permission_details:
        category = perm['category']
        if category not in permission_groups:
            permission_groups[category] = []
        permission_groups[category].append(perm)
    
    context = {
        'staff': staff,
        'role_name': get_role_display_name(staff.role),
        'permission_count': len(user_permissions),
        'permission_groups': permission_groups,
        'accessible_departments': permission_manager.get_accessible_departments(staff)
    }
    
    return render(request, 'admin/permissions/staff_permissions.html', context)


@require_permission(Permission.SYS_MANAGE_PERMISSIONS)
def staff_history(request, staff_id):
    """查看员工操作历史"""
    staff = get_object_or_404(Staff, id=staff_id, deleted_at__isnull=True)
    
    from common.models import AuditLog
    
    # 获取相关审计日志
    logs = AuditLog.objects.filter(
        target_model='Staff',
        target_id=staff_id
    ).order_by('-created_at')[:50]
    
    context = {
        'staff': staff,
        'logs': logs
    }
    
    return render(request, 'admin/permissions/staff_history.html', context)