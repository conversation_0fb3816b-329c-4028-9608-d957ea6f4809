# -*- coding: utf-8 -*-
"""
URL处理相关的模板标签
用于处理URL参数的保持和构建
"""

from django import template
from django.http import QueryDict
from urllib.parse import urlencode

register = template.Library()


@register.simple_tag(takes_context=True)
def url_replace(context, **kwargs):
    """
    替换或添加URL参数，保持其他参数不变
    
    用法：
    {% url_replace page=2 %}  # 设置page=2，保持其他参数
    {% url_replace page=2 view='table' %}  # 设置多个参数
    """
    request = context['request']
    query = request.GET.copy()
    
    for key, value in kwargs.items():
        if value is None:
            # 如果值为None，则删除该参数
            query.pop(key, None)
        else:
            query[key] = value
    
    return '?' + query.urlencode()


@register.simple_tag(takes_context=True)
def preserve_params(context, **kwargs):
    """
    保持当前URL参数，只替换指定的参数
    
    用法：
    {% preserve_params page=2 %}
    """
    return url_replace(context, **kwargs)


@register.simple_tag(takes_context=True)
def add_param(context, key, value):
    """
    添加单个参数到当前URL
    
    用法：
    {% add_param 'page' 2 %}
    """
    return url_replace(context, **{key: value})


@register.simple_tag(takes_context=True)
def remove_param(context, key):
    """
    从当前URL中移除指定参数
    
    用法：
    {% remove_param 'page' %}
    """
    return url_replace(context, **{key: None})


@register.simple_tag(takes_context=True)
def current_url_with_page(context, page_number):
    """
    构建包含页码的URL，保持其他所有参数
    
    用法：
    {% current_url_with_page 2 %}
    """
    return url_replace(context, page=page_number)


@register.simple_tag(takes_context=True)
def get_current_param(context, param_name, default=''):
    """
    获取当前URL中的参数值
    
    用法：
    {% get_current_param 'view' 'card' %}
    """
    request = context['request']
    return request.GET.get(param_name, default)


@register.filter
def url_encode(value):
    """
    URL编码过滤器
    
    用法：
    {{ some_value|url_encode }}
    """
    if value is None:
        return ''
    return urlencode({'': value})[1:]  # 移除开头的'='


@register.inclusion_tag('admin/includes/pagination_links.html', takes_context=True)
def pagination_links(context, page_obj):
    """
    生成分页链接，自动保持当前URL参数
    
    用法：
    {% pagination_links page_obj %}
    """
    return {
        'page_obj': page_obj,
        'request': context['request'],
    }
