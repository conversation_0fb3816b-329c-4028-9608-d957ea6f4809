# Generated by Django 5.2.3 on 2025-07-30 07:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('organizations', '0005_update_role_field'),
    ]

    operations = [
        migrations.AddField(
            model_name='staff',
            name='account_locked_until',
            field=models.DateTimeField(blank=True, help_text='账户锁定的截止时间', null=True, verbose_name='账户锁定截止时间'),
        ),
        migrations.AddField(
            model_name='staff',
            name='failed_login_attempts',
            field=models.PositiveIntegerField(default=0, help_text='连续失败登录的次数', verbose_name='失败登录尝试次数'),
        ),
        migrations.AddField(
            model_name='staff',
            name='force_password_change',
            field=models.BooleanField(default=False, help_text='是否强制用户在下次登录时修改密码', verbose_name='强制修改密码'),
        ),
        migrations.AddField(
            model_name='staff',
            name='last_token_refresh',
            field=models.DateTimeField(blank=True, help_text='JWT token最后刷新的时间，用于token撤销', null=True, verbose_name='最后Token刷新时间'),
        ),
        migrations.AddField(
            model_name='staff',
            name='password_changed_at',
            field=models.DateTimeField(blank=True, help_text='密码最后修改的时间', null=True, verbose_name='密码修改时间'),
        ),
    ]
