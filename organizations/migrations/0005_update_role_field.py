# Generated by Django 4.2+ on 2025-07-29
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('organizations', '0004_remove_staff_org_staff_new_anon_code_idx_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='staff',
            name='role',
            field=models.CharField(
                choices=[
                    ('super_admin', '超级管理员'),
                    ('system_admin', '系统管理员'),
                    ('hr_admin', 'HR管理员'),
                    ('eval_admin', '考评管理员'),
                    ('dept_manager', '部门经理'),
                    ('admin', '普通管理员'),
                    ('employee', '普通员工'),
                ],
                default='employee',
                help_text='用户的权限角色',
                max_length=30,
                verbose_name='用户角色'
            ),
        ),
    ]