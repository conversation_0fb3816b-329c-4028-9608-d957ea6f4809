# Generated by Django 5.2.4 on 2025-07-29 02:04

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('organizations', '0003_add_new_anonymous_code'),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='staff',
            name='org_staff_new_anon_code_idx',
        ),
        migrations.RemoveIndex(
            model_name='staff',
            name='org_staff_anon_gen_time_idx',
        ),
        migrations.RemoveIndex(
            model_name='staff',
            name='org_staff_anon_version_idx',
        ),
        migrations.RemoveIndex(
            model_name='staff',
            name='org_staff_anon_active_idx',
        ),
        migrations.RemoveField(
            model_name='staff',
            name='enable_two_factor',
        ),
        migrations.RemoveField(
            model_name='staff',
            name='failed_login_attempts',
        ),
        migrations.RemoveField(
            model_name='staff',
            name='force_password_change',
        ),
        migrations.RemoveField(
            model_name='staff',
            name='last_failed_login',
        ),
        migrations.RemoveField(
            model_name='staff',
            name='last_password_change_reminder',
        ),
        migrations.RemoveField(
            model_name='staff',
            name='last_token_refresh',
        ),
        migrations.RemoveField(
            model_name='staff',
            name='locked_until',
        ),
        migrations.RemoveField(
            model_name='staff',
            name='password_changed_at',
        ),
    ]
