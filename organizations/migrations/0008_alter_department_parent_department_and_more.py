# Generated by Django 5.2.3 on 2025-07-31 04:38

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('organizations', '0007_add_is_department_manager_field'),
    ]

    operations = [
        migrations.AlterField(
            model_name='department',
            name='parent_department',
            field=models.ForeignKey(blank=True, help_text='上级部门，总经理室为顶级部门', null=True, on_delete=django.db.models.deletion.SET_NULL, to='organizations.department', verbose_name='上级部门'),
        ),
        migrations.AlterField(
            model_name='position',
            name='department',
            field=models.ForeignKey(help_text='职位所属的部门', null=True, on_delete=django.db.models.deletion.SET_NULL, to='organizations.department', verbose_name='所属部门'),
        ),
        migrations.AlterField(
            model_name='staff',
            name='department',
            field=models.ForeignKey(help_text='员工所属的部门', null=True, on_delete=django.db.models.deletion.SET_NULL, to='organizations.department', verbose_name='所属部门'),
        ),
        migrations.AlterField(
            model_name='staff',
            name='position',
            field=models.ForeignKey(blank=True, help_text='员工的职位', null=True, on_delete=django.db.models.deletion.SET_NULL, to='organizations.position', verbose_name='职位'),
        ),
        migrations.AlterField(
            model_name='staffloginlog',
            name='staff',
            field=models.ForeignKey(help_text='登录的员工', null=True, on_delete=django.db.models.deletion.SET_NULL, to='organizations.staff', verbose_name='员工'),
        ),
    ]
