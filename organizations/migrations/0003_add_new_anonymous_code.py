# -*- coding: utf-8 -*-
"""
匿名编号安全升级迁移
添加新的安全匿名编号字段，支持向后兼容
"""

from django.db import migrations, models


class Migration(migrations.Migration):
    """
    匿名编号安全升级数据库迁移
    
    变更内容：
    1. 添加 new_anonymous_code 字段用于存储新的安全编号
    2. 添加相关的数据库索引
    3. 保持旧的 anonymous_code 字段以确保向后兼容
    """

    dependencies = [
        ('organizations', '0002_add_security_fields'),
    ]

    operations = [
        # 添加新的安全匿名编号字段
        migrations.AddField(
            model_name='staff',
            name='new_anonymous_code',
            field=models.CharField(
                max_length=15,  # XXXX-XXXX-XXXX 格式，总长度14位加1位余量
                unique=True,
                null=True,
                blank=True,
                verbose_name='新安全匿名编号',
                help_text='使用SHA256加密生成的安全匿名编号，格式：XXXX-XXXX-XXXX'
            ),
        ),
        
        # 添加匿名编号生成时间字段
        migrations.AddField(
            model_name='staff',
            name='anonymous_code_generated_at',
            field=models.DateTimeField(
                null=True,
                blank=True,
                verbose_name='匿名编号生成时间',
                help_text='新匿名编号的生成时间'
            ),
        ),
        
        # 添加匿名编号版本字段
        migrations.AddField(
            model_name='staff',
            name='anonymous_code_version',
            field=models.CharField(
                max_length=10,
                default='v1.0',
                verbose_name='匿名编号版本',
                help_text='匿名编号的算法版本，用于追踪和兼容性管理'
            ),
        ),
        
        # 为新字段添加数据库索引
        migrations.AddIndex(
            model_name='staff',
            index=models.Index(
                fields=['new_anonymous_code'],
                name='org_staff_new_anon_code_idx'
            ),
        ),
        
        # 为生成时间添加索引（用于查询和统计）
        migrations.AddIndex(
            model_name='staff',
            index=models.Index(
                fields=['anonymous_code_generated_at'],
                name='org_staff_anon_gen_time_idx'
            ),
        ),
        
        # 为版本字段添加索引
        migrations.AddIndex(
            model_name='staff',
            index=models.Index(
                fields=['anonymous_code_version'],
                name='org_staff_anon_version_idx'
            ),
        ),
        
        # 添加复合索引：活跃用户的新匿名编号查询优化
        migrations.AddIndex(
            model_name='staff',
            index=models.Index(
                fields=['new_anonymous_code', 'is_active', 'deleted_at'],
                name='org_staff_anon_active_idx'
            ),
        ),
    ]