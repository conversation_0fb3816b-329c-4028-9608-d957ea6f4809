# Generated by Django 4.2+ on 2025-07-28

from django.db import migrations, models
import django.utils.timezone

class Migration(migrations.Migration):
    """
    添加安全相关字段到Staff模型
    为JWT认证和账户安全功能提供支持
    """
    
    dependencies = [
        ('organizations', '0001_initial'),
    ]

    operations = [
        # 添加JWT相关字段
        migrations.AddField(
            model_name='staff',
            name='last_token_refresh',
            field=models.DateTimeField(
                null=True, 
                blank=True, 
                verbose_name='最后token刷新时间',
                help_text='用户最后一次刷新JWT token的时间'
            ),
        ),
        
        # 添加账户安全字段
        migrations.AddField(
            model_name='staff',
            name='failed_login_attempts',
            field=models.IntegerField(
                default=0, 
                verbose_name='失败登录尝试次数',
                help_text='连续失败登录的次数，达到上限将锁定账户'
            ),
        ),
        
        migrations.AddField(
            model_name='staff',
            name='locked_until',
            field=models.DateTimeField(
                null=True, 
                blank=True, 
                verbose_name='账户锁定到',
                help_text='账户锁定的截止时间，过期后自动解锁'
            ),
        ),
        
        migrations.AddField(
            model_name='staff',
            name='last_failed_login',
            field=models.DateTimeField(
                null=True, 
                blank=True, 
                verbose_name='最后失败登录时间',
                help_text='最后一次登录失败的时间'
            ),
        ),
        
        # 添加安全策略相关字段
        migrations.AddField(
            model_name='staff',
            name='password_changed_at',
            field=models.DateTimeField(
                null=True, 
                blank=True, 
                verbose_name='密码修改时间',
                help_text='最后一次修改密码的时间'
            ),
        ),
        
        migrations.AddField(
            model_name='staff',
            name='force_password_change',
            field=models.BooleanField(
                default=False,
                verbose_name='强制修改密码',
                help_text='是否强制用户在下次登录时修改密码'
            ),
        ),
        
        # 添加登录安全设置
        migrations.AddField(
            model_name='staff',
            name='enable_two_factor',
            field=models.BooleanField(
                default=False,
                verbose_name='启用双因子认证',
                help_text='是否启用双因子认证（预留功能）'
            ),
        ),
        
        migrations.AddField(
            model_name='staff',
            name='last_password_change_reminder',
            field=models.DateTimeField(
                null=True,
                blank=True,
                verbose_name='最后密码修改提醒时间',
                help_text='最后一次发送密码修改提醒的时间'
            ),
        ),
    ]