# Generated by Django 5.2.4 on 2025-07-25 04:51

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='记录创建时间', verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='记录最后更新时间', verbose_name='更新时间')),
                ('created_by', models.CharField(blank=True, help_text='记录创建者', max_length=50, null=True, verbose_name='创建人')),
                ('updated_by', models.CharField(blank=True, help_text='记录最后更新者', max_length=50, null=True, verbose_name='更新人')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='软删除时间，为空表示未删除', null=True, verbose_name='删除时间')),
                ('deleted_by', models.CharField(blank=True, help_text='软删除操作者', max_length=50, null=True, verbose_name='删除人')),
                ('dept_code', models.CharField(help_text='部门的唯一编号（必填）', max_length=20, unique=True, verbose_name='部门编号')),
                ('name', models.CharField(help_text='部门的名称（必填）', max_length=100, verbose_name='部门名称')),
                ('description', models.TextField(blank=True, help_text='部门职责和描述信息', verbose_name='部门描述')),
                ('is_active', models.BooleanField(default=True, help_text='部门是否处于活跃状态', verbose_name='是否启用')),
                ('sort_order', models.PositiveIntegerField(default=0, help_text='部门显示顺序', verbose_name='排序')),
                ('parent_department', models.ForeignKey(blank=True, help_text='上级部门，总经理室为顶级部门', null=True, on_delete=django.db.models.deletion.CASCADE, to='organizations.department', verbose_name='上级部门')),
            ],
            options={
                'verbose_name': '部门',
                'verbose_name_plural': '部门',
                'ordering': ['sort_order', 'dept_code'],
            },
        ),
        migrations.CreateModel(
            name='Position',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='记录创建时间', verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='记录最后更新时间', verbose_name='更新时间')),
                ('created_by', models.CharField(blank=True, help_text='记录创建者', max_length=50, null=True, verbose_name='创建人')),
                ('updated_by', models.CharField(blank=True, help_text='记录最后更新者', max_length=50, null=True, verbose_name='更新人')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='软删除时间，为空表示未删除', null=True, verbose_name='删除时间')),
                ('deleted_by', models.CharField(blank=True, help_text='软删除操作者', max_length=50, null=True, verbose_name='删除人')),
                ('position_code', models.CharField(help_text='职位的唯一编码（必填）', max_length=20, verbose_name='职位编码')),
                ('name', models.CharField(help_text='职位的名称（必填）', max_length=100, verbose_name='职位名称')),
                ('level', models.PositiveSmallIntegerField(blank=True, choices=[(1, '1级员工'), (2, '2级员工'), (3, '3级员工'), (4, '4级员工'), (5, '5级-副主管'), (6, '6级-正主管（柜组长）'), (7, '7级-副经理'), (8, '8级-正经理'), (9, '9级-领导班子')], help_text='职位的层级（1-9级）', null=True, verbose_name='职位级别')),
                ('description', models.TextField(blank=True, help_text='职位的职责描述', verbose_name='职位描述')),
                ('is_management', models.BooleanField(default=False, help_text='是否为管理职位', verbose_name='是否管理岗位')),
                ('is_active', models.BooleanField(default=True, help_text='职位是否处于活跃状态', verbose_name='是否启用')),
                ('sort_order', models.PositiveIntegerField(default=0, help_text='职位显示顺序', verbose_name='排序')),
                ('department', models.ForeignKey(help_text='职位所属的部门（必填）', on_delete=django.db.models.deletion.CASCADE, to='organizations.department', verbose_name='所属部门')),
            ],
            options={
                'verbose_name': '职位',
                'verbose_name_plural': '职位',
                'ordering': ['department', 'level', 'sort_order'],
            },
        ),
        migrations.CreateModel(
            name='Staff',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='记录创建时间', verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='记录最后更新时间', verbose_name='更新时间')),
                ('created_by', models.CharField(blank=True, help_text='记录创建者', max_length=50, null=True, verbose_name='创建人')),
                ('updated_by', models.CharField(blank=True, help_text='记录最后更新者', max_length=50, null=True, verbose_name='更新人')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='软删除时间，为空表示未删除', null=True, verbose_name='删除时间')),
                ('deleted_by', models.CharField(blank=True, help_text='软删除操作者', max_length=50, null=True, verbose_name='删除人')),
                ('username', models.CharField(help_text='登录用户名', max_length=50, unique=True, verbose_name='用户名')),
                ('password', models.CharField(help_text='登录密码（加密存储）', max_length=128, verbose_name='密码')),
                ('anonymous_code', models.CharField(help_text='匿名评分用的编号', max_length=50, unique=True, verbose_name='匿名编号')),
                ('role', models.CharField(choices=[('super_admin', '超级管理员'), ('dept_manager', '部门经理'), ('admin', '普通管理员'), ('employee', '普通员工')], default='employee', help_text='用户的权限角色', max_length=20, verbose_name='用户角色')),
                ('is_active', models.BooleanField(default=True, help_text='账号是否激活', verbose_name='是否激活')),
                ('last_login', models.DateTimeField(blank=True, help_text='用户最后登录的时间', null=True, verbose_name='最后登录时间')),
                ('employee_no', models.CharField(help_text='员工的唯一编号（必填）', max_length=20, unique=True, verbose_name='员工编号')),
                ('name', models.CharField(help_text='员工的真实姓名（必填）', max_length=50, verbose_name='员工姓名')),
                ('email', models.EmailField(blank=True, help_text='员工的邮箱地址', max_length=254, verbose_name='邮箱')),
                ('phone', models.CharField(blank=True, help_text='员工的手机号码', max_length=20, verbose_name='手机号')),
                ('hire_date', models.DateField(blank=True, help_text='员工入职时间', null=True, verbose_name='入职日期')),
                ('avatar', models.ImageField(blank=True, help_text='员工头像图片', null=True, upload_to='avatars/', verbose_name='头像')),
                ('department', models.ForeignKey(help_text='员工所属的部门（必填）', on_delete=django.db.models.deletion.CASCADE, to='organizations.department', verbose_name='所属部门')),
                ('position', models.ForeignKey(blank=True, help_text='员工的职位', null=True, on_delete=django.db.models.deletion.CASCADE, to='organizations.position', verbose_name='职位')),
            ],
            options={
                'verbose_name': '员工',
                'verbose_name_plural': '员工',
                'ordering': ['department', 'employee_no'],
            },
        ),
        migrations.AddField(
            model_name='department',
            name='manager',
            field=models.ForeignKey(blank=True, help_text='部门负责人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_departments', to='organizations.staff', verbose_name='部门经理'),
        ),
        migrations.CreateModel(
            name='StaffLoginLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='记录创建时间', verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='记录最后更新时间', verbose_name='更新时间')),
                ('created_by', models.CharField(blank=True, help_text='记录创建者', max_length=50, null=True, verbose_name='创建人')),
                ('updated_by', models.CharField(blank=True, help_text='记录最后更新者', max_length=50, null=True, verbose_name='更新人')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='软删除时间，为空表示未删除', null=True, verbose_name='删除时间')),
                ('deleted_by', models.CharField(blank=True, help_text='软删除操作者', max_length=50, null=True, verbose_name='删除人')),
                ('login_type', models.CharField(choices=[('normal', '普通登录'), ('anonymous', '匿名登录')], help_text='登录方式类型', max_length=20, verbose_name='登录类型')),
                ('ip_address', models.GenericIPAddressField(help_text='登录时的IP地址', verbose_name='IP地址')),
                ('user_agent', models.TextField(blank=True, help_text='浏览器用户代理信息', verbose_name='用户代理')),
                ('is_success', models.BooleanField(default=True, help_text='登录是否成功', verbose_name='是否成功')),
                ('failure_reason', models.CharField(blank=True, help_text='登录失败的原因', max_length=100, verbose_name='失败原因')),
                ('logout_time', models.DateTimeField(blank=True, help_text='用户注销的时间', null=True, verbose_name='注销时间')),
                ('staff', models.ForeignKey(help_text='登录的员工', on_delete=django.db.models.deletion.CASCADE, to='organizations.staff', verbose_name='员工')),
            ],
            options={
                'verbose_name': '登录日志',
                'verbose_name_plural': '登录日志',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='position',
            index=models.Index(fields=['department', 'position_code'], name='organizatio_departm_790a2e_idx'),
        ),
        migrations.AddIndex(
            model_name='position',
            index=models.Index(fields=['level'], name='organizatio_level_023197_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='position',
            unique_together={('department', 'position_code')},
        ),
        migrations.AddIndex(
            model_name='staff',
            index=models.Index(fields=['username'], name='organizatio_usernam_4f0547_idx'),
        ),
        migrations.AddIndex(
            model_name='staff',
            index=models.Index(fields=['employee_no'], name='organizatio_employe_11058c_idx'),
        ),
        migrations.AddIndex(
            model_name='staff',
            index=models.Index(fields=['anonymous_code'], name='organizatio_anonymo_121a9a_idx'),
        ),
        migrations.AddIndex(
            model_name='staff',
            index=models.Index(fields=['department'], name='organizatio_departm_e39b92_idx'),
        ),
        migrations.AddIndex(
            model_name='department',
            index=models.Index(fields=['dept_code'], name='organizatio_dept_co_553ffa_idx'),
        ),
        migrations.AddIndex(
            model_name='department',
            index=models.Index(fields=['parent_department'], name='organizatio_parent__7b2bc7_idx'),
        ),
        migrations.AddIndex(
            model_name='staffloginlog',
            index=models.Index(fields=['staff', 'login_type'], name='organizatio_staff_i_54af7c_idx'),
        ),
        migrations.AddIndex(
            model_name='staffloginlog',
            index=models.Index(fields=['created_at'], name='organizatio_created_8bd14f_idx'),
        ),
        migrations.AddIndex(
            model_name='staffloginlog',
            index=models.Index(fields=['ip_address'], name='organizatio_ip_addr_8c8ea1_idx'),
        ),
    ]
