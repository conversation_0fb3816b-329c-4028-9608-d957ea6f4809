# -*- coding: utf-8 -*-
"""
组织架构模型
包含部门、职位、员工信息和认证管理
"""

from django.db import models
from django.contrib.auth.hashers import make_password, check_password
from django.utils import timezone
from common.models import BaseModel
import hashlib
import random
import string


class Department(BaseModel):
    """
    部门模型
    管理企业组织架构中的部门信息
    """
    dept_code = models.CharField(
        max_length=20,
        unique=True,
        verbose_name='部门编号',
        help_text='部门的唯一编号（必填）'
    )
    name = models.CharField(
        max_length=100,
        verbose_name='部门名称',
        help_text='部门的名称（必填）'
    )
    parent_department = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='上级部门',
        help_text='上级部门，总经理室为顶级部门'
    )
    manager = models.ForeignKey(
        'Staff',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='managed_departments',
        verbose_name='部门经理',
        help_text='部门负责人'
    )
    description = models.TextField(
        blank=True,
        verbose_name='部门描述',
        help_text='部门职责和描述信息'
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name='是否启用',
        help_text='部门是否处于活跃状态'
    )
    sort_order = models.PositiveIntegerField(
        default=0,
        verbose_name='排序',
        help_text='部门显示顺序'
    )
    
    class Meta:
        verbose_name = '部门'
        verbose_name_plural = '部门'
        ordering = ['sort_order', 'dept_code']
        indexes = [
            models.Index(fields=['dept_code']),
            models.Index(fields=['parent_department']),
        ]
    
    def __str__(self):
        return f'{self.dept_code} - {self.name}'
    
    def get_full_name(self):
        """获取部门完整名称（包含上级部门）"""
        if self.parent_department:
            return f'{self.parent_department.get_full_name()} > {self.name}'
        return self.name
    
    def get_staff_count(self):
        """获取部门员工数量"""
        return self.staff_set.filter(is_active=True, deleted_at__isnull=True).count()
    
    def get_all_children(self):
        """获取所有下级部门（递归）"""
        children = []
        for child in self.department_set.filter(is_active=True, deleted_at__isnull=True):
            children.append(child)
            children.extend(child.get_all_children())
        return children
    
    def soft_delete(self, deleted_by=None):
        """部门软删除时的业务处理"""
        # 处理子部门：转移到上级部门
        children = self.department_set.filter(deleted_at__isnull=True)
        for child in children:
            child.parent_department = self.parent_department
            child.save(update_fields=['parent_department'])
        
        # 员工保留历史部门信息，不做变更
        # 这样可以保持数据的历史完整性
        
        super().soft_delete(deleted_by)
    
    def can_be_deleted(self):
        """检查是否可以删除"""
        # 检查是否有活跃员工
        active_staff = self.staff_set.filter(is_active=True, deleted_at__isnull=True).count()
        if active_staff > 0:
            return False, f"部门下还有 {active_staff} 名活跃员工"
        
        # 检查是否有活跃子部门
        active_children = self.department_set.filter(deleted_at__isnull=True).count()
        if active_children > 0:
            return True, f"部门下有 {active_children} 个子部门，删除时将转移到上级部门"
        
        return True, "可以删除"


class Position(BaseModel):
    """
    职位模型
    管理企业中的职位层级信息
    """
    LEVEL_CHOICES = [
        (1, '1级员工'),
        (2, '2级员工'),
        (3, '3级员工'),
        (4, '4级员工'),
        (5, '5级-副主管'),
        (6, '6级-正主管（柜组长）'),
        (7, '7级-副经理'),
        (8, '8级-正经理'),
        (9, '9级-领导班子'),
    ]
    
    department = models.ForeignKey(
        Department,
        on_delete=models.SET_NULL,
        null=True,
        verbose_name='所属部门',
        help_text='职位所属的部门'
    )
    position_code = models.CharField(
        max_length=20,
        verbose_name='职位编码',
        help_text='职位的唯一编码（必填）'
    )
    name = models.CharField(
        max_length=100,
        verbose_name='职位名称',
        help_text='职位的名称（必填）'
    )
    level = models.PositiveSmallIntegerField(
        choices=LEVEL_CHOICES,
        null=True,
        blank=True,
        verbose_name='职位级别',
        help_text='职位的层级（1-9级）'
    )
    description = models.TextField(
        blank=True,
        verbose_name='职位描述',
        help_text='职位的职责描述'
    )
    is_management = models.BooleanField(
        default=False,
        verbose_name='是否管理岗位',
        help_text='是否为管理职位'
    )
    is_department_manager = models.BooleanField(
        default=False,
        verbose_name='是否部门经理',
        help_text='是否为部门经理职位'
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name='是否启用',
        help_text='职位是否处于活跃状态'
    )
    sort_order = models.PositiveIntegerField(
        default=0,
        verbose_name='排序',
        help_text='职位显示顺序'
    )
    
    class Meta:
        verbose_name = '职位'
        verbose_name_plural = '职位'
        ordering = ['department', 'level', 'sort_order']
        unique_together = [['department', 'position_code']]
        indexes = [
            models.Index(fields=['department', 'position_code']),
            models.Index(fields=['level']),
        ]
    
    def __str__(self):
        return f'{self.department.name} - {self.name}'
    
    def get_level_display_full(self):
        """获取完整的级别显示"""
        if self.level:
            return self.get_level_display()
        return '未设置级别'
    
    def get_staff_count(self):
        """获取该职位的在职员工数量"""
        return self.staff_set.filter(is_active=True, deleted_at__isnull=True).count()


class Staff(BaseModel):
    """
    员工模型
    包含员工基本信息和认证信息
    """
    ROLE_CHOICES = [
        ('super_admin', '超级管理员'),
        ('system_admin', '系统管理员'),
        ('hr_admin', 'HR管理员'),
        ('eval_admin', '考评管理员'),
        ('dept_manager', '部门经理'),
        ('admin', '普通管理员'),
        ('employee', '普通员工'),
    ]
    
    # 认证相关字段
    username = models.CharField(
        max_length=50,
        unique=True,
        verbose_name='用户名',
        help_text='登录用户名'
    )
    password = models.CharField(
        max_length=128,
        verbose_name='密码',
        help_text='登录密码（加密存储）'
    )
    anonymous_code = models.CharField(
        max_length=50,
        unique=True,
        verbose_name='匿名编号',
        help_text='匿名评分用的编号'
    )
    
    # 新增安全匿名编号字段
    new_anonymous_code = models.CharField(
        max_length=15,
        unique=True,
        null=True,
        blank=True,
        verbose_name='新安全匿名编号',
        help_text='使用SHA256加密生成的安全匿名编号，格式：XXXX-XXXX-XXXX'
    )
    anonymous_code_generated_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='匿名编号生成时间',
        help_text='新匿名编号的生成时间'
    )
    anonymous_code_version = models.CharField(
        max_length=10,
        default='v1.0',
        verbose_name='匿名编号版本',
        help_text='匿名编号的算法版本，用于追踪和兼容性管理'
    )
    role = models.CharField(
        max_length=30,
        choices=ROLE_CHOICES,
        default='employee',
        verbose_name='用户角色',
        help_text='用户的权限角色'
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name='是否激活',
        help_text='账号是否激活'
    )
    last_login = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='最后登录时间',
        help_text='用户最后登录的时间'
    )
    
    # JWT认证和安全相关字段
    last_token_refresh = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='最后Token刷新时间',
        help_text='JWT token最后刷新的时间，用于token撤销'
    )
    failed_login_attempts = models.PositiveIntegerField(
        default=0,
        verbose_name='失败登录尝试次数',
        help_text='连续失败登录的次数'
    )
    account_locked_until = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='账户锁定截止时间',
        help_text='账户锁定的截止时间'
    )
    password_changed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='密码修改时间',
        help_text='密码最后修改的时间'
    )
    force_password_change = models.BooleanField(
        default=False,
        verbose_name='强制修改密码',
        help_text='是否强制用户在下次登录时修改密码'
    )
    
    # 业务相关字段
    employee_no = models.CharField(
        max_length=20,
        unique=True,
        verbose_name='员工编号',
        help_text='员工的唯一编号（必填）'
    )
    name = models.CharField(
        max_length=50,
        verbose_name='员工姓名',
        help_text='员工的真实姓名（必填）'
    )
    department = models.ForeignKey(
        Department,
        on_delete=models.SET_NULL,
        null=True,
        verbose_name='所属部门',
        help_text='员工所属的部门'
    )
    position = models.ForeignKey(
        Position,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='职位',
        help_text='员工的职位'
    )
    email = models.EmailField(
        blank=True,
        verbose_name='邮箱',
        help_text='员工的邮箱地址'
    )
    phone = models.CharField(
        max_length=20,
        blank=True,
        verbose_name='手机号',
        help_text='员工的手机号码'
    )
    hire_date = models.DateField(
        null=True,
        blank=True,
        verbose_name='入职日期',
        help_text='员工入职时间'
    )
    avatar = models.ImageField(
        upload_to='avatars/',
        null=True,
        blank=True,
        verbose_name='头像',
        help_text='员工头像图片'
    )
    
    class Meta:
        verbose_name = '员工'
        verbose_name_plural = '员工'
        ordering = ['department', 'employee_no']
        indexes = [
            models.Index(fields=['username']),
            models.Index(fields=['employee_no']),
            models.Index(fields=['anonymous_code']),
            models.Index(fields=['department']),
        ]
    
    def __str__(self):
        return f'{self.employee_no} - {self.name}'
    
    def set_password(self, raw_password):
        """设置密码（加密存储）"""
        self.password = make_password(raw_password)
    
    def check_password(self, raw_password):
        """验证密码"""
        return check_password(raw_password, self.password)
    
    def generate_anonymous_code(self):
        """生成匿名编号"""
        if not self.anonymous_code:
            # 格式：部门代码 + 职位代码 + 随机数字
            dept_code = self.department.dept_code[:3].upper()
            pos_code = self.position.position_code[:2].upper() if self.position else 'XX'
            random_num = ''.join(random.choices(string.digits, k=4))
            self.anonymous_code = f'{dept_code}{pos_code}{random_num}'
    
    def get_full_name_with_dept(self):
        """获取包含部门的完整信息"""
        return f'{self.department.name} - {self.name} ({self.employee_no})'
    
    def get_role_permissions(self):
        """获取角色权限"""
        from common.security.permissions import permission_manager
        return list(permission_manager.get_user_permissions(self))
    
    def can_manage_department(self, department):
        """检查是否可以管理指定部门"""
        from common.security.permissions import permission_manager, Permission
        
        # 检查是否有全局部门管理权限
        if permission_manager.has_permission(self, Permission.ORG_MANAGE_STRUCTURE):
            return True
        
        # 检查是否有编辑部门权限且为本部门
        if (permission_manager.has_permission(self, Permission.ORG_EDIT_DEPARTMENT) and
            self.department == department):
            return True
        
        return False
    
    def update_last_login(self):
        """更新最后登录时间"""
        self.last_login = timezone.now()
        self.save(update_fields=['last_login'])
    
    @property
    def is_manager(self):
        """是否为管理员（兼容属性）"""
        return self.role in ['super_admin', 'system_admin', 'hr_admin', 
                           'eval_admin', 'dept_manager', 'admin']
    
    @property
    def is_admin(self):
        """是否为管理员（兼容属性）"""
        return self.role in ['super_admin', 'system_admin', 'hr_admin', 
                           'eval_admin', 'admin']
    
    @property
    def is_super_admin(self):
        """是否为超级管理员"""
        return self.role == 'super_admin'
    
    def has_permission(self, permission: str) -> bool:
        """检查用户是否具有指定权限"""
        from common.security.permissions import permission_manager
        return permission_manager.has_permission(self, permission)
    
    def has_role_or_higher(self, min_role: str) -> bool:
        """检查用户是否具有指定角色或更高级角色"""
        from common.security.permissions import permission_manager
        return permission_manager.has_role_or_higher(self, min_role)
    
    def can_access_department_data(self, department_id: int) -> bool:
        """检查是否可以访问指定部门的数据"""
        from common.security.permissions import permission_manager
        return permission_manager.can_access_department_data(self, department_id)
    
    # ==================== JWT认证和安全相关方法 ====================
    
    # 安全相关常量
    MAX_FAILED_ATTEMPTS = 5
    LOCKOUT_DURATION_MINUTES = 30
    
    def increment_failed_attempts(self):
        """增加失败尝试次数"""
        self.failed_login_attempts += 1
        
        # 如果达到最大尝试次数，锁定账户
        if self.failed_login_attempts >= self.MAX_FAILED_ATTEMPTS:
            self.account_locked_until = timezone.now() + timezone.timedelta(minutes=self.LOCKOUT_DURATION_MINUTES)
        
        self.save(update_fields=['failed_login_attempts', 'account_locked_until'])
    
    def reset_failed_attempts(self):
        """重置失败尝试次数"""
        self.failed_login_attempts = 0
        self.account_locked_until = None
        self.save(update_fields=['failed_login_attempts', 'account_locked_until'])
    
    def is_account_locked(self):
        """检查账户是否被锁定"""
        if not self.account_locked_until:
            return False
        
        # 检查锁定时间是否已过期
        if timezone.now() > self.account_locked_until:
            # 自动解锁
            self.account_locked_until = None
            self.failed_login_attempts = 0
            self.save(update_fields=['account_locked_until', 'failed_login_attempts'])
            return False
        
        return True
    
    def get_unlock_time(self):
        """获取解锁时间的友好显示"""
        if not self.account_locked_until:
            return ""
        
        remaining_time = self.account_locked_until - timezone.now()
        if remaining_time.total_seconds() <= 0:
            return ""
        
        minutes = int(remaining_time.total_seconds() / 60)
        return f"{minutes}分钟"
    
    def should_force_password_change(self):
        """是否应该强制修改密码"""
        return self.force_password_change
    
    def update_password(self, new_password):
        """更新密码并记录修改时间"""
        self.set_password(new_password)
        self.password_changed_at = timezone.now()
        self.force_password_change = False
        self.save(update_fields=['password', 'password_changed_at', 'force_password_change'])
    
    def can_generate_token(self):
        """检查是否可以生成token（临时简化）"""
        if not self.is_active:
            return False, "账户已禁用"
        
        return True, "可以生成token"
    
    def update_last_token_refresh(self):
        """更新最后token刷新时间"""
        self.last_token_refresh = timezone.now()
        self.save(update_fields=['last_token_refresh'])
    
    def get_security_info(self):
        """获取账户安全信息"""
        return {
            'failed_attempts': self.failed_login_attempts,
            'is_locked': self.is_account_locked(),
            'locked_until': self.account_locked_until,
            'last_failed_login': None,  # TODO: 需要记录最后失败登录时间
            'last_token_refresh': self.last_token_refresh,
            'password_changed_at': self.password_changed_at,
            'force_password_change': self.force_password_change,
            'two_factor_enabled': False,  # TODO: 未来支持双因子认证
        }
    
    def log_security_event(self, event_type, description, ip_address=None):
        """记录安全事件"""
        from common.models import AuditLog
        
        try:
            AuditLog.objects.create(
                user=self.username,
                action=event_type,
                target_model='Staff',
                target_id=self.id,
                description=f'[安全事件] {description}',
                ip_address=ip_address or '127.0.0.1'
            )
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"记录安全事件失败: {e}")
    
    def save(self, *args, **kwargs):
        """重写保存方法，自动生成匿名编号"""
        if not self.anonymous_code:
            self.generate_anonymous_code()
        super().save(*args, **kwargs)


class StaffLoginLog(BaseModel):
    """
    员工登录日志模型
    记录登录和注销信息
    """
    LOGIN_TYPES = [
        ('normal', '普通登录'),
        ('anonymous', '匿名登录'),
    ]
    
    staff = models.ForeignKey(
        Staff,
        on_delete=models.SET_NULL,
        null=True,
        verbose_name='员工',
        help_text='登录的员工'
    )
    login_type = models.CharField(
        max_length=20,
        choices=LOGIN_TYPES,
        verbose_name='登录类型',
        help_text='登录方式类型'
    )
    ip_address = models.GenericIPAddressField(
        verbose_name='IP地址',
        help_text='登录时的IP地址'
    )
    user_agent = models.TextField(
        blank=True,
        verbose_name='用户代理',
        help_text='浏览器用户代理信息'
    )
    is_success = models.BooleanField(
        default=True,
        verbose_name='是否成功',
        help_text='登录是否成功'
    )
    failure_reason = models.CharField(
        max_length=100,
        blank=True,
        verbose_name='失败原因',
        help_text='登录失败的原因'
    )
    logout_time = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='注销时间',
        help_text='用户注销的时间'
    )
    
    class Meta:
        verbose_name = '登录日志'
        verbose_name_plural = '登录日志'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['staff', 'login_type']),
            models.Index(fields=['created_at']),
            models.Index(fields=['ip_address']),
        ]
    
    def __str__(self):
        return f'{self.staff.name} {self.get_login_type_display()} at {self.created_at}'
    
    def get_session_duration(self):
        """获取会话持续时间"""
        if self.logout_time:
            return self.logout_time - self.created_at
        return None
