# -*- coding: utf-8 -*-
"""
创建测试数据管理命令
生成企业考评系统的基础测试数据，包括部门、职位、员工等信息
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.hashers import make_password
from django.utils import timezone
from organizations.models import Department, Position, Staff
from common.models import AuditLog
import random
import string

class Command(BaseCommand):
    """
    创建测试数据的Django管理命令
    使用方法：python manage.py create_test_data
    """
    help = '创建企业考评系统的测试数据'

    def add_arguments(self, parser):
        """添加命令行参数"""
        parser.add_argument(
            '--clear',
            action='store_true',
            help='清除现有数据后重新创建',
        )

    def handle(self, *args, **options):
        """命令执行入口"""
        self.stdout.write(self.style.SUCCESS('开始创建测试数据...'))
        
        try:
            # 如果指定清除，先清除现有数据
            if options['clear']:
                self.clear_existing_data()
            
            # 创建测试数据
            self.create_departments()
            self.create_positions()
            self.create_staff()
            
            self.stdout.write(self.style.SUCCESS('测试数据创建完成！'))
            self.display_test_accounts()
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'创建测试数据时发生错误: {str(e)}')
            )
            raise

    def clear_existing_data(self):
        """清除现有测试数据"""
        self.stdout.write('清除现有数据...')
        
        # 按依赖关系顺序删除
        Staff.objects.all().delete()
        Position.objects.all().delete()
        Department.objects.all().delete()
        AuditLog.objects.all().delete()
        
        self.stdout.write(self.style.WARNING('现有数据已清除'))

    def create_departments(self):
        """创建部门数据"""
        self.stdout.write('创建部门数据...')
        
        # 创建总经理室（顶级部门）
        gm_office, created = Department.objects.get_or_create(
            dept_code='GM',
            defaults={
                'name': '总经理室',
                'parent_department': None,
                'description': '企业最高管理机构，负责公司整体战略决策',
                'is_active': True,
                'sort_order': 1,
                'created_by': 'system'
            }
        )
        if created:
            self.stdout.write(f'  ✓ 创建部门: {gm_office.name}')

        # 创建6个业务部门
        departments_data = [
            {
                'code': 'HR', 'name': '人力资源部', 
                'desc': '负责人力资源管理、员工发展和企业文化建设'
            },
            {
                'code': 'IT', 'name': '信息技术部', 
                'desc': '负责信息系统建设、技术支持和数字化转型'
            },
            {
                'code': 'FIN', 'name': '财务部', 
                'desc': '负责财务管理、会计核算和资金运营'
            },
            {
                'code': 'MKT', 'name': '市场部', 
                'desc': '负责市场营销、品牌推广和客户关系管理'
            },
            {
                'code': 'OPS', 'name': '运营部', 
                'desc': '负责业务运营、流程管理和质量控制'
            },
            {
                'code': 'SALES', 'name': '销售部', 
                'desc': '负责销售业务、客户开发和业绩管理'
            },
        ]

        self.created_departments = [gm_office]
        
        for i, dept_data in enumerate(departments_data, 2):
            dept, created = Department.objects.get_or_create(
                dept_code=dept_data['code'],
                defaults={
                    'name': dept_data['name'],
                    'parent_department': gm_office,
                    'description': dept_data['desc'],
                    'is_active': True,
                    'sort_order': i,
                    'created_by': 'system'
                }
            )
            if created:
                self.stdout.write(f'  ✓ 创建部门: {dept.name}')
            self.created_departments.append(dept)

    def create_positions(self):
        """创建职位数据"""
        self.stdout.write('创建职位数据...')
        
        # 职位层级数据（1-9级）
        positions_data = [
            {'level': 9, 'name': '总经理', 'is_mgmt': True, 'desc': '企业最高管理者'},
            {'level': 8, 'name': '部门经理', 'is_mgmt': True, 'desc': '部门最高负责人'},
            {'level': 7, 'name': '副经理', 'is_mgmt': True, 'desc': '部门副职管理者'},
            {'level': 6, 'name': '主管', 'is_mgmt': True, 'desc': '团队负责人'},
            {'level': 5, 'name': '副主管', 'is_mgmt': True, 'desc': '团队副职负责人'},
            {'level': 4, 'name': '高级专员', 'is_mgmt': False, 'desc': '资深专业人员'},
            {'level': 3, 'name': '专员', 'is_mgmt': False, 'desc': '专业技术人员'},
            {'level': 2, 'name': '助理专员', 'is_mgmt': False, 'desc': '初级专业人员'},
            {'level': 1, 'name': '实习生', 'is_mgmt': False, 'desc': '实习培训人员'},
        ]

        self.created_positions = []
        
        for dept in self.created_departments:
            for pos_data in positions_data:
                # 总经理室只有总经理职位
                if dept.dept_code == 'GM' and pos_data['level'] != 9:
                    continue
                # 其他部门不设总经理职位
                if dept.dept_code != 'GM' and pos_data['level'] == 9:
                    continue
                    
                pos, created = Position.objects.get_or_create(
                    department=dept,
                    position_code=f"{dept.dept_code}_{pos_data['level']}",
                    defaults={
                        'name': pos_data['name'],
                        'level': pos_data['level'],
                        'is_management': pos_data['is_mgmt'],
                        'description': f"{dept.name}{pos_data['name']} - {pos_data['desc']}",
                        'is_active': True,
                        'sort_order': pos_data['level'],
                        'created_by': 'system'
                    }
                )
                if created:
                    self.stdout.write(f'  ✓ 创建职位: {dept.name} - {pos.name}')
                self.created_positions.append(pos)

    def create_staff(self):
        """创建员工数据"""
        self.stdout.write('创建员工数据...')
        
        # 核心管理员工数据
        core_staff_data = [
            {
                'username': 'admin', 'name': '系统管理员', 'dept': 'GM', 
                'employee_no': 'GM001', 'role': 'super_admin', 'level': 9,
                'email': '<EMAIL>', 'phone': '13800000001'
            },
            {
                'username': 'hr_manager', 'name': '张人事', 'dept': 'HR', 
                'employee_no': 'HR001', 'role': 'dept_manager', 'level': 8,
                'email': '<EMAIL>', 'phone': '13800000002'
            },
            {
                'username': 'it_manager', 'name': '李技术', 'dept': 'IT', 
                'employee_no': 'IT001', 'role': 'dept_manager', 'level': 8,
                'email': '<EMAIL>', 'phone': '13800000003'
            },
            {
                'username': 'fin_manager', 'name': '王会计', 'dept': 'FIN', 
                'employee_no': 'FIN001', 'role': 'dept_manager', 'level': 8,
                'email': '<EMAIL>', 'phone': '13800000004'
            },
            {
                'username': 'mkt_manager', 'name': '赵营销', 'dept': 'MKT', 
                'employee_no': 'MKT001', 'role': 'dept_manager', 'level': 8,
                'email': '<EMAIL>', 'phone': '13800000005'
            },
        ]

        # 创建核心员工
        self.created_staff = []
        for staff_info in core_staff_data:
            staff = self.create_single_staff(staff_info)
            self.created_staff.append(staff)

        # 为每个部门创建一些普通员工
        self.create_regular_staff()
        
        # 设置部门经理
        self.assign_department_managers()

    def create_single_staff(self, staff_info):
        """创建单个员工"""
        # 找到对应部门和职位
        department = Department.objects.get(dept_code=staff_info['dept'])
        position = Position.objects.filter(
            department=department, 
            level=staff_info['level']
        ).first()
        
        if not position:
            # 如果找不到对应职位，创建一个
            position = Position.objects.create(
                department=department,
                position_code=f"{staff_info['dept']}_{staff_info['level']}",
                name='临时职位',
                level=staff_info['level'],
                is_management=staff_info['level'] >= 6,
                created_by='system'
            )
        
        staff, created = Staff.objects.get_or_create(
            username=staff_info['username'],
            defaults={
                'employee_no': staff_info['employee_no'],
                'name': staff_info['name'],
                'department': department,
                'position': position,
                'role': staff_info['role'],
                'password': make_password('123456'),  # 默认密码
                'is_active': True,
                'email': staff_info.get('email', f"{staff_info['username']}@company.com"),
                'phone': staff_info.get('phone', ''),
                'hire_date': timezone.now().date(),
                'created_by': 'system'
            }
        )
        
        if created:
            self.stdout.write(f'  ✓ 创建员工: {staff.name} ({staff.username})')
            # 生成匿名编号
            self.generate_anonymous_code(staff)
            
        return staff

    def create_regular_staff(self):
        """创建普通员工"""
        # 员工名字库
        first_names = ['张', '李', '王', '赵', '刘', '陈', '杨', '黄', '周', '吴']
        second_names = ['伟', '芳', '娜', '秀英', '敏', '静', '立', '丽', '强', '磊', '军', '洋', '勇', '艳', '杰']
        
        dept_prefixes = {
            'HR': '人力', 'IT': '技术', 'FIN': '财务', 
            'MKT': '市场', 'OPS': '运营', 'SALES': '销售'
        }
        
        employee_counter = 10  # 从010开始编号
        
        # 为每个非总经理室部门创建2-4名员工
        for dept in self.created_departments[1:]:  # 跳过总经理室
            dept_size = random.randint(2, 4)
            
            for i in range(dept_size):
                employee_counter += 1
                
                # 随机生成员工信息
                first_name = random.choice(first_names)
                second_name = random.choice(second_names)
                name = first_name + second_name
                
                # 随机选择职位级别（2-6级）
                level = random.randint(2, 6)
                position = Position.objects.filter(
                    department=dept, 
                    level=level
                ).first()
                
                if not position:
                    continue
                
                # 根据级别确定角色
                if level >= 6:
                    role = 'admin'
                else:
                    role = 'employee'
                
                staff_info = {
                    'username': f'{dept.dept_code.lower()}_user{i+1:02d}',
                    'name': name,
                    'dept': dept.dept_code,
                    'employee_no': f'{dept.dept_code}{employee_counter:03d}',
                    'role': role,
                    'level': level,
                    'email': f'{dept.dept_code.lower()}{i+1:02d}@company.com',
                    'phone': f'138{random.randint(10000000, 99999999)}'
                }
                
                staff = self.create_single_staff(staff_info)
                self.created_staff.append(staff)

    def generate_anonymous_code(self, staff):
        """为员工生成匿名编号"""
        if not staff.anonymous_code:
            # 生成格式：部门代码(3位) + 职位代码(2位) + 随机数字(4位)
            dept_code = staff.department.dept_code[:3].upper()
            pos_code = str(staff.position.level).zfill(2) if staff.position else 'XX'
            random_num = ''.join(random.choices(string.digits, k=4))
            
            # 确保匿名编号唯一
            anonymous_code = f'{dept_code}{pos_code}{random_num}'
            while Staff.objects.filter(anonymous_code=anonymous_code).exists():
                random_num = ''.join(random.choices(string.digits, k=4))
                anonymous_code = f'{dept_code}{pos_code}{random_num}'
            
            staff.anonymous_code = anonymous_code
            staff.save(update_fields=['anonymous_code'])
            
            self.stdout.write(f'    → 匿名编号: {anonymous_code}')

    def assign_department_managers(self):
        """设置部门经理"""
        self.stdout.write('设置部门经理...')
        
        # 需要设置经理的部门和对应的用户名
        manager_assignments = [
            ('HR', 'hr_manager'),
            ('IT', 'it_manager'),
            ('FIN', 'fin_manager'),
            ('MKT', 'mkt_manager'),
        ]
        
        for dept_code, manager_username in manager_assignments:
            try:
                dept = Department.objects.get(dept_code=dept_code)
                manager = Staff.objects.get(username=manager_username)
                dept.manager = manager
                dept.save(update_fields=['manager'])
                self.stdout.write(f'  ✓ 设置 {dept.name} 经理: {manager.name}')
            except (Department.DoesNotExist, Staff.DoesNotExist) as e:
                self.stdout.write(
                    self.style.WARNING(f'  ⚠ 设置部门经理失败 {dept_code}-{manager_username}: {e}')
                )

    def display_test_accounts(self):
        """显示测试账号信息"""
        self.stdout.write('\n' + '='*60)
        self.stdout.write(self.style.SUCCESS('测试账号信息'))
        self.stdout.write('='*60)
        
        test_accounts = [
            ('admin', '系统管理员', '超级管理员权限'),
            ('hr_manager', '人力资源经理', '部门管理权限'),
            ('it_manager', '信息技术经理', '部门管理权限'),
            ('fin_manager', '财务经理', '部门管理权限'),
        ]
        
        for username, name, permission in test_accounts:
            self.stdout.write(f'用户名: {username:<15} 密码: 123456    ({name} - {permission})')
        
        self.stdout.write('\n登录地址:')
        self.stdout.write('管理端: http://localhost:8000/admin/login/')
        self.stdout.write('匿名端: http://localhost:8000/anonymous/login/')
        
        # 显示匿名编号示例
        sample_staff = Staff.objects.filter(anonymous_code__isnull=False).first()
        if sample_staff:
            self.stdout.write(f'\n匿名编号示例: {sample_staff.anonymous_code} (用于匿名端登录)')
        
        self.stdout.write('='*60)