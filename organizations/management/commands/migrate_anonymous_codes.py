# -*- coding: utf-8 -*-
"""
匿名编号安全迁移管理命令
用于将现有的匿名编号升级为新的安全编号

使用方法：
python manage.py migrate_anonymous_codes [--batch-size 50] [--dry-run] [--force]

参数说明：
--batch-size: 每批处理的数量，默认50
--dry-run: 只预览，不实际执行
--force: 强制重新生成已有的新编号
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.conf import settings
from organizations.models import Staff
from common.security.anonymous import AnonymousCodeMigrator, SecureAnonymousCodeGenerator
import logging
from datetime import datetime

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """匿名编号安全迁移命令"""
    
    help = '将现有的匿名编号升级为新的安全编号（SHA256加密）'
    
    def add_arguments(self, parser):
        """添加命令行参数"""
        parser.add_argument(
            '--batch-size',
            type=int,
            default=50,
            help='每批处理的员工数量（默认：50）'
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='只预览迁移结果，不实际执行'
        )
        
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制重新生成已有的新编号'
        )
        
        parser.add_argument(
            '--department',
            type=str,
            help='只迁移指定部门的员工（部门编号）'
        )
        
        parser.add_argument(
            '--staff-id',
            type=int,
            help='只迁移指定员工（员工ID）'
        )
        
        parser.add_argument(
            '--rollback',
            type=str,
            help='回滚指定的迁移操作（迁移ID）'
        )
    
    def handle(self, *args, **options):
        """主处理方法"""
        try:
            # 检查系统配置
            self._check_system_configuration()
            
            # 处理回滚操作
            if options.get('rollback'):
                return self._handle_rollback(options['rollback'])
            
            # 处理迁移操作
            return self._handle_migration(options)
            
        except Exception as e:
            logger.error(f"匿名编号迁移失败: {str(e)}")
            raise CommandError(f"迁移执行失败: {str(e)}")
    
    def _check_system_configuration(self):
        """检查系统配置"""
        self.stdout.write("🔍 检查系统配置...")
        
        # 检查匿名编号盐配置
        salt = getattr(settings, 'ANONYMOUS_CODE_SALT', None)
        if not salt:
            self.stdout.write(
                self.style.WARNING('⚠️  警告：未配置ANONYMOUS_CODE_SALT，将使用默认值')
            )
        elif len(salt) < 20:
            self.stdout.write(
                self.style.WARNING('⚠️  警告：ANONYMOUS_CODE_SALT长度过短，建议至少20位')
            )
        
        # 检查数据库连接
        try:
            Staff.objects.count()
            self.stdout.write(self.style.SUCCESS("✅ 数据库连接正常"))
        except Exception as e:
            raise CommandError(f"数据库连接失败: {str(e)}")
    
    def _handle_rollback(self, migration_id):
        """处理回滚操作"""
        self.stdout.write(f"🔄 开始回滚迁移: {migration_id}")
        
        migrator = AnonymousCodeMigrator()
        
        try:
            result = migrator.rollback_migration(migration_id)
            
            self.stdout.write(
                self.style.SUCCESS(
                    f"✅ 回滚完成！回滚数量: {result['rolled_back_count']}, "
                    f"失败数量: {result['failed_count']}"
                )
            )
            
        except Exception as e:
            raise CommandError(f"回滚失败: {str(e)}")
    
    def _handle_migration(self, options):
        """处理迁移操作"""
        batch_size = options['batch_size']
        dry_run = options['dry_run']
        force = options['force']
        department_code = options.get('department')
        staff_id = options.get('staff_id')
        
        # 显示迁移配置
        self._show_migration_config(options)
        
        # 获取需要迁移的员工
        staff_queryset = self._get_staff_queryset(department_code, staff_id, force)
        
        if not staff_queryset.exists():
            self.stdout.write(
                self.style.WARNING("⚠️  没有找到需要迁移的员工")
            )
            return
        
        total_count = staff_queryset.count()
        self.stdout.write(f"📊 发现 {total_count} 个员工需要迁移")
        
        # 预览模式
        if dry_run:
            return self._show_migration_preview(staff_queryset)
        
        # 确认执行
        if not self._confirm_migration(total_count):
            self.stdout.write("❌ 迁移已取消")
            return
        
        # 执行迁移
        return self._execute_migration(staff_queryset, batch_size)
    
    def _show_migration_config(self, options):
        """显示迁移配置"""
        self.stdout.write("📋 迁移配置:")
        self.stdout.write(f"   批处理大小: {options['batch_size']}")
        self.stdout.write(f"   预览模式: {'是' if options['dry_run'] else '否'}")
        self.stdout.write(f"   强制重新生成: {'是' if options['force'] else '否'}")
        
        if options.get('department'):
            self.stdout.write(f"   限定部门: {options['department']}")
        if options.get('staff_id'):
            self.stdout.write(f"   限定员工ID: {options['staff_id']}")
        
        self.stdout.write("")
    
    def _get_staff_queryset(self, department_code, staff_id, force):
        """获取需要迁移的员工查询集"""
        queryset = Staff.objects.filter(
            deleted_at__isnull=True,
            is_active=True
        ).select_related('department')
        
        # 按部门筛选
        if department_code:
            queryset = queryset.filter(department__dept_code=department_code)
        
        # 按员工ID筛选
        if staff_id:
            queryset = queryset.filter(id=staff_id)
        
        # 是否强制重新生成
        if not force:
            queryset = queryset.filter(new_anonymous_code__isnull=True)
        
        return queryset.order_by('department__dept_code', 'employee_no')
    
    def _show_migration_preview(self, staff_queryset):
        """显示迁移预览"""
        self.stdout.write("👀 迁移预览:")
        self.stdout.write("=" * 80)
        
        generator = SecureAnonymousCodeGenerator()
        preview_count = min(10, staff_queryset.count())
        
        for staff in staff_queryset[:preview_count]:
            try:
                # 生成预览编号（不保存）
                new_code = generator.generate_secure_code(
                    staff.id, 
                    staff.department.id if staff.department else 1
                )
                
                self.stdout.write(
                    f"  {staff.employee_no:10} {staff.name:15} "
                    f"{staff.anonymous_code:15} → {new_code}"
                )
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f"  {staff.employee_no:10} {staff.name:15} "
                        f"预览失败: {str(e)}"
                    )
                )
        
        if staff_queryset.count() > preview_count:
            remaining = staff_queryset.count() - preview_count
            self.stdout.write(f"  ... 还有 {remaining} 个员工")
        
        self.stdout.write("=" * 80)
    
    def _confirm_migration(self, total_count):
        """确认是否执行迁移"""
        self.stdout.write(
            self.style.WARNING(
                f"⚠️  即将为 {total_count} 个员工生成新的安全匿名编号"
            )
        )
        self.stdout.write("   此操作不可逆，请确认是否继续？")
        
        while True:
            response = input("请输入 'yes' 确认或 'no' 取消: ").lower().strip()
            if response in ['yes', 'y']:
                return True
            elif response in ['no', 'n']:
                return False
            else:
                self.stdout.write("请输入 'yes' 或 'no'")
    
    def _execute_migration(self, staff_queryset, batch_size):
        """执行迁移"""
        self.stdout.write("🚀 开始执行迁移...")
        start_time = datetime.now()
        
        # 准备员工数据
        staff_data = []
        for staff in staff_queryset:
            staff_data.append({
                'staff_id': staff.id,
                'department_id': staff.department.id if staff.department else 1,
                'employee_no': staff.employee_no,
                'name': staff.name
            })
        
        # 执行批量迁移
        migrator = AnonymousCodeMigrator()
        
        try:
            result = migrator.migrate_existing_codes(batch_size=batch_size)
            
            # 显示结果
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            self.stdout.write("=" * 60)
            self.stdout.write(self.style.SUCCESS("🎉 迁移完成！"))
            self.stdout.write(f"📊 统计信息:")
            self.stdout.write(f"   总员工数: {result['total_count']}")
            self.stdout.write(f"   成功迁移: {result['migrated_count']}")
            self.stdout.write(f"   跳过处理: {result['skipped_count']}")
            self.stdout.write(f"   失败数量: {result['failed_count']}")
            self.stdout.write(f"   耗时: {duration:.2f} 秒")
            self.stdout.write(f"   迁移ID: {result['migration_id']}")
            
            # 显示失败详情
            if result['failed_items']:
                self.stdout.write(self.style.ERROR("\n❌ 失败详情:"))
                for item in result['failed_items'][:5]:  # 只显示前5个
                    self.stdout.write(
                        f"   员工ID {item['staff_id']} ({item.get('staff_name', 'unknown')}): "
                        f"{item['error']}"
                    )
                
                if len(result['failed_items']) > 5:
                    remaining = len(result['failed_items']) - 5
                    self.stdout.write(f"   ... 还有 {remaining} 个失败项")
            
            # 显示后续操作建议
            self.stdout.write(f"\n📝 后续操作:")
            self.stdout.write(f"   1. 检查迁移结果: python manage.py check_anonymous_codes")
            self.stdout.write(f"   2. 测试登录功能: 使用新旧编号都应该能正常登录")
            self.stdout.write(f"   3. 如需回滚: python manage.py migrate_anonymous_codes --rollback {result['migration_id']}")
            
            self.stdout.write("=" * 60)
            
            return result
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ 迁移执行失败: {str(e)}")
            )
            raise
    
    def _show_migration_status(self):
        """显示迁移状态"""
        migrator = AnonymousCodeMigrator()
        status = migrator.get_migration_status()
        
        self.stdout.write("📊 当前迁移状态:")
        self.stdout.write(f"   总员工数: {status['total_staff']}")
        self.stdout.write(f"   已迁移: {status['migrated_staff']}")
        self.stdout.write(f"   待迁移: {status['remaining_staff']}")
        self.stdout.write(f"   进度: {status['migration_progress']:.1f}%")
        self.stdout.write(f"   是否完成: {'是' if status['is_complete'] else '否'}")
        
        if status.get('error'):
            self.stdout.write(
                self.style.ERROR(f"   错误: {status['error']}")
            )