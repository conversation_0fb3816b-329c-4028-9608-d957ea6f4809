# -*- coding: utf-8 -*-
"""
匿名编号状态检查管理命令
用于检查匿名编号的迁移状态、安全性和一致性

使用方法：
python manage.py check_anonymous_codes [--detailed] [--security-check] [--export-report]
"""

from django.core.management.base import BaseCommand
from organizations.models import Staff
from common.security.anonymous import AnonymousCodeMigrator, AnonymousCodeValidator, SecureAnonymousCodeGenerator
from django.db.models import Count
import json
from datetime import datetime, timedelta
import csv
import os


class Command(BaseCommand):
    """匿名编号检查命令"""
    
    help = '检查匿名编号的迁移状态、安全性和一致性'
    
    def add_arguments(self, parser):
        """添加命令行参数"""
        parser.add_argument(
            '--detailed',
            action='store_true',
            help='显示详细信息，包括每个员工的编号状态'
        )
        
        parser.add_argument(
            '--security-check',
            action='store_true',
            help='执行安全性检查，分析编号强度'
        )
        
        parser.add_argument(
            '--export-report',
            action='store_true',
            help='导出检查报告到CSV文件'
        )
        
        parser.add_argument(
            '--department',
            type=str,
            help='只检查指定部门的员工（部门编号）'
        )
    
    def handle(self, *args, **options):
        """主处理方法"""
        self.stdout.write("🔍 开始检查匿名编号状态...")
        self.stdout.write("=" * 60)
        
        # 基础统计检查
        self._show_basic_statistics(options.get('department'))
        
        # 迁移状态检查
        self._show_migration_status()
        
        # 数据一致性检查
        self._check_data_consistency()
        
        # 详细信息检查
        if options['detailed']:
            self._show_detailed_information(options.get('department'))
        
        # 安全性检查
        if options['security_check']:
            self._perform_security_check(options.get('department'))
        
        # 导出报告
        if options['export_report']:
            self._export_report(options.get('department'))
        
        self.stdout.write("=" * 60)
        self.stdout.write(self.style.SUCCESS("✅ 检查完成"))
    
    def _show_basic_statistics(self, department_code=None):
        """显示基础统计信息"""
        self.stdout.write("📊 基础统计:")
        
        # 构建查询条件
        queryset = Staff.objects.filter(deleted_at__isnull=True)
        if department_code:
            queryset = queryset.filter(department__dept_code=department_code)
        
        total_staff = queryset.count()
        active_staff = queryset.filter(is_active=True).count()
        inactive_staff = total_staff - active_staff
        
        # 匿名编号统计
        with_old_code = queryset.filter(anonymous_code__isnull=False).count()
        with_new_code = queryset.filter(new_anonymous_code__isnull=False).count()
        with_both_codes = queryset.filter(
            anonymous_code__isnull=False,
            new_anonymous_code__isnull=False
        ).count()
        
        self.stdout.write(f"   总员工数: {total_staff}")
        self.stdout.write(f"   活跃员工: {active_staff}")
        self.stdout.write(f"   非活跃员工: {inactive_staff}")
        self.stdout.write(f"   有旧编号: {with_old_code}")
        self.stdout.write(f"   有新编号: {with_new_code}")
        self.stdout.write(f"   双编号员工: {with_both_codes}")
        
        # 按部门统计
        from django.db.models import Q
        dept_stats = (
            queryset.values('department__name', 'department__dept_code')
            .annotate(
                total=Count('id'),
                with_new_code=Count('id', filter=Q(new_anonymous_code__isnull=False)),
            )
            .order_by('department__dept_code')
        )
        
        if dept_stats:
            self.stdout.write("\n📈 部门统计:")
            for stat in dept_stats:
                dept_name = stat['department__name'] or '未知部门'
                total = stat['total']
                migrated = stat['with_new_code']
                progress = (migrated / total * 100) if total > 0 else 0
                
                self.stdout.write(
                    f"   {stat['department__dept_code']:8} {dept_name:15} "
                    f"{migrated:3}/{total:3} ({progress:5.1f}%)"
                )
        
        self.stdout.write("")
    
    def _show_migration_status(self):
        """显示迁移状态"""
        self.stdout.write("🔄 迁移状态:")
        
        migrator = AnonymousCodeMigrator()
        status = migrator.get_migration_status()
        
        if status.get('error'):
            self.stdout.write(
                self.style.ERROR(f"   获取状态失败: {status['error']}")
            )
            return
        
        self.stdout.write(f"   总员工数: {status['total_staff']}")
        self.stdout.write(f"   已迁移: {status['migrated_staff']}")
        self.stdout.write(f"   待迁移: {status['remaining_staff']}")
        self.stdout.write(f"   迁移进度: {status['migration_progress']:.1f}%")
        
        if status['is_complete']:
            self.stdout.write(self.style.SUCCESS("   ✅ 迁移已完成"))
        else:
            self.stdout.write(
                self.style.WARNING("   ⚠️  迁移未完成")
            )
        
        self.stdout.write("")
    
    def _check_data_consistency(self):
        """检查数据一致性"""
        self.stdout.write("🔎 数据一致性检查:")
        
        issues = []
        
        # 检查重复的新编号
        duplicate_new_codes = (
            Staff.objects.filter(
                new_anonymous_code__isnull=False,
                deleted_at__isnull=True
            )
            .values('new_anonymous_code')
            .annotate(count=Count('new_anonymous_code'))
            .filter(count__gt=1)
        )
        
        if duplicate_new_codes:
            issues.append(f"发现 {duplicate_new_codes.count()} 个重复的新编号")
        
        # 检查重复的旧编号
        duplicate_old_codes = (
            Staff.objects.filter(
                anonymous_code__isnull=False,
                deleted_at__isnull=True
            )
            .values('anonymous_code')
            .annotate(count=Count('anonymous_code'))
            .filter(count__gt=1)
        )
        
        if duplicate_old_codes:
            issues.append(f"发现 {duplicate_old_codes.count()} 个重复的旧编号")
        
        # 检查格式异常的新编号
        invalid_format_count = 0
        generator = SecureAnonymousCodeGenerator()
        
        for staff in Staff.objects.filter(new_anonymous_code__isnull=False, deleted_at__isnull=True):
            if not generator.validate_code_format(staff.new_anonymous_code):
                invalid_format_count += 1
        
        if invalid_format_count > 0:
            issues.append(f"发现 {invalid_format_count} 个格式异常的新编号")
        
        # 检查孤立的编号（员工已删除但编号仍存在）
        orphaned_codes = Staff.objects.filter(
            new_anonymous_code__isnull=False,
            deleted_at__isnull=False
        ).count()
        
        if orphaned_codes > 0:
            issues.append(f"发现 {orphaned_codes} 个孤立的编号（已删除员工）")
        
        # 显示检查结果
        if issues:
            for issue in issues:
                self.stdout.write(self.style.WARNING(f"   ⚠️  {issue}"))
        else:
            self.stdout.write(self.style.SUCCESS("   ✅ 数据一致性检查通过"))
        
        self.stdout.write("")
    
    def _show_detailed_information(self, department_code=None):
        """显示详细信息"""
        self.stdout.write("📋 详细信息:")
        
        # 构建查询条件
        queryset = Staff.objects.filter(deleted_at__isnull=True).select_related('department')
        if department_code:
            queryset = queryset.filter(department__dept_code=department_code)
        
        queryset = queryset.order_by('department__dept_code', 'employee_no')
        
        # 表头
        self.stdout.write(
            f"{'员工编号':12} {'姓名':10} {'部门':8} {'旧编号':15} {'新编号':15} {'状态':8}"
        )
        self.stdout.write("-" * 80)
        
        for staff in queryset[:50]:  # 限制显示数量
            dept_code = staff.department.dept_code if staff.department else 'N/A'
            old_code = staff.anonymous_code or 'None'
            new_code = staff.new_anonymous_code or 'None'
            
            # 确定状态
            if staff.new_anonymous_code:
                if staff.anonymous_code:
                    status = '双编号'
                else:
                    status = '仅新编号'
            elif staff.anonymous_code:
                status = '仅旧编号'
            else:
                status = '无编号'
            
            self.stdout.write(
                f"{staff.employee_no:12} {staff.name:10} {dept_code:8} "
                f"{old_code:15} {new_code:15} {status:8}"
            )
        
        if queryset.count() > 50:
            remaining = queryset.count() - 50
            self.stdout.write(f"... 还有 {remaining} 个员工（只显示前50个）")
        
        self.stdout.write("")
    
    def _perform_security_check(self, department_code=None):
        """执行安全性检查"""
        self.stdout.write("🔐 安全性检查:")
        
        validator = AnonymousCodeValidator()
        
        # 构建查询条件
        queryset = Staff.objects.filter(
            new_anonymous_code__isnull=False,
            deleted_at__isnull=True
        )
        if department_code:
            queryset = queryset.filter(department__dept_code=department_code)
        
        total_codes = queryset.count()
        if total_codes == 0:
            self.stdout.write("   没有新编号需要检查")
            return
        
        # 安全强度统计
        strong_count = 0
        weak_count = 0
        pattern_issues = 0
        
        for staff in queryset:
            check_result = validator.check_code_strength(staff.new_anonymous_code)
            
            if check_result['is_strong']:
                strong_count += 1
            else:
                weak_count += 1
                
                if '明显的字符模式' in str(check_result['issues']):
                    pattern_issues += 1
        
        # 显示结果
        self.stdout.write(f"   检查编号数量: {total_codes}")
        self.stdout.write(f"   强安全编号: {strong_count} ({strong_count/total_codes*100:.1f}%)")
        self.stdout.write(f"   弱安全编号: {weak_count} ({weak_count/total_codes*100:.1f}%)")
        
        if pattern_issues > 0:
            self.stdout.write(
                self.style.WARNING(f"   ⚠️  发现 {pattern_issues} 个编号存在模式问题")
            )
        
        # 唯一性检查
        unique_codes = set()
        duplicate_count = 0
        
        for staff in queryset:
            code = staff.new_anonymous_code
            if code in unique_codes:
                duplicate_count += 1
            else:
                unique_codes.add(code)
        
        if duplicate_count > 0:
            self.stdout.write(
                self.style.ERROR(f"   ❌ 发现 {duplicate_count} 个重复编号")
            )
        else:
            self.stdout.write(self.style.SUCCESS("   ✅ 所有编号唯一"))
        
        self.stdout.write("")
    
    def _export_report(self, department_code=None):
        """导出检查报告"""
        self.stdout.write("📄 导出检查报告...")
        
        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        dept_suffix = f"_{department_code}" if department_code else ""
        filename = f"anonymous_codes_report{dept_suffix}_{timestamp}.csv"
        filepath = os.path.join('reports', filename)
        
        # 确保目录存在
        os.makedirs('reports', exist_ok=True)
        
        # 构建查询条件
        queryset = Staff.objects.filter(deleted_at__isnull=True).select_related('department')
        if department_code:
            queryset = queryset.filter(department__dept_code=department_code)
        
        # 导出CSV
        with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            
            # 写入表头
            writer.writerow([
                '员工编号', '员工姓名', '部门编号', '部门名称',
                '旧匿名编号', '新匿名编号', '编号生成时间', '编号版本',
                '迁移状态', '是否活跃', '最后登录'
            ])
            
            # 写入数据
            for staff in queryset:
                # 确定迁移状态
                if staff.new_anonymous_code:
                    if staff.anonymous_code:
                        migration_status = '已迁移(保留旧编号)'
                    else:
                        migration_status = '已迁移(仅新编号)'
                elif staff.anonymous_code:
                    migration_status = '未迁移'
                else:
                    migration_status = '无编号'
                
                writer.writerow([
                    staff.employee_no,
                    staff.name,
                    staff.department.dept_code if staff.department else '',
                    staff.department.name if staff.department else '',
                    staff.anonymous_code or '',
                    staff.new_anonymous_code or '',
                    staff.anonymous_code_generated_at.strftime('%Y-%m-%d %H:%M:%S') if hasattr(staff, 'anonymous_code_generated_at') and staff.anonymous_code_generated_at else '',
                    getattr(staff, 'anonymous_code_version', 'v1.0'),
                    migration_status,
                    '是' if staff.is_active else '否',
                    staff.last_login.strftime('%Y-%m-%d %H:%M:%S') if staff.last_login else ''
                ])
        
        self.stdout.write(
            self.style.SUCCESS(f"   ✅ 报告已导出至: {filepath}")
        )
        self.stdout.write("")