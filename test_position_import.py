#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
职位导入功能测试脚本
"""

import os
import sys
import django
import pandas as pd
from io import BytesIO

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
django.setup()

from organizations.models import Department, Position, Staff
from common.excel_utils import ExcelExporter

def create_test_departments():
    """创建测试部门"""
    departments = [
        {'dept_code': 'DEPT001', 'name': '总经理室'},
        {'dept_code': 'DEPT002', 'name': '技术部'},
        {'dept_code': 'DEPT003', 'name': '人事部'},
        {'dept_code': 'DEPT004', 'name': '财务部'},
    ]
    
    created_depts = []
    for dept_data in departments:
        dept, created = Department.objects.get_or_create(
            dept_code=dept_data['dept_code'],
            defaults={
                'name': dept_data['name'],
                'created_by': Staff.objects.filter(role='super_admin').first()
            }
        )
        created_depts.append(dept)
        if created:
            print(f"创建部门: {dept.name}")
        else:
            print(f"部门已存在: {dept.name}")
    
    return created_depts

def create_test_excel():
    """创建测试Excel文件"""
    # 测试数据
    test_data = [
        ['总经理室', '总经理', 'CEO001', '是', '是', '9', '公司最高管理者'],
        ['技术部', '技术总监', 'CTO001', '是', '是', '8', '技术部门负责人'],
        ['技术部', '高级工程师', 'SE001', '否', '否', '6', '高级软件工程师'],
        ['技术部', '初级工程师', 'JE001', '否', '否', '3', '初级软件工程师'],
        ['人事部', '人事经理', 'HR001', '是', '是', '7', '人事部门负责人'],
        ['人事部', '人事专员', 'HR002', '否', '否', '4', '人事专员'],
        ['财务部', '财务经理', 'FIN001', '是', '是', '7', '财务部门负责人'],
        ['财务部', '会计', 'ACC001', '否', '否', '4', '会计'],
    ]
    
    # 创建Excel文件
    exporter = ExcelExporter()
    headers = ['部门*', '职位*', '编码*', '是否管理岗', '是否部门主管', '职位级别', '备注']
    exporter.set_headers(headers)
    exporter.add_data_rows(test_data)
    
    # 保存到文件
    filename = 'test_position_import.xlsx'
    exporter.save_to_file(filename)
    print(f"创建测试Excel文件: {filename}")
    return filename

def test_position_import():
    """测试职位导入功能"""
    print("=== 职位导入功能测试 ===")
    
    # 1. 创建测试部门
    print("\n1. 创建测试部门...")
    create_test_departments()
    
    # 2. 创建测试Excel文件
    print("\n2. 创建测试Excel文件...")
    excel_file = create_test_excel()
    
    # 3. 显示当前职位数量
    print(f"\n3. 导入前职位数量: {Position.objects.count()}")
    
    # 4. 模拟导入过程
    print("\n4. 模拟导入过程...")
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file)
        print(f"Excel文件包含 {len(df)} 行数据")
        
        # 显示数据预览
        print("\n数据预览:")
        print(df.head())
        
        # 验证部门是否存在
        print("\n验证部门:")
        for dept_name in df['部门*'].unique():
            try:
                dept = Department.objects.filter(name=dept_name, deleted_at__isnull=True).first()
                if dept:
                    print(f"✓ 部门 '{dept_name}' 存在")
                else:
                    print(f"✗ 部门 '{dept_name}' 不存在")
            except Exception as e:
                print(f"✗ 检查部门 '{dept_name}' 时出错: {e}")
        
        print("\n测试Excel文件创建成功！")
        print(f"文件位置: {os.path.abspath(excel_file)}")
        print("\n可以通过以下步骤测试导入功能:")
        print("1. 启动开发服务器: python manage.py runserver")
        print("2. 访问: http://127.0.0.1:8000/admin/positions/")
        print("3. 点击'导入'按钮")
        print("4. 上传生成的Excel文件进行测试")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_position_import()
