#!/usr/bin/env python
"""
修复数据库和创建测试用户脚本
"""
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
django.setup()

from django.db import connection
from organizations.models import Staff, Department

def check_database_structure():
    """检查数据库结构"""
    try:
        with connection.cursor() as cursor:
            # 检查Staff表结构
            cursor.execute("DESCRIBE organizations_staff")
            columns = cursor.fetchall()
            
            print("📋 当前Staff表字段:")
            for column in columns:
                print(f"   {column[0]} - {column[1]} - {'NULL' if column[2] == 'YES' else 'NOT NULL'} - Default: {column[4]}")
            
            # 检查是否有安全字段
            security_fields = [
                'failed_login_attempts',
                'locked_until', 
                'last_failed_login',
                'password_changed_at',
                'force_password_change',
                'enable_two_factor',
                'last_token_refresh',
                'last_password_change_reminder'
            ]
            
            existing_fields = [col[0] for col in columns]
            missing_fields = [field for field in security_fields if field not in existing_fields]
            
            if missing_fields:
                print(f"\n⚠️  缺少安全字段: {missing_fields}")
                return False
            else:
                print("\n✅ 所有安全字段都存在")
                return True
                
    except Exception as e:
        print(f"❌ 检查数据库结构失败: {e}")
        return False

def manual_add_security_fields():
    """手动添加安全字段"""
    try:
        with connection.cursor() as cursor:
            print("🔧 手动添加安全字段...")
            
            # 安全字段SQL
            sql_commands = [
                """
                ALTER TABLE organizations_staff 
                ADD COLUMN failed_login_attempts INT DEFAULT 0 NOT NULL
                """,
                """
                ALTER TABLE organizations_staff 
                ADD COLUMN locked_until DATETIME NULL
                """,
                """
                ALTER TABLE organizations_staff 
                ADD COLUMN last_failed_login DATETIME NULL
                """,
                """
                ALTER TABLE organizations_staff 
                ADD COLUMN password_changed_at DATETIME NULL
                """,
                """
                ALTER TABLE organizations_staff 
                ADD COLUMN force_password_change BOOLEAN DEFAULT FALSE NOT NULL
                """,
                """
                ALTER TABLE organizations_staff 
                ADD COLUMN enable_two_factor BOOLEAN DEFAULT FALSE NOT NULL
                """,
                """
                ALTER TABLE organizations_staff 
                ADD COLUMN last_token_refresh DATETIME NULL
                """,
                """
                ALTER TABLE organizations_staff 
                ADD COLUMN last_password_change_reminder DATETIME NULL
                """
            ]
            
            for sql in sql_commands:
                try:
                    cursor.execute(sql)
                    print(f"✅ 执行成功: {sql.split('ADD COLUMN')[1].split()[0]}")
                except Exception as e:
                    if "Duplicate column name" in str(e):
                        print(f"ℹ️  字段已存在: {sql.split('ADD COLUMN')[1].split()[0]}")
                    else:
                        print(f"❌ 执行失败: {e}")
                        
        return True
    except Exception as e:
        print(f"❌ 手动添加字段失败: {e}")
        return False

def create_test_user_safe():
    """安全地创建测试用户"""
    try:
        # 1. 创建部门
        dept, created = Department.objects.get_or_create(
            dept_code='TEST001',
            defaults={
                'name': '测试部门',
                'is_active': True,
                'sort_order': 1
            }
        )
        
        print(f"✅ 部门: {dept.name}")
        
        # 2. 删除现有用户（如果存在）
        existing_users = Staff.objects.filter(username='testadmin')
        if existing_users.exists():
            existing_users.delete()
            print("🗑️  删除现有测试用户")
        
        # 3. 创建新用户，显式设置所有字段
        staff = Staff(
            username='testadmin',
            employee_no='EMP001',
            name='测试管理员',
            department=dept,
            role='super_admin',
            is_active=True,
            email='<EMAIL>',
            # 安全字段
            failed_login_attempts=0,
            locked_until=None,
            last_failed_login=None,
            password_changed_at=None,
            force_password_change=False,
            enable_two_factor=False,
            last_token_refresh=None,
            last_password_change_reminder=None
        )
        
        # 设置密码
        staff.set_password('test123456')
        staff.save()
        
        print("✅ 测试用户创建成功!")
        print(f"   用户名: {staff.username}")
        print(f"   密码: test123456")
        print(f"   员工编号: {staff.employee_no}")
        print(f"   角色: {staff.role}")
        
        # 验证用户
        from django.contrib.auth.hashers import check_password
        is_valid = check_password('test123456', staff.password)
        print(f"   密码验证: {'✅ 正确' if is_valid else '❌ 错误'}")
        
        return staff
        
    except Exception as e:
        print(f"❌ 创建用户失败: {e}")
        return None

if __name__ == '__main__':
    print("🔧 开始修复数据库和创建测试用户...")
    print("=" * 60)
    
    # 1. 检查数据库结构
    has_fields = check_database_structure()
    
    # 2. 如果缺少字段，手动添加
    if not has_fields:
        print("\n" + "=" * 60)
        success = manual_add_security_fields()
        if success:
            print("\n🔄 重新检查数据库结构...")
            check_database_structure()
    
    # 3. 创建测试用户
    print("\n" + "=" * 60)
    user = create_test_user_safe()
    
    if user:
        print("\n" + "=" * 60)
        print("🎉 修复完成!")
        print("\n📝 登录信息:")
        print("   URL: http://127.0.0.1:8000/admin/login/")
        print("   用户名: testadmin")
        print("   密码: test123456")
        print("\n现在可以进行JWT功能测试了！")
    else:
        print("\n❌ 修复失败，请检查错误信息")