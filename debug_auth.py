#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单的认证调试脚本
"""

import os
import sys
import django
from django.test import Client
from django.urls import reverse

# 配置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
django.setup()

from organizations.models import Department, Staff

def test_basic_auth():
    """测试基本认证流程"""
    print("开始认证测试...")
    
    # 创建测试用户
    try:
        # 清理可能存在的测试数据
        Staff.objects.filter(username='test_debug').delete()
        Department.objects.filter(dept_code='DEBUG001').delete()
        
        # 创建测试部门
        dept = Department.objects.create(
            dept_code='DEBUG001',
            name='调试部门',
            description='用于调试的部门',
            is_active=True,
        )
        
        # 创建测试管理员
        admin_staff = Staff.objects.create(
            username='test_debug',
            employee_no='DEBUG001',
            name='调试管理员',
            department=dept,
            role='admin',
            is_active=True,
            anonymous_code='DEBUG001',
        )
        admin_staff.set_password('debug123')
        admin_staff.save()
        
        print(f"创建测试用户成功: {admin_staff.username}")
        
    except Exception as e:
        print(f"创建测试用户失败: {e}")
        return False
    
    # 测试登录
    client = Client()
    
    try:
        # 1. 访问登录页面
        login_url = reverse('organizations:admin:login')
        print(f"访问登录页面: {login_url}")
        
        response = client.get(login_url)
        print(f"登录页面状态码: {response.status_code}")
        
        if response.status_code != 200:
            print("无法访问登录页面")
            return False
        
        # 2. 提交登录表单（使用Session模式）
        login_data = {
            'username': 'test_debug',
            'password': 'debug123',
            'use_jwt': 'false'  # 使用Session模式
        }
        
        print("提交登录表单（Session模式）...")
        response = client.post(login_url, login_data, follow=False)
        print(f"登录响应状态码: {response.status_code}")
        print(f"响应头 Location: {response.get('Location', 'None')}")
        
        # 检查是否重定向到仪表板
        if response.status_code == 302:
            redirect_url = response.get('Location', '')
            print(f"重定向到: {redirect_url}")
            
            if 'admin' in redirect_url:
                print("登录成功，正在重定向到仪表板")
                
                # 3. 访问仪表板页面
                dashboard_url = reverse('organizations:admin:dashboard')
                print(f"访问仪表板: {dashboard_url}")
                
                response = client.get(dashboard_url)
                print(f"仪表板页面状态码: {response.status_code}")
                
                if response.status_code == 200:
                    print("✓ 认证测试成功!")
                    return True
                elif response.status_code == 302:
                    print(f"✗ 仪表板页面重定向到: {response.get('Location', '')}")
                    print("这表明认证中间件未能识别已登录用户")
                    return False
                else:
                    print(f"✗ 仪表板页面返回错误状态码: {response.status_code}")
                    return False
            else:
                print(f"✗ 登录后重定向到错误页面: {redirect_url}")
                return False
        else:
            print(f"✗ 登录失败，状态码: {response.status_code}")
            if hasattr(response, 'content'):
                content = response.content.decode('utf-8')
                if '用户名或密码错误' in content:
                    print("用户名或密码错误")
                elif '权限' in content:
                    print("权限错误")
                else:
                    print(f"响应内容: {content[:200]}...")
            return False
            
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理测试数据
        try:
            Staff.objects.filter(username='test_debug').delete()
            Department.objects.filter(dept_code='DEBUG001').delete()
            print("测试数据清理完成")
        except:
            pass

def test_session_data():
    """测试Session数据"""
    print("\n检查Session配置...")
    from django.conf import settings
    
    print(f"SESSION_COOKIE_AGE: {getattr(settings, 'SESSION_COOKIE_AGE', 'Not set')}")
    print(f"SESSION_EXPIRE_AT_BROWSER_CLOSE: {getattr(settings, 'SESSION_EXPIRE_AT_BROWSER_CLOSE', 'Not set')}")
    print(f"SESSION_SAVE_EVERY_REQUEST: {getattr(settings, 'SESSION_SAVE_EVERY_REQUEST', 'Not set')}")

if __name__ == '__main__':
    print("=" * 50)
    print("Django认证调试测试")
    print("=" * 50)
    
    test_session_data()
    
    success = test_basic_auth()
    
    print("\n" + "=" * 50)
    if success:
        print("测试结果: 通过")
    else:
        print("测试结果: 失败")
    print("=" * 50)