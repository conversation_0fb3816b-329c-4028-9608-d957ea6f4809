#!/usr/bin/env python
"""
直接修复数据库字段默认值问题
"""
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
django.setup()

from django.db import connection
from organizations.models import Staff, Department

def fix_database_defaults():
    """修复数据库字段默认值"""
    try:
        with connection.cursor() as cursor:
            print("🔧 正在修复数据库字段默认值...")
            
            # 修复安全字段的默认值
            sql_commands = [
                "ALTER TABLE organizations_staff MODIFY COLUMN failed_login_attempts INT DEFAULT 0 NOT NULL",
                "ALTER TABLE organizations_staff MODIFY COLUMN force_password_change BOOLEAN DEFAULT FALSE NOT NULL", 
                "ALTER TABLE organizations_staff MODIFY COLUMN enable_two_factor BOOLEAN DEFAULT FALSE NOT NULL",
            ]
            
            for sql in sql_commands:
                try:
                    cursor.execute(sql)
                    field_name = sql.split("COLUMN ")[1].split()[0]
                    print(f"✅ 修复字段: {field_name}")
                except Exception as e:
                    print(f"❌ 修复失败: {e}")
            
            # 更新现有记录的默认值
            print("\n🔄 更新现有记录...")
            update_commands = [
                "UPDATE organizations_staff SET failed_login_attempts = 0 WHERE failed_login_attempts IS NULL",
                "UPDATE organizations_staff SET force_password_change = FALSE WHERE force_password_change IS NULL",
                "UPDATE organizations_staff SET enable_two_factor = FALSE WHERE enable_two_factor IS NULL",
            ]
            
            for sql in update_commands:
                try:
                    cursor.execute(sql)
                    print(f"✅ 更新完成")
                except Exception as e:
                    print(f"❌ 更新失败: {e}")
                    
        return True
    except Exception as e:
        print(f"❌ 修复数据库失败: {e}")
        return False

def create_test_user_directly():
    """直接通过SQL创建测试用户"""
    try:
        with connection.cursor() as cursor:
            # 1. 确保部门存在
            cursor.execute("""
                INSERT IGNORE INTO organizations_department 
                (dept_code, name, is_active, sort_order, created_at, updated_at) 
                VALUES ('TEST001', '测试部门', TRUE, 1, NOW(), NOW())
            """)
            
            # 2. 获取部门ID
            cursor.execute("SELECT id FROM organizations_department WHERE dept_code = 'TEST001'")
            dept_id = cursor.fetchone()[0]
            
            # 3. 删除现有测试用户
            cursor.execute("DELETE FROM organizations_staff WHERE username = 'testadmin'")
            
            # 4. 创建新用户（直接通过SQL）
            from django.contrib.auth.hashers import make_password
            hashed_password = make_password('test123456')
            
            cursor.execute("""
                INSERT INTO organizations_staff 
                (username, password, employee_no, name, department_id, role, is_active, 
                 anonymous_code, created_at, updated_at, failed_login_attempts, 
                 force_password_change, enable_two_factor) 
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW(), 0, FALSE, FALSE)
            """, [
                'testadmin',
                hashed_password,
                'EMP001', 
                '测试管理员',
                dept_id,
                'super_admin',
                True,
                'TEST001XX1234'  # 生成匿名编号
            ])
            
            print("✅ 测试用户创建成功!")
            print("   用户名: testadmin")
            print("   密码: test123456")
            print("   员工编号: EMP001")
            print("   角色: super_admin")
            
            return True
            
    except Exception as e:
        print(f"❌ 创建用户失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def verify_user():
    """验证用户创建成功"""
    try:
        staff = Staff.objects.get(username='testadmin')
        print("\n🔍 用户验证:")
        print(f"   用户名: {staff.username}")
        print(f"   员工编号: {staff.employee_no}")
        print(f"   姓名: {staff.name}")
        print(f"   角色: {staff.role}")
        print(f"   是否激活: {staff.is_active}")
        print(f"   部门: {staff.department.name}")
        
        # 测试密码
        from django.contrib.auth.hashers import check_password
        is_valid = check_password('test123456', staff.password)
        print(f"   密码验证: {'✅ 正确' if is_valid else '❌ 错误'}")
        
        return True
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

if __name__ == '__main__':
    print("🚀 开始直接修复数据库和创建测试用户...")
    print("=" * 60)
    
    # 1. 修复数据库字段默认值
    success = fix_database_defaults()
    
    if success:
        print("\n" + "=" * 60)
        # 2. 创建测试用户
        user_created = create_test_user_directly()
        
        if user_created:
            print("\n" + "=" * 60)
            # 3. 验证用户
            verify_user()
            
            print("\n" + "=" * 60)
            print("🎉 修复和创建完成!")
            print("\n📝 登录信息:")
            print("   URL: http://127.0.0.1:8000/admin/login/")
            print("   用户名: testadmin")
            print("   密码: test123456")
            print("\n现在可以进行JWT功能测试了！")
        else:
            print("\n❌ 用户创建失败")
    else:
        print("\n❌ 数据库修复失败")