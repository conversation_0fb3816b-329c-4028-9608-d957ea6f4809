{"permissions": {"allow": ["mcp__thinking__sequentialthinking", "Bash(ls:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(python manage.py runserver:*)", "Bash(/mnt/d/code/newmachinecode/UniversalStaffEvaluation3/.venv/Scripts/python.exe test_permissions_system.py)", "mcp__sequential-thinking__sequentialthinking", "Bash(find:*)", "Bash(python.exe:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(curl:*)", "Bash(grep:*)", "Bash(powershell.exe:*)"], "deny": []}}