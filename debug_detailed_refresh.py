#!/usr/bin/env python
"""
详细调试Token刷新功能
"""
import os
import sys
import django

# 设置Django环境  
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
django.setup()

from organizations.models import Staff
from common.security.jwt_auth import JWTAuthentication
from organizations.views import TokenRefreshView
from django.test import RequestFactory
import json

def test_token_refresh_view_directly():
    """直接测试TokenRefreshView"""
    try:
        print("🧪 直接测试TokenRefreshView...")
        print("=" * 50)
        
        # 1. 获取用户和tokens
        staff = Staff.objects.get(username='testadmin')
        initial_tokens = JWTAuthentication.generate_tokens(staff)
        refresh_token = initial_tokens['refresh_token']
        
        print(f"   用户: {staff.name}")
        print(f"   Refresh Token: {refresh_token[:50]}...")
        
        # 2. 创建模拟请求
        factory = RequestFactory()
        request_data = {'refresh_token': refresh_token}
        
        request = factory.post(
            '/admin/api/auth/refresh/',
            data=json.dumps(request_data),
            content_type='application/json'
        )
        
        # 3. 直接调用视图
        view = TokenRefreshView()
        
        try:
            response = view.post(request)
            print(f"   视图返回状态码: {response.status_code}")
            
            if hasattr(response, 'content'):
                content = response.content.decode('utf-8')
                print(f"   视图返回内容: {content}")
                
                try:
                    data = json.loads(content)
                    if data.get('success'):
                        print("✅ 视图调用成功!")
                        return True
                    else:
                        print(f"❌ 视图返回失败: {data.get('error')}")
                except json.JSONDecodeError:
                    print("   响应不是有效JSON")
            
        except Exception as e:
            print(f"❌ 视图调用异常: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            
        return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_refresh_method_step_by_step():
    """逐步调试refresh方法"""
    try:
        print("\n🔍 逐步调试refresh方法...")
        print("=" * 50)
        
        # 1. 获取用户和tokens
        staff = Staff.objects.get(username='testadmin')
        initial_tokens = JWTAuthentication.generate_tokens(staff)
        refresh_token = initial_tokens['refresh_token']
        
        print(f"   步骤1 - 获取refresh token: ✅")
        
        # 2. 验证refresh token
        try:
            payload = JWTAuthentication.verify_token(refresh_token, 'refresh')
            print(f"   步骤2 - 验证refresh token: ✅")
            print(f"      Token payload: {payload}")
        except Exception as e:
            print(f"   步骤2 - 验证refresh token: ❌ {e}")
            return False
        
        # 3. 获取用户信息
        try:
            staff_from_token = Staff.objects.get(id=payload['staff_id'])
            print(f"   步骤3 - 获取用户信息: ✅ {staff_from_token.name}")
        except Exception as e:
            print(f"   步骤3 - 获取用户信息: ❌ {e}")
            return False
        
        # 4. 生成新payload
        try:
            from django.utils import timezone
            now = timezone.now()
            new_payload = {
                'staff_id': staff.id,
                'username': staff.username,
                'role': staff.role,
                'department_id': staff.department_id if staff.department else None,
                'is_manager': staff.is_manager,
                'token_type': 'access',
                'token_id': payload['token_id'],
                'iat': int(now.timestamp()),
                'exp': int((now + JWTAuthentication.ACCESS_TOKEN_LIFETIME).timestamp())
            }
            print(f"   步骤4 - 生成新payload: ✅")
            print(f"      New payload: {new_payload}")
        except Exception as e:
            print(f"   步骤4 - 生成新payload: ❌ {e}")
            return False
        
        # 5. 编码新token
        try:
            import jwt
            from django.conf import settings
            new_access_token = jwt.encode(new_payload, settings.SECRET_KEY, algorithm=JWTAuthentication.ALGORITHM)
            print(f"   步骤5 - 编码新token: ✅")
            print(f"      新token: {new_access_token[:50]}...")
        except Exception as e:
            print(f"   步骤5 - 编码新token: ❌ {e}")
            return False
        
        # 6. 生成返回数据
        try:
            result = {
                'access_token': new_access_token,
                'expires_in': int(JWTAuthentication.ACCESS_TOKEN_LIFETIME.total_seconds()),
                'token_type': 'Bearer'
            }
            print(f"   步骤6 - 生成返回数据: ✅")
            print(f"      结果: {result}")
            return True
        except Exception as e:
            print(f"   步骤6 - 生成返回数据: ❌ {e}")
            return False
            
    except Exception as e:
        print(f"❌ 逐步调试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

if __name__ == '__main__':
    print("🚀 开始详细调试Token刷新功能...")
    
    # 逐步调试refresh方法
    step_success = test_refresh_method_step_by_step()
    
    # 直接测试视图
    if step_success:
        view_success = test_token_refresh_view_directly()
        
        print("\n" + "=" * 50)
        if view_success:
            print("🎉 Token刷新功能完全正常!")
        else:
            print("⚠️  视图层有问题，但核心逻辑正常")
    else:
        print("\n⚠️  核心刷新逻辑有问题")