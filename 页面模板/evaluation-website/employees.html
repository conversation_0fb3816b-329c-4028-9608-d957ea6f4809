<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>员工管理 - 考评系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lucide/0.263.1/lucide.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lucide/0.263.1/lucide.min.js"></script>
</head>
<body class="bg-gray-50">
    <div class="flex min-h-screen">
        <!-- Sidebar -->
        <div class="w-64 bg-white border-r border-gray-200 flex flex-col">
            <!-- Logo -->
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                        <i data-lucide="clipboard-check" class="w-5 h-5 text-white"></i>
                    </div>
                    <div>
                        <h1 class="text-lg font-semibold text-gray-900">考评系统</h1>
                        <p class="text-xs text-gray-500">Performance System</p>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <nav class="flex-1 p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="index.html" class="flex items-center space-x-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100 transition-colors">
                            <i data-lucide="home" class="w-4 h-4"></i>
                            <span>仪表板</span>
                        </a>
                    </li>
                    <li>
                        <a href="employees.html" class="flex items-center space-x-3 px-3 py-2 rounded-md bg-blue-50 text-blue-700 border border-blue-200">
                            <i data-lucide="users" class="w-4 h-4"></i>
                            <span class="font-medium">员工管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="departments.html" class="flex items-center space-x-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100 transition-colors">
                            <i data-lucide="building" class="w-4 h-4"></i>
                            <span>部门管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="batches.html" class="flex items-center space-x-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100 transition-colors">
                            <i data-lucide="calendar" class="w-4 h-4"></i>
                            <span>考评批次</span>
                        </a>
                    </li>
                    <li>
                        <a href="reports.html" class="flex items-center space-x-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100 transition-colors">
                            <i data-lucide="bar-chart-3" class="w-4 h-4"></i>
                            <span>统计报告</span>
                        </a>
                    </li>
                    <li>
                        <a href="evaluate.html" class="flex items-center space-x-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100 transition-colors">
                            <i data-lucide="shield" class="w-4 h-4"></i>
                            <span>匿名考评</span>
                        </a>
                    </li>
                </ul>
            </nav>

            <!-- User Info -->
            <div class="p-4 border-t border-gray-200">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                        <span class="text-sm font-medium">管</span>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900">管理员</p>
                        <p class="text-xs text-gray-500"><EMAIL></p>
                    </div>
                    <button class="p-1 text-gray-400 hover:text-gray-600">
                        <i data-lucide="settings" class="w-4 h-4"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col">
            <!-- Header -->
            <div class="bg-white border-b border-gray-200 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">员工管理</h1>
                        <p class="text-gray-600 mt-1">管理公司所有员工信息和考评记录</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
                            <i data-lucide="download" class="w-4 h-4 mr-2 inline"></i>导出
                        </button>
                        <button class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
                            <i data-lucide="upload" class="w-4 h-4 mr-2 inline"></i>导入
                        </button>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            <i data-lucide="plus" class="w-4 h-4 mr-2 inline"></i>添加员工
                        </button>
                    </div>
                </div>
            </div>

            <!-- Content -->
            <div class="flex-1 p-6 overflow-auto">
                <!-- Stats -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-2 bg-blue-100 rounded-lg">
                                <i data-lucide="users" class="w-6 h-6 text-blue-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">总员工数</p>
                                <p class="text-2xl font-bold text-gray-900">156</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-2 bg-green-100 rounded-lg">
                                <i data-lucide="building" class="w-6 h-6 text-green-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">在职员工</p>
                                <p class="text-2xl font-bold text-gray-900">142</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-2 bg-yellow-100 rounded-lg">
                                <i data-lucide="users" class="w-6 h-6 text-yellow-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">试用期员工</p>
                                <p class="text-2xl font-bold text-gray-900">8</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-2 bg-red-100 rounded-lg">
                                <i data-lucide="users" class="w-6 h-6 text-red-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">本月离职</p>
                                <p class="text-2xl font-bold text-gray-900">6</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters and Search -->
                <div class="bg-white rounded-lg shadow mb-6 p-6">
                    <div class="flex flex-col sm:flex-row gap-4">
                        <div class="flex-1 relative">
                            <input type="text" placeholder="搜索员工姓名、邮箱或职位..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md">
                            <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"></i>
                        </div>
                        <select class="px-3 py-2 border border-gray-300 rounded-md w-48">
                            <option>全部部门</option>
                            <option>技术部</option>
                            <option>产品部</option>
                            <option>设计部</option>
                            <option>市场部</option>
                            <option>人事部</option>
                        </select>
                        <button class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
                            <i data-lucide="filter" class="w-4 h-4 mr-2 inline"></i>更多筛选
                        </button>
                    </div>
                </div>

                <!-- Employee Table -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">员工列表</h3>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <input type="checkbox" class="rounded">
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">员工信息</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">职位</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">部门</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">级别</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最近考评</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">平均分</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="checkbox" class="rounded">
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                                <span class="text-sm font-medium">张</span>
                                            </div>
                                            <div>
                                                <div class="font-medium text-gray-900">张三</div>
                                                <div class="text-sm text-gray-500 flex items-center">
                                                    <i data-lucide="mail" class="w-3 h-3 mr-1"></i>
                                                    <EMAIL>
                                                </div>
                                                <div class="text-sm text-gray-500 flex items-center">
                                                    <i data-lucide="phone" class="w-3 h-3 mr-1"></i>
                                                    138-0000-0001
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="font-medium">前端工程师</div>
                                        <div class="text-sm text-gray-500">入职: 2022-03-15</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">技术部</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">P6</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">在职</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">2024-01-15</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <span class="font-medium">4.5</span>
                                            <span class="text-gray-400 ml-1">/5.0</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <button class="text-gray-400 hover:text-gray-600">
                                            <i data-lucide="more-horizontal" class="w-4 h-4"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        lucide.createIcons();
    </script>
</body>
</html>
