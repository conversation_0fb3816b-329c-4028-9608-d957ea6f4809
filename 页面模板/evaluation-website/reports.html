<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统计报告 - 考评系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lucide/0.263.1/lucide.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lucide/0.263.1/lucide.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gray-50">
    <div class="flex min-h-screen">
        <!-- Sidebar -->
        <div class="w-64 bg-white border-r border-gray-200 flex flex-col">
            <!-- Logo -->
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                        <i data-lucide="clipboard-check" class="w-5 h-5 text-white"></i>
                    </div>
                    <div>
                        <h1 class="text-lg font-semibold text-gray-900">考评系统</h1>
                        <p class="text-xs text-gray-500">Performance System</p>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <nav class="flex-1 p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="index.html" class="flex items-center space-x-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100 transition-colors">
                            <i data-lucide="home" class="w-4 h-4"></i>
                            <span>仪表板</span>
                        </a>
                    </li>
                    <li>
                        <a href="employees.html" class="flex items-center space-x-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100 transition-colors">
                            <i data-lucide="users" class="w-4 h-4"></i>
                            <span>员工管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="departments.html" class="flex items-center space-x-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100 transition-colors">
                            <i data-lucide="building" class="w-4 h-4"></i>
                            <span>部门管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="batches.html" class="flex items-center space-x-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100 transition-colors">
                            <i data-lucide="calendar" class="w-4 h-4"></i>
                            <span>考评批次</span>
                        </a>
                    </li>
                    <li>
                        <a href="reports.html" class="flex items-center space-x-3 px-3 py-2 rounded-md bg-blue-50 text-blue-700 border border-blue-200">
                            <i data-lucide="bar-chart-3" class="w-4 h-4"></i>
                            <span class="font-medium">统计报告</span>
                        </a>
                    </li>
                    <li>
                        <a href="evaluate.html" class="flex items-center space-x-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100 transition-colors">
                            <i data-lucide="shield" class="w-4 h-4"></i>
                            <span>匿名考评</span>
                        </a>
                    </li>
                </ul>
            </nav>

            <!-- User Info -->
            <div class="p-4 border-t border-gray-200">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                        <span class="text-sm font-medium">管</span>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900">管理员</p>
                        <p class="text-xs text-gray-500"><EMAIL></p>
                    </div>
                    <button class="p-1 text-gray-400 hover:text-gray-600">
                        <i data-lucide="settings" class="w-4 h-4"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col">
            <!-- Header -->
            <div class="bg-white border-b border-gray-200 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">统计报告</h1>
                        <p class="text-gray-600 mt-1">考评数据分析和统计报告</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button onclick="openCustomReportModal()" class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
                            <i data-lucide="filter" class="w-4 h-4 mr-2 inline"></i>自定义报告
                        </button>
                        <button onclick="exportReport()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            <i data-lucide="download" class="w-4 h-4 mr-2 inline"></i>导出报告
                        </button>
                    </div>
                </div>
            </div>

            <!-- Content -->
            <div class="flex-1 p-6 overflow-auto">
                <!-- Filters -->
                <div class="bg-white rounded-lg shadow mb-8 p-6">
                    <div class="flex flex-col sm:flex-row gap-4">
                        <select id="timeRange" class="px-3 py-2 border border-gray-300 rounded-md w-48">
                            <option value="month">本月</option>
                            <option value="quarter" selected>本季度</option>
                            <option value="year">本年度</option>
                            <option value="custom">自定义</option>
                        </select>
                        <select id="departmentFilter" class="px-3 py-2 border border-gray-300 rounded-md w-48">
                            <option value="all">全部部门</option>
                            <option value="tech">技术部</option>
                            <option value="product">产品部</option>
                            <option value="design">设计部</option>
                            <option value="marketing">市场部</option>
                            <option value="hr">人事部</option>
                        </select>
                        <button onclick="refreshData()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">刷新数据</button>
                    </div>
                </div>

                <!-- Overall Stats -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-2 bg-blue-100 rounded-lg">
                                <i data-lucide="users" class="w-6 h-6 text-blue-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">总考评数</p>
                                <p class="text-2xl font-bold text-gray-900">156</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-2 bg-green-100 rounded-lg">
                                <i data-lucide="target" class="w-6 h-6 text-green-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">完成率</p>
                                <p class="text-2xl font-bold text-gray-900">91%</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-2 bg-yellow-100 rounded-lg">
                                <i data-lucide="star" class="w-6 h-6 text-yellow-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">平均评分</p>
                                <p class="text-2xl font-bold text-gray-900">4.2</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-2 bg-purple-100 rounded-lg">
                                <i data-lucide="trending-up" class="w-6 h-6 text-purple-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">环比增长</p>
                                <p class="text-2xl font-bold text-gray-900">+5.2%</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                    <!-- Department Performance Chart -->
                    <div class="bg-white rounded-lg shadow">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                                <i data-lucide="bar-chart-3" class="w-5 h-5 mr-2"></i>
                                部门绩效对比
                            </h3>
                        </div>
                        <div class="p-6">
                            <canvas id="departmentChart" width="400" height="300"></canvas>
                        </div>
                    </div>

                    <!-- Score Distribution Chart -->
                    <div class="bg-white rounded-lg shadow">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                                <i data-lucide="pie-chart" class="w-5 h-5 mr-2"></i>
                                评分分布
                            </h3>
                        </div>
                        <div class="p-6">
                            <canvas id="scoreChart" width="400" height="300"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Top Performers -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900 flex items-center">
                            <i data-lucide="star" class="w-5 h-5 mr-2"></i>
                            优秀员工排行
                        </h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                        <span class="text-sm font-bold text-yellow-600">#1</span>
                                    </div>
                                    <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                        <span class="text-sm font-medium">张</span>
                                    </div>
                                    <div>
                                        <p class="font-medium">张三</p>
                                        <p class="text-sm text-gray-600">前端工程师 · 技术部</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="font-bold text-lg">4.9</p>
                                    <p class="text-sm text-gray-600">分</p>
                                </div>
                            </div>

                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                        <span class="text-sm font-bold text-gray-600">#2</span>
                                    </div>
                                    <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                        <span class="text-sm font-medium">李</span>
                                    </div>
                                    <div>
                                        <p class="font-medium">李四</p>
                                        <p class="text-sm text-gray-600">UI设计师 · 设计部</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="font-bold text-lg">4.8</p>
                                    <p class="text-sm text-gray-600">分</p>
                                </div>
                            </div>

                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                        <span class="text-sm font-bold text-gray-600">#3</span>
                                    </div>
                                    <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                        <span class="text-sm font-medium">王</span>
                                    </div>
                                    <div>
                                        <p class="font-medium">王五</p>
                                        <p class="text-sm text-gray-600">产品经理 · 产品部</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="font-bold text-lg">4.7</p>
                                    <p class="text-sm text-gray-600">分</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        lucide.createIcons();

        // Initialize charts when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
        });

        function initCharts() {
            // Department Performance Chart
            const deptCtx = document.getElementById('departmentChart').getContext('2d');
            new Chart(deptCtx, {
                type: 'bar',
                data: {
                    labels: ['技术部', '产品部', '设计部', '市场部', '人事部'],
                    datasets: [{
                        label: '平均评分',
                        data: [4.3, 4.1, 4.6, 4.0, 4.2],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(239, 68, 68, 0.8)',
                            'rgba(139, 92, 246, 0.8)'
                        ],
                        borderColor: [
                            'rgba(59, 130, 246, 1)',
                            'rgba(16, 185, 129, 1)',
                            'rgba(245, 158, 11, 1)',
                            'rgba(239, 68, 68, 1)',
                            'rgba(139, 92, 246, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 5
                        }
                    }
                }
            });

            // Score Distribution Chart
            const scoreCtx = document.getElementById('scoreChart').getContext('2d');
            new Chart(scoreCtx, {
                type: 'doughnut',
                data: {
                    labels: ['4.5-5.0分', '4.0-4.4分', '3.5-3.9分', '3.0-3.4分', '2.5-2.9分'],
                    datasets: [{
                        data: [45, 52, 28, 12, 5],
                        backgroundColor: [
                            'rgba(34, 197, 94, 0.8)',
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(249, 115, 22, 0.8)',
                            'rgba(239, 68, 68, 0.8)'
                        ],
                        borderColor: [
                            'rgba(34, 197, 94, 1)',
                            'rgba(59, 130, 246, 1)',
                            'rgba(245, 158, 11, 1)',
                            'rgba(249, 115, 22, 1)',
                            'rgba(239, 68, 68, 1)'
                        ],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // Placeholder functions
        function openCustomReportModal() { alert('打开自定义报告模态框'); }
        function exportReport() { alert('导出报告'); }
        function refreshData() { alert('刷新数据'); }
    </script>
</body>
</html>
