<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考评批次管理 - 考评系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lucide/0.263.1/lucide.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lucide/0.263.1/lucide.min.js"></script>
</head>
<body class="bg-gray-50">
    <div class="flex min-h-screen">
        <!-- Sidebar -->
        <div class="w-64 bg-white border-r border-gray-200 flex flex-col">
            <!-- Logo -->
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                        <i data-lucide="clipboard-check" class="w-5 h-5 text-white"></i>
                    </div>
                    <div>
                        <h1 class="text-lg font-semibold text-gray-900">考评系统</h1>
                        <p class="text-xs text-gray-500">Performance System</p>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <nav class="flex-1 p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="index.html" class="flex items-center space-x-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100 transition-colors">
                            <i data-lucide="home" class="w-4 h-4"></i>
                            <span>仪表板</span>
                        </a>
                    </li>
                    <li>
                        <a href="employees.html" class="flex items-center space-x-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100 transition-colors">
                            <i data-lucide="users" class="w-4 h-4"></i>
                            <span>员工管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="departments.html" class="flex items-center space-x-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100 transition-colors">
                            <i data-lucide="building" class="w-4 h-4"></i>
                            <span>部门管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="batches.html" class="flex items-center space-x-3 px-3 py-2 rounded-md bg-blue-50 text-blue-700 border border-blue-200">
                            <i data-lucide="calendar" class="w-4 h-4"></i>
                            <span class="font-medium">考评批次</span>
                        </a>
                    </li>
                    <li>
                        <a href="reports.html" class="flex items-center space-x-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100 transition-colors">
                            <i data-lucide="bar-chart-3" class="w-4 h-4"></i>
                            <span>统计报告</span>
                        </a>
                    </li>
                    <li>
                        <a href="evaluate.html" class="flex items-center space-x-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100 transition-colors">
                            <i data-lucide="shield" class="w-4 h-4"></i>
                            <span>匿名考评</span>
                        </a>
                    </li>
                </ul>
            </nav>

            <!-- User Info -->
            <div class="p-4 border-t border-gray-200">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                        <span class="text-sm font-medium">管</span>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900">管理员</p>
                        <p class="text-xs text-gray-500"><EMAIL></p>
                    </div>
                    <button class="p-1 text-gray-400 hover:text-gray-600">
                        <i data-lucide="settings" class="w-4 h-4"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col">
            <!-- Header -->
            <div class="bg-white border-b border-gray-200 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">考评批次管理</h1>
                        <p class="text-gray-600 mt-1">创建和管理不同类型的考评活动</p>
                    </div>
                    <button onclick="openAddBatchModal()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        <i data-lucide="plus" class="w-4 h-4 mr-2 inline"></i>创建新批次
                    </button>
                </div>
            </div>

            <!-- Content -->
            <div class="flex-1 p-6 overflow-auto">
                <!-- Stats -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-2 bg-blue-100 rounded-lg">
                                <i data-lucide="calendar" class="w-6 h-6 text-blue-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">总批次数</p>
                                <p class="text-2xl font-bold text-gray-900">12</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-2 bg-green-100 rounded-lg">
                                <i data-lucide="play" class="w-6 h-6 text-green-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">进行中</p>
                                <p class="text-2xl font-bold text-gray-900">3</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-2 bg-yellow-100 rounded-lg">
                                <i data-lucide="clock" class="w-6 h-6 text-yellow-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">待开始</p>
                                <p class="text-2xl font-bold text-gray-900">2</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-2 bg-purple-100 rounded-lg">
                                <i data-lucide="check-circle" class="w-6 h-6 text-purple-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">已完成</p>
                                <p class="text-2xl font-bold text-gray-900">7</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-white rounded-lg shadow mb-6 p-6">
                    <div class="flex flex-col sm:flex-row gap-4">
                        <div class="flex-1 relative">
                            <input type="text" id="searchInput" placeholder="搜索批次名称或描述..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md">
                            <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"></i>
                        </div>
                        <select id="statusFilter" class="px-3 py-2 border border-gray-300 rounded-md w-48">
                            <option value="all">全部状态</option>
                            <option value="pending">待开始</option>
                            <option value="active">进行中</option>
                            <option value="completed">已完成</option>
                            <option value="paused">已暂停</option>
                        </select>
                    </div>
                </div>

                <!-- Batches List -->
                <div class="space-y-6" id="batchesList">
                    <!-- Batch Item 1 -->
                    <div class="bg-white rounded-lg shadow hover:shadow-lg transition-shadow batch-item" data-status="active">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-3 mb-2">
                                        <i data-lucide="play" class="w-5 h-5 text-blue-600"></i>
                                        <h3 class="text-xl font-semibold text-gray-900">2024年第一季度绩效考评</h3>
                                        <span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">进行中</span>
                                    </div>
                                    <p class="text-gray-600 mb-3">全公司季度绩效评估，包含所有在职员工</p>
                                    <div class="flex flex-wrap gap-2">
                                        <span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">季度考评</span>
                                        <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">技术部</span>
                                        <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">产品部</span>
                                        <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">设计部</span>
                                    </div>
                                </div>
                                <div class="relative">
                                    <button onclick="toggleBatchDropdown(this)" class="p-1 text-gray-400 hover:text-gray-600">
                                        <i data-lucide="more-horizontal" class="w-4 h-4"></i>
                                    </button>
                                    <div class="dropdown-menu hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border">
                                        <button onclick="viewBatch(1)" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            <i data-lucide="eye" class="w-4 h-4 mr-2 inline"></i>查看详情
                                        </button>
                                        <button onclick="editBatch(1)" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            <i data-lucide="edit" class="w-4 h-4 mr-2 inline"></i>编辑批次
                                        </button>
                                        <button onclick="pauseBatch(1)" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            <i data-lucide="pause" class="w-4 h-4 mr-2 inline"></i>暂停批次
                                        </button>
                                        <button onclick="deleteBatch(1)" class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100">
                                            <i data-lucide="trash-2" class="w-4 h-4 mr-2 inline"></i>删除批次
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                                <div class="flex items-center space-x-2">
                                    <i data-lucide="calendar" class="w-4 h-4 text-gray-400"></i>
                                    <div>
                                        <p class="text-sm text-gray-600">开始时间</p>
                                        <p class="font-medium">2024-03-01</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <i data-lucide="calendar" class="w-4 h-4 text-gray-400"></i>
                                    <div>
                                        <p class="text-sm text-gray-600">结束时间</p>
                                        <p class="font-medium">2024-03-31</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <i data-lucide="users" class="w-4 h-4 text-gray-400"></i>
                                    <div>
                                        <p class="text-sm text-gray-600">参与人数</p>
                                        <p class="font-medium">156</p>
                                    </div>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">创建者</p>
                                    <p class="font-medium">HR管理员</p>
                                </div>
                            </div>

                            <!-- Progress -->
                            <div class="space-y-2 mb-4">
                                <div class="flex items-center justify-between text-sm">
                                    <span class="text-gray-600">完成进度</span>
                                    <span class="font-medium">118/156 (76%)</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: 76%"></div>
                                </div>
                            </div>

                            <!-- Actions -->
                            <div class="flex space-x-2">
                                <button onclick="viewParticipants(1)" class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">查看参与者</button>
                                <button onclick="exportData(1)" class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">导出数据</button>
                                <button onclick="sendReminder(1)" class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">发送提醒</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        lucide.createIcons();

        // Dropdown functions
        function toggleBatchDropdown(button) {
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                if (menu !== button.nextElementSibling) {
                    menu.classList.add('hidden');
                }
            });
            
            const menu = button.nextElementSibling;
            menu.classList.toggle('hidden');
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.relative')) {
                document.querySelectorAll('.dropdown-menu').forEach(menu => {
                    menu.classList.add('hidden');
                });
            }
        });

        // Placeholder functions
        function openAddBatchModal() { alert('打开创建新批次模态框'); }
        function viewBatch(id) { alert('查看批次详情: ' + id); }
        function editBatch(id) { alert('编辑批次: ' + id); }
        function pauseBatch(id) { alert('暂停批次: ' + id); }
        function deleteBatch(id) { alert('删除批次: ' + id); }
        function viewParticipants(id) { alert('查看参与者: ' + id); }
        function exportData(id) { alert('导出数据: ' + id); }
        function sendReminder(id) { alert('发送提醒: ' + id); }
    </script>
</body>
</html>
