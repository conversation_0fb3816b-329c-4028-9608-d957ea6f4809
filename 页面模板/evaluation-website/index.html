<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考评系统 - 仪表板</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lucide/0.263.1/lucide.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lucide/0.263.1/lucide.min.js"></script>
    <style>
        .sidebar-collapsed {
            width: 4rem;
        }
        .sidebar-expanded {
            width: 16rem;
        }
        .sidebar-transition {
            transition: width 0.3s ease-in-out;
        }
        .content-collapsed {
            margin-left: 4rem;
        }
        .content-expanded {
            margin-left: 16rem;
        }
        .content-transition {
            transition: margin-left 0.3s ease-in-out;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Sidebar -->
    <div id="sidebar" class="fixed left-0 top-0 h-full bg-white border-r border-gray-200 flex flex-col sidebar-expanded sidebar-transition z-50">
        <!-- Logo Section -->
        <div class="p-4 border-b border-gray-200">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
                    <i data-lucide="clipboard-check" class="w-5 h-5 text-white"></i>
                </div>
                <div class="sidebar-text">
                    <h1 class="text-lg font-semibold text-gray-900">考评系统</h1>
                    <p class="text-xs text-gray-500">Performance System</p>
                </div>
            </div>
            <!-- Collapse Button -->
            <button id="collapseBtn" class="absolute -right-3 top-6 w-6 h-6 bg-white border border-gray-200 rounded-full flex items-center justify-center hover:bg-gray-50">
                <i data-lucide="chevron-left" class="w-4 h-4 text-gray-600"></i>
            </button>
        </div>

        <!-- Navigation -->
        <nav class="flex-1 p-4">
            <ul class="space-y-2">
                <li>
                    <a href="index.html" class="flex items-center space-x-3 px-3 py-2 rounded-md bg-blue-50 text-blue-700 border-r-2 border-blue-600">
                        <i data-lucide="home" class="w-5 h-5 flex-shrink-0"></i>
                        <span class="sidebar-text font-medium">仪表板</span>
                    </a>
                </li>
                <li>
                    <a href="employees.html" class="flex items-center space-x-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100 transition-colors">
                        <i data-lucide="users" class="w-5 h-5 flex-shrink-0"></i>
                        <span class="sidebar-text">员工管理</span>
                    </a>
                </li>
                <li>
                    <a href="departments.html" class="flex items-center space-x-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100 transition-colors">
                        <i data-lucide="building" class="w-5 h-5 flex-shrink-0"></i>
                        <span class="sidebar-text">部门管理</span>
                    </a>
                </li>
                <li>
                    <a href="batches.html" class="flex items-center space-x-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100 transition-colors">
                        <i data-lucide="calendar" class="w-5 h-5 flex-shrink-0"></i>
                        <span class="sidebar-text">考评批次</span>
                    </a>
                </li>
                <li>
                    <a href="reports.html" class="flex items-center space-x-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100 transition-colors">
                        <i data-lucide="bar-chart-3" class="w-5 h-5 flex-shrink-0"></i>
                        <span class="sidebar-text">统计报告</span>
                    </a>
                </li>
                <li>
                    <a href="evaluate.html" class="flex items-center space-x-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100 transition-colors">
                        <i data-lucide="shield" class="w-5 h-5 flex-shrink-0"></i>
                        <span class="sidebar-text">匿名考评</span>
                    </a>
                </li>
            </ul>
        </nav>

        <!-- User Info -->
        <div class="p-4 border-t border-gray-200">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center flex-shrink-0">
                    <span class="text-sm font-medium">管</span>
                </div>
                <div class="sidebar-text flex-1">
                    <p class="text-sm font-medium text-gray-900">管理员</p>
                    <p class="text-xs text-gray-500"><EMAIL></p>
                </div>
                <button class="sidebar-text p-1 text-gray-400 hover:text-gray-600">
                    <i data-lucide="settings" class="w-4 h-4"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div id="mainContent" class="content-expanded content-transition">
        <!-- Header -->
        <div class="bg-white border-b border-gray-200 px-6 py-4">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">仪表板</h1>
                    <p class="text-gray-600 mt-1">欢迎回来，查看最新的考评数据</p>
                </div>
                <div class="flex items-center space-x-3">
                    <button class="p-2 text-gray-400 hover:text-gray-600 relative">
                        <i data-lucide="bell" class="w-5 h-5"></i>
                        <span class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
                    </button>
                    <button class="p-2 text-gray-400 hover:text-gray-600">
                        <i data-lucide="search" class="w-5 h-5"></i>
                    </button>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2">
                        <i data-lucide="plus" class="w-4 h-4"></i>
                        <span>新建考评</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="p-6">
            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="p-3 bg-blue-100 rounded-lg">
                            <i data-lucide="users" class="w-6 h-6 text-blue-600"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">总员工数</p>
                            <p class="text-2xl font-bold text-gray-900">156</p>
                            <p class="text-xs text-green-600 mt-1">↑ 12% 较上月</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="p-3 bg-green-100 rounded-lg">
                            <i data-lucide="target" class="w-6 h-6 text-green-600"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">完成考评</p>
                            <p class="text-2xl font-bold text-gray-900">142</p>
                            <p class="text-xs text-green-600 mt-1">91% 完成率</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="p-3 bg-yellow-100 rounded-lg">
                            <i data-lucide="star" class="w-6 h-6 text-yellow-600"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">平均评分</p>
                            <p class="text-2xl font-bold text-gray-900">4.2</p>
                            <p class="text-xs text-green-600 mt-1">↑ 0.3 较上月</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="p-3 bg-purple-100 rounded-lg">
                            <i data-lucide="calendar" class="w-6 h-6 text-purple-600"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">活跃批次</p>
                            <p class="text-2xl font-bold text-gray-900">3</p>
                            <p class="text-xs text-blue-600 mt-1">2个进行中</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts and Tables -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- Recent Evaluations -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                                <i data-lucide="clock" class="w-5 h-5 mr-2 text-gray-500"></i>
                                最近考评
                            </h3>
                            <a href="reports.html" class="text-sm text-blue-600 hover:text-blue-800">查看全部</a>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                        <span class="text-sm font-medium text-blue-600">张</span>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900">张三</p>
                                        <p class="text-sm text-gray-500">前端工程师 · 技术部</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center space-x-1">
                                        <i data-lucide="star" class="w-4 h-4 text-yellow-400 fill-current"></i>
                                        <span class="font-medium text-gray-900">4.5</span>
                                    </div>
                                    <p class="text-sm text-gray-500">2024-01-15</p>
                                </div>
                            </div>

                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                                        <span class="text-sm font-medium text-green-600">李</span>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900">李四</p>
                                        <p class="text-sm text-gray-500">UI设计师 · 设计部</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center space-x-1">
                                        <i data-lucide="star" class="w-4 h-4 text-yellow-400 fill-current"></i>
                                        <span class="font-medium text-gray-900">4.8</span>
                                    </div>
                                    <p class="text-sm text-gray-500">2024-01-14</p>
                                </div>
                            </div>

                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                                        <span class="text-sm font-medium text-purple-600">王</span>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900">王五</p>
                                        <p class="text-sm text-gray-500">产品经理 · 产品部</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center space-x-1">
                                        <i data-lucide="star" class="w-4 h-4 text-yellow-400 fill-current"></i>
                                        <span class="font-medium text-gray-900">4.3</span>
                                    </div>
                                    <p class="text-sm text-gray-500">2024-01-13</p>
                                </div>
                            </div>

                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                                        <span class="text-sm font-medium text-red-600">赵</span>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900">赵六</p>
                                        <p class="text-sm text-gray-500">后端工程师 · 技术部</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center space-x-1">
                                        <i data-lucide="star" class="w-4 h-4 text-yellow-400 fill-current"></i>
                                        <span class="font-medium text-gray-900">4.6</span>
                                    </div>
                                    <p class="text-sm text-gray-500">2024-01-12</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Department Performance -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                                <i data-lucide="trending-up" class="w-5 h-5 mr-2 text-gray-500"></i>
                                部门绩效
                            </h3>
                            <a href="departments.html" class="text-sm text-blue-600 hover:text-blue-800">管理部门</a>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="space-y-6">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                        <i data-lucide="code" class="w-4 h-4 text-blue-600"></i>
                                    </div>
                                    <span class="text-sm font-medium text-gray-900">技术部</span>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="w-32 bg-gray-200 rounded-full h-2">
                                        <div class="bg-blue-600 h-2 rounded-full" style="width: 86%"></div>
                                    </div>
                                    <span class="text-sm font-medium text-gray-900 w-8">4.3</span>
                                </div>
                            </div>

                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                        <i data-lucide="package" class="w-4 h-4 text-green-600"></i>
                                    </div>
                                    <span class="text-sm font-medium text-gray-900">产品部</span>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="w-32 bg-gray-200 rounded-full h-2">
                                        <div class="bg-green-600 h-2 rounded-full" style="width: 82%"></div>
                                    </div>
                                    <span class="text-sm font-medium text-gray-900 w-8">4.1</span>
                                </div>
                            </div>

                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                                        <i data-lucide="palette" class="w-4 h-4 text-yellow-600"></i>
                                    </div>
                                    <span class="text-sm font-medium text-gray-900">设计部</span>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="w-32 bg-gray-200 rounded-full h-2">
                                        <div class="bg-yellow-600 h-2 rounded-full" style="width: 92%"></div>
                                    </div>
                                    <span class="text-sm font-medium text-gray-900 w-8">4.6</span>
                                </div>
                            </div>

                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                                        <i data-lucide="megaphone" class="w-4 h-4 text-red-600"></i>
                                    </div>
                                    <span class="text-sm font-medium text-gray-900">市场部</span>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="w-32 bg-gray-200 rounded-full h-2">
                                        <div class="bg-red-600 h-2 rounded-full" style="width: 80%"></div>
                                    </div>
                                    <span class="text-sm font-medium text-gray-900 w-8">4.0</span>
                                </div>
                            </div>

                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                        <i data-lucide="users" class="w-4 h-4 text-purple-600"></i>
                                    </div>
                                    <span class="text-sm font-medium text-gray-900">人事部</span>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="w-32 bg-gray-200 rounded-full h-2">
                                        <div class="bg-purple-600 h-2 rounded-full" style="width: 84%"></div>
                                    </div>
                                    <span class="text-sm font-medium text-gray-900 w-8">4.2</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900 flex items-center">
                        <i data-lucide="zap" class="w-5 h-5 mr-2 text-gray-500"></i>
                        快速操作
                    </h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <button onclick="window.location.href='batches.html'" class="flex flex-col items-center justify-center p-6 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-400 hover:bg-blue-50 transition-all duration-200 group">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-3 group-hover:bg-blue-200">
                                <i data-lucide="plus" class="w-6 h-6 text-blue-600"></i>
                            </div>
                            <p class="text-sm font-medium text-gray-900">创建新批次</p>
                            <p class="text-xs text-gray-500 mt-1">开始新的考评周期</p>
                        </button>

                        <button onclick="window.location.href='employees.html'" class="flex flex-col items-center justify-center p-6 border-2 border-dashed border-gray-300 rounded-lg hover:border-green-400 hover:bg-green-50 transition-all duration-200 group">
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-3 group-hover:bg-green-200">
                                <i data-lucide="user-plus" class="w-6 h-6 text-green-600"></i>
                            </div>
                            <p class="text-sm font-medium text-gray-900">添加员工</p>
                            <p class="text-xs text-gray-500 mt-1">录入新员工信息</p>
                        </button>

                        <button onclick="window.location.href='reports.html'" class="flex flex-col items-center justify-center p-6 border-2 border-dashed border-gray-300 rounded-lg hover:border-purple-400 hover:bg-purple-50 transition-all duration-200 group">
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-3 group-hover:bg-purple-200">
                                <i data-lucide="download" class="w-6 h-6 text-purple-600"></i>
                            </div>
                            <p class="text-sm font-medium text-gray-900">导出报告</p>
                            <p class="text-xs text-gray-500 mt-1">生成统计分析</p>
                        </button>

                        <button onclick="window.location.href='evaluate.html'" class="flex flex-col items-center justify-center p-6 border-2 border-dashed border-gray-300 rounded-lg hover:border-yellow-400 hover:bg-yellow-50 transition-all duration-200 group">
                            <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mb-3 group-hover:bg-yellow-200">
                                <i data-lucide="shield" class="w-6 h-6 text-yellow-600"></i>
                            </div>
                            <p class="text-sm font-medium text-gray-900">匿名考评</p>
                            <p class="text-xs text-gray-500 mt-1">进行匿名评价</p>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="mt-8 bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900 flex items-center">
                        <i data-lucide="activity" class="w-5 h-5 mr-2 text-gray-500"></i>
                        最近活动
                    </h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                                <i data-lucide="user-plus" class="w-4 h-4 text-blue-600"></i>
                            </div>
                            <div class="flex-1">
                                <p class="text-sm text-gray-900">
                                    <span class="font-medium">张三</span> 完成了对 <span class="font-medium">李四</span> 的考评
                                </p>
                                <p class="text-xs text-gray-500 mt-1">2小时前</p>
                            </div>
                        </div>

                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                                <i data-lucide="calendar" class="w-4 h-4 text-green-600"></i>
                            </div>
                            <div class="flex-1">
                                <p class="text-sm text-gray-900">
                                    新的考评批次 <span class="font-medium">"2024年第一季度考评"</span> 已创建
                                </p>
                                <p class="text-xs text-gray-500 mt-1">5小时前</p>
                            </div>
                        </div>

                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center flex-shrink-0">
                                <i data-lucide="building" class="w-4 h-4 text-yellow-600"></i>
                            </div>
                            <div class="flex-1">
                                <p class="text-sm text-gray-900">
                                    <span class="font-medium">王五</span> 加入了 <span class="font-medium">产品部</span>
                                </p>
                                <p class="text-xs text-gray-500 mt-1">1天前</p>
                            </div>
                        </div>

                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
                                <i data-lucide="download" class="w-4 h-4 text-purple-600"></i>
                            </div>
                            <div class="flex-1">
                                <p class="text-sm text-gray-900">
                                    系统生成了 <span class="font-medium">月度考评报告</span>
                                </p>
                                <p class="text-xs text-gray-500 mt-1">2天前</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        lucide.createIcons();

        // Sidebar collapse functionality
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('mainContent');
        const collapseBtn = document.getElementById('collapseBtn');
        const sidebarTexts = document.querySelectorAll('.sidebar-text');
        
        let isCollapsed = false;

        collapseBtn.addEventListener('click', function() {
            isCollapsed = !isCollapsed;
            
            if (isCollapsed) {
                sidebar.classList.remove('sidebar-expanded');
                sidebar.classList.add('sidebar-collapsed');
                mainContent.classList.remove('content-expanded');
                mainContent.classList.add('content-collapsed');
                
                // Hide text elements
                sidebarTexts.forEach(text => {
                    text.style.display = 'none';
                });
                
                // Rotate collapse button
                collapseBtn.querySelector('i').style.transform = 'rotate(180deg)';
            } else {
                sidebar.classList.remove('sidebar-collapsed');
                sidebar.classList.add('sidebar-expanded');
                mainContent.classList.remove('content-collapsed');
                mainContent.classList.add('content-expanded');
                
                // Show text elements
                sidebarTexts.forEach(text => {
                    text.style.display = 'block';
                });
                
                // Reset collapse button
                collapseBtn.querySelector('i').style.transform = 'rotate(0deg)';
            }
        });

        // Add notification functionality
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 px-4 py-3 rounded-md text-white z-50 shadow-lg transform transition-all duration-300 ${
                type === 'success' ? 'bg-green-500' : 
                type === 'error' ? 'bg-red-500' : 
                type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
            }`;
            notification.innerHTML = `
                <div class="flex items-center space-x-2">
                    <i data-lucide="${type === 'success' ? 'check-circle' : type === 'error' ? 'x-circle' : type === 'warning' ? 'alert-triangle' : 'info'}" class="w-4 h-4"></i>
                    <span>${message}</span>
                </div>
            `;
            
            document.body.appendChild(notification);
            lucide.createIcons();
            
            // Animate in
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);
            
            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 3000);
        }

        // Add some interactive features
        document.addEventListener('DOMContentLoaded', function() {
            // Simulate loading data
            setTimeout(() => {
                showNotification('数据加载完成', 'success');
            }, 1000);
        });
    </script>
</body>
</html>
