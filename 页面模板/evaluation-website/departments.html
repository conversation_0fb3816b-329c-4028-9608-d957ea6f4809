<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>部门管理 - 考评系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lucide/0.263.1/lucide.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lucide/0.263.1/lucide.min.js"></script>
</head>
<body class="bg-gray-50">
    <div class="flex min-h-screen">
        <!-- Sidebar -->
        <div class="w-64 bg-white border-r border-gray-200 flex flex-col">
            <!-- Logo -->
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                        <i data-lucide="clipboard-check" class="w-5 h-5 text-white"></i>
                    </div>
                    <div>
                        <h1 class="text-lg font-semibold text-gray-900">考评系统</h1>
                        <p class="text-xs text-gray-500">Performance System</p>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <nav class="flex-1 p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="index.html" class="flex items-center space-x-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100 transition-colors">
                            <i data-lucide="home" class="w-4 h-4"></i>
                            <span>仪表板</span>
                        </a>
                    </li>
                    <li>
                        <a href="employees.html" class="flex items-center space-x-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100 transition-colors">
                            <i data-lucide="users" class="w-4 h-4"></i>
                            <span>员工管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="departments.html" class="flex items-center space-x-3 px-3 py-2 rounded-md bg-blue-50 text-blue-700 border border-blue-200">
                            <i data-lucide="building" class="w-4 h-4"></i>
                            <span class="font-medium">部门管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="batches.html" class="flex items-center space-x-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100 transition-colors">
                            <i data-lucide="calendar" class="w-4 h-4"></i>
                            <span>考评批次</span>
                        </a>
                    </li>
                    <li>
                        <a href="reports.html" class="flex items-center space-x-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100 transition-colors">
                            <i data-lucide="bar-chart-3" class="w-4 h-4"></i>
                            <span>统计报告</span>
                        </a>
                    </li>
                    <li>
                        <a href="evaluate.html" class="flex items-center space-x-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100 transition-colors">
                            <i data-lucide="shield" class="w-4 h-4"></i>
                            <span>匿名考评</span>
                        </a>
                    </li>
                </ul>
            </nav>

            <!-- User Info -->
            <div class="p-4 border-t border-gray-200">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                        <span class="text-sm font-medium">管</span>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900">管理员</p>
                        <p class="text-xs text-gray-500"><EMAIL></p>
                    </div>
                    <button class="p-1 text-gray-400 hover:text-gray-600">
                        <i data-lucide="settings" class="w-4 h-4"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col">
            <!-- Header -->
            <div class="bg-white border-b border-gray-200 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">部门管理</h1>
                        <p class="text-gray-600 mt-1">管理公司组织架构和部门信息</p>
                    </div>
                    <button onclick="openAddModal()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        <i data-lucide="plus" class="w-4 h-4 mr-2 inline"></i>新建部门
                    </button>
                </div>
            </div>

            <!-- Content -->
            <div class="flex-1 p-6 overflow-auto">
                <!-- Stats -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-2 bg-blue-100 rounded-lg">
                                <i data-lucide="building" class="w-6 h-6 text-blue-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">总部门数</p>
                                <p class="text-2xl font-bold text-gray-900">8</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-2 bg-green-100 rounded-lg">
                                <i data-lucide="users" class="w-6 h-6 text-green-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">总员工数</p>
                                <p class="text-2xl font-bold text-gray-900">156</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-2 bg-yellow-100 rounded-lg">
                                <i data-lucide="trending-up" class="w-6 h-6 text-yellow-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">平均评分</p>
                                <p class="text-2xl font-bold text-gray-900">4.2</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-2 bg-purple-100 rounded-lg">
                                <i data-lucide="user" class="w-6 h-6 text-purple-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">部门经理</p>
                                <p class="text-2xl font-bold text-gray-900">8</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Search -->
                <div class="bg-white rounded-lg shadow mb-6 p-6">
                    <div class="relative">
                        <input type="text" id="searchInput" placeholder="搜索部门名称或描述..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md">
                        <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"></i>
                    </div>
                </div>

                <!-- Departments Grid -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6" id="departmentsGrid">
                    <!-- Department Card 1 -->
                    <div class="bg-white rounded-lg shadow hover:shadow-lg transition-shadow department-card">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-xl font-semibold text-gray-900">技术部</h3>
                                    <p class="text-gray-600 mt-1">负责产品技术开发和维护</p>
                                </div>
                                <div class="relative">
                                    <button onclick="toggleDropdown(this)" class="p-1 text-gray-400 hover:text-gray-600">
                                        <i data-lucide="more-horizontal" class="w-4 h-4"></i>
                                    </button>
                                    <div class="dropdown-menu hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border">
                                        <button onclick="viewDepartment(1)" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            <i data-lucide="eye" class="w-4 h-4 mr-2 inline"></i>查看详情
                                        </button>
                                        <button onclick="editDepartment(1)" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            <i data-lucide="edit" class="w-4 h-4 mr-2 inline"></i>编辑部门
                                        </button>
                                        <button onclick="deleteDepartment(1)" class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100">
                                            <i data-lucide="trash-2" class="w-4 h-4 mr-2 inline"></i>删除部门
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="p-6">
                            <!-- Manager Info -->
                            <div class="flex items-center space-x-3 mb-4">
                                <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                    <span class="text-sm font-medium">李</span>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">李技术</p>
                                    <p class="text-sm text-gray-500">部门经理</p>
                                </div>
                            </div>

                            <!-- Stats -->
                            <div class="grid grid-cols-2 gap-4 mb-4">
                                <div>
                                    <p class="text-sm text-gray-600">员工人数</p>
                                    <p class="text-lg font-semibold">45</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">平均评分</p>
                                    <p class="text-lg font-semibold">4.3/5.0</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">年度预算</p>
                                    <p class="text-lg font-semibold">¥2,500,000</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">成立时间</p>
                                    <p class="text-lg font-semibold">2020-01-15</p>
                                </div>
                            </div>

                            <!-- Evaluation Progress -->
                            <div class="space-y-2 mb-4">
                                <div class="flex items-center justify-between text-sm">
                                    <span class="text-gray-600">考评完成度</span>
                                    <span class="font-medium">38/45</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: 84%"></div>
                                </div>
                            </div>

                            <!-- Actions -->
                            <div class="flex space-x-2">
                                <button onclick="viewEmployees(1)" class="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">查看员工</button>
                                <button onclick="viewReports(1)" class="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">考评报告</button>
                            </div>
                        </div>
                    </div>

                    <!-- Department Card 2 -->
                    <div class="bg-white rounded-lg shadow hover:shadow-lg transition-shadow department-card">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-xl font-semibold text-gray-900">产品部</h3>
                                    <p class="text-gray-600 mt-1">产品规划、设计和用户体验</p>
                                </div>
                                <div class="relative">
                                    <button onclick="toggleDropdown(this)" class="p-1 text-gray-400 hover:text-gray-600">
                                        <i data-lucide="more-horizontal" class="w-4 h-4"></i>
                                    </button>
                                    <div class="dropdown-menu hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border">
                                        <button onclick="viewDepartment(2)" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            <i data-lucide="eye" class="w-4 h-4 mr-2 inline"></i>查看详情
                                        </button>
                                        <button onclick="editDepartment(2)" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            <i data-lucide="edit" class="w-4 h-4 mr-2 inline"></i>编辑部门
                                        </button>
                                        <button onclick="deleteDepartment(2)" class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100">
                                            <i data-lucide="trash-2" class="w-4 h-4 mr-2 inline"></i>删除部门
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="p-6">
                            <!-- Manager Info -->
                            <div class="flex items-center space-x-3 mb-4">
                                <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                    <span class="text-sm font-medium">王</span>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">王产品</p>
                                    <p class="text-sm text-gray-500">部门经理</p>
                                </div>
                            </div>

                            <!-- Stats -->
                            <div class="grid grid-cols-2 gap-4 mb-4">
                                <div>
                                    <p class="text-sm text-gray-600">员工人数</p>
                                    <p class="text-lg font-semibold">28</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">平均评分</p>
                                    <p class="text-lg font-semibold">4.1/5.0</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">年度预算</p>
                                    <p class="text-lg font-semibold">¥1,800,000</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">成立时间</p>
                                    <p class="text-lg font-semibold">2020-03-20</p>
                                </div>
                            </div>

                            <!-- Evaluation Progress -->
                            <div class="space-y-2 mb-4">
                                <div class="flex items-center justify-between text-sm">
                                    <span class="text-gray-600">考评完成度</span>
                                    <span class="font-medium">25/28</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: 89%"></div>
                                </div>
                            </div>

                            <!-- Actions -->
                            <div class="flex space-x-2">
                                <button onclick="viewEmployees(2)" class="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">查看员工</button>
                                <button onclick="viewReports(2)" class="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">考评报告</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        lucide.createIcons();

        // Dropdown functions
        function toggleDropdown(button) {
            // Close all other dropdowns
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                if (menu !== button.nextElementSibling) {
                    menu.classList.add('hidden');
                }
            });
            
            // Toggle current dropdown
            const menu = button.nextElementSibling;
            menu.classList.toggle('hidden');
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.relative')) {
                document.querySelectorAll('.dropdown-menu').forEach(menu => {
                    menu.classList.add('hidden');
                });
            }
        });

        // Placeholder functions
        function openAddModal() { alert('打开新建部门模态框'); }
        function viewDepartment(id) { alert('查看部门详情: ' + id); }
        function editDepartment(id) { alert('编辑部门: ' + id); }
        function deleteDepartment(id) { alert('删除部门: ' + id); }
        function viewEmployees(id) { alert('查看部门员工: ' + id); }
        function viewReports(id) { alert('查看部门报告: ' + id); }
    </script>
</body>
</html>
