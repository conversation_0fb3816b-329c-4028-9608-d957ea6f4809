"use client"

import { useState } from "react"
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>Up, Download, Users, Star, Target, Filter } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"

export default function ReportsPage() {
  const [timeRange, setTimeRange] = useState("quarter")
  const [department, setDepartment] = useState("all")

  const overallStats = {
    totalEvaluations: 156,
    completedEvaluations: 142,
    averageScore: 4.2,
    participationRate: 91,
  }

  const departmentStats = [
    { name: "技术部", employees: 45, avgScore: 4.3, completion: 95, trend: "+0.2" },
    { name: "产品部", employees: 28, avgScore: 4.1, completion: 89, trend: "-0.1" },
    { name: "设计部", employees: 18, avgScore: 4.6, completion: 100, trend: "+0.3" },
    { name: "市场部", employees: 22, avgScore: 4.0, completion: 86, trend: "+0.1" },
    { name: "人事部", employees: 12, avgScore: 4.2, completion: 100, trend: "0.0" },
  ]

  const scoreDistribution = [
    { range: "4.5-5.0", count: 45, percentage: 32 },
    { range: "4.0-4.4", count: 52, percentage: 37 },
    { range: "3.5-3.9", count: 28, percentage: 20 },
    { range: "3.0-3.4", count: 12, percentage: 8 },
    { range: "2.5-2.9", count: 5, percentage: 3 },
  ]

  const topPerformers = [
    { name: "张三", department: "技术部", score: 4.9, position: "前端工程师" },
    { name: "李四", department: "设计部", score: 4.8, position: "UI设计师" },
    { name: "王五", department: "产品部", score: 4.7, position: "产品经理" },
    { name: "赵六", department: "技术部", score: 4.6, position: "后端工程师" },
    { name: "钱七", department: "市场部", score: 4.5, position: "市场专员" },
  ]

  const monthlyTrends = [
    { month: "1月", score: 4.1, participation: 88 },
    { month: "2月", score: 4.2, participation: 91 },
    { month: "3月", score: 4.2, participation: 93 },
  ]

  return (
    <div className="p-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">统计报告</h1>
            <p className="text-gray-600 mt-1">考评数据分析和统计报告</p>
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline">
              <Filter className="w-4 h-4 mr-2" />
              自定义报告
            </Button>
            <Button>
              <Download className="w-4 h-4 mr-2" />
              导出报告
            </Button>
          </div>
        </div>
      </div>

      {/* Filters */}
      <Card className="mb-8">
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="选择时间范围" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="month">本月</SelectItem>
                <SelectItem value="quarter">本季度</SelectItem>
                <SelectItem value="year">本年度</SelectItem>
                <SelectItem value="custom">自定义</SelectItem>
              </SelectContent>
            </Select>
            <Select value={department} onValueChange={setDepartment}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="选择部门" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部部门</SelectItem>
                <SelectItem value="tech">技术部</SelectItem>
                <SelectItem value="product">产品部</SelectItem>
                <SelectItem value="design">设计部</SelectItem>
                <SelectItem value="marketing">市场部</SelectItem>
                <SelectItem value="hr">人事部</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Overall Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Users className="w-6 h-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">总考评数</p>
                <p className="text-2xl font-bold text-gray-900">{overallStats.totalEvaluations}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <Target className="w-6 h-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">完成率</p>
                <p className="text-2xl font-bold text-gray-900">{overallStats.participationRate}%</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Star className="w-6 h-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">平均评分</p>
                <p className="text-2xl font-bold text-gray-900">{overallStats.averageScore}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <TrendingUp className="w-6 h-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">环比增长</p>
                <p className="text-2xl font-bold text-gray-900">+5.2%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Department Performance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="w-5 h-5 mr-2" />
              部门绩效对比
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {departmentStats.map((dept, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{dept.name}</span>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-600">{dept.avgScore}/5.0</span>
                      <Badge
                        variant={
                          dept.trend.startsWith("+")
                            ? "default"
                            : dept.trend.startsWith("-")
                              ? "destructive"
                              : "secondary"
                        }
                      >
                        {dept.trend}
                      </Badge>
                    </div>
                  </div>
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <span>{dept.employees}人</span>
                    <span>完成率: {dept.completion}%</span>
                  </div>
                  <Progress value={(dept.avgScore / 5) * 100} className="h-2" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Score Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <PieChart className="w-5 h-5 mr-2" />
              评分分布
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {scoreDistribution.map((item, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{item.range}分</span>
                    <span className="text-sm text-gray-600">
                      {item.count}人 ({item.percentage}%)
                    </span>
                  </div>
                  <Progress value={item.percentage} className="h-2" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Top Performers */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Star className="w-5 h-5 mr-2" />
              优秀员工排行
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topPerformers.map((performer, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-bold text-yellow-600">#{index + 1}</span>
                    </div>
                    <div>
                      <p className="font-medium">{performer.name}</p>
                      <p className="text-sm text-gray-600">
                        {performer.position} · {performer.department}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-lg">{performer.score}</p>
                    <p className="text-sm text-gray-600">分</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Monthly Trends */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="w-5 h-5 mr-2" />
              月度趋势
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {monthlyTrends.map((trend, index) => (
                <div key={index} className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{trend.month}</span>
                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <p className="text-sm text-gray-600">平均分</p>
                        <p className="font-bold">{trend.score}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-gray-600">参与率</p>
                        <p className="font-bold">{trend.participation}%</p>
                      </div>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-xs text-gray-500 mb-1">评分进度</p>
                      <Progress value={(trend.score / 5) * 100} className="h-1" />
                    </div>
                    <div>
                      <p className="text-xs text-gray-500 mb-1">参与进度</p>
                      <Progress value={trend.participation} className="h-1" />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
