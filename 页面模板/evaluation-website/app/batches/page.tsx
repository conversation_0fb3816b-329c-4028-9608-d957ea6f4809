"use client"

import { useState } from "react"
import {
  Search,
  Plus,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Play,
  Pause,
  Calendar,
  Users,
  Clock,
  CheckCircle,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Progress } from "@/components/ui/progress"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export default function BatchesPage() {
  const [statusFilter, setStatusFilter] = useState("all")

  const batches = [
    {
      id: 1,
      name: "2024年第一季度绩效考评",
      description: "全公司季度绩效评估，包含所有在职员工",
      type: "季度考评",
      status: "进行中",
      startDate: "2024-03-01",
      endDate: "2024-03-31",
      participants: 156,
      completed: 118,
      departments: ["技术部", "产品部", "设计部", "市场部"],
      creator: "HR管理员",
      createdAt: "2024-02-25",
      progress: 76,
    },
    {
      id: 2,
      name: "新员工试用期考评批次",
      description: "2024年1-2月入职新员工试用期评估",
      type: "试用期考评",
      status: "已完成",
      startDate: "2024-02-15",
      endDate: "2024-03-15",
      participants: 12,
      completed: 12,
      departments: ["技术部", "产品部"],
      creator: "HR管理员",
      createdAt: "2024-02-10",
      progress: 100,
    },
    {
      id: 3,
      name: "管理层360度评估",
      description: "中高层管理人员360度全方位评估",
      type: "360度评估",
      status: "待开始",
      startDate: "2024-04-01",
      endDate: "2024-04-30",
      participants: 24,
      completed: 0,
      departments: ["全部"],
      creator: "CEO办公室",
      createdAt: "2024-03-20",
      progress: 0,
    },
    {
      id: 4,
      name: "年度优秀员工评选",
      description: "2023年度优秀员工评选活动",
      type: "年度评选",
      status: "已暂停",
      startDate: "2024-01-15",
      endDate: "2024-02-15",
      participants: 156,
      completed: 89,
      departments: ["全部"],
      creator: "HR管理员",
      createdAt: "2024-01-10",
      progress: 57,
    },
  ]

  const getStatusBadge = (status: string) => {
    const variants = {
      进行中: { variant: "default", color: "bg-blue-100 text-blue-800" },
      已完成: { variant: "default", color: "bg-green-100 text-green-800" },
      待开始: { variant: "secondary", color: "bg-gray-100 text-gray-800" },
      已暂停: { variant: "destructive", color: "bg-red-100 text-red-800" },
    }
    const config = variants[status as keyof typeof variants] || variants["待开始"]
    return <Badge className={config.color}>{status}</Badge>
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "进行中":
        return <Play className="w-4 h-4 text-blue-600" />
      case "已完成":
        return <CheckCircle className="w-4 h-4 text-green-600" />
      case "待开始":
        return <Clock className="w-4 h-4 text-gray-600" />
      case "已暂停":
        return <Pause className="w-4 h-4 text-red-600" />
      default:
        return <Clock className="w-4 h-4 text-gray-600" />
    }
  }

  return (
    <div className="p-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">考评批次管理</h1>
            <p className="text-gray-600 mt-1">创建和管理不同类型的考评活动</p>
          </div>
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            创建新批次
          </Button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Calendar className="w-6 h-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">总批次数</p>
                <p className="text-2xl font-bold text-gray-900">12</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <Play className="w-6 h-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">进行中</p>
                <p className="text-2xl font-bold text-gray-900">3</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Clock className="w-6 h-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">待开始</p>
                <p className="text-2xl font-bold text-gray-900">2</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <CheckCircle className="w-6 h-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">已完成</p>
                <p className="text-2xl font-bold text-gray-900">7</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input placeholder="搜索批次名称或描述..." className="pl-10" />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="选择状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部状态</SelectItem>
                <SelectItem value="active">进行中</SelectItem>
                <SelectItem value="completed">已完成</SelectItem>
                <SelectItem value="pending">待开始</SelectItem>
                <SelectItem value="paused">已暂停</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Batches List */}
      <div className="space-y-6">
        {batches.map((batch) => (
          <Card key={batch.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    {getStatusIcon(batch.status)}
                    <CardTitle className="text-xl">{batch.name}</CardTitle>
                    {getStatusBadge(batch.status)}
                  </div>
                  <p className="text-gray-600 mb-3">{batch.description}</p>
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="outline">{batch.type}</Badge>
                    {batch.departments.map((dept, index) => (
                      <Badge key={index} variant="secondary">
                        {dept}
                      </Badge>
                    ))}
                  </div>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem>
                      <Eye className="w-4 h-4 mr-2" />
                      查看详情
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Edit className="w-4 h-4 mr-2" />
                      编辑批次
                    </DropdownMenuItem>
                    {batch.status === "进行中" && (
                      <DropdownMenuItem>
                        <Pause className="w-4 h-4 mr-2" />
                        暂停批次
                      </DropdownMenuItem>
                    )}
                    {batch.status === "已暂停" && (
                      <DropdownMenuItem>
                        <Play className="w-4 h-4 mr-2" />
                        恢复批次
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuItem className="text-red-600">
                      <Trash2 className="w-4 h-4 mr-2" />
                      删除批次
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <div className="flex items-center space-x-2">
                  <Calendar className="w-4 h-4 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-600">开始时间</p>
                    <p className="font-medium">{batch.startDate}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Calendar className="w-4 h-4 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-600">结束时间</p>
                    <p className="font-medium">{batch.endDate}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Users className="w-4 h-4 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-600">参与人数</p>
                    <p className="font-medium">{batch.participants}</p>
                  </div>
                </div>
                <div>
                  <p className="text-sm text-gray-600">创建者</p>
                  <p className="font-medium">{batch.creator}</p>
                </div>
              </div>

              {/* Progress */}
              <div className="space-y-2 mb-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">完成进度</span>
                  <span className="font-medium">
                    {batch.completed}/{batch.participants} ({batch.progress}%)
                  </span>
                </div>
                <Progress value={batch.progress} className="h-2" />
              </div>

              {/* Actions */}
              <div className="flex space-x-2">
                <Button variant="outline" size="sm">
                  查看参与者
                </Button>
                <Button variant="outline" size="sm">
                  导出数据
                </Button>
                <Button variant="outline" size="sm">
                  发送提醒
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
