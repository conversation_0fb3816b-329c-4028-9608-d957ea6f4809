"use client"

import { useState } from "react"
import { ChevronLeft, ChevronRight, Save, Send, User, Shield, CheckCircle, AlertCircle, FileText } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Textarea } from "@/components/ui/textarea"
import { Progress } from "@/components/ui/progress"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Input } from "@/components/ui/input"

export default function EvaluateInputVersionPage() {
  const [currentEvaluee, setCurrentEvaluee] = useState(0)
  const [ratings, setRatings] = useState<{ [key: string]: number }>({})
  const [comments, setComments] = useState<{ [key: string]: string }>({})
  const [overallRating, setOverallRating] = useState<number>(0)
  const [overallComment, setOverallComment] = useState("")

  // 待评价人员列表
  const evaluees = [
    {
      id: 1,
      name: "张**",
      position: "前端工程师",
      department: "技术部",
      avatar: "/placeholder.svg?height=80&width=80",
      workYears: "2年",
      relationship: "同事",
    },
    {
      id: 2,
      name: "李**",
      position: "产品经理",
      department: "产品部",
      avatar: "/placeholder.svg?height=80&width=80",
      workYears: "3年",
      relationship: "合作伙伴",
    },
    {
      id: 3,
      name: "王**",
      position: "UI设计师",
      department: "设计部",
      avatar: "/placeholder.svg?height=80&width=80",
      workYears: "1.5年",
      relationship: "同事",
    },
  ]

  // 评价维度
  const evaluationCriteria = [
    {
      id: "work_quality",
      name: "工作质量",
      description: "工作成果的质量和准确性",
      icon: "⭐",
    },
    {
      id: "efficiency",
      name: "工作效率",
      description: "完成任务的速度和时间管理能力",
      icon: "⚡",
    },
    {
      id: "communication",
      name: "沟通协作",
      description: "与团队成员的沟通和协作能力",
      icon: "💬",
    },
    {
      id: "innovation",
      name: "创新能力",
      description: "提出新想法和解决问题的创造性",
      icon: "💡",
    },
    {
      id: "responsibility",
      name: "责任心",
      description: "对工作的责任感和主动性",
      icon: "🎯",
    },
    {
      id: "learning",
      name: "学习能力",
      description: "学习新知识和技能的能力",
      icon: "📚",
    },
  ]

  const currentEmployee = evaluees[currentEvaluee]
  const progress = ((currentEvaluee + 1) / evaluees.length) * 100

  const handleRatingChange = (criteriaId: string, rating: number) => {
    // 确保评分在1-10范围内
    if (rating >= 1 && rating <= 10) {
      setRatings({ ...ratings, [criteriaId]: rating })
    }
  }

  const handleCommentChange = (criteriaId: string, comment: string) => {
    setComments({ ...comments, [criteriaId]: comment })
  }

  const handleOverallRatingChange = (rating: number) => {
    if (rating >= 1 && rating <= 10) {
      setOverallRating(rating)
    }
  }

  const renderInputRating = (criteriaId: string, currentRating: number) => {
    return (
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <Label className="text-sm font-medium text-gray-700 min-w-[40px]">评分:</Label>
          <Input
            type="number"
            min="1"
            max="10"
            value={currentRating || ""}
            onChange={(e) => {
              const value = Number.parseInt(e.target.value)
              if (!isNaN(value)) {
                handleRatingChange(criteriaId, value)
              } else if (e.target.value === "") {
                handleRatingChange(criteriaId, 0)
              }
            }}
            className="w-20 text-center"
            placeholder="1-10"
          />
          <span className="text-sm text-gray-500">/ 10</span>
        </div>
        <div className="flex-1">
          <div className="flex items-center space-x-1 text-xs text-gray-500">
            <span>1-3: 待改进</span>
            <span>•</span>
            <span>4-6: 一般</span>
            <span>•</span>
            <span>7-8: 良好</span>
            <span>•</span>
            <span>9-10: 优秀</span>
          </div>
        </div>
      </div>
    )
  }

  const isCurrentEvaluationComplete = () => {
    const hasAllRatings = evaluationCriteria.every((criteria) => ratings[criteria.id] > 0)
    const hasOverallRating = overallRating > 0
    return hasAllRatings && hasOverallRating
  }

  const handleNext = () => {
    if (currentEvaluee < evaluees.length - 1) {
      setCurrentEvaluee(currentEvaluee + 1)
      // 清空当前评分（实际应用中可能需要保存）
      setRatings({})
      setComments({})
      setOverallRating(0)
      setOverallComment("")
    }
  }

  const handlePrevious = () => {
    if (currentEvaluee > 0) {
      setCurrentEvaluee(currentEvaluee - 1)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">匿名考评</h1>
              <p className="text-gray-600 mt-1">请客观公正地对同事进行评价（1-10分制）</p>
            </div>
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <Shield className="w-4 h-4" />
              <span>匿名保护</span>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Progress */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <span className="text-sm font-medium text-gray-700">
                评价进度: {currentEvaluee + 1} / {evaluees.length}
              </span>
              <span className="text-sm text-gray-600">{Math.round(progress)}% 完成</span>
            </div>
            <Progress value={progress} className="h-2" />
          </CardContent>
        </Card>

        {/* Current Evaluee Info */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="w-5 h-5 mr-2" />
              被评价人信息
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-6">
              <Avatar className="w-20 h-20">
                <AvatarImage src={currentEmployee.avatar || "/placeholder.svg"} />
                <AvatarFallback className="text-lg">{currentEmployee.name[0]}</AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <h3 className="text-xl font-semibold text-gray-900">{currentEmployee.name}</h3>
                <div className="mt-2 space-y-1">
                  <p className="text-gray-600">{currentEmployee.position}</p>
                  <p className="text-gray-600">{currentEmployee.department}</p>
                  <div className="flex items-center space-x-4 mt-2">
                    <Badge variant="outline">工作经验: {currentEmployee.workYears}</Badge>
                    <Badge variant="secondary">关系: {currentEmployee.relationship}</Badge>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Evaluation Form */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="w-5 h-5 mr-2" />
              详细评价
            </CardTitle>
            <p className="text-sm text-gray-600">请根据您的观察和合作经验，对以下各项进行评分（1-10分）</p>
          </CardHeader>
          <CardContent className="space-y-8">
            {evaluationCriteria.map((criteria, index) => (
              <div key={criteria.id}>
                <div className="flex items-start space-x-4">
                  <div className="text-2xl">{criteria.icon}</div>
                  <div className="flex-1">
                    <div className="mb-4">
                      <h4 className="text-lg font-medium text-gray-900 mb-2">{criteria.name}</h4>
                      <p className="text-sm text-gray-600 mb-4">{criteria.description}</p>
                      {renderInputRating(criteria.id, ratings[criteria.id] || 0)}
                    </div>
                    <Textarea
                      placeholder={`请详细说明您对${currentEmployee.name}在${criteria.name}方面的评价...`}
                      value={comments[criteria.id] || ""}
                      onChange={(e) => handleCommentChange(criteria.id, e.target.value)}
                      className="min-h-[80px]"
                    />
                  </div>
                </div>
                {index < evaluationCriteria.length - 1 && <Separator className="mt-8" />}
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Overall Rating */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="w-5 h-5 mr-2" />
              综合评价
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div>
                <Label className="text-base font-medium mb-4 block">总体评分</Label>
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <Input
                      type="number"
                      min="1"
                      max="10"
                      value={overallRating || ""}
                      onChange={(e) => {
                        const value = Number.parseInt(e.target.value)
                        if (!isNaN(value)) {
                          handleOverallRatingChange(value)
                        } else if (e.target.value === "") {
                          setOverallRating(0)
                        }
                      }}
                      className="w-24 text-center text-lg"
                      placeholder="1-10"
                    />
                    <span className="text-lg text-gray-500">/ 10</span>
                  </div>
                  <div className="flex items-center space-x-1 text-sm text-gray-500">
                    <span>1-3: 待改进</span>
                    <span>•</span>
                    <span>4-6: 一般</span>
                    <span>•</span>
                    <span>7-8: 良好</span>
                    <span>•</span>
                    <span>9-10: 优秀</span>
                  </div>
                </div>
              </div>

              <div>
                <Label className="text-base font-medium mb-2 block">综合评价意见</Label>
                <Textarea
                  placeholder={`请对${currentEmployee.name}的整体工作表现进行综合评价，包括优点、需要改进的地方以及建议...`}
                  value={overallComment}
                  onChange={(e) => setOverallComment(e.target.value)}
                  className="min-h-[120px]"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Completion Status */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              {isCurrentEvaluationComplete() ? (
                <>
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="text-green-600 font-medium">评价已完成，可以提交</span>
                </>
              ) : (
                <>
                  <AlertCircle className="w-5 h-5 text-yellow-600" />
                  <span className="text-yellow-600 font-medium">请完成所有评分项目（1-10分）</span>
                </>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex items-center justify-between">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentEvaluee === 0}
            className="flex items-center bg-transparent"
          >
            <ChevronLeft className="w-4 h-4 mr-2" />
            上一位
          </Button>

          <div className="flex items-center space-x-3">
            <Button variant="outline" className="flex items-center bg-transparent">
              <Save className="w-4 h-4 mr-2" />
              保存草稿
            </Button>

            {currentEvaluee < evaluees.length - 1 ? (
              <Button onClick={handleNext} disabled={!isCurrentEvaluationComplete()} className="flex items-center">
                下一位
                <ChevronRight className="w-4 h-4 ml-2" />
              </Button>
            ) : (
              <Button
                disabled={!isCurrentEvaluationComplete()}
                className="flex items-center bg-green-600 hover:bg-green-700"
              >
                <Send className="w-4 h-4 mr-2" />
                提交评价
              </Button>
            )}
          </div>
        </div>

        {/* Anonymous Notice */}
        <Card className="mt-8 bg-blue-50 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-start space-x-3">
              <Shield className="w-5 h-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-900 mb-2">匿名保护说明</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• 您的评价将完全匿名，被评价人无法知道评价来源</li>
                  <li>• 系统会对所有评价数据进行加密处理</li>
                  <li>• 请客观公正地进行评价（1-10分制），您的意见对同事的发展很重要</li>
                  <li>• 评价提交后无法修改，请仔细检查后再提交</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
