"use client"

import { useState } from "react"
import {
  Star,
  ChevronLeft,
  ChevronRight,
  Save,
  Send,
  User,
  Shield,
  CheckCircle,
  AlertCircle,
  FileText,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"

export default function EvaluatePage() {
  const [currentEvaluee, setCurrentEvaluee] = useState(0)
  const [ratings, setRatings] = useState<{ [key: string]: number }>({})
  const [overallRating, setOverallRating] = useState<number>(0)

  // 待评价人员列表
  const evaluees = [
    {
      id: 1,
      name: "张**",
      position: "前端工程师",
      department: "技术部",
      avatar: "/placeholder.svg?height=80&width=80",
      workYears: "2年",
      relationship: "同事",
    },
    {
      id: 2,
      name: "李**",
      position: "产品经理",
      department: "产品部",
      avatar: "/placeholder.svg?height=80&width=80",
      workYears: "3年",
      relationship: "合作伙伴",
    },
    {
      id: 3,
      name: "王**",
      position: "UI设计师",
      department: "设计部",
      avatar: "/placeholder.svg?height=80&width=80",
      workYears: "1.5年",
      relationship: "同事",
    },
  ]

  // 评价维度
  const evaluationCriteria = [
    {
      id: "work_quality",
      name: "工作质量",
      description: "工作成果的质量和准确性",
      icon: "⭐",
    },
    {
      id: "efficiency",
      name: "工作效率",
      description: "完成任务的速度和时间管理能力",
      icon: "⚡",
    },
    {
      id: "communication",
      name: "沟通协作",
      description: "与团队成员的沟通和协作能力",
      icon: "💬",
    },
    {
      id: "innovation",
      name: "创新能力",
      description: "提出新想法和解决问题的创造性",
      icon: "💡",
    },
    {
      id: "responsibility",
      name: "责任心",
      description: "对工作的责任感和主动性",
      icon: "🎯",
    },
    {
      id: "learning",
      name: "学习能力",
      description: "学习新知识和技能的能力",
      icon: "📚",
    },
  ]

  const currentEmployee = evaluees[currentEvaluee]
  const progress = ((currentEvaluee + 1) / evaluees.length) * 100

  const handleRatingChange = (criteriaId: string, rating: number) => {
    setRatings({ ...ratings, [criteriaId]: rating })
  }

  const renderNumberRating = (criteriaId: string, currentRating: number) => {
    return (
      <div className="flex items-center space-x-2">
        <div className="flex items-center space-x-1">
          {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((num) => (
            <button
              key={num}
              onClick={() => handleRatingChange(criteriaId, num)}
              className={`w-8 h-8 rounded-md border-2 text-sm font-medium transition-all focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 ${
                num === currentRating
                  ? "bg-blue-600 text-white border-blue-600"
                  : num < currentRating
                    ? "bg-blue-100 text-blue-700 border-blue-300"
                    : "bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50"
              }`}
            >
              {num}
            </button>
          ))}
        </div>
        <span className="ml-3 text-sm text-gray-600 min-w-[60px]">
          {currentRating > 0 ? `${currentRating}/10` : "未评分"}
        </span>
      </div>
    )
  }

  const isCurrentEvaluationComplete = () => {
    const hasAllRatings = evaluationCriteria.every((criteria) => ratings[criteria.id] > 0)
    const hasOverallRating = overallRating > 0
    return hasAllRatings && hasOverallRating
  }

  const handleNext = () => {
    if (currentEvaluee < evaluees.length - 1) {
      setCurrentEvaluee(currentEvaluee + 1)
      // 清空当前评分（实际应用中可能需要保存）
      setRatings({})
      setOverallRating(0)
    }
  }

  const handlePrevious = () => {
    if (currentEvaluee > 0) {
      setCurrentEvaluee(currentEvaluee - 1)
    }
  }

  return (
    <div className="p-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">匿名考评</h1>
            <p className="text-gray-600 mt-1">请客观公正地对同事进行评分</p>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <Shield className="w-4 h-4" />
            <span>匿名保护</span>
          </div>
        </div>
      </div>

      {/* Progress */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <span className="text-sm font-medium text-gray-700">
              评价进度: {currentEvaluee + 1} / {evaluees.length}
            </span>
            <span className="text-sm text-gray-600">{Math.round(progress)}% 完成</span>
          </div>
          <Progress value={progress} className="h-2" />
        </CardContent>
      </Card>

      {/* Current Evaluee Info */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center">
            <User className="w-5 h-5 mr-2" />
            被评价人信息
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-6">
            <Avatar className="w-20 h-20">
              <AvatarImage src={currentEmployee.avatar || "/placeholder.svg"} />
              <AvatarFallback className="text-lg">{currentEmployee.name[0]}</AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <h3 className="text-xl font-semibold text-gray-900">{currentEmployee.name}</h3>
              <div className="mt-2 space-y-1">
                <p className="text-gray-600">{currentEmployee.position}</p>
                <p className="text-gray-600">{currentEmployee.department}</p>
                <div className="flex items-center space-x-4 mt-2">
                  <Badge variant="outline">工作经验: {currentEmployee.workYears}</Badge>
                  <Badge variant="secondary">关系: {currentEmployee.relationship}</Badge>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Evaluation Form */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="w-5 h-5 mr-2" />
            能力评分
          </CardTitle>
          <p className="text-sm text-gray-600">请根据您的观察和合作经验，对以下各项进行评分（1-10分）</p>
        </CardHeader>
        <CardContent className="space-y-6">
          {evaluationCriteria.map((criteria, index) => (
            <div key={criteria.id}>
              <div className="flex items-center space-x-4">
                <div className="text-2xl">{criteria.icon}</div>
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <h4 className="text-lg font-medium text-gray-900">{criteria.name}</h4>
                      <p className="text-sm text-gray-600">{criteria.description}</p>
                    </div>
                    {renderNumberRating(criteria.id, ratings[criteria.id] || 0)}
                  </div>
                </div>
              </div>
              {index < evaluationCriteria.length - 1 && <Separator className="mt-6" />}
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Overall Rating */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Star className="w-5 h-5 mr-2" />
            综合评分
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-base font-medium block mb-2">总体评分</Label>
              <p className="text-sm text-gray-600">对该员工的整体工作表现进行综合评分</p>
            </div>
            <div className="flex items-center space-x-2">
              <div className="flex items-center space-x-1">
                {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((num) => (
                  <button
                    key={num}
                    onClick={() => setOverallRating(num)}
                    className={`w-10 h-10 rounded-md border-2 text-sm font-medium transition-all focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 ${
                      num === overallRating
                        ? "bg-blue-600 text-white border-blue-600"
                        : num < overallRating
                          ? "bg-blue-100 text-blue-700 border-blue-300"
                          : "bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50"
                    }`}
                  >
                    {num}
                  </button>
                ))}
              </div>
              <span className="ml-4 text-lg font-medium">
                {overallRating > 0 ? `${overallRating}/10` : "请选择评分"}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Rating Summary */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>评分汇总</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {evaluationCriteria.map((criteria) => (
              <div key={criteria.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-2">
                  <span className="text-lg">{criteria.icon}</span>
                  <span className="text-sm font-medium text-gray-700">{criteria.name}</span>
                </div>
                <span className={`text-sm font-bold ${ratings[criteria.id] > 0 ? "text-blue-600" : "text-gray-400"}`}>
                  {ratings[criteria.id] > 0 ? `${ratings[criteria.id]}/10` : "--"}
                </span>
              </div>
            ))}
            <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border-2 border-blue-200">
              <div className="flex items-center space-x-2">
                <Star className="w-5 h-5 text-blue-600" />
                <span className="text-sm font-medium text-blue-700">综合评分</span>
              </div>
              <span className={`text-sm font-bold ${overallRating > 0 ? "text-blue-600" : "text-gray-400"}`}>
                {overallRating > 0 ? `${overallRating}/10` : "--"}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Completion Status */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="flex items-center space-x-3">
            {isCurrentEvaluationComplete() ? (
              <>
                <CheckCircle className="w-5 h-5 text-green-600" />
                <span className="text-green-600 font-medium">评分已完成，可以提交</span>
              </>
            ) : (
              <>
                <AlertCircle className="w-5 h-5 text-yellow-600" />
                <span className="text-yellow-600 font-medium">请完成所有评分项目</span>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={handlePrevious}
          disabled={currentEvaluee === 0}
          className="flex items-center bg-transparent"
        >
          <ChevronLeft className="w-4 h-4 mr-2" />
          上一位
        </Button>

        <div className="flex items-center space-x-3">
          <Button variant="outline" className="flex items-center bg-transparent">
            <Save className="w-4 h-4 mr-2" />
            保存草稿
          </Button>

          {currentEvaluee < evaluees.length - 1 ? (
            <Button onClick={handleNext} disabled={!isCurrentEvaluationComplete()} className="flex items-center">
              下一位
              <ChevronRight className="w-4 h-4 ml-2" />
            </Button>
          ) : (
            <Button
              disabled={!isCurrentEvaluationComplete()}
              className="flex items-center bg-green-600 hover:bg-green-700"
            >
              <Send className="w-4 h-4 mr-2" />
              提交评价
            </Button>
          )}
        </div>
      </div>

      {/* Anonymous Notice */}
      <Card className="mt-8 bg-blue-50 border-blue-200">
        <CardContent className="p-6">
          <div className="flex items-start space-x-3">
            <Shield className="w-5 h-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-900 mb-2">匿名保护说明</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• 您的评分将完全匿名，被评价人无法知道评分来源</li>
                <li>• 系统会对所有评分数据进行加密处理</li>
                <li>• 请客观公正地进行评分，您的评价对同事的发展很重要</li>
                <li>• 评分提交后无法修改，请仔细检查后再提交</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
