"use client"
import { Clock, CheckCircle, AlertCircle, User, Calendar, Star } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"

export default function EvaluationListPage() {
  const evaluationTasks = [
    {
      id: 1,
      batchName: "2024年第一季度绩效考评",
      deadline: "2024-03-31",
      totalEvaluees: 3,
      completedEvaluees: 0,
      status: "pending",
      evaluees: [
        { name: "张**", position: "前端工程师", department: "技术部" },
        { name: "李**", position: "产品经理", department: "产品部" },
        { name: "王**", position: "UI设计师", department: "设计部" },
      ],
    },
    {
      id: 2,
      batchName: "新员工试用期考评",
      deadline: "2024-03-15",
      totalEvaluees: 2,
      completedEvaluees: 2,
      status: "completed",
      evaluees: [
        { name: "赵**", position: "后端工程师", department: "技术部" },
        { name: "钱**", position: "市场专员", department: "市场部" },
      ],
    },
    {
      id: 3,
      batchName: "管理层360度评估",
      deadline: "2024-04-30",
      totalEvaluees: 1,
      completedEvaluees: 0,
      status: "upcoming",
      evaluees: [{ name: "孙**", position: "技术总监", department: "技术部" }],
    },
  ]

  const getStatusInfo = (status: string) => {
    switch (status) {
      case "pending":
        return {
          icon: <AlertCircle className="w-5 h-5 text-yellow-600" />,
          badge: <Badge className="bg-yellow-100 text-yellow-800">待完成</Badge>,
          color: "border-yellow-200",
        }
      case "completed":
        return {
          icon: <CheckCircle className="w-5 h-5 text-green-600" />,
          badge: <Badge className="bg-green-100 text-green-800">已完成</Badge>,
          color: "border-green-200",
        }
      case "upcoming":
        return {
          icon: <Clock className="w-5 h-5 text-blue-600" />,
          badge: <Badge className="bg-blue-100 text-blue-800">即将开始</Badge>,
          color: "border-blue-200",
        }
      default:
        return {
          icon: <Clock className="w-5 h-5 text-gray-600" />,
          badge: <Badge variant="secondary">未知</Badge>,
          color: "border-gray-200",
        }
    }
  }

  const getDaysRemaining = (deadline: string) => {
    const today = new Date()
    const deadlineDate = new Date(deadline)
    const diffTime = deadlineDate.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">我的考评任务</h1>
            <p className="text-gray-600 mt-1">查看和完成分配给您的考评任务</p>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <AlertCircle className="w-6 h-6 text-yellow-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">待完成</p>
                  <p className="text-2xl font-bold text-gray-900">1</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <CheckCircle className="w-6 h-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">已完成</p>
                  <p className="text-2xl font-bold text-gray-900">1</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Clock className="w-6 h-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">即将开始</p>
                  <p className="text-2xl font-bold text-gray-900">1</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Evaluation Tasks */}
        <div className="space-y-6">
          {evaluationTasks.map((task) => {
            const statusInfo = getStatusInfo(task.status)
            const daysRemaining = getDaysRemaining(task.deadline)
            const progress = (task.completedEvaluees / task.totalEvaluees) * 100

            return (
              <Card key={task.id} className={`${statusInfo.color} border-2`}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {statusInfo.icon}
                      <CardTitle className="text-xl">{task.batchName}</CardTitle>
                      {statusInfo.badge}
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-600">截止日期</p>
                      <p className="font-medium">{task.deadline}</p>
                      {daysRemaining > 0 && task.status !== "completed" && (
                        <p className="text-sm text-red-600">还有 {daysRemaining} 天</p>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {/* Progress */}
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-700">完成进度</span>
                        <span className="text-sm text-gray-600">
                          {task.completedEvaluees}/{task.totalEvaluees}
                        </span>
                      </div>
                      <Progress value={progress} className="h-2" />
                    </div>

                    {/* Evaluees List */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-3">待评价人员</h4>
                      <div className="space-y-3">
                        {task.evaluees.map((evaluee, index) => (
                          <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center space-x-3">
                              <Avatar>
                                <AvatarImage src="/placeholder.svg" />
                                <AvatarFallback>{evaluee.name[0]}</AvatarFallback>
                              </Avatar>
                              <div>
                                <p className="font-medium">{evaluee.name}</p>
                                <p className="text-sm text-gray-600">
                                  {evaluee.position} · {evaluee.department}
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              {task.status === "completed" ? (
                                <Badge className="bg-green-100 text-green-800">已完成</Badge>
                              ) : (
                                <Badge variant="outline">待评价</Badge>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center justify-between pt-4 border-t">
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <div className="flex items-center">
                          <User className="w-4 h-4 mr-1" />
                          {task.totalEvaluees} 人
                        </div>
                        <div className="flex items-center">
                          <Calendar className="w-4 h-4 mr-1" />
                          {task.deadline}
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        {task.status === "completed" ? (
                          <Button variant="outline" size="sm">
                            <Star className="w-4 h-4 mr-2" />
                            查看结果
                          </Button>
                        ) : task.status === "pending" ? (
                          <Button size="sm">开始评价</Button>
                        ) : (
                          <Button variant="outline" size="sm" disabled>
                            尚未开始
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
      </div>
    </div>
  )
}
