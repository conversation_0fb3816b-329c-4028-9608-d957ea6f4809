"use client"

import { CheckCircle, Home, FileText, Clock } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function EvaluationCompletedPage() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <Card className="text-center">
          <CardHeader className="pb-4">
            <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <CardTitle className="text-2xl text-gray-900">评价提交成功！</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <p className="text-gray-600 text-lg">
              感谢您认真完成本次匿名考评，您的客观评价对同事的成长和发展非常重要。
            </p>

            <div className="bg-gray-50 rounded-lg p-6 space-y-4">
              <h3 className="font-medium text-gray-900 mb-3">本次评价统计</h3>
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">3</div>
                  <div className="text-gray-600">评价人数</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">18</div>
                  <div className="text-gray-600">评价项目</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">100%</div>
                  <div className="text-gray-600">完成度</div>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <Clock className="w-5 h-5 text-blue-600 mt-0.5" />
                <div className="text-sm text-blue-800">
                  <p className="font-medium mb-1">后续流程</p>
                  <p>评价结果将在3-5个工作日内汇总完成，相关报告将发送给HR部门和直属领导。</p>
                </div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button variant="outline" className="flex items-center bg-transparent">
                <FileText className="w-4 h-4 mr-2" />
                查看我的评价记录
              </Button>
              <Button className="flex items-center">
                <Home className="w-4 h-4 mr-2" />
                返回首页
              </Button>
            </div>

            <div className="text-xs text-gray-500 pt-4 border-t">
              <p>如有任何问题，请联系HR部门或系统管理员</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
