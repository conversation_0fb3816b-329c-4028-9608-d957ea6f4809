"use client"

import { Search, Plus, MoreHorizontal, Edit, Trash2, Users, TrendingUp, Building, User } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Progress } from "@/components/ui/progress"

export default function DepartmentsPage() {
  const departments = [
    {
      id: 1,
      name: "技术部",
      description: "负责产品技术开发和维护",
      manager: {
        name: "李技术",
        avatar: "/placeholder.svg?height=40&width=40",
      },
      employeeCount: 45,
      avgScore: 4.3,
      completedEvaluations: 38,
      totalEvaluations: 45,
      budget: "¥2,500,000",
      established: "2020-01-15",
    },
    {
      id: 2,
      name: "产品部",
      description: "产品规划、设计和用户体验",
      manager: {
        name: "王产品",
        avatar: "/placeholder.svg?height=40&width=40",
      },
      employeeCount: 28,
      avgScore: 4.1,
      completedEvaluations: 25,
      totalEvaluations: 28,
      budget: "¥1,800,000",
      established: "2020-03-20",
    },
    {
      id: 3,
      name: "设计部",
      description: "UI/UX设计和品牌视觉设计",
      manager: {
        name: "张设计",
        avatar: "/placeholder.svg?height=40&width=40",
      },
      employeeCount: 18,
      avgScore: 4.6,
      completedEvaluations: 18,
      totalEvaluations: 18,
      budget: "¥1,200,000",
      established: "2020-05-10",
    },
    {
      id: 4,
      name: "市场部",
      description: "市场推广和品牌营销",
      manager: {
        name: "刘市场",
        avatar: "/placeholder.svg?height=40&width=40",
      },
      employeeCount: 22,
      avgScore: 4.0,
      completedEvaluations: 18,
      totalEvaluations: 22,
      budget: "¥3,000,000",
      established: "2020-02-01",
    },
    {
      id: 5,
      name: "人事部",
      description: "人力资源管理和企业文化建设",
      manager: {
        name: "陈人事",
        avatar: "/placeholder.svg?height=40&width=40",
      },
      employeeCount: 12,
      avgScore: 4.2,
      completedEvaluations: 12,
      totalEvaluations: 12,
      budget: "¥800,000",
      established: "2020-01-01",
    },
  ]

  return (
    <div className="p-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">部门管理</h1>
            <p className="text-gray-600 mt-1">管理公司组织架构和部门信息</p>
          </div>
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            新建部门
          </Button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Building className="w-6 h-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">总部门数</p>
                <p className="text-2xl font-bold text-gray-900">8</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <Users className="w-6 h-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">总员工数</p>
                <p className="text-2xl font-bold text-gray-900">156</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <TrendingUp className="w-6 h-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">平均评分</p>
                <p className="text-2xl font-bold text-gray-900">4.2</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <User className="w-6 h-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">部门经理</p>
                <p className="text-2xl font-bold text-gray-900">8</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input placeholder="搜索部门名称或描述..." className="pl-10" />
          </div>
        </CardContent>
      </Card>

      {/* Departments Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {departments.map((dept) => (
          <Card key={dept.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-xl">{dept.name}</CardTitle>
                  <p className="text-gray-600 mt-1">{dept.description}</p>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem>
                      <Edit className="w-4 h-4 mr-2" />
                      编辑部门
                    </DropdownMenuItem>
                    <DropdownMenuItem className="text-red-600">
                      <Trash2 className="w-4 h-4 mr-2" />
                      删除部门
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardHeader>
            <CardContent>
              {/* Manager Info */}
              <div className="flex items-center space-x-3 mb-4">
                <Avatar>
                  <AvatarImage src={dept.manager.avatar || "/placeholder.svg"} />
                  <AvatarFallback>{dept.manager.name[0]}</AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium text-gray-900">{dept.manager.name}</p>
                  <p className="text-sm text-gray-500">部门经理</p>
                </div>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <p className="text-sm text-gray-600">员工人数</p>
                  <p className="text-lg font-semibold">{dept.employeeCount}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">平均评分</p>
                  <p className="text-lg font-semibold">{dept.avgScore}/5.0</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">年度预算</p>
                  <p className="text-lg font-semibold">{dept.budget}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">成立时间</p>
                  <p className="text-lg font-semibold">{dept.established}</p>
                </div>
              </div>

              {/* Evaluation Progress */}
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">考评完成度</span>
                  <span className="font-medium">
                    {dept.completedEvaluations}/{dept.totalEvaluations}
                  </span>
                </div>
                <Progress value={(dept.completedEvaluations / dept.totalEvaluations) * 100} className="h-2" />
              </div>

              {/* Actions */}
              <div className="flex space-x-2 mt-4">
                <Button variant="outline" size="sm" className="flex-1 bg-transparent">
                  查看员工
                </Button>
                <Button variant="outline" size="sm" className="flex-1 bg-transparent">
                  考评报告
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
