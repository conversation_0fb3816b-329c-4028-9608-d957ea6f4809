<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>匿名考评 - 考评系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lucide/0.263.1/lucide.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lucide/0.263.1/lucide.min.js"></script>
</head>
<body class="bg-gray-50">
    <div class="flex min-h-screen">
        <!-- Sidebar -->
        <div class="w-64 bg-white border-r border-gray-200 flex flex-col">
            <!-- Logo -->
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                        <i data-lucide="clipboard-check" class="w-5 h-5 text-white"></i>
                    </div>
                    <div>
                        <h1 class="text-lg font-semibold text-gray-900">考评系统</h1>
                        <p class="text-xs text-gray-500">Performance System</p>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <nav class="flex-1 p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="index.html" class="flex items-center space-x-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100 transition-colors">
                            <i data-lucide="home" class="w-4 h-4"></i>
                            <span>仪表板</span>
                        </a>
                    </li>
                    <li>
                        <a href="employees.html" class="flex items-center space-x-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100 transition-colors">
                            <i data-lucide="users" class="w-4 h-4"></i>
                            <span>员工管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="departments.html" class="flex items-center space-x-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100 transition-colors">
                            <i data-lucide="building" class="w-4 h-4"></i>
                            <span>部门管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="batches.html" class="flex items-center space-x-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100 transition-colors">
                            <i data-lucide="calendar" class="w-4 h-4"></i>
                            <span>考评批次</span>
                        </a>
                    </li>
                    <li>
                        <a href="reports.html" class="flex items-center space-x-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100 transition-colors">
                            <i data-lucide="bar-chart-3" class="w-4 h-4"></i>
                            <span>统计报告</span>
                        </a>
                    </li>
                    <li>
                        <a href="evaluate.html" class="flex items-center space-x-3 px-3 py-2 rounded-md bg-blue-50 text-blue-700 border border-blue-200">
                            <i data-lucide="shield" class="w-4 h-4"></i>
                            <span class="font-medium">匿名考评</span>
                        </a>
                    </li>
                </ul>
            </nav>

            <!-- User Info -->
            <div class="p-4 border-t border-gray-200">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                        <span class="text-sm font-medium">管</span>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900">管理员</p>
                        <p class="text-xs text-gray-500"><EMAIL></p>
                    </div>
                    <button class="p-1 text-gray-400 hover:text-gray-600">
                        <i data-lucide="settings" class="w-4 h-4"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col">
            <!-- Header -->
            <div class="bg-white border-b border-gray-200 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">匿名考评</h1>
                        <p class="text-gray-600 mt-1">请客观公正地对同事进行评分</p>
                    </div>
                    <div class="flex items-center space-x-2 text-sm text-gray-600">
                        <i data-lucide="shield" class="w-4 h-4"></i>
                        <span>匿名保护</span>
                    </div>
                </div>
            </div>

            <!-- Content -->
            <div class="flex-1 p-6 overflow-auto max-w-4xl mx-auto w-full">
                <!-- Progress -->
                <div class="bg-white rounded-lg shadow mb-6 p-6">
                    <div class="flex items-center justify-between mb-4">
                        <span class="text-sm font-medium text-gray-700">评价进度: 1 / 3</span>
                        <span class="text-sm text-gray-600">33% 完成</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-600 h-2 rounded-full" style="width: 33%"></div>
                    </div>
                </div>

                <!-- Current Evaluee Info -->
                <div class="bg-white rounded-lg shadow mb-6">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900 flex items-center">
                            <i data-lucide="user" class="w-5 h-5 mr-2"></i>
                            被评价人信息
                        </h3>
                    </div>
                    <div class="p-6">
                        <div class="flex items-center space-x-6">
                            <div class="w-20 h-20 bg-gray-300 rounded-full flex items-center justify-center">
                                <span class="text-2xl font-medium">张</span>
                            </div>
                            <div class="flex-1">
                                <h3 class="text-xl font-semibold text-gray-900">张**</h3>
                                <div class="mt-2 space-y-1">
                                    <p class="text-gray-600">前端工程师</p>
                                    <p class="text-gray-600">技术部</p>
                                    <div class="flex items-center space-x-4 mt-2">
                                        <span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">工作经验: 2年</span>
                                        <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">关系: 同事</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Evaluation Form -->
                <div class="bg-white rounded-lg shadow mb-6">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900 flex items-center">
                            <i data-lucide="file-text" class="w-5 h-5 mr-2"></i>
                            能力评分
                        </h3>
                        <p class="text-sm text-gray-600">请根据您的观察和合作经验，对以下各项进行评分（1-10分）</p>
                    </div>
                    <div class="p-6 space-y-6">
                        <!-- Work Quality -->
                        <div class="flex items-center space-x-4">
                            <div class="text-2xl">⭐</div>
                            <div class="flex-1">
                                <div class="flex items-center justify-between mb-2">
                                    <div>
                                        <h4 class="text-lg font-medium text-gray-900">工作质量</h4>
                                        <p class="text-sm text-gray-600">工作成果的质量和准确性</p>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <div class="flex items-center space-x-1" id="work-quality-rating">
                                            <button class="rating-btn w-8 h-8 rounded-md border-2 text-sm font-medium transition-all bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50" data-value="1">1</button>
                                            <button class="rating-btn w-8 h-8 rounded-md border-2 text-sm font-medium transition-all bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50" data-value="2">2</button>
                                            <button class="rating-btn w-8 h-8 rounded-md border-2 text-sm font-medium transition-all bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50" data-value="3">3</button>
                                            <button class="rating-btn w-8 h-8 rounded-md border-2 text-sm font-medium transition-all bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50" data-value="4">4</button>
                                            <button class="rating-btn w-8 h-8 rounded-md border-2 text-sm font-medium transition-all bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50" data-value="5">5</button>
                                            <button class="rating-btn w-8 h-8 rounded-md border-2 text-sm font-medium transition-all bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50" data-value="6">6</button>
                                            <button class="rating-btn w-8 h-8 rounded-md border-2 text-sm font-medium transition-all bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50" data-value="7">7</button>
                                            <button class="rating-btn w-8 h-8 rounded-md border-2 text-sm font-medium transition-all bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50" data-value="8">8</button>
                                            <button class="rating-btn w-8 h-8 rounded-md border-2 text-sm font-medium transition-all bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50" data-value="9">9</button>
                                            <button class="rating-btn w-8 h-8 rounded-md border-2 text-sm font-medium transition-all bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50" data-value="10">10</button>
                                        </div>
                                        <span class="ml-3 text-sm text-gray-600 min-w-[60px]">未评分</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <hr class="border-gray-200">

                        <!-- Efficiency -->
                        <div class="flex items-center space-x-4">
                            <div class="text-2xl">⚡</div>
                            <div class="flex-1">
                                <div class="flex items-center justify-between mb-2">
                                    <div>
                                        <h4 class="text-lg font-medium text-gray-900">工作效率</h4>
                                        <p class="text-sm text-gray-600">完成任务的速度和时间管理能力</p>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <div class="flex items-center space-x-1" id="efficiency-rating">
                                            <button class="rating-btn w-8 h-8 rounded-md border-2 text-sm font-medium transition-all bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50" data-value="1">1</button>
                                            <button class="rating-btn w-8 h-8 rounded-md border-2 text-sm font-medium transition-all bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50" data-value="2">2</button>
                                            <button class="rating-btn w-8 h-8 rounded-md border-2 text-sm font-medium transition-all bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50" data-value="3">3</button>
                                            <button class="rating-btn w-8 h-8 rounded-md border-2 text-sm font-medium transition-all bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50" data-value="4">4</button>
                                            <button class="rating-btn w-8 h-8 rounded-md border-2 text-sm font-medium transition-all bg-white text-gray-700 border-gray-300 hover:bg-blue-50" data-value="5">5</button>
                                            <button class="rating-btn w-8 h-8 rounded-md border-2 text-sm font-medium transition-all bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50" data-value="6">6</button>
                                            <button class="rating-btn w-8 h-8 rounded-md border-2 text-sm font-medium transition-all bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50" data-value="7">7</button>
                                            <button class="rating-btn w-8 h-8 rounded-md border-2 text-sm font-medium transition-all bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50" data-value="8">8</button>
                                            <button class="rating-btn w-8 h-8 rounded-md border-2 text-sm font-medium transition-all bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50" data-value="9">9</button>
                                            <button class="rating-btn w-8 h-8 rounded-md border-2 text-sm font-medium transition-all bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50" data-value="10">10</button>
                                        </div>
                                        <span class="ml-3 text-sm text-gray-600 min-w-[60px]">未评分</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Overall Rating -->
                <div class="bg-white rounded-lg shadow mb-6">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900 flex items-center">
                            <i data-lucide="star" class="w-5 h-5 mr-2"></i>
                            综合评分
                        </h3>
                    </div>
                    <div class="p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <label class="text-base font-medium block mb-2">总体评分</label>
                                <p class="text-sm text-gray-600">对该员工的整体工作表现进行综合评分</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div class="flex items-center space-x-1" id="overall-rating">
                                    <button class="rating-btn w-10 h-10 rounded-md border-2 text-sm font-medium transition-all bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50" data-value="1">1</button>
                                    <button class="rating-btn w-10 h-10 rounded-md border-2 text-sm font-medium transition-all bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50" data-value="2">2</button>
                                    <button class="rating-btn w-10 h-10 rounded-md border-2 text-sm font-medium transition-all bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50" data-value="3">3</button>
                                    <button class="rating-btn w-10 h-10 rounded-md border-2 text-sm font-medium transition-all bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50" data-value="4">4</button>
                                    <button class="rating-btn w-10 h-10 rounded-md border-2 text-sm font-medium transition-all bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50" data-value="5">5</button>
                                    <button class="rating-btn w-10 h-10 rounded-md border-2 text-sm font-medium transition-all bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50" data-value="6">6</button>
                                    <button class="rating-btn w-10 h-10 rounded-md border-2 text-sm font-medium transition-all bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50" data-value="7">7</button>
                                    <button class="rating-btn w-10 h-10 rounded-md border-2 text-sm font-medium transition-all bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50" data-value="8">8</button>
                                    <button class="rating-btn w-10 h-10 rounded-md border-2 text-sm font-medium transition-all bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50" data-value="9">9</button>
                                    <button class="rating-btn w-10 h-10 rounded-md border-2 text-sm font-medium transition-all bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50" data-value="10">10</button>
                                </div>
                                <span class="ml-4 text-lg font-medium">请选择评分</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex items-center justify-between">
                    <button class="flex items-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50" disabled>
                        <i data-lucide="chevron-left" class="w-4 h-4 mr-2"></i>
                        上一位
                    </button>

                    <div class="flex items-center space-x-3">
                        <button class="flex items-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                            <i data-lucide="save" class="w-4 h-4 mr-2"></i>
                            保存草稿
                        </button>
                        <button class="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            下一位
                            <i data-lucide="chevron-right" class="w-4 h-4 ml-2"></i>
                        </button>
                    </div>
                </div>

                <!-- Anonymous Notice -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg mt-8 p-6">
                    <div class="flex items-start space-x-3">
                        <i data-lucide="shield" class="w-5 h-5 text-blue-600 mt-0.5"></i>
                        <div>
                            <h4 class="font-medium text-blue-900 mb-2">匿名保护说明</h4>
                            <ul class="text-sm text-blue-800 space-y-1">
                                <li>• 您的评分将完全匿名，被评价人无法知道评分来源</li>
                                <li>• 系统会对所有评分数据进行加密处理</li>
                                <li>• 请客观公正地进行评分，您的评价对同事的发展很重要</li>
                                <li>• 评分提交后无法修改，请仔细检查后再提交</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        lucide.createIcons();

        // Rating functionality
        document.querySelectorAll('.rating-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const container = this.parentElement;
                const value = parseInt(this.dataset.value);
                const textSpan = container.nextElementSibling;
                
                // Reset all buttons in this container
                container.querySelectorAll('.rating-btn').forEach(b => {
                    b.className = 'rating-btn w-8 h-8 rounded-md border-2 text-sm font-medium transition-all bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50';
                    if (container.id === 'overall-rating') {
                        b.className = 'rating-btn w-10 h-10 rounded-md border-2 text-sm font-medium transition-all bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50';
                    }
                });
                
                // Style selected and previous buttons
                container.querySelectorAll('.rating-btn').forEach(b => {
                    const btnValue = parseInt(b.dataset.value);
                    if (btnValue === value) {
                        b.className = b.className.replace('bg-white text-gray-700 border-gray-300', 'bg-blue-600 text-white border-blue-600');
                    } else if (btnValue < value) {
                        b.className = b.className.replace('bg-white text-gray-700 border-gray-300', 'bg-blue-100 text-blue-700 border-blue-300');
                    }
                });
                
                // Update text
                textSpan.textContent = `${value}/10`;
            });
        });
    </script>
</body>
</html>
