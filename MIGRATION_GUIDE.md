# 数据库迁移说明文档
# 
# 软删除机制修复需要的数据库变更
# 请在Windows系统中运行以下命令来生成和应用迁移

## 1. 生成迁移文件
```bash
# 在项目根目录运行
python manage.py makemigrations --name="fix_soft_delete_foreign_keys"
```

## 2. 查看迁移内容
```bash
python manage.py sqlmigrate organizations [迁移文件编号]
```

## 3. 应用迁移
```bash
python manage.py migrate
```

## 4. 运行测试验证
```bash
python test_soft_delete_fixes.py
```

## 主要变更内容:

### 外键关系变更 (organizations/models.py):
1. Department.parent_department: CASCADE -> SET_NULL
2. Position.department: CASCADE -> SET_NULL  
3. Staff.department: CASCADE -> SET_NULL
4. Staff.position: CASCADE -> SET_NULL (已经是SET_NULL)
5. StaffLoginLog.staff: CASCADE -> SET_NULL

### 新增字段说明:
- 所有外键字段现在都允许NULL值
- 保持历史数据完整性，避免级联硬删除

### 索引优化:
- BaseModel添加了deleted_at索引
- 复合索引(deleted_at, created_at)优化查询性能

## 注意事项:
1. 迁移前请备份数据库
2. 外键变更可能需要处理现有的NOT NULL约束
3. 如果有外键约束冲突，可能需要手动处理数据
4. 建议在测试环境先验证迁移效果

## 可能的迁移问题及解决方案:

### 问题1: NOT NULL约束冲突
如果现有数据中有外键字段不允许为空，迁移可能失败。

解决方案:
```python
# 在迁移文件中先处理数据，再修改字段
operations = [
    # 第一步：为NULL的外键设置默认值
    migrations.RunSQL(
        "UPDATE organizations_position SET department_id = 1 WHERE department_id IS NULL;",
        reverse_sql="-- No reverse needed"
    ),
    # 第二步：修改外键约束
    migrations.AlterField(
        model_name='position',
        name='department',
        field=models.ForeignKey(on_delete=models.SET_NULL, null=True, to='organizations.Department'),
    ),
]
```

### 问题2: 外键引用完整性
确保SET_NULL不会破坏业务逻辑。

解决方案:
在视图和业务逻辑中添加NULL值检查:
```python
if staff.department:
    # 处理有部门的情况
else:
    # 处理部门被删除的情况
```