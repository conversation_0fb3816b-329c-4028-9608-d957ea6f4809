#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试登录跳转问题
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
django.setup()

from django.test import RequestFactory, Client
from django.contrib.sessions.middleware import SessionMiddleware
from django.contrib.messages.middleware import MessageMiddleware
from django.contrib.messages.storage.fallback import FallbackStorage
from organizations.models import Staff
from organizations.views import LoginView, DashboardView
import json

def test_login_flow():
    """测试完整的登录流程"""
    print("=== 登录流程调试 ===")
    
    # 1. 检查测试用户
    print("\n1. 检查测试用户...")
    # 查找管理员角色的用户
    staff = Staff.objects.filter(
        is_active=True,
        role__in=['super_admin', 'system_admin', 'hr_admin', 'eval_admin', 'dept_manager', 'admin']
    ).first()
    if not staff:
        print("❌ 没有找到管理员用户")
        # 尝试查找任何活跃用户
        staff = Staff.objects.filter(is_active=True).first()
        if not staff:
            print("❌ 没有找到任何活跃用户")
            return
        else:
            print(f"⚠️ 找到普通用户: {staff.username} - {staff.name}")
    else:
        print(f"✅ 找到管理员用户: {staff.username} - {staff.name}")
    
    print(f"✅ 找到管理员用户: {staff.username} - {staff.name}")
    print(f"   角色: {staff.role}")
    print(f"   是否管理员: {staff.is_manager}")
    
    # 2. 测试登录API
    print("\n2. 测试登录API...")
    client = Client()
    
    # 测试JSON登录
    login_data = {
        'username': staff.username,
        'password': '123456'  # 假设密码是123456
    }
    
    response = client.post('/api/login/', 
                          data=json.dumps(login_data),
                          content_type='application/json')
    
    print(f"   登录API响应状态码: {response.status_code}")
    
    if response.status_code == 200:
        try:
            data = response.json()
            print(f"   登录成功: {data.get('success', False)}")
            if data.get('success'):
                print(f"   用户信息: {data.get('user', {}).get('name')}")
                print(f"   Token存在: {'tokens' in data}")
            else:
                print(f"   登录失败原因: {data.get('message')}")
        except:
            print("   响应不是JSON格式")
    else:
        print(f"   登录失败，状态码: {response.status_code}")
        print(f"   响应内容: {response.content.decode('utf-8')[:200]}")
    
    # 3. 测试仪表板访问
    print("\n3. 测试仪表板访问...")
    
    # 创建带认证的请求
    factory = RequestFactory()
    request = factory.get('/admin/')
    
    # 添加session支持
    middleware = SessionMiddleware(lambda x: None)
    middleware.process_request(request)
    request.session.save()
    
    # 添加messages支持
    request._messages = FallbackStorage(request)
    
    # 模拟认证状态
    request.current_staff = staff
    request.is_authenticated = True
    request.auth_method = 'jwt'
    
    # 测试仪表板视图
    dashboard_view = DashboardView()
    try:
        response = dashboard_view.get(request)
        print(f"   仪表板访问状态码: {response.status_code}")
        if hasattr(response, 'url'):
            print(f"   重定向URL: {response.url}")
    except Exception as e:
        print(f"   仪表板访问异常: {e}")
    
    # 4. 检查URL配置
    print("\n4. 检查URL配置...")
    from django.urls import reverse
    try:
        login_url = reverse('organizations:admin:login')
        dashboard_url = reverse('organizations:admin:dashboard')
        print(f"   登录URL: {login_url}")
        print(f"   仪表板URL: {dashboard_url}")
    except Exception as e:
        print(f"   URL配置错误: {e}")
    
    # 5. 检查中间件配置
    print("\n5. 检查中间件配置...")
    from django.conf import settings
    middleware_list = settings.MIDDLEWARE
    auth_middlewares = [m for m in middleware_list if 'auth' in m.lower() or 'jwt' in m.lower()]
    print(f"   认证相关中间件: {auth_middlewares}")
    
    # 6. 测试前端登录页面
    print("\n6. 测试前端登录页面...")
    response = client.get('/admin/login/')
    print(f"   登录页面状态码: {response.status_code}")
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        if '/api/login/' in content:
            print("   ✅ 登录页面包含正确的API端点")
        else:
            print("   ❌ 登录页面可能没有正确的API端点")
        
        if 'window.location.href = \'/admin/\'' in content:
            print("   ✅ 登录页面包含正确的跳转逻辑")
        else:
            print("   ❌ 登录页面可能没有正确的跳转逻辑")

def test_middleware_chain():
    """测试中间件链"""
    print("\n=== 中间件链测试 ===")
    
    client = Client()
    
    # 测试未认证访问仪表板
    print("\n1. 测试未认证访问仪表板...")
    response = client.get('/admin/')
    print(f"   状态码: {response.status_code}")
    if hasattr(response, 'url'):
        print(f"   重定向到: {response.url}")
    
    # 测试登录页面访问
    print("\n2. 测试登录页面访问...")
    response = client.get('/admin/login/')
    print(f"   状态码: {response.status_code}")

if __name__ == '__main__':
    test_login_flow()
    test_middleware_chain()
    print("\n=== 调试完成 ===")
