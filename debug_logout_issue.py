#!/usr/bin/env python
"""
调试登出问题
"""
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
django.setup()

from organizations.models import Staff
from common.security.jwt_auth import JWTAuthentication
from organizations.views import LogoutView
from django.test import RequestFactory
import json

def test_logout_with_valid_token():
    """测试使用有效token登出"""
    try:
        print("🧪 测试使用有效token登出...")
        print("=" * 50)
        
        # 1. 获取用户和tokens
        staff = Staff.objects.get(username='testadmin')
        tokens = JWTAuthentication.generate_tokens(staff)
        access_token = tokens['access_token']
        
        print(f"   用户: {staff.name}")
        print(f"   Access Token: {access_token[:50]}...")
        
        # 2. 创建模拟请求
        factory = RequestFactory()
        request = factory.post('/admin/logout/')
        
        # 手动设置Authorization头
        request.META['HTTP_AUTHORIZATION'] = f'Bearer {access_token}'
        request.META['CONTENT_TYPE'] = 'application/json'
        request.META['HTTP_ACCEPT'] = 'application/json'
        
        # 3. 手动运行JWT认证中间件
        from common.security.middleware import JWTAuthenticationMiddleware
        middleware = JWTAuthenticationMiddleware(lambda x: None)
        middleware.process_request(request)
        
        print(f"   中间件认证结果:")
        print(f"      current_staff: {getattr(request, 'current_staff', None)}")
        print(f"      is_authenticated: {getattr(request, 'is_authenticated', False)}")
        print(f"      auth_method: {getattr(request, 'auth_method', None)}")
        
        # 4. 直接调用登出视图
        view = LogoutView()
        
        try:
            response = view.post(request)
            print(f"   视图返回状态码: {response.status_code}")
            
            if hasattr(response, 'content'):
                content = response.content.decode('utf-8')
                print(f"   视图返回内容: {content[:300]}...")
                
                try:
                    data = json.loads(content)
                    if data.get('success'):
                        print("✅ 登出视图调用成功!")
                        return True
                    else:
                        print(f"❌ 登出返回失败: {data.get('error')}")
                except json.JSONDecodeError:
                    print("   响应不是有效JSON")
            
        except Exception as e:
            print(f"❌ 登出视图调用异常: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            
        return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_jwt_middleware_behavior():
    """测试JWT中间件对不同路径的行为"""
    print("\n🛡️ 测试JWT中间件行为...")
    print("=" * 50)
    
    from common.security.middleware import JWTAuthenticationMiddleware
    
    # 获取有效token
    staff = Staff.objects.get(username='testadmin')
    tokens = JWTAuthentication.generate_tokens(staff)
    access_token = tokens['access_token']
    
    middleware = JWTAuthenticationMiddleware(lambda x: None)
    factory = RequestFactory()
    
    test_paths = [
        ('/admin/logout/', 'POST'),
        ('/admin/', 'GET'),
        ('/admin/login/', 'POST'),
        ('/admin/api/auth/refresh/', 'POST'),
    ]
    
    for path, method in test_paths:
        print(f"\n   测试路径: {method} {path}")
        
        if method == 'GET':
            request = factory.get(path)
        else:
            request = factory.post(path)
            
        # 设置JWT认证头
        request.META['HTTP_AUTHORIZATION'] = f'Bearer {access_token}'
        
        # 运行中间件
        try:
            result = middleware.process_request(request)
            print(f"      中间件返回: {result}")
            print(f"      是否跳过认证: {middleware._should_skip_auth(request)}")
            print(f"      认证结果: {getattr(request, 'is_authenticated', False)}")
            print(f"      用户: {getattr(request, 'current_staff', None)}")
        except Exception as e:
            print(f"      中间件错误: {e}")

if __name__ == '__main__':
    print("🚀 开始调试登出问题...")
    
    # 测试中间件行为
    test_jwt_middleware_behavior()
    
    # 测试登出逻辑
    success = test_logout_with_valid_token()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 登出功能调试完成，问题已定位!")
    else:
        print("⚠️  登出功能仍需修复")