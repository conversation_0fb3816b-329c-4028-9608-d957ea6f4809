# 企业考评系统业务逻辑简化方案

## 问题概述

当前系统存在以下业务逻辑复杂度问题：
1. 权重规则引擎过于复杂，普通管理员难以理解和维护
2. 三种评分模式（数值/等级/文本）混合使用可能导致用户困惑
3. 智能分配算法虽然强大，但边界情况处理可能不完善

## 解决方案总体思路

### 核心原则
- **简化优先**：提供简化模式，隐藏复杂配置
- **向导引导**：分步骤引导用户完成复杂操作
- **预设模板**：提供常用场景的预设配置
- **渐进式复杂度**：基础功能简单，高级功能可选

---

## 方案一：权重规则引擎简化

### 1.1 创建简化权重管理服务

```python
# evaluations/services/simplified_weighting.py
class SimplifiedWeightingService:
    """简化权重管理服务"""
    
    # 预设权重方案
    PRESET_SCHEMES = {
        'balanced': {
            'name': '平衡模式',
            'description': '所有评价关系权重相等，适合民主化考评',
            'rules': {
                'subordinate_to_superior': 1.0,
                'superior_to_subordinate': 1.0,
                'peer_to_peer': 1.0,
                'cross_department': 1.0
            }
        },
        'hierarchical': {
            'name': '层级模式',
            'description': '体现管理层级权威，适合传统企业',
            'rules': {
                'subordinate_to_superior': 0.8,
                'superior_to_subordinate': 1.2,
                'peer_to_peer': 1.0,
                'cross_department': 0.9
            }
        },
        'collaborative': {
            'name': '协作模式',
            'description': '强调团队协作，适合项目型组织',
            'rules': {
                'subordinate_to_superior': 1.1,
                'superior_to_subordinate': 1.0,
                'peer_to_peer': 1.2,
                'cross_department': 1.1
            }
        }
    }
    
    @classmethod
    def apply_preset_scheme(cls, batch, scheme_name):
        """应用预设权重方案"""
        if scheme_name not in cls.PRESET_SCHEMES:
            raise ValueError(f"未知的权重方案: {scheme_name}")
        
        scheme = cls.PRESET_SCHEMES[scheme_name]
        
        # 清除现有权重规则
        WeightingRule.objects.filter(
            batch=batch,
            is_system_generated=True
        ).update(is_active=False)
        
        # 创建新的权重规则
        for relation_type, weight in scheme['rules'].items():
            WeightingRule.objects.create(
                name=f"{scheme['name']}_{relation_type}",
                description=f"系统生成的{scheme['name']}权重规则",
                condition_type='relation_type',
                relation_type=relation_type,
                weight_factor=weight,
                priority=1,
                is_active=True,
                is_system_generated=True,
                created_by='system'
            )
```

### 1.2 简化权重管理界面

创建向导式权重配置界面：

```html
<!-- templates/admin/rule/wizard.html -->
<div class="weight-wizard">
    <!-- 步骤1：选择权重方案 -->
    <div class="wizard-step" id="step1">
        <h3>选择权重方案</h3>
        <div class="scheme-cards">
            <div class="scheme-card" data-scheme="balanced">
                <h4>平衡模式</h4>
                <p>所有评价关系权重相等</p>
                <div class="preview">1.0 : 1.0 : 1.0</div>
            </div>
            <div class="scheme-card" data-scheme="hierarchical">
                <h4>层级模式</h4>
                <p>体现管理层级权威</p>
                <div class="preview">0.8 : 1.2 : 1.0</div>
            </div>
            <div class="scheme-card" data-scheme="collaborative">
                <h4>协作模式</h4>
                <p>强调团队协作</p>
                <div class="preview">1.1 : 1.0 : 1.2</div>
            </div>
        </div>
    </div>
    
    <!-- 步骤2：微调权重（可选） -->
    <div class="wizard-step" id="step2" style="display:none;">
        <h3>微调权重（可选）</h3>
        <div class="weight-sliders">
            <div class="slider-group">
                <label>下级评上级</label>
                <input type="range" min="0.5" max="1.5" step="0.1" value="1.0">
                <span class="value">1.0</span>
            </div>
            <!-- 其他滑块... -->
        </div>
    </div>
    
    <!-- 步骤3：确认应用 -->
    <div class="wizard-step" id="step3" style="display:none;">
        <h3>确认配置</h3>
        <div class="config-summary">
            <!-- 配置摘要 -->
        </div>
    </div>
</div>
```

---

## 方案二：评分模式简化

### 2.1 创建评分模式向导

```python
# evaluations/services/template_wizard.py
class TemplateWizardService:
    """模板创建向导服务"""
    
    TEMPLATE_PRESETS = {
        'simple_numeric': {
            'name': '简单数值评分',
            'description': '适合快速评分，每项0-10分',
            'items': [
                {'name': '工作态度', 'max_score': 10, 'scoring_mode': 'score'},
                {'name': '工作能力', 'max_score': 10, 'scoring_mode': 'score'},
                {'name': '工作成果', 'max_score': 10, 'scoring_mode': 'score'},
                {'name': '团队协作', 'max_score': 10, 'scoring_mode': 'score'},
            ]
        },
        'detailed_tier': {
            'name': '详细等级评分',
            'description': '适合细致评价，使用等级标准',
            'items': [
                {
                    'name': '专业能力',
                    'scoring_mode': 'level',
                    'tiers': [
                        {'name': '优秀', 'value': 10, 'desc': '专业技能突出'},
                        {'name': '良好', 'value': 8, 'desc': '专业技能熟练'},
                        {'name': '一般', 'value': 6, 'desc': '专业技能基本'},
                        {'name': '待提升', 'value': 4, 'desc': '专业技能不足'}
                    ]
                }
                # 其他评分项...
            ]
        },
        'mixed_comprehensive': {
            'name': '综合评价模式',
            'description': '数值+等级+文本，全面评价',
            'items': [
                {'name': '量化指标', 'scoring_mode': 'score', 'max_score': 10},
                {'name': '能力等级', 'scoring_mode': 'level'},
                {'name': '改进建议', 'scoring_mode': 'text'}
            ]
        }
    }
    
    @classmethod
    def create_from_preset(cls, preset_name, template_name, created_by):
        """从预设创建模板"""
        if preset_name not in cls.TEMPLATE_PRESETS:
            raise ValueError(f"未知的模板预设: {preset_name}")
        
        preset = cls.TEMPLATE_PRESETS[preset_name]
        
        # 创建模板
        template = EvaluationTemplate.objects.create(
            name=template_name,
            description=preset['description'],
            template_type='structured',
            created_by=created_by
        )
        
        # 创建评分项
        for i, item_data in enumerate(preset['items']):
            item = EvaluationItem.objects.create(
                template=template,
                name=item_data['name'],
                scoring_mode=item_data['scoring_mode'],
                max_score=item_data.get('max_score', 10),
                sort_order=i,
                created_by=created_by
            )
            
            # 创建等级（如果是等级评分）
            if item_data['scoring_mode'] == 'level' and 'tiers' in item_data:
                for j, tier_data in enumerate(item_data['tiers']):
                    ScoringTier.objects.create(
                        item=item,
                        tier_name=tier_data['name'],
                        tier_value=tier_data['value'],
                        description=tier_data.get('desc', ''),
                        sort_order=j,
                        created_by=created_by
                    )
        
        return template
```

### 2.2 简化模板创建界面

```html
<!-- templates/admin/template/wizard.html -->
<div class="template-wizard">
    <!-- 步骤1：选择模板类型 -->
    <div class="wizard-step active">
        <h3>选择评分模式</h3>
        <div class="template-presets">
            <div class="preset-card" data-preset="simple_numeric">
                <div class="icon">📊</div>
                <h4>简单数值评分</h4>
                <p>适合快速评分，每项0-10分</p>
                <div class="features">
                    <span class="tag">快速</span>
                    <span class="tag">简单</span>
                </div>
            </div>
            
            <div class="preset-card" data-preset="detailed_tier">
                <div class="icon">⭐</div>
                <h4>详细等级评分</h4>
                <p>适合细致评价，使用等级标准</p>
                <div class="features">
                    <span class="tag">详细</span>
                    <span class="tag">标准化</span>
                </div>
            </div>
            
            <div class="preset-card" data-preset="mixed_comprehensive">
                <div class="icon">🎯</div>
                <h4>综合评价模式</h4>
                <p>数值+等级+文本，全面评价</p>
                <div class="features">
                    <span class="tag">全面</span>
                    <span class="tag">灵活</span>
                </div>
            </div>
            
            <div class="preset-card" data-preset="custom">
                <div class="icon">⚙️</div>
                <h4>自定义模式</h4>
                <p>完全自定义评分项和模式</p>
                <div class="features">
                    <span class="tag">高级</span>
                    <span class="tag">自定义</span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 步骤2：预览和确认 -->
    <div class="wizard-step">
        <h3>预览模板</h3>
        <div class="template-preview">
            <!-- 动态生成预览内容 -->
        </div>
    </div>
</div>
```

---

## 方案三：智能分配算法简化

### 3.1 创建分配策略服务

```python
# evaluations/services/assignment_strategy.py
class AssignmentStrategyService:
    """分配策略服务"""
    
    STRATEGIES = {
        'simple': {
            'name': '简单分配',
            'description': '每人评价直接上级和同级同事',
            'rules': {
                'include_superior': True,
                'include_peers': True,
                'include_subordinates': False,
                'cross_department': False,
                'max_evaluators': 5
            }
        },
        'comprehensive': {
            'name': '全面分配',
            'description': '360度评价，包含上级、同级、下级',
            'rules': {
                'include_superior': True,
                'include_peers': True,
                'include_subordinates': True,
                'cross_department': True,
                'max_evaluators': 10
            }
        },
        'peer_focused': {
            'name': '同级互评',
            'description': '主要由同级同事进行评价',
            'rules': {
                'include_superior': False,
                'include_peers': True,
                'include_subordinates': False,
                'cross_department': True,
                'max_evaluators': 8
            }
        }
    }
    
    @classmethod
    def execute_strategy(cls, batch, strategy_name, operator):
        """执行分配策略"""
        if strategy_name not in cls.STRATEGIES:
            raise ValueError(f"未知的分配策略: {strategy_name}")
        
        strategy = cls.STRATEGIES[strategy_name]
        rules = strategy['rules']
        
        # 使用简化的分配逻辑
        service = SimplifiedAssignmentService(batch, operator, rules)
        return service.execute()
```

### 3.2 简化分配界面

```html
<!-- templates/admin/batch/assignment_wizard.html -->
<div class="assignment-wizard">
    <div class="strategy-selection">
        <h3>选择分配策略</h3>
        <div class="strategy-cards">
            <div class="strategy-card" data-strategy="simple">
                <h4>简单分配</h4>
                <p>每人评价直接上级和同级同事</p>
                <div class="metrics">
                    <span>平均评价者：3-5人</span>
                    <span>复杂度：低</span>
                </div>
            </div>
            
            <div class="strategy-card" data-strategy="comprehensive">
                <h4>全面分配</h4>
                <p>360度评价，包含上级、同级、下级</p>
                <div class="metrics">
                    <span>平均评价者：8-10人</span>
                    <span>复杂度：高</span>
                </div>
            </div>
            
            <div class="strategy-card" data-strategy="peer_focused">
                <h4>同级互评</h4>
                <p>主要由同级同事进行评价</p>
                <div class="metrics">
                    <span>平均评价者：6-8人</span>
                    <span>复杂度：中</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="preview-section">
        <h4>分配预览</h4>
        <div class="assignment-preview">
            <!-- 动态显示分配结果预览 -->
        </div>
    </div>
</div>
```

---

## 实施计划

### 阶段一：创建简化服务层（1-2天）
1. 创建 `SimplifiedWeightingService`
2. 创建 `TemplateWizardService`
3. 创建 `AssignmentStrategyService`

### 阶段二：开发向导界面（2-3天）
1. 权重配置向导
2. 模板创建向导
3. 分配策略向导

### 阶段三：集成和测试（1-2天）
1. 集成新服务到现有系统
2. 添加简化模式开关
3. 用户体验测试

### 阶段四：文档和培训（1天）
1. 编写用户操作手册
2. 创建视频教程
3. 管理员培训材料

## 预期效果

1. **降低使用门槛**：普通管理员可以快速上手
2. **减少配置错误**：预设方案减少人为错误
3. **提高效率**：向导式操作提高配置速度
4. **保持灵活性**：高级用户仍可使用完整功能

## 风险控制

1. **向下兼容**：保留现有复杂功能，新增简化模式
2. **渐进迁移**：允许用户逐步从简化模式过渡到高级模式
3. **数据安全**：简化操作不影响数据完整性
4. **性能优化**：简化逻辑提高系统响应速度
