#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试中间件执行情况
"""

import requests
import json

def debug_middleware():
    """调试中间件执行情况"""
    base_url = "http://127.0.0.1:8000"
    
    print("=== 调试中间件执行情况 ===")
    
    # 1. 测试未认证访问仪表板
    print("\n1. 测试未认证访问仪表板...")
    try:
        response = requests.get(f"{base_url}/admin/", allow_redirects=False)
        print(f"   状态码: {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
        
        if response.status_code == 302:
            print(f"   重定向到: {response.headers.get('Location', '未知')}")
            print("   ✅ 中间件正确工作，重定向到登录页面")
        elif response.status_code == 200:
            print("   ❌ 中间件未工作，允许未认证访问")
            print(f"   响应内容（前200字符）: {response.text[:200]}")
        else:
            print(f"   ⚠️ 意外的状态码: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
    
    # 2. 测试登录后访问
    print("\n2. 测试登录后访问...")
    
    # 先登录获取token
    login_data = {"username": "admin", "password": "123456"}
    try:
        login_response = requests.post(
            f"{base_url}/api/login/",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )
        
        if login_response.status_code == 200:
            data = login_response.json()
            access_token = data.get('tokens', {}).get('access_token')
            
            if access_token:
                print("   ✅ 登录成功，获取到token")
                
                # 使用Cookie访问（模拟浏览器）
                print("\n   2.1 使用Cookie访问...")
                cookies = {'access_token': access_token}
                cookie_response = requests.get(
                    f"{base_url}/admin/",
                    cookies=cookies,
                    allow_redirects=False
                )
                print(f"       状态码: {cookie_response.status_code}")
                
                # 使用Authorization头访问
                print("\n   2.2 使用Authorization头访问...")
                headers = {'Authorization': f'Bearer {access_token}'}
                auth_response = requests.get(
                    f"{base_url}/admin/",
                    headers=headers,
                    allow_redirects=False
                )
                print(f"       状态码: {auth_response.status_code}")
                
            else:
                print("   ❌ 登录成功但未获取到token")
        else:
            print(f"   ❌ 登录失败: {login_response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 登录异常: {e}")
    
    # 3. 测试其他路径
    print("\n3. 测试其他路径...")
    
    test_paths = [
        "/admin/login/",
        "/api/login/",
        "/static/css/style.css",
        "/admin/departments/",
        "/admin/staff/"
    ]
    
    for path in test_paths:
        try:
            response = requests.get(f"{base_url}{path}", allow_redirects=False)
            print(f"   {path}: {response.status_code}")
            if response.status_code == 302:
                print(f"       重定向到: {response.headers.get('Location', '未知')}")
        except Exception as e:
            print(f"   {path}: 异常 - {e}")

if __name__ == '__main__':
    debug_middleware()
    print("\n=== 调试完成 ===")
