#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查当前项目的URL配置
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
django.setup()

from django.urls import get_resolver
from django.core.management import execute_from_command_line

def check_login_urls():
    """检查登录相关的URL"""
    print("=== 检查登录相关URL ===")
    
    resolver = get_resolver()
    
    def print_patterns(patterns, prefix=""):
        for pattern in patterns:
            pattern_str = str(pattern.pattern)
            if hasattr(pattern, 'url_patterns'):
                # 这是一个include的URL
                print(f"{prefix}{pattern_str} -> [包含子URL]")
                print_patterns(pattern.url_patterns, prefix + "  ")
            else:
                # 这是一个具体的URL
                view_name = getattr(pattern, 'name', 'unnamed') or 'unnamed'
                if 'login' in pattern_str.lower() or 'login' in view_name.lower():
                    print(f"{prefix}{pattern_str} -> {view_name}")
    
    print_patterns(resolver.url_patterns)

def check_available_urls():
    """检查所有可用的URL"""
    print("\n=== 所有可用的URL模式 ===")
    
    resolver = get_resolver()
    
    def collect_urls(patterns, prefix=""):
        urls = []
        for pattern in patterns:
            pattern_str = str(pattern.pattern)
            current_prefix = prefix + pattern_str
            
            if hasattr(pattern, 'url_patterns'):
                # 递归处理子URL
                urls.extend(collect_urls(pattern.url_patterns, current_prefix))
            else:
                # 具体的URL
                view_name = getattr(pattern, 'name', 'unnamed') or 'unnamed'
                urls.append((current_prefix, view_name))
        return urls
    
    all_urls = collect_urls(resolver.url_patterns)
    
    # 过滤包含login的URL
    login_urls = [url for url in all_urls if 'login' in url[0].lower() or 'login' in url[1].lower()]
    
    print("登录相关的URL:")
    for url_pattern, name in login_urls:
        print(f"  {url_pattern} -> {name}")
    
    print(f"\n总共找到 {len(login_urls)} 个登录相关URL")
    
    # 检查是否有jwt-login
    jwt_urls = [url for url in all_urls if 'jwt' in url[0].lower() or 'jwt' in url[1].lower()]
    print(f"\nJWT相关的URL:")
    for url_pattern, name in jwt_urls:
        print(f"  {url_pattern} -> {name}")

def check_admin_urls():
    """检查管理端URL"""
    print("\n=== 管理端URL ===")
    
    resolver = get_resolver()
    
    def collect_urls(patterns, prefix=""):
        urls = []
        for pattern in patterns:
            pattern_str = str(pattern.pattern)
            current_prefix = prefix + pattern_str
            
            if hasattr(pattern, 'url_patterns'):
                urls.extend(collect_urls(pattern.url_patterns, current_prefix))
            else:
                view_name = getattr(pattern, 'name', 'unnamed') or 'unnamed'
                urls.append((current_prefix, view_name))
        return urls
    
    all_urls = collect_urls(resolver.url_patterns)
    
    # 过滤admin相关的URL
    admin_urls = [url for url in all_urls if 'admin' in url[0].lower()]
    
    print("管理端URL:")
    for url_pattern, name in admin_urls[:20]:  # 只显示前20个
        print(f"  {url_pattern} -> {name}")
    
    if len(admin_urls) > 20:
        print(f"  ... 还有 {len(admin_urls) - 20} 个管理端URL")

if __name__ == '__main__':
    check_login_urls()
    check_available_urls()
    check_admin_urls()
