#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
通信模块功能测试脚本
验证消息发送和公告管理功能是否正常
"""

import os
import sys
import django
from django.test import TestCase, Client
from django.urls import reverse
import json

# 配置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
django.setup()

from organizations.models import Department, Staff
from communications.models import Message, MessageRecipient, Announcement


def create_test_data():
    """创建测试数据"""
    print("正在创建测试数据...")
    
    # 创建测试部门
    dept, created = Department.objects.get_or_create(
        dept_code='TEST001',
        defaults={
            'name': '测试部门',
            'description': '用于测试的部门',
            'is_active': True,
        }
    )
    
    # 创建测试管理员
    admin_staff, created = Staff.objects.get_or_create(
        username='test_admin',
        defaults={
            'employee_no': 'ADM001',
            'name': '测试管理员',
            'department': dept,
            'role': 'admin',
            'is_active': True,
            'anonymous_code': 'TEST001',
        }
    )
    
    if created:
        admin_staff.set_password('admin123')
        admin_staff.save()
    
    # 创建测试员工
    test_staff, created = Staff.objects.get_or_create(
        username='test_user',
        defaults={
            'employee_no': 'USR001',
            'name': '测试员工',
            'department': dept,
            'role': 'employee',
            'is_active': True,
            'anonymous_code': 'TEST002',
        }
    )
    
    if created:
        test_staff.set_password('user123')
        test_staff.save()
    
    print(f"测试数据创建完成:")
    print(f"- 部门: {dept.name}")
    print(f"- 管理员: {admin_staff.name} (用户名: {admin_staff.username})")
    print(f"- 员工: {test_staff.name} (用户名: {test_staff.username})")
    
    return dept, admin_staff, test_staff


def test_authentication():
    """测试认证功能"""
    print("\n=== 测试认证功能 ===")
    
    client = Client()
    
    # 测试管理员登录
    login_url = reverse('organizations:admin:login')
    response = client.post(login_url, {
        'username': 'test_admin',
        'password': 'admin123'
    }, follow=True)
    
    print(f"管理员登录状态码: {response.status_code}")
    if response.status_code == 200:
        print("✓ 管理员登录成功")
    else:
        print("✗ 管理员登录失败")
    
    return client


def test_staff_data_api(client):
    """测试员工数据API"""
    print("\n=== 测试员工数据API ===")
    
    try:
        staff_api_url = reverse('communications:staff_data_api')
        response = client.get(staff_api_url)
        
        print(f"员工数据API状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                departments = data.get('departments', {})
                print(f"✓ 员工数据API成功，获取到 {len(departments)} 个部门")
                for dept_name, staff_list in departments.items():
                    print(f"  - {dept_name}: {len(staff_list)} 名员工")
            else:
                print(f"✗ 员工数据API返回错误: {data.get('error')}")
        else:
            print("✗ 员工数据API请求失败")
    
    except Exception as e:
        print(f"✗ 员工数据API测试异常: {e}")


def test_message_send_api(client):
    """测试消息发送API"""
    print("\n=== 测试消息发送API ===")
    
    try:
        # 获取测试员工ID
        test_staff = Staff.objects.get(username='test_user')
        
        send_api_url = reverse('communications:message_send_api')
        
        message_data = {
            'message_type': 'PERSONAL',
            'priority': 'MEDIUM',
            'subject': '测试消息',
            'content': '这是一条测试消息，验证发送功能是否正常。',
            'recipients': [test_staff.id],
            'require_read': True,
            'allow_reply': True
        }
        
        response = client.post(
            send_api_url,
            data=json.dumps(message_data),
            content_type='application/json'
        )
        
        print(f"消息发送API状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"✓ 消息发送成功，消息ID: {data.get('message_id')}")
                print(f"  收件人数量: {data.get('recipient_count')}")
            else:
                print(f"✗ 消息发送失败: {data.get('error')}")
        else:
            print("✗ 消息发送API请求失败")
    
    except Exception as e:
        print(f"✗ 消息发送API测试异常: {e}")


def test_announcement_functionality(client):
    """测试公告功能"""
    print("\n=== 测试公告功能 ===")
    
    try:
        # 创建测试公告
        admin_staff = Staff.objects.get(username='test_admin')
        
        announcement = Announcement.objects.create(
            title='测试公告',
            content='这是一条测试公告，验证公告管理功能。',
            announcement_type='SYSTEM',
            priority='HIGH',
            created_by=admin_staff,
            is_published=True,
            is_pinned=True
        )
        
        print(f"✓ 测试公告创建成功，ID: {announcement.id}")
        
        # 测试公告列表页面
        announcement_list_url = reverse('communications:admin:announcement_list')
        response = client.get(announcement_list_url)
        
        print(f"公告列表页面状态码: {response.status_code}")
        if response.status_code == 200:
            print("✓ 公告列表页面访问成功")
        else:
            print("✗ 公告列表页面访问失败")
        
        # 测试公告删除API
        delete_api_url = reverse('communications:announcement_delete_api', args=[announcement.id])
        response = client.post(delete_api_url)
        
        print(f"公告删除API状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✓ 公告删除成功")
            else:
                print(f"✗ 公告删除失败: {data.get('error')}")
        else:
            print("✗ 公告删除API请求失败")
    
    except Exception as e:
        print(f"✗ 公告功能测试异常: {e}")


def test_message_center_pages(client):
    """测试消息中心页面"""
    print("\n=== 测试消息中心页面 ===")
    
    try:
        # 测试消息中心主页
        message_center_url = reverse('communications:admin:message_center')
        response = client.get(message_center_url)
        
        print(f"消息中心页面状态码: {response.status_code}")
        if response.status_code == 200:
            print("✓ 消息中心页面访问成功")
        else:
            print("✗ 消息中心页面访问失败")
        
        # 测试写消息页面
        compose_url = reverse('communications:admin:message_compose')
        response = client.get(compose_url)
        
        print(f"写消息页面状态码: {response.status_code}")
        if response.status_code == 200:
            print("✓ 写消息页面访问成功")
        else:
            print("✗ 写消息页面访问失败")
    
    except Exception as e:
        print(f"✗ 消息中心页面测试异常: {e}")


def main():
    """主测试函数"""
    print("=" * 50)
    print("通信模块功能测试")
    print("=" * 50)
    
    # 创建测试数据
    dept, admin_staff, test_staff = create_test_data()
    
    # 测试认证
    client = test_authentication()
    
    if client:
        # 测试各项功能
        test_staff_data_api(client)
        test_message_send_api(client) 
        test_announcement_functionality(client)
        test_message_center_pages(client)
    
    print("\n" + "=" * 50)
    print("测试完成!")
    print("=" * 50)


if __name__ == '__main__':
    main()