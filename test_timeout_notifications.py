#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试考评超时异常通知功能
"""

import os
import sys
import django
from datetime import timedelta

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UniversalStaffEvaluation3.settings')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

django.setup()

from django.utils import timezone
from communications.services import notification_service
from communications.models import NotificationTemplate, Message, MessageRecipient
from organizations.models import Staff, Department
from evaluations.models import EvaluationBatch, EvaluationTemplate, EvaluationRelation


def create_test_timeout_template():
    """创建测试用超时通知模板"""
    print("=== 创建超时通知模板 ===")
    
    template, created = NotificationTemplate.objects.get_or_create(
        template_type='EVALUATION_TIMEOUT_ALERT',
        defaults={
            'name': '考评超时异常通知模板（测试）',
            'subject_template': '🚨 考评超时警报：批次【{batch_name}】有{timeout_count}个任务超时',
            'content_template': '''
<div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
    <div style="background: #dc2626; color: white; padding: 16px; border-radius: 8px 8px 0 0;">
        <h2 style="margin: 0; font-size: 20px;">⚠️ 考评超时异常警报</h2>
    </div>
    
    <div style="background: #ffffff; padding: 20px; border: 1px solid #e5e7eb; border-top: none; border-radius: 0 0 8px 8px;">
        <h3 style="color: #991b1b;">📊 超时统计</h3>
        <ul>
            <li><strong>批次名称：</strong>{batch_name}</li>
            <li><strong>超时任务数：</strong>{timeout_count} 个</li>
            <li><strong>超时天数：</strong>{overdue_days} 天</li>
            <li><strong>涉及部门：</strong>{departments}</li>
        </ul>
        
        <h3 style="color: #991b1b;">📋 超时任务详情</h3>
        <p>截止时间：{batch_end_date}</p>
        <table style="width: 100%; border-collapse: collapse;">
            <thead>
                <tr style="background: #f9fafb;">
                    <th style="padding: 8px; border: 1px solid #e5e7eb;">评价者</th>
                    <th style="padding: 8px; border: 1px solid #e5e7eb;">部门</th>
                    <th style="padding: 8px; border: 1px solid #e5e7eb;">被评价者</th>
                    <th style="padding: 8px; border: 1px solid #e5e7eb;">超时天数</th>
                </tr>
            </thead>
            <tbody>
                {timeout_users_table}
            </tbody>
        </table>
        
        <div style="margin-top: 20px; padding: 12px; background: #fffbeb; border: 1px solid #fed7aa; border-radius: 4px;">
            <h4 style="margin: 0 0 8px 0; color: #92400e;">🎯 建议处理措施</h4>
            <ul style="margin: 0; color: #92400e;">
                <li>联系相关部门负责人</li>
                <li>向超时员工发送催促通知</li>
                <li>检查系统是否存在问题</li>
            </ul>
        </div>
        
        <p style="margin-top: 16px; text-align: center; color: #6b7280; font-size: 12px;">
            此警报由系统自动发送 | 发送时间：{current_time}
        </p>
    </div>
</div>
            '''.strip(),
            'variables': {
                'batch_name': '考评批次名称',
                'timeout_count': '超时任务数量',
                'overdue_days': '超时天数',
                'departments': '涉及部门列表',
                'batch_end_date': '批次截止日期',
                'timeout_users_table': '超时用户表格HTML',
                'current_time': '当前发送时间'
            }
        }
    )
    
    if created:
        print(f"✅ 创建超时通知模板: {template.name}")
    else:
        print(f"✅ 超时通知模板已存在: {template.name}")
    
    return template


def create_test_timeout_scenario():
    """创建测试超时场景"""
    print("\n=== 创建测试超时场景 ===")
    
    # 获取测试用户和模板
    staff_users = list(Staff.objects.filter(deleted_at__isnull=True)[:3])
    if len(staff_users) < 2:
        print("❌ 需要至少2个测试用户")
        return None, None
    
    template = EvaluationTemplate.objects.first()
    if not template:
        print("❌ 没有找到考评模板")
        return None, None
    
    # 创建一个已过期的测试批次
    past_date = timezone.now() - timedelta(days=3)  # 3天前截止
    
    batch = EvaluationBatch.objects.create(
        name="超时测试批次",
        description="用于测试超时通知功能的批次",
        default_template=template,
        start_date=past_date - timedelta(days=30),
        end_date=past_date,  # 已经过期3天
        status='active'  # 仍然是活跃状态，表示有未完成任务
    )
    
    print(f"📋 创建测试批次: {batch.name}")
    print(f"   截止时间: {batch.end_date}")
    print(f"   超时天数: {(timezone.now().date() - batch.end_date.date()).days}天")
    
    # 创建一些未完成的考评关系（模拟超时情况）
    timeout_relations = []
    for i, evaluator in enumerate(staff_users[:2]):
        evaluatee = staff_users[(i + 1) % len(staff_users)]
        
        relation = EvaluationRelation.objects.create(
            batch=batch,
            evaluator=evaluator,
            evaluatee=evaluatee,
            template=template,
            weight_factor=1.0,
            status='pending',  # 未完成状态
            created_by='test_system'
        )
        timeout_relations.append(relation)
        print(f"   📝 创建超时关系: {evaluator.name} 评价 {evaluatee.name}")
    
    return batch, timeout_relations


def test_timeout_notification():
    """测试超时通知发送"""
    print("\n=== 测试超时通知发送 ===")
    
    # 创建测试场景
    batch, timeout_relations = create_test_timeout_scenario()
    if not batch:
        return False
    
    # 测试超时警报发送
    from django.db.models import Q
    timeout_relations_qs = EvaluationRelation.objects.filter(
        batch=batch,
        status__in=['pending', 'draft']
    )
    
    success = notification_service.send_evaluation_timeout_alert(
        batch, timeout_relations_qs
    )
    
    if success:
        print("✅ 超时警报发送成功")
        
        # 检查消息创建情况
        recent_messages = Message.objects.filter(
            related_model='EvaluationBatch',
            related_id=batch.id,
            message_type='SYSTEM'
        ).order_by('-created_at')
        
        for message in recent_messages[:1]:  # 只显示最新的一条
            recipients_count = message.recipients.count()
            print(f"   📧 管理员警报: {message.subject}")
            print(f"   👥 接收者数量: {recipients_count}")
        
        # 检查个人催促通知
        user_messages = Message.objects.filter(
            message_type='EVALUATION',
            subject__contains='超时催促'
        ).order_by('-created_at')
        
        print(f"   📨 用户催促通知: {user_messages.count()}条")
        
    else:
        print("❌ 超时警报发送失败")
    
    # 清理测试数据
    batch.delete()
    print("🧹 清理测试数据完成")
    
    return success


def test_timeout_check_batch():
    """测试批量超时检查"""
    print("\n=== 测试批量超时检查 ===")
    
    # 创建测试场景
    batch, timeout_relations = create_test_timeout_scenario()
    if not batch:
        return
    
    # 执行批量超时检查
    sent_count = notification_service.check_and_send_timeout_alerts()
    
    print(f"📊 批量检查结果: 发送了{sent_count}个警报")
    
    if sent_count > 0:
        print("✅ 批量超时检查成功")
    else:
        print("⚠️ 没有需要发送的警报（可能是时间条件不满足）")
    
    # 清理测试数据
    batch.delete()
    print("🧹 清理测试数据完成")


def main():
    """主测试函数"""
    print("🚀 开始测试考评超时异常通知功能\n")
    
    try:
        # 创建超时通知模板
        create_test_timeout_template()
        
        # 测试超时通知发送
        success = test_timeout_notification()
        
        # 测试批量超时检查
        test_timeout_check_batch()
        
        print("\n🎉 超时异常通知功能测试完成！")
        
        if success:
            print("✅ 所有测试通过，功能正常工作")
        else:
            print("⚠️ 部分测试未通过，请检查日志")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()